# StoryWeaver (故事织梦) 🎨📚

> AI驱动的个性化儿童有声绘本创作平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![Cloudflare Workers](https://img.shields.io/badge/Cloudflare-Workers-orange)](https://workers.cloudflare.com/)
[![React](https://img.shields.io/badge/React-18-blue)](https://reactjs.org/)

## 🌟 项目简介

StoryWeaver是一个充满魔力的AI平台，让父母和孩子能够共同创作个性化的、配有精美插图和生动旁白的故事书。只需提供主角的名字、性格特点和故事主题，平台就能生成独一无二的故事，配上统一风格的插图，并制作成带有生动旁白的有声读物。

## 📚 文档导航

### 🏠 核心文档
- [项目详细介绍](./docs/core/项目介绍.md) - 项目概念、愿景和核心功能
- [项目状态总结](./docs/core/项目状态总结_2025-01-02.md) - 最新项目进展和状态
- [完整项目文档](./docs/core/README.md) - 详细的技术架构和使用指南
- [贡献指南](./docs/core/CONTRIBUTING.md) - 如何参与项目开发
- [行为准则](./docs/core/CODE_OF_CONDUCT.md) - 社区行为规范
- [安全指南](./docs/core/SECURITY.md) - 安全相关信息

### 🏗️ 架构设计
- [完整项目计划](./docs/architecture/StoryWeaver_完整项目计划.md) - 项目整体规划
- [DurableObjects实施计划](./docs/architecture/StoryWeaver_DurableObjects_实施计划.md) - 实时功能架构
- [技术实现方案](./docs/architecture/technical_implementation.md) - 技术选型和实现细节
- [网站框架设计](./docs/architecture/website_framework.md) - 前端架构设计
- [UI/UX设计指南](./docs/architecture/ui_ux_design_guide.md) - 用户界面设计规范

### 👨‍💻 开发指南
- [开发指南](./docs/development/DEVELOPMENT_GUIDE.md) - 基础开发环境搭建
- [开发指南 Part 2](./docs/development/DEVELOPMENT_GUIDE_PART2.md) - 高级开发技巧
- [开发指南 Part 3](./docs/development/DEVELOPMENT_GUIDE_PART3.md) - 测试和调试
- [开发指南 Part 4](./docs/development/DEVELOPMENT_GUIDE_PART4.md) - 部署和维护
- [前端开发指南](./docs/development/FRONTEND_DEVELOPMENT_GUIDE.md) - 前端开发详细说明
- [DurableObjects快速开始](./docs/development/DurableObjects_快速开始指南.md) - 实时功能开发
- [性能优化指南](./docs/development/PERFORMANCE_GUIDE.md) - 性能优化最佳实践

### 🚀 部署文档
- [部署指南](./docs/deployment/DEPLOYMENT_GUIDE.md) - 基础部署流程
- [生产环境部署](./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md) - 生产环境配置
- [部署报告](./docs/deployment/PRODUCTION_DEPLOYMENT_REPORT.md) - 部署状态和问题记录

### 🔌 API文档
- [API概览](./docs/api/README.md) - 后端API总体介绍
- [API详细文档](./docs/api/API_DOCUMENTATION.md) - 完整API接口说明
- [环境变量配置](./docs/api/ENVIRONMENT_VARIABLES.md) - 环境变量设置指南
- [API部署指南](./docs/api/DEPLOYMENT.md) - 后端部署说明
- [API测试指南](./docs/api/TESTING.md) - 后端测试方法

### 🎨 前端文档
- [前端概览](./docs/frontend/README.md) - 前端项目介绍
- [StoryWeaver前端](./docs/frontend/README-StoryWeaver.md) - 项目特定前端说明
- [构建指南](./docs/frontend/BUILD-GUIDE.md) - 前端构建流程
- [前端部署](./docs/frontend/DEPLOYMENT-FULL.md) - 前端部署详细说明
- [Stripe集成](./docs/frontend/STRIPE_INTEGRATION.md) - 支付系统集成
- [支付测试](./docs/frontend/test-stripe-payment.md) - 支付功能测试

### 📁 历史记录
- [修复记录归档](./docs/archive/fixes/) - 历史问题修复记录

## 🚀 快速开始

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
npm install
npm run dev
```

## 🌐 在线访问

- **前端**: https://storyweaver.pages.dev
- **后端API**: https://storyweaver-api.stawky.workers.dev

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献！请查看我们的[贡献指南](./docs/core/CONTRIBUTING.md)了解如何参与项目开发。

## 📞 联系我们

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-repo/issues)
- 发送邮件至项目维护者

---

*最后更新: 2025-01-04*