# StoryWeaver Backend - 生产环境配置 (免费计划兼容)
name = "storyweaver-api"
main = "src/index.ts"
compatibility_date = "2024-06-01"
compatibility_flags = ["nodejs_compat"]

# 生产环境变量（使用明文配置）
[vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.pages.dev"
GOOGLE_CLIENT_ID = "463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com"
STRIPE_PUBLISHABLE_KEY = "pk_test_51RfGiA2azdJC9VD2y5T6LIwDx4dLgb6EHOXdAiCWmz7LwW48jYnyLz4xQbsqQmj7wCV7UK1y43TlOrh9CwJgoPUd00z5VOHjsG"
STRIPE_SECRET_KEY = "sk_test_51RfGiA2azdJC9VD2y5T6LIwDx4dLgb6EHOXdAiCWmz7LwW48jYnyLz4xQbsqQmj7wCV7UK1y43TlOrh9CwJgoPUd00z5VOHjsG"
JWT_SECRET = "storyweaver-jwt-secret-key-2024"

# 数据库和存储配置
[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"

# 日志配置
[observability.logs]
enabled = true
