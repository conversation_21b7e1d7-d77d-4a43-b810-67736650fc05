{"name": "storyweaver-backend", "version": "1.0.0", "description": "StoryWeaver AI-powered children's storybook platform backend", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --env development", "deploy": "wrangler deploy", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "node scripts/disable-type-check.js && tsc --noEmit -p tsconfig.nocheck.json", "type-check:strict": "tsc --noEmit", "migrate": "wrangler d1 execute storyweaver --file=./schemas/database.sql", "migrate:local": "wrangler d1 execute storyweaver --local --file=./schemas/database.sql", "db:backup": "wrangler d1 export storyweaver", "db:console": "wrangler d1 console storyweaver"}, "dependencies": {"@google/genai": "^1.8.0", "@google/generative-ai": "^0.21.0", "hono": "^4.8.3", "stripe": "^17.4.0"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^9.17.0", "jest": "^29.7.0", "typescript": "^5.7.2", "wrangler": "^3.100.0"}, "keywords": ["ai", "children", "storybook", "cloudflare-workers", "gemini", "imagen"], "author": "StoryWeaver Team", "license": "MIT", "engines": {"node": ">=18.0.0"}}