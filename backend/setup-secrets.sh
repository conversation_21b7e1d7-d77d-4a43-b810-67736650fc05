#!/bin/bash

# StoryWeaver 后端环境变量配置脚本
# 使用 wrangler secret put 命令设置 Cloudflare Workers 的环境变量

echo "🔧 配置 StoryWeaver 后端环境变量..."

# Gemini API 密钥 (使用第一个密钥)
echo "设置 Gemini API 密钥..."
echo "AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw" | wrangler secret put GEMINI_API_KEY

# Google OAuth 配置 (需要用户提供)
echo "⚠️  Google OAuth 配置需要手动设置:"
echo "请先获取 Google OAuth 客户端ID和密钥，然后运行:"
echo "wrangler secret put GOOGLE_CLIENT_ID"
echo "wrangler secret put GOOGLE_CLIENT_SECRET"
echo ""

# Stripe 配置 (测试模式)
echo "设置 Stripe 密钥 (测试模式)..."
echo "sk_test_51RfGiA2azdJC9VD2pw6iqqZ7YslQYTCwvksZnrM4ESJwOGvMMPwq19WhvM3B6i0qwOxKqNyR868D69y3lK0e0qxC00CccfMW6o" | wrangler secret put STRIPE_SECRET_KEY

echo "设置 Stripe Webhook 密钥..."
echo "whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA" | wrangler secret put STRIPE_WEBHOOK_SECRET

# JWT 密钥 (生成随机密钥)
echo "设置 JWT 密钥..."
JWT_SECRET=$(openssl rand -base64 32)
echo "$JWT_SECRET" | wrangler secret put JWT_SECRET

echo "✅ 环境变量配置完成!"
echo ""
echo "📝 还需要手动配置的项目:"
echo "1. Google OAuth 客户端ID和密钥"
echo "2. 创建 Cloudflare 资源 (KV, R2, D1)"
echo "3. 更新 wrangler.toml 配置文件"
echo ""
echo "🚀 配置完成后可以运行:"
echo "cd backend && npm run dev"
