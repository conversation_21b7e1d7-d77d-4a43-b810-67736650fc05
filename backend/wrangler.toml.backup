name = "storyweaver-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 开发环境配置
[env.development]
[env.development.vars]
ENVIRONMENT = "development"
CORS_ORIGIN = "http://localhost:3000,https://storyweaver.jamintextiles.com"

[[env.development.kv_namespaces]]
binding = "CACHE"
id = "your-dev-kv-namespace-id"
preview_id = "your-dev-kv-preview-id"

[[env.development.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets-dev"

[[env.development.d1_databases]]
binding = "DB"
database_name = "storyweaver-dev"
database_id = "your-dev-d1-database-id"

# 生产环境配置
[env.production]
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.jamintextiles.com"

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-prod-kv-namespace-id"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[env.production.d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "your-prod-d1-database-id"

# 环境变量（通过 wrangler secret 命令设置）
# wrangler secret put GEMINI_API_KEY
# wrangler secret put GOOGLE_CLIENT_ID
# wrangler secret put GOOGLE_CLIENT_SECRET
# wrangler secret put STRIPE_SECRET_KEY
# wrangler secret put STRIPE_WEBHOOK_SECRET
# wrangler secret put JWT_SECRET

# 路由配置
[[routes]]
pattern = "api.storyweaver.com/*"
zone_name = "storyweaver.com"

# 限制配置
[limits]
cpu_ms = 30000  # 30秒CPU时间限制（适合AI生成任务）

# 构建配置
[build]
command = "npm run build"

# 部署配置
[triggers]
crons = ["0 2 * * *"]  # 每天凌晨2点执行清理任务