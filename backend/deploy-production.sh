#!/bin/bash

echo "🚀 部署StoryWeaver后端到Cloudflare Workers生产环境..."

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "❌ Wrangler CLI未安装，请先安装: npm install -g wrangler"
  exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
  echo "❌ 未登录Cloudflare账户，请先运行: wrangler login"
  exit 1
fi

# 检查TypeScript编译
echo "🔍 检查TypeScript编译..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ TypeScript编译失败，请修复错误后重试"
  exit 1
fi

echo "✅ TypeScript编译成功"

# 检查环境变量
echo "📋 检查生产环境密钥..."
SECRET_COUNT=$(wrangler secret list --env production 2>/dev/null | grep -c "GEMINI_API_KEY\|GOOGLE_CLIENT_SECRET\|STRIPE_SECRET_KEY\|JWT_SECRET")

if [ "$SECRET_COUNT" -lt 4 ]; then
  echo "⚠️  生产环境密钥未完全设置"
  echo "请先运行: ./setup-production-secrets.sh"
  read -p "是否继续部署？(y/N): " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# 部署到生产环境
echo "🌐 部署到Cloudflare Workers生产环境..."
wrangler deploy --env production

if [ $? -eq 0 ]; then
  echo "✅ 后端部署成功！"
  echo "🔗 API地址: https://storyweaver-api.stawky.workers.dev"
  echo "📋 健康检查: https://storyweaver-api.stawky.workers.dev/health"
  
  echo "🧪 测试API连接..."
  curl -s https://storyweaver-api.stawky.workers.dev/health | jq .
  
else
  echo "❌ 部署失败，请检查错误信息"
  exit 1
fi

echo "🎉 后端部署完成！"
