// @ts-nocheck
/**
 * 调试路由 - 用于测试各个AI功能模块
 */

import { GeminiService } from '../services/gemini';

export interface DebugRequest {
  test: 'text' | 'image' | 'audio' | 'all';
  content?: string;
}

export class DebugRoutes {
  private geminiService: GeminiService;

  constructor(geminiApiKey: string) {
    this.geminiService = new GeminiService(geminiApiKey);
  }

  /**
   * 处理调试请求
   */
  async handleDebugRequest(request: Request): Promise<Response> {
    try {
      const body = await request.json() as DebugRequest;
      const { test, content } = body;

      console.log(`[DEBUG] Testing: ${test}`);

      switch (test) {
        case 'text':
          return await this.testTextGeneration(content);
        case 'image':
          return await this.testImageGeneration(content);
        case 'audio':
          return await this.testAudioGeneration(content);
        case 'all':
          return await this.testAllFeatures();
        case 'cleanup':
          return await this.cleanupStuckTasks();
        default:
          return new Response(JSON.stringify({ 
            success: false, 
            error: 'Invalid test type' 
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
      }
    } catch (error) {
      console.error('[DEBUG] Error:', error);
      return new Response(JSON.stringify({ 
        success: false, 
        error: error.message 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 测试文本生成
   */
  private async testTextGeneration(customContent?: string): Promise<Response> {
    console.log('[DEBUG] Testing text generation...');
    
    const testRequest = {
      characterName: '小测试',
      characterAge: 5,
      characterTraits: ['聪明', '可爱'],
      theme: '友谊',
      setting: '花园',
      style: 'cartoon' as const,
      voice: 'gentle_female' as const
    };

    const startTime = Date.now();
    const result = await this.geminiService.generateStory(testRequest);
    const duration = Date.now() - startTime;

    console.log(`[DEBUG] Text generation completed in ${duration}ms`);
    console.log(`[DEBUG] Generated story with ${result.pages.length} pages`);

    return new Response(JSON.stringify({
      success: true,
      test: 'text',
      duration: `${duration}ms`,
      result: {
        title: result.title,
        pageCount: result.pages.length,
        totalLength: result.fullText.length,
        firstPage: result.pages[0]?.text.substring(0, 100) + '...'
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 测试图片生成
   */
  private async testImageGeneration(customPrompt?: string): Promise<Response> {
    console.log('[DEBUG] Testing image generation...');
    
    const testPrompt = customPrompt || 'A cute little girl playing in the garden, cartoon style, vibrant colors';
    
    const startTime = Date.now();
    const result = await this.geminiService.generateImages([testPrompt], 'cartoon');
    const duration = Date.now() - startTime;

    console.log(`[DEBUG] Image generation completed in ${duration}ms`);
    console.log(`[DEBUG] Generated ${result.length} images`);

    return new Response(JSON.stringify({
      success: true,
      test: 'image',
      duration: `${duration}ms`,
      result: {
        imageCount: result.length,
        imageUrls: result,
        prompt: testPrompt
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 测试音频生成
   */
  private async testAudioGeneration(customText?: string): Promise<Response> {
    console.log('[DEBUG] Testing audio generation...');
    
    const testText = customText || '你好，我是小助手，很高兴为你讲故事！';
    
    const startTime = Date.now();
    const result = await this.geminiService.generateAudio(testText, 'gentle_female');
    const duration = Date.now() - startTime;

    console.log(`[DEBUG] Audio generation completed in ${duration}ms`);
    console.log(`[DEBUG] Generated audio URL: ${result.substring(0, 50)}...`);

    return new Response(JSON.stringify({
      success: true,
      test: 'audio',
      duration: `${duration}ms`,
      result: {
        audioUrl: result,
        text: testText,
        isDataUrl: result.startsWith('data:'),
        isPlaceholder: result.includes('soundjay.com')
      }
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 测试所有功能
   */
  private async testAllFeatures(): Promise<Response> {
    console.log('[DEBUG] Testing all features...');
    
    const results = {
      text: null,
      image: null,
      audio: null,
      totalDuration: 0
    };

    const overallStart = Date.now();

    try {
      // 测试文本生成
      console.log('[DEBUG] Step 1: Testing text...');
      const textResponse = await this.testTextGeneration();
      results.text = await textResponse.json();
    } catch (error) {
      results.text = { success: false, error: error.message };
    }

    try {
      // 测试图片生成
      console.log('[DEBUG] Step 2: Testing image...');
      const imageResponse = await this.testImageGeneration();
      results.image = await imageResponse.json();
    } catch (error) {
      results.image = { success: false, error: error.message };
    }

    try {
      // 测试音频生成
      console.log('[DEBUG] Step 3: Testing audio...');
      const audioResponse = await this.testAudioGeneration();
      results.audio = await audioResponse.json();
    } catch (error) {
      results.audio = { success: false, error: error.message };
    }

    results.totalDuration = Date.now() - overallStart;

    console.log(`[DEBUG] All tests completed in ${results.totalDuration}ms`);

    return new Response(JSON.stringify({
      success: true,
      test: 'all',
      totalDuration: `${results.totalDuration}ms`,
      results
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 紧急清理卡住的任务
   */
  async cleanupStuckTasks(): Promise<Response> {
    console.log('[DEBUG] 🚨 Emergency cleanup of stuck tasks started');

    try {
      // 查找超时的任务（10分钟阈值）
      const stuckTasks = await this.env.DB.prepare(`
        SELECT id, title, status, updated_at,
               ROUND((julianday('now') - julianday(updated_at)) * 24 * 60, 2) as minutes_stuck
        FROM stories
        WHERE status IN ('generating_text', 'generating_images', 'generating_audio', 'preparing')
        AND datetime(updated_at) < datetime('now', '-10 minutes')
        LIMIT 50
      `).all();

      const results = [];

      if (stuckTasks.results && stuckTasks.results.length > 0) {
        console.log(`[DEBUG] 🔧 Found ${stuckTasks.results.length} stuck tasks`);

        for (const task of stuckTasks.results) {
          try {
            await this.storageService.updateStory(task.id as string, {
              status: 'failed',
              updated_at: new Date().toISOString()
            });

            results.push({
              id: task.id,
              title: task.title,
              previousStatus: task.status,
              minutesStuck: task.minutes_stuck,
              action: 'marked_as_failed'
            });

            console.log(`[DEBUG] ✅ Marked task ${task.id} as failed (stuck for ${task.minutes_stuck} minutes)`);
          } catch (error) {
            console.error(`[DEBUG] ❌ Failed to update task ${task.id}:`, error);
            results.push({
              id: task.id,
              title: task.title,
              error: error.message,
              action: 'failed_to_update'
            });
          }
        }
      } else {
        console.log('[DEBUG] ✅ No stuck tasks found');
      }

      return new Response(JSON.stringify({
        success: true,
        message: 'Emergency cleanup completed',
        timestamp: new Date().toISOString(),
        tasksFound: stuckTasks.results?.length || 0,
        results: results
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('[DEBUG] ❌ Emergency cleanup failed:', error);
      return new Response(JSON.stringify({
        success: false,
        error: 'Emergency cleanup failed',
        details: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
}
