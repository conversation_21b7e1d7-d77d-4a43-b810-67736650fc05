// @ts-nocheck
/**
 * StoryWeaver API 简化部署版本
 * 只包含基本功能用于测试部署
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';

interface Env {
  GEMINI_API_KEY?: string;
  JWT_SECRET?: string;
  GOOGLE_CLIENT_ID?: string;
  GOOGLE_CLIENT_SECRET?: string;
  STRIPE_SECRET_KEY?: string;
  ENVIRONMENT?: string;
  CACHE?: KVNamespace;
  ASSETS?: R2Bucket;
  DB?: D1Database;
}

const app = new Hono<{ Bindings: Env }>();

// CORS 中间件
app.use('*', cors({
  origin: ['https://storyweaver.jamintextiles.com', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
}));

// 根路径 - 健康检查
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'StoryWeaver API is running!',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'production',
    version: '1.0.0'
  });
});

// API 健康检查
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'healthy',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      environment: c.env?.ENVIRONMENT || 'production',
      services: {
        gemini: !!c.env?.GEMINI_API_KEY,
        jwt: !!c.env?.JWT_SECRET,
        google_oauth: !!c.env?.GOOGLE_CLIENT_ID && !!c.env?.GOOGLE_CLIENT_SECRET,
        stripe: !!c.env?.STRIPE_SECRET_KEY,
        kv: !!c.env?.CACHE,
        r2: !!c.env?.ASSETS,
        d1: !!c.env?.DB,
      }
    }
  });
});

// 测试数据库连接
app.get('/api/test/db', async (c) => {
  try {
    if (!c.env?.DB) {
      return c.json({
        success: false,
        error: 'Database not configured'
      }, 500);
    }

    // 测试数据库查询
    const result = await c.env.DB.prepare('SELECT COUNT(*) as count FROM users').first();
    
    return c.json({
      success: true,
      data: {
        message: 'Database connection successful',
        userCount: result?.count || 0
      }
    });
  } catch (error: any) {
    return c.json({
      success: false,
      error: 'Database connection failed',
      details: error?.message || 'Unknown error'
    }, 500);
  }
});

// 测试 KV 存储
app.get('/api/test/kv', async (c) => {
  try {
    if (!c.env?.CACHE) {
      return c.json({
        success: false,
        error: 'KV storage not configured'
      }, 500);
    }

    // 测试 KV 写入和读取
    const testKey = 'test-key';
    const testValue = JSON.stringify({ timestamp: Date.now(), message: 'KV test' });
    
    await c.env.CACHE.put(testKey, testValue);
    const retrieved = await c.env.CACHE.get(testKey);
    
    return c.json({
      success: true,
      data: {
        message: 'KV storage working',
        written: testValue,
        retrieved: retrieved
      }
    });
  } catch (error: any) {
    return c.json({
      success: false,
      error: 'KV storage test failed',
      details: error?.message || 'Unknown error'
    }, 500);
  }
});

// 测试环境变量
app.get('/api/test/env', (c) => {
  return c.json({
    success: true,
    data: {
      environment: c.env?.ENVIRONMENT || 'production',
      services_configured: {
        gemini: !!c.env?.GEMINI_API_KEY,
        jwt: !!c.env?.JWT_SECRET,
        google_oauth: !!c.env?.GOOGLE_CLIENT_ID && !!c.env?.GOOGLE_CLIENT_SECRET,
        stripe: !!c.env?.STRIPE_SECRET_KEY,
        kv: !!c.env?.CACHE,
        r2: !!c.env?.ASSETS,
        d1: !!c.env?.DB,
      },
      api_keys_preview: {
        gemini: c.env?.GEMINI_API_KEY ? c.env.GEMINI_API_KEY.substring(0, 10) + '...' : 'Not configured',
        google_client_id: c.env?.GOOGLE_CLIENT_ID ? c.env.GOOGLE_CLIENT_ID.substring(0, 15) + '...' : 'Not configured',
        stripe: c.env?.STRIPE_SECRET_KEY ? c.env.STRIPE_SECRET_KEY.substring(0, 15) + '...' : 'Not configured'
      }
    }
  });
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    path: c.req.path,
    method: c.req.method,
    available_endpoints: [
      'GET /',
      'GET /api/health',
      'GET /api/test/db',
      'GET /api/test/kv',
      'GET /api/test/env'
    ]
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.json({
    success: false,
    error: 'Internal server error',
    message: err.message
  }, 500);
});

export default app;
