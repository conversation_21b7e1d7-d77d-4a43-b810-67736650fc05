// @ts-nocheck
/**
 * 简化的 StoryWeaver API 入口文件
 * 用于基础功能测试
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';

// 简化的环境变量类型
export interface Env {
  GEMINI_API_KEY?: string;
  GOOGLE_CLIENT_ID?: string;
  GOOGLE_CLIENT_SECRET?: string;
  STRIPE_SECRET_KEY?: string;
  JWT_SECRET?: string;
  ENVIRONMENT?: string;
  CORS_ORIGIN?: string;
}

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env }>();

// 全局中间件
app.use('*', logger());
app.use('*', cors({
  origin: ['https://storyweaver.jamintextiles.com', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// 健康检查端点
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: c.env.ENVIRONMENT || 'unknown'
  });
});

// API 信息端点
app.get('/api/info', (c) => {
  return c.json({
    success: true,
    data: {
      name: 'StoryWeaver API',
      version: '1.0.0',
      description: 'AI-powered children\'s storybook platform',
      endpoints: {
        health: 'GET /health',
        info: 'GET /api/info',
        auth: {
          google: 'POST /api/auth/google',
          refresh: 'POST /api/auth/refresh'
        },
        stories: {
          create: 'POST /api/stories',
          list: 'GET /api/stories',
          get: 'GET /api/stories/:id'
        },
        users: {
          profile: 'GET /api/users/profile'
        }
      }
    }
  });
});

// 模拟认证端点
app.post('/api/auth/google', async (c) => {
  try {
    const body = await c.req.json();
    
    return c.json({
      success: true,
      data: {
        user: {
          id: 'mock-user-' + Date.now(),
          email: '<EMAIL>',
          name: 'Test User',
          avatar: 'https://via.placeholder.com/150',
          credits: 1,
          createdAt: new Date().toISOString()
        },
        tokens: {
          accessToken: 'mock-access-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
          expiresIn: 3600
        }
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: '登录失败',
      code: 'AUTH_ERROR'
    }, 400);
  }
});

// 模拟故事创建端点
app.post('/api/stories', async (c) => {
  try {
    const body = await c.req.json();
    
    // 简单验证
    if (!body.characterName || !body.theme) {
      return c.json({
        success: false,
        error: '缺少必要参数',
        code: 'VALIDATION_ERROR'
      }, 400);
    }
    
    return c.json({
      success: true,
      data: {
        id: 'story-' + Date.now(),
        title: `${body.characterName}的${body.theme}`,
        status: 'generating',
        progress: {
          text: false,
          images: false,
          audio: false
        },
        estimatedTimeRemaining: 120,
        createdAt: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: '创建故事失败',
      code: 'STORY_CREATION_ERROR'
    }, 500);
  }
});

// 模拟故事列表端点
app.get('/api/stories', (c) => {
  return c.json({
    success: true,
    data: [
      {
        id: 'story-1',
        title: '小明的冒险',
        characterName: '小明',
        theme: '冒险故事',
        status: 'completed',
        createdAt: '2024-01-15T10:00:00Z'
      },
      {
        id: 'story-2',
        title: '小红的魔法',
        characterName: '小红',
        theme: '魔法故事',
        status: 'generating',
        createdAt: '2024-01-15T11:00:00Z'
      }
    ],
    pagination: {
      page: 1,
      limit: 10,
      total: 2,
      totalPages: 1
    }
  });
});

// 模拟用户信息端点
app.get('/api/users/profile', (c) => {
  return c.json({
    success: true,
    data: {
      id: 'user-123',
      email: '<EMAIL>',
      name: 'Test User',
      avatar: 'https://via.placeholder.com/150',
      credits: 5,
      subscription: {
        plan: 'free',
        status: 'active'
      },
      createdAt: '2024-01-01T00:00:00Z'
    }
  });
});

// 测试端点 - 验证环境变量
app.get('/api/test/env', (c) => {
  return c.json({
    success: true,
    data: {
      environment: c.env.ENVIRONMENT,
      hasGeminiKey: !!c.env.GEMINI_API_KEY,
      hasGoogleClientId: !!c.env.GOOGLE_CLIENT_ID,
      hasStripeKey: !!c.env.STRIPE_SECRET_KEY,
      hasJwtSecret: !!c.env.JWT_SECRET,
      corsOrigin: c.env.CORS_ORIGIN
    }
  });
});

// 测试端点 - 模拟AI服务
app.post('/api/test/ai', async (c) => {
  try {
    const body = await c.req.json();
    
    // 模拟AI响应延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return c.json({
      success: true,
      data: {
        input: body,
        output: {
          story: `这是一个关于${body.characterName || '小朋友'}的${body.theme || '精彩'}故事...`,
          imagePrompt: `A cartoon illustration of ${body.characterName || 'a child'} in a ${body.setting || 'magical place'}`,
          audioUrl: 'https://example.com/audio/story.mp3'
        },
        processingTime: '1000ms',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'AI 服务测试失败',
      code: 'AI_TEST_ERROR'
    }, 500);
  }
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'API endpoint not found',
    code: 'NOT_FOUND',
    availableEndpoints: [
      'GET /health',
      'GET /api/info',
      'POST /api/auth/google',
      'POST /api/stories',
      'GET /api/stories',
      'GET /api/users/profile',
      'GET /api/test/env',
      'POST /api/test/ai'
    ]
  }, 404);
});

// 全局错误处理
app.onError((error, c) => {
  console.error('Error occurred:', error);
  return c.json({
    success: false,
    error: '内部服务器错误',
    code: 'INTERNAL_ERROR',
    message: error.message
  }, 500);
});

export default app;