/**
 * 简化版测试服务器 - 用于部署测试
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { prettyJSON } from 'hono/pretty-json';

// 创建 Hono 应用实例
const app = new Hono();

// 全局中间件
app.use('*', prettyJSON());
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// 健康检查端点
app.get('/', (c) => {
  return c.json({
    status: 'healthy',
    message: 'StoryWeaver API is running!',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'development'
  });
});

// API 状态端点
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'healthy',
      version: '1.0.0',
      services: {
        gemini: !!c.env?.GEMINI_API_KEY,
        google: !!c.env?.GOOGLE_CLIENT_ID,
        stripe: !!c.env?.STRIPE_SECRET_KEY,
        jwt: !!c.env?.JWT_SECRET,
        db: !!c.env?.DB,
        kv: !!c.env?.CACHE,
        r2: !!c.env?.ASSETS
      }
    }
  });
});

// 测试端点
app.get('/api/test', (c) => {
  return c.json({
    success: true,
    data: {
      message: 'Test endpoint working!',
      timestamp: new Date().toISOString()
    }
  });
});

// 测试数据库连接
app.get('/api/test/db', async (c) => {
  try {
    if (!c.env?.DB) {
      return c.json({
        success: false,
        error: 'Database not available'
      }, 500);
    }
    
    const result = await c.env.DB.prepare('SELECT sqlite_version() as version').first();
    
    return c.json({
      success: true,
      data: {
        dbVersion: result?.version,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Database test error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// 测试KV存储
app.get('/api/test/kv', async (c) => {
  try {
    if (!c.env?.CACHE) {
      return c.json({
        success: false,
        error: 'KV not available'
      }, 500);
    }
    
    const testKey = 'test-key';
    const testValue = { timestamp: new Date().toISOString() };
    
    await c.env.CACHE.put(testKey, JSON.stringify(testValue));
    const storedValue = await c.env.CACHE.get(testKey, 'json');
    
    return c.json({
      success: true,
      data: {
        testKey,
        storedValue,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('KV test error:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    path: c.req.path
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.json({
    success: false,
    error: 'Internal server error',
    message: err instanceof Error ? err.message : 'Unknown error'
  }, 500);
});

export default app;