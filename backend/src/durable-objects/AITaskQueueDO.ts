// @ts-nocheck
/**
 * AI任务队列 Durable Object
 * 负责管理AI生成任务的队列、状态和实时通信
 */

// 使用正确的Durable Object基类
export class DurableObjectBase {
  protected ctx: DurableObjectState;
  protected env: any;

  constructor(ctx: DurableObjectState, env: any) {
    this.ctx = ctx;
    this.env = env;
  }

  async fetch(request: Request): Promise<Response> {
    return new Response('Not implemented', { status: 501 });
  }
}
import { GeminiService } from '../services/gemini';
import { StorageService } from '../services/storage';

export interface AITask {
  id: string;
  storyId: string;
  type: 'text' | 'image' | 'audio';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  params: any;
  result?: any;
  error?: string;
  createdAt: number;
  updatedAt: number;
  skipped?: boolean;
  skipReason?: string;
}

export interface StoryGenerationRequest {
  storyId: string;
  characterName: string;
  age?: number;
  traits?: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  userId?: string;
  skipAudio?: boolean; // 新增：是否跳过音频生成
}

export class AITaskQueueDO extends DurableObjectBase {
  private sessions: Set<WebSocket> = new Set();
  private env: any;
  
  constructor(ctx: DurableObjectState, env: any) {
    super(ctx, env);
    this.env = env;
  }
  
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);

    console.log(`🔥 AITaskQueueDO.fetch called!`);
    console.log(`🔥 Request URL: ${request.url}`);
    console.log(`🔥 Request method: ${request.method}`);
    console.log(`🔥 URL pathname: ${url.pathname}`);
    console.log(`🔥 URL search: ${url.search}`);

    try {
      // 开始故事生成
      if (url.pathname === '/generate' && request.method === 'POST') {
        console.log(`🔥 Matched /generate POST route, calling handleGenerate`);
        return this.handleGenerate(request);
      }
      
      // 获取任务状态
      if (url.pathname === '/status' && request.method === 'GET') {
        return this.handleGetStatus(request);
      }

      // 获取任务内容
      if (url.pathname === '/content' && request.method === 'GET') {
        return this.handleGetContent(request);
      }

      // 取消任务
      if (url.pathname === '/cancel' && request.method === 'POST') {
        return this.handleCancel(request);
      }

      // 手动修复数据库同步
      if (url.pathname === '/fix-database' && request.method === 'POST') {
        return this.handleFixDatabase(request);
      }

      // 广播消息端点（用于传统模式）
      if (url.pathname === '/broadcast' && request.method === 'POST') {
        return this.handleBroadcast(request);
      }

      // WebSocket连接管理端点
      if (url.pathname === '/websocket-connect' && request.method === 'POST') {
        return this.handleWebSocketConnect(request);
      }

      if (url.pathname === '/websocket-disconnect' && request.method === 'POST') {
        return this.handleWebSocketDisconnect(request);
      }

      if (url.pathname === '/websocket-message' && request.method === 'POST') {
        return this.handleWebSocketMessage(request);
      }

      console.log(`🔥 No matching route found for ${url.pathname} ${request.method}`);
      return new Response('Not found', { status: 404 });
    } catch (error) {
      console.error('🔥 AITaskQueueDO fetch error:', error);
      console.error('🔥 Error stack:', error.stack);
      return new Response(JSON.stringify({
        success: false,
        error: 'Internal server error',
        message: error.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }



  /**
   * 处理故事生成请求
   */
  private async handleGenerate(request: Request): Promise<Response> {
    console.log(`🔥 handleGenerate called!`);

    try {
      const requestData: StoryGenerationRequest = await request.json();
      console.log(`🔥 Request data parsed:`, JSON.stringify(requestData, null, 2));

      const { storyId, characterName, theme, setting, style, voice, age, traits, userId, skipAudio } = requestData;

      console.log(`🔥 [${storyId}] Starting story generation with DO`);
    
    // 创建任务队列
    const tasks: AITask[] = [
      {
        id: `${storyId}-text`,
        storyId,
        type: 'text',
        status: 'pending',
        progress: 0,
        params: { characterName, age, traits, theme, setting, style },
        createdAt: Date.now(),
        updatedAt: Date.now()
      },
      {
        id: `${storyId}-image`,
        storyId,
        type: 'image',
        status: 'pending',
        progress: 0,
        params: { style, theme, setting },
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
    ];

    // 条件性添加音频任务
    if (!skipAudio) {
      tasks.push({
        id: `${storyId}-audio`,
        storyId,
        type: 'audio',
        status: 'pending',
        progress: 0,
        params: { voice },
        createdAt: Date.now(),
        updatedAt: Date.now()
      });
      console.log(`🎵 [${storyId}] 音频任务已添加到队列`);
    } else {
      console.log(`🔇 [${storyId}] 跳过音频生成，用户选择不生成语音`);
    }

    // 存储任务状态到Durable Object存储
    await this.ctx.storage.put(`tasks:${storyId}`, tasks);
    await this.ctx.storage.put(`story:${storyId}`, {
      id: storyId,
      status: 'generating',
      userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });
    
    // 广播任务创建事件
    this.broadcast({
      type: 'storyStarted',
      storyId,
      tasks: tasks.map(t => ({
        id: t.id,
        type: t.type,
        status: t.status,
        progress: t.progress
      })),
      timestamp: Date.now()
    });
    
    // 异步执行任务（不等待完成）
    console.log(`🔥 [${storyId}] Starting async task execution...`);
    this.executeTasksAsync(storyId, tasks).catch((error) => {
      console.error(`🔥 [${storyId}] Async task execution failed:`, error);
      console.error(`🔥 [${storyId}] Error stack:`, error.stack);
    });

    console.log(`🔥 [${storyId}] Returning success response`);
    return new Response(JSON.stringify({
      success: true,
      storyId,
      message: 'Story generation started with Durable Objects',
      tasks: tasks.map(t => ({
        id: t.id,
        type: t.type,
        status: t.status,
        progress: t.progress
      }))
    }), {
      headers: { 'Content-Type': 'application/json' }
    });

    } catch (error) {
      console.error(`🔥 handleGenerate error:`, error);
      console.error(`🔥 handleGenerate error stack:`, error.stack);
      throw error;
    }
  }

  /**
   * 获取任务状态
   */
  private async handleGetStatus(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const storyId = url.searchParams.get('storyId');

    if (!storyId) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing storyId parameter'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    try {
      const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[] | undefined;
      const story = await this.ctx.storage.get(`story:${storyId}`) as any;

      if (!tasks || !story) {
        // DO实例中没有找到任务，可能是实例失效
        console.log(`[${storyId}] DO instance not found, checking database fallback`);

        // 返回DO实例不可用的错误，让上层处理
        return new Response(JSON.stringify({
          success: false,
          error: '内部服务器错误',
          code: 'INTERNAL_ERROR',
          doStatus: 'unavailable'
        }), {
          status: 500,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 检查任务是否超时（10分钟阈值）
      const now = Date.now();
      const timeoutThreshold = 10 * 60 * 1000; // 10分钟
      let hasTimeoutTasks = false;

      const updatedTasks = tasks.map(task => {
        if (['pending', 'running'].includes(task.status)) {
          const taskAge = now - task.updatedAt;
          if (taskAge > timeoutThreshold) {
            console.log(`[${storyId}] Task ${task.type} timed out after ${Math.round(taskAge / 1000)}s`);
            hasTimeoutTasks = true;
            return {
              ...task,
              status: 'failed' as const,
              error: 'Task timed out after 10 minutes',
              updatedAt: now
            };
          }
        }
        return task;
      });

      // 如果有超时任务，更新存储
      if (hasTimeoutTasks) {
        await this.ctx.storage.put(`tasks:${storyId}`, updatedTasks);
        console.log(`[${storyId}] Updated timeout tasks in DO storage`);
      }

      return new Response(JSON.stringify({
        success: true,
        story,
        tasks: updatedTasks.map(t => ({
          id: t.id,
          type: t.type,
          status: t.status,
          progress: t.progress,
          error: t.error,
          updatedAt: t.updatedAt
        }))
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error(`[${storyId}] DO status query failed:`, error);
      return new Response(JSON.stringify({
        success: false,
        error: '内部服务器错误',
        code: 'INTERNAL_ERROR',
        doStatus: 'error'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 获取任务内容
   */
  private async handleGetContent(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const storyId = url.searchParams.get('storyId');
    const taskId = url.searchParams.get('taskId');
    const contentType = url.searchParams.get('type');

    if (!storyId || !taskId || !contentType) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required parameters: storyId, taskId, or type'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 验证内容类型
    if (!['text', 'image', 'audio'].includes(contentType)) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Invalid content type. Must be one of: text, image, audio'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取任务
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[] | undefined;
    if (!tasks) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Story not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 查找特定任务
    const task = tasks.find(t => t.id === taskId || t.id === `${storyId}-${contentType}`);
    if (!task) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Task not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 检查任务是否完成
    if (task.status !== 'completed') {
      return new Response(JSON.stringify({
        success: false,
        error: 'Content not available yet',
        taskStatus: task.status
      }), {
        status: 202,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 获取内容
    const content = await this.ctx.storage.get(`content:${storyId}:${contentType}`);
    if (!content) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Content not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: true,
      content
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

    return new Response(JSON.stringify({
      success: true,
      story,
      tasks: tasks.map(t => ({
        id: t.id,
        type: t.type,
        status: t.status,
        progress: t.progress,
        error: t.error
      }))
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消任务
   */
  private async handleCancel(request: Request): Promise<Response> {
    const { storyId } = await request.json();
    
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[] | undefined;
    if (!tasks) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Story not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // 标记所有未完成的任务为已取消
    const updatedTasks = tasks.map(task => {
      if (task.status === 'pending' || task.status === 'running') {
        return {
          ...task,
          status: 'failed' as const,
          error: 'Cancelled by user',
          updatedAt: Date.now()
        };
      }
      return task;
    });

    await this.ctx.storage.put(`tasks:${storyId}`, updatedTasks);
    
    this.broadcast({
      type: 'storyCancelled',
      storyId,
      timestamp: Date.now()
    });

    return new Response(JSON.stringify({
      success: true,
      message: 'Story generation cancelled'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 异步执行任务队列
   */
  private async executeTasksAsync(storyId: string, tasks: AITask[]) {
    console.log(`[${storyId}] Starting async task execution`);
    console.log(`[${storyId}] Environment check - GEMINI_API_KEY: ${this.env.GEMINI_API_KEY ? 'Available' : 'Missing'}`);
    console.log(`[${storyId}] Environment check - CACHE: ${this.env.CACHE ? 'Available' : 'Missing'}`);
    console.log(`[${storyId}] Environment check - ASSETS: ${this.env.ASSETS ? 'Available' : 'Missing'}`);
    console.log(`[${storyId}] Environment check - DB: ${this.env.DB ? 'Available' : 'Missing'}`);

    try {
      // 初始化服务
      console.log(`[${storyId}] Initializing GeminiService...`);
      const geminiService = new GeminiService(this.env.GEMINI_API_KEY);
      console.log(`[${storyId}] GeminiService initialized successfully`);

      console.log(`[${storyId}] Initializing StorageService...`);
      const storageService = new StorageService(this.env.CACHE, this.env.ASSETS, this.env.DB);
      console.log(`[${storyId}] StorageService initialized successfully`);

      // 按顺序执行任务
      console.log(`[${storyId}] Starting task execution loop with ${tasks.length} tasks`);
      for (const task of tasks) {
        try {
          console.log(`[${storyId}] Starting task: ${task.type} (ID: ${task.id})`);
          console.log(`[${storyId}] Task params:`, JSON.stringify(task.params, null, 2));

          // 更新任务状态为运行中
          task.status = 'running';
          task.updatedAt = Date.now();
          console.log(`[${storyId}] Updating task ${task.type} status to running...`);
          await this.updateTaskInStorage(storyId, task);
          console.log(`[${storyId}] Task ${task.type} status updated to running`);

          // 执行具体的AI生成任务
          console.log(`[${storyId}] Executing task ${task.type}...`);
          await this.executeTask(task, geminiService, storageService);
          console.log(`[${storyId}] Task ${task.type} execution completed`);

          // 标记任务完成
          task.status = 'completed';
          task.progress = 100;
          task.updatedAt = Date.now();
          console.log(`[${storyId}] Updating task ${task.type} status to completed...`);
          await this.updateTaskInStorage(storyId, task);

          console.log(`[${storyId}] Task ${task.type} completed successfully`);

        } catch (error) {
          console.error(`[${storyId}] Task ${task.type} failed with error:`, error);
          console.error(`[${storyId}] Error stack:`, error.stack);

          task.status = 'failed';
          task.error = error.message;
          task.updatedAt = Date.now();
          console.log(`[${storyId}] Updating task ${task.type} status to failed...`);
          await this.updateTaskInStorage(storyId, task);
          console.log(`[${storyId}] Task ${task.type} marked as failed`);
        }
      }

      // 检查所有任务是否完成
      const allTasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[];
      const allCompleted = allTasks.every(t => t.status === 'completed');
      const anyFailed = allTasks.some(t => t.status === 'failed');

      if (allCompleted) {
        await this.handleAllTasksCompleted(storyId, allTasks);
      } else if (anyFailed) {
        await this.handleTasksFailed(storyId, allTasks);
      }

    } catch (error) {
      console.error(`[${storyId}] Task execution failed with critical error:`, error);
      console.error(`[${storyId}] Critical error stack:`, error.stack);
      console.error(`[${storyId}] Critical error type:`, typeof error);
      console.error(`[${storyId}] Critical error name:`, error.name);

      // 标记所有任务为失败
      console.log(`[${storyId}] Marking all tasks as failed due to critical error`);
      const failedTasks = tasks.map(task => ({
        ...task,
        status: 'failed' as const,
        error: error.message,
        updatedAt: Date.now()
      }));

      console.log(`[${storyId}] Storing failed tasks to Durable Object storage`);
      await this.ctx.storage.put(`tasks:${storyId}`, failedTasks);

      console.log(`[${storyId}] Broadcasting story failed event`);
      this.broadcast({
        type: 'storyFailed',
        storyId,
        error: error.message,
        timestamp: Date.now()
      });
      console.log(`[${storyId}] Story failed event broadcasted`);
    }
  }

  /**
   * 执行单个AI任务
   */
  private async executeTask(task: AITask, geminiService: GeminiService, storageService: StorageService) {
    const progressCallback = (progress: number) => {
      task.progress = progress;
      this.broadcast({
        type: 'taskProgress',
        taskId: task.id,
        storyId: task.storyId,
        progress,
        status: task.status,
        timestamp: Date.now()
      });
    };

    switch (task.type) {
      case 'text':
        task.result = await this.generateStoryText(task, geminiService, progressCallback);
        // 保存文本内容到存储
        await this.saveContentToStorage(task.storyId, 'text', task.result);
        break;
      case 'image':
        task.result = await this.generateStoryImages(task, geminiService, storageService, progressCallback);
        // 保存图片内容到存储
        await this.saveContentToStorage(task.storyId, 'image', task.result);
        break;
      case 'audio':
        task.result = await this.generateStoryAudio(task, geminiService, storageService, progressCallback);
        // 保存音频内容到存储
        await this.saveContentToStorage(task.storyId, 'audio', task.result);
        break;
      default:
        throw new Error(`Unknown task type: ${task.type}`);
    }
  }

  /**
   * 生成故事文本
   */
  private async generateStoryText(task: AITask, geminiService: GeminiService, progressCallback: (progress: number) => void): Promise<any> {
    progressCallback(10);

    const storyRequest = {
      characterName: task.params.characterName,
      characterAge: task.params.age || 6,
      characterTraits: task.params.traits || ['善良', '勇敢'],
      theme: task.params.theme,
      setting: task.params.setting,
      style: task.params.style || 'cartoon',
      voice: task.params.voice || 'gentle_female'
    };

    console.log(`[${task.storyId}] Story request parameters:`, JSON.stringify(storyRequest, null, 2));

    progressCallback(30);

    const storyResponse = await geminiService.generateStory(storyRequest);

    progressCallback(70);

    // 内容安全检查
    const isSafe = await geminiService.checkContentSafety(storyResponse.fullText);
    if (!isSafe) {
      throw new Error('Generated content failed safety check');
    }

    // 广播故事结构信息
    if (storyResponse && storyResponse.title && storyResponse.pages) {
      this.broadcastStoryStructure(task.storyId, storyResponse.title, storyResponse.pages.length);

      // 逐页广播文本内容
      storyResponse.pages.forEach((page: any, index: number) => {
        this.broadcastPageContent(task.storyId, index, {
          text: page.text,
          imagePrompt: page.imagePrompt
        });
      });
    }

    progressCallback(100);

    return storyResponse;
  }

  /**
   * 生成故事图片
   */
  private async generateStoryImages(task: AITask, geminiService: GeminiService, storageService: StorageService, progressCallback: (progress: number) => void): Promise<any> {
    progressCallback(10);

    // 获取文本任务的结果作为图片生成的输入
    const textTask = await this.getTaskResult(task.storyId, 'text');
    if (!textTask || !textTask.result) {
      throw new Error('Text generation must complete before image generation');
    }

    // 提取编辑指令（优先使用editPrompt，向后兼容imagePrompt）
    const editPrompts = textTask.result.pages.map((page: any) => 
      page.editPrompt || page.imagePrompt || `将角色置于第${page.pageNumber}页的场景中`
    );

    progressCallback(30);

    // 使用视觉锚定工作流生成图片
    console.log(`🔗 [${task.storyId}] 启动视觉锚定工作流，生成 ${editPrompts.length} 张插图`);
    
    // 广播图片生成开始
    this.broadcastGenerationProgress(task.storyId, 'image', {
      completed: 0,
      total: editPrompts.length,
      currentItem: '角色基准图',
      percentage: 0
    });

    progressCallback(30);

    // 直接调用视觉锚定工作流
    const imageUrls = await geminiService.generateImages(
      editPrompts,
      task.params.style,
      undefined, // subscription
      undefined, // characterImageUrl
      task.params.age, // characterAge
      task.params.characterGender // characterGender
    );
    
    // 上传所有图片并广播进度
    const uploadedImages = [];
    const totalImages = imageUrls.length;

    for (let i = 0; i < totalImages; i++) {
      const imageUrl = imageUrls[i];

      // 广播当前图片上传进度
      this.broadcastGenerationProgress(task.storyId, 'image', {
        completed: i,
        total: totalImages,
        currentItem: `上传第 ${i + 1} 页插图`,
        percentage: Math.round(70 + (i / totalImages) * 30)
      });

      // 上传图片到存储
      const uploadedUrl = await storageService.uploadImageFromUrl(imageUrl, `${task.storyId}/page-${i + 1}.jpg`);
      uploadedImages.push(uploadedUrl);

      // 立即广播这张图片完成
      this.broadcastPageContent(task.storyId, i, {
        imageUrl: uploadedUrl
      });

      progressCallback(70 + (i + 1) / totalImages * 30);
    }

    // 广播图片生成完成
    this.broadcastGenerationProgress(task.storyId, 'image', {
      completed: totalImages,
      total: totalImages,
      percentage: 100
    });

    return { imageUrls: uploadedImages };
  }

  /**
   * 生成故事音频
   */
  private async generateStoryAudio(task: AITask, geminiService: GeminiService, storageService: StorageService, progressCallback: (progress: number) => void): Promise<any> {
    progressCallback(10);

    // 获取文本任务的结果
    const textTask = await this.getTaskResult(task.storyId, 'text');
    if (!textTask || !textTask.result) {
      throw new Error('Text generation must complete before audio generation');
    }

    // 广播音频生成开始
    this.broadcastGenerationProgress(task.storyId, 'audio', {
      completed: 0,
      total: 1,
      currentItem: '故事语音朗读',
      percentage: 0
    });

    progressCallback(30);

    const audioData = await geminiService.generateAudio(textTask.result.fullText, task.params.voice);

    progressCallback(80);

    // 将音频上传到R2存储
    const uploadedAudioUrl = await storageService.uploadAudio(`${task.storyId}/story-audio.mp3`, audioData);

    // 广播音频完成 - 为所有页面添加音频URL
    const totalPages = textTask.result.pages.length;
    for (let i = 0; i < totalPages; i++) {
      this.broadcastPageContent(task.storyId, i, {
        audioUrl: uploadedAudioUrl
      });
    }

    // 广播音频生成完成
    this.broadcastGenerationProgress(task.storyId, 'audio', {
      completed: 1,
      total: 1,
      percentage: 100
    });

    progressCallback(100);

    return { audioUrl: uploadedAudioUrl };
  }

  /**
   * 获取指定类型任务的结果
   */
  private async getTaskResult(storyId: string, taskType: string): Promise<AITask | null> {
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[] | undefined;
    if (!tasks) return null;

    return tasks.find(t => t.type === taskType && t.status === 'completed') || null;
  }

  /**
   * 更新存储中的任务状态并同步到数据库
   */
  private async updateTaskInStorage(storyId: string, updatedTask: AITask) {
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[];
    const taskIndex = tasks.findIndex(t => t.id === updatedTask.id);

    if (taskIndex !== -1) {
      tasks[taskIndex] = updatedTask;
      await this.ctx.storage.put(`tasks:${storyId}`, tasks);

      // 自动同步状态到数据库
      await this.syncStatusToDatabase(storyId, tasks);
    }

    // 广播任务更新
    this.broadcast({
      type: 'taskUpdate',
      storyId,
      task: {
        id: updatedTask.id,
        type: updatedTask.type,
        status: updatedTask.status,
        progress: updatedTask.progress,
        error: updatedTask.error
      },
      timestamp: Date.now()
    });
  }

  /**
   * 处理所有任务完成
   */
  private async handleAllTasksCompleted(storyId: string, tasks: AITask[]) {
    console.log(`[${storyId}] All tasks completed successfully`);

    // 更新故事状态
    await this.ctx.storage.put(`story:${storyId}`, {
      id: storyId,
      status: 'completed',
      completedAt: Date.now(),
      updatedAt: Date.now()
    });

    // 组装完整的故事数据
    const textResult = tasks.find(t => t.type === 'text')?.result;
    const imageResult = tasks.find(t => t.type === 'image')?.result;
    const audioResult = tasks.find(t => t.type === 'audio')?.result;

    // 将图片URL合并到页面数据中
    const pagesWithImages = (textResult?.pages || []).map((page: any, index: number) => ({
      ...page,
      imageUrl: imageResult?.imageUrls?.[index] || null
    }));

    const completeStory = {
      id: storyId,
      title: textResult?.title || 'Untitled Story',
      pages: pagesWithImages,
      imageUrls: imageResult?.imageUrls || [],
      audioUrl: audioResult?.audioUrl,
      status: 'completed',
      completedAt: Date.now()
    };

    await this.ctx.storage.put(`complete:${storyId}`, completeStory);

    // 更新主数据库中的故事记录
    try {
      console.log(`[${storyId}] Updating story in main database`);

      // 准备数据库更新数据
      const updateData = {
        status: 'completed',
        pages: JSON.stringify(pagesWithImages),
        audio_url: audioResult?.audioUrl || null,
        cover_image_url: imageResult?.imageUrls?.[0] || null,
        updated_at: new Date().toISOString()
      };

      // 更新数据库记录
      const result = await this.env.DB.prepare(`
        UPDATE stories
        SET status = ?, pages = ?, audio_url = ?, cover_image_url = ?, updated_at = ?
        WHERE id = ?
      `).bind(
        updateData.status,
        updateData.pages,
        updateData.audio_url,
        updateData.cover_image_url,
        updateData.updated_at,
        storyId
      ).run();

      if (result.success) {
        console.log(`[${storyId}] Database updated successfully`);
      } else {
        console.error(`[${storyId}] Database update failed:`, result.error);
      }

    } catch (dbError) {
      console.error(`[${storyId}] Failed to update database:`, dbError);
    }

    this.broadcast({
      type: 'storyCompleted',
      storyId,
      story: completeStory,
      timestamp: Date.now()
    });
  }

  /**
   * 处理任务失败
   */
  private async handleTasksFailed(storyId: string, tasks: AITask[]) {
    console.log(`[${storyId}] Some tasks failed`);

    const failedTasks = tasks.filter(t => t.status === 'failed');

    await this.ctx.storage.put(`story:${storyId}`, {
      id: storyId,
      status: 'failed',
      failedAt: Date.now(),
      updatedAt: Date.now()
    });

    this.broadcast({
      type: 'storyFailed',
      storyId,
      failedTasks: failedTasks.map(t => ({
        id: t.id,
        type: t.type,
        error: t.error
      })),
      timestamp: Date.now()
    });
  }

  /**
   * 手动修复数据库同步
   */
  private async handleFixDatabase(request: Request): Promise<Response> {
    try {
      const url = new URL(request.url);
      // 从请求体或查询参数获取storyId
      let storyId: string;

      if (request.method === 'POST') {
        try {
          const body = await request.json();
          storyId = body.storyId;
        } catch {
          // 如果没有请求体，从URL路径获取
          const pathParts = url.pathname.split('/');
          storyId = pathParts[pathParts.length - 2]; // /fix-database前的部分
        }
      } else {
        const pathParts = url.pathname.split('/');
        storyId = pathParts[pathParts.length - 2];
      }

      if (!storyId) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Story ID is required'
        }), { status: 400 });
      }

      console.log(`[${storyId}] Manual database fix requested`);

      // 获取所有任务
      const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as AITask[];
      if (!tasks) {
        return new Response(JSON.stringify({
          success: false,
          error: 'No tasks found for this story'
        }), { status: 404 });
      }

      // 检查是否所有任务都已完成
      const allCompleted = tasks.every(t => t.status === 'completed');
      if (!allCompleted) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Not all tasks are completed'
        }), { status: 400 });
      }

      // 重新执行数据库更新逻辑
      await this.handleAllTasksCompleted(storyId, tasks);

      return new Response(JSON.stringify({
        success: true,
        message: 'Database sync fixed successfully'
      }));

    } catch (error) {
      console.error('Fix database error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message
      }), { status: 500 });
    }
  }

  /**
   * 处理广播消息（用于传统模式的进度更新）
   */
  private async handleBroadcast(request: Request): Promise<Response> {
    try {
      const message = await request.json();
      console.log(`Broadcasting message from traditional mode:`, message.type);

      // 直接广播消息给所有连接的WebSocket客户端
      this.broadcast(message);

      return new Response(JSON.stringify({
        success: true,
        message: 'Message broadcasted successfully'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('Broadcast error:', error);
      return new Response(JSON.stringify({
        success: false,
        error: error.message
      }), { status: 500 });
    }
  }

  /**
   * 处理WebSocket连接注册
   */
  private async handleWebSocketConnect(request: Request): Promise<Response> {
    try {
      const { storyId } = await request.json();
      console.log('WebSocket connection registered for story:', storyId);

      // 这里我们记录连接，但实际的WebSocket对象在主路由中管理
      // 我们可以存储连接状态或其他元数据
      await this.ctx.storage.put(`websocket:${storyId}`, {
        connected: true,
        timestamp: Date.now()
      });

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('WebSocket connect error:', error);
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 处理WebSocket断开连接
   */
  private async handleWebSocketDisconnect(request: Request): Promise<Response> {
    try {
      const { storyId } = await request.json();
      console.log('WebSocket disconnected for story:', storyId);

      await this.ctx.storage.delete(`websocket:${storyId}`);

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('WebSocket disconnect error:', error);
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 处理WebSocket消息
   */
  private async handleWebSocketMessage(request: Request): Promise<Response> {
    try {
      const { storyId, message } = await request.json();
      console.log('WebSocket message for story:', storyId, message);

      // 处理来自客户端的消息
      if (message.type === 'skipStage') {
        await this.handleSkipStage(storyId, message.stage, message.reason);
      }

      return new Response(JSON.stringify({ success: true }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('WebSocket message error:', error);
      return new Response(JSON.stringify({ success: false, error: error.message }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 处理跳过阶段请求
   */
  private async handleSkipStage(storyId: string, stage: string, reason: string): Promise<void> {
    console.log(`🚀 [${storyId}] 用户请求跳过阶段: ${stage}, 原因: ${reason}`);

    try {
      // 获取当前任务状态
      const tasks = await this.getStoryTasks(storyId);
      const currentTask = tasks.find(t => t.type === stage && t.status === 'running');

      if (!currentTask) {
        console.log(`⚠️ [${storyId}] 没有找到正在运行的${stage}任务`);
        return;
      }

      // 根据不同阶段执行跳过逻辑
      let skipResult;
      switch (stage) {
        case 'text':
          skipResult = await this.skipTextGeneration(storyId, currentTask);
          break;
        case 'image':
          skipResult = await this.skipImageGeneration(storyId, currentTask);
          break;
        case 'audio':
          skipResult = await this.skipAudioGeneration(storyId, currentTask);
          break;
        default:
          throw new Error(`不支持跳过的阶段: ${stage}`);
      }

      // 更新任务状态为已完成（跳过）
      currentTask.status = 'completed';
      currentTask.result = skipResult;
      currentTask.skipped = true;
      currentTask.skipReason = reason;
      currentTask.completedAt = new Date().toISOString();

      // 保存任务状态
      await this.saveTask(currentTask);

      // 广播跳过事件
      this.broadcast({
        type: 'stageSkipped',
        storyId,
        stage,
        reason,
        timestamp: new Date().toISOString()
      });

      // 继续下一阶段或完成故事
      await this.processNextStage(storyId);

      console.log(`✅ [${storyId}] 成功跳过${stage}阶段`);
    } catch (error) {
      console.error(`❌ [${storyId}] 跳过${stage}阶段失败:`, error);

      // 广播跳过失败事件
      this.broadcast({
        type: 'stageSkipFailed',
        storyId,
        stage,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 广播消息给所有连接的WebSocket客户端
   */
  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    console.log(`Broadcasting message to ${this.sessions.size} sessions:`, message.type);

    this.sessions.forEach(session => {
      try {
        session.send(messageStr);
      } catch (error) {
        console.error('Failed to send message to WebSocket session:', error);
        // 移除断开的连接
        this.sessions.delete(session);
      }
    });
  }

  /**
   * 广播故事结构信息
   */
  private broadcastStoryStructure(storyId: string, title: string, totalPages: number) {
    this.broadcast({
      type: 'storyStructure',
      storyId,
      title,
      totalPages,
      timestamp: Date.now()
    });
  }

  /**
   * 广播页面内容就绪消息
   */
  private broadcastPageContent(storyId: string, pageIndex: number, content: {
    text?: string;
    imageUrl?: string;
    audioUrl?: string;
    imagePrompt?: string;
  }) {
    this.broadcast({
      type: 'pageContentReady',
      storyId,
      pageIndex,
      content,
      timestamp: Date.now()
    });
  }

  /**
   * 广播生成进度消息
   */
  private broadcastGenerationProgress(storyId: string, stage: 'text' | 'image' | 'audio', progress: {
    completed: number;
    total: number;
    currentItem?: string;
    percentage?: number;
  }) {
    this.broadcast({
      type: 'generationProgress',
      storyId,
      stage,
      progress,
      timestamp: Date.now()
    });
  }

  /**
   * 同步状态到数据库
   */
  private async syncStatusToDatabase(storyId: string, tasks: AITask[]): Promise<void> {
    try {
      const storyStatus = this.calculateStoryStatus(tasks);
      console.log(`[${storyId}] 🔄 Syncing status to database: ${storyStatus}`);

      // 创建StorageService实例来更新数据库
      if (this.env.DB) {
        const { StorageService } = await import('../services/storage');
        const storageService = new StorageService(this.env.CACHE, this.env.ASSETS, this.env.DB);

        // 更新数据库中的故事状态
        await storageService.updateStory(storyId, {
          status: storyStatus,
          updated_at: new Date().toISOString()
        });

        console.log(`[${storyId}] ✅ Database status synced: ${storyStatus}`);
      }
    } catch (error) {
      console.error(`[${storyId}] ❌ Failed to sync status to database:`, error);
      // 不抛出错误，避免影响主要的任务执行流程
    }
  }

  /**
   * 跳过文本生成阶段
   */
  private async skipTextGeneration(storyId: string, task: AITask): Promise<any> {
    console.log(`📝 [${storyId}] 跳过文本生成，使用预设模板`);

    // 使用预设的故事模板
    const fallbackStory = {
      title: `${task.params.characterName}的故事`,
      pages: [
        {
          pageNumber: 1,
          text: `${task.params.characterName}是一个${task.params.characterAge}岁的孩子，喜欢探索和冒险。`,
          imagePrompt: `一个${task.params.characterAge}岁的孩子${task.params.characterName}，${task.params.characterTraits.join('，')}，在${task.params.setting}中。`
        },
        {
          pageNumber: 2,
          text: `今天，${task.params.characterName}遇到了一个有趣的挑战。`,
          imagePrompt: `${task.params.characterName}面对挑战的场景，在${task.params.setting}环境中。`
        },
        {
          pageNumber: 3,
          text: `通过勇气和智慧，${task.params.characterName}成功解决了问题。`,
          imagePrompt: `${task.params.characterName}成功解决问题，露出开心的笑容。`
        },
        {
          pageNumber: 4,
          text: `${task.params.characterName}学到了很多，期待着下一次的冒险！`,
          imagePrompt: `${task.params.characterName}满足地结束这次冒险，准备迎接新的挑战。`
        }
      ],
      fullText: `${task.params.characterName}是一个${task.params.characterAge}岁的孩子，喜欢探索和冒险。今天，${task.params.characterName}遇到了一个有趣的挑战。通过勇气和智慧，${task.params.characterName}成功解决了问题。${task.params.characterName}学到了很多，期待着下一次的冒险！`
    };

    return fallbackStory;
  }

  /**
   * 跳过图片生成阶段
   */
  private async skipImageGeneration(storyId: string, task: AITask): Promise<any> {
    console.log(`🖼️ [${storyId}] 跳过图片生成，使用占位符图片`);

    // 获取文本任务结果
    const textTask = await this.getTaskResult(storyId, 'text');
    if (!textTask || !textTask.result) {
      throw new Error('需要文本内容才能跳过图片生成');
    }

    const pages = textTask.result.pages;
    const placeholderImages = pages.map((_, index) =>
      `data:image/svg+xml;base64,${Buffer.from(`
        <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
          <rect width="100%" height="100%" fill="#f3f4f6"/>
          <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#6b7280">
            第${index + 1}页插图
          </text>
          <text x="50%" y="65%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
            (已跳过图片生成)
          </text>
        </svg>
      `).toString('base64')}`
    );

    return { imageUrls: placeholderImages };
  }

  /**
   * 跳过音频生成阶段
   */
  private async skipAudioGeneration(storyId: string, task: AITask): Promise<any> {
    console.log(`🎵 [${storyId}] 跳过音频生成`);

    // 返回空的音频结果，表示没有音频
    return {
      audioUrl: null,
      skipped: true,
      message: '音频生成已跳过，故事可正常阅读'
    };
  }

  /**
   * 增强图片提示词以确保风格一致性
   */
  private enhanceImagePromptForConsistency(
    originalPrompt: string,
    style: string,
    pageNumber: number,
    totalPages: number
  ): string {
    // 风格一致性描述符
    const styleConsistencyMap = {
      cartoon: "保持卡通风格一致性，相同的角色设计，统一的色彩调色板，一致的线条风格",
      watercolor: "保持水彩画风格一致性，相同的笔触技法，统一的色彩渲染，一致的艺术表现",
      sketch: "保持简笔画风格一致性，相同的线条粗细，统一的绘画技法，一致的简化程度",
      fantasy: "保持奇幻风格一致性，相同的魔法元素，统一的梦幻色调，一致的想象力表现",
      realistic: "保持写实风格一致性，相同的光影处理，统一的细节水准，一致的真实感",
      anime: "保持动漫风格一致性，相同的角色比例，统一的色彩饱和度，一致的日式表现"
    };

    const consistencyDescription = styleConsistencyMap[style as keyof typeof styleConsistencyMap] ||
      "保持艺术风格一致性，统一的视觉表现";

    // 角色一致性约束
    const characterConsistency = pageNumber === 1
      ? "建立主要角色的视觉设计基准"
      : "严格保持与前面页面相同的角色外观、服装、特征";

    // 环境一致性约束
    const environmentConsistency = "保持故事环境的连贯性，统一的场景风格和氛围";

    return `
${originalPrompt}

【风格一致性要求】
${consistencyDescription}

【角色一致性要求】
${characterConsistency}

【环境一致性要求】
${environmentConsistency}

【技术要求】
- 这是第${pageNumber}页，共${totalPages}页的连续故事插图
- 必须与整个故事的视觉风格保持完全一致
- 色彩调色板、光影处理、艺术技法必须统一
- 角色外观、服装、表情风格必须连贯
- 适合儿童观看，温馨友好，无暴力内容
    `.trim();
  }

  /**
   * 根据任务状态计算故事状态
   */
  private calculateStoryStatus(tasks: AITask[]): string {
    if (!tasks || tasks.length === 0) {
      return 'preparing';
    }

    const textTask = tasks.find(t => t.type === 'text');
    const imageTask = tasks.find(t => t.type === 'image');
    const audioTask = tasks.find(t => t.type === 'audio');

    // 检查是否有失败的任务
    if (tasks.some(t => t.status === 'failed')) {
      return 'failed';
    }

    // 检查所有任务是否完成
    if (tasks.every(t => t.status === 'completed')) {
      return 'completed';
    }

    // 根据当前运行的任务确定状态
    if (audioTask?.status === 'running') {
      return 'generating_audio';
    } else if (imageTask?.status === 'running') {
      return 'generating_images';
    } else if (textTask?.status === 'running') {
      return 'generating_text';
    } else if (audioTask?.status === 'completed' && imageTask?.status === 'completed' && textTask?.status === 'completed') {
      return 'composing';
    } else {
      return 'preparing';
    }
  }

  /**
   * 保存内容到存储
   */
  private async saveContentToStorage(storyId: string, contentType: 'text' | 'image' | 'audio', content: any): Promise<void> {
    try {
      console.log(`[${storyId}] 💾 Saving ${contentType} content to storage`);

      // 格式化内容以便预览
      let formattedContent;

      switch (contentType) {
        case 'text':
          // 格式化文本内容为页面结构
          formattedContent = {
            pages: content.pages?.map((page: any, index: number) => ({
              pageNumber: index + 1,
              content: page.text || page.content || ''
            })) || []
          };
          break;

        case 'image':
          // 格式化图片内容
          formattedContent = {
            images: content.images?.map((image: any, index: number) => ({
              pageNumber: index + 1,
              url: image.url || image.imageUrl || '',
              description: image.description || image.prompt || ''
            })) || []
          };
          break;

        case 'audio':
          // 格式化音频内容
          formattedContent = {
            url: content.audioUrl || content.url || '',
            duration: content.duration || 0
          };
          break;

        default:
          formattedContent = content;
      }

      // 保存到DO存储
      await this.ctx.storage.put(`content:${storyId}:${contentType}`, formattedContent);
      console.log(`[${storyId}] ✅ ${contentType} content saved successfully`);

    } catch (error) {
      console.error(`[${storyId}] ❌ Failed to save ${contentType} content:`, error);
      // 不抛出错误，避免影响主要流程
    }
  }
}
