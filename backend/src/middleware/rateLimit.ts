// @ts-nocheck
/**
 * 限流中间件
 */

import { Context, Next } from 'hono';
import { ApiResponse, ErrorCodes } from '../types/api';

interface RateLimitConfig {
  windowMs: number; // 时间窗口（毫秒）
  maxRequests: number; // 最大请求数
  keyGenerator?: (c: Context) => string; // 自定义key生成器
}

const defaultConfig: RateLimitConfig = {
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 60, // 每分钟60次请求
};

export function createRateLimitMiddleware(config: Partial<RateLimitConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config };

  return async function rateLimitMiddleware(c: Context<any>, next: Next) {
    try {
      const kv = c.env.CACHE;
      
      // 生成限流key
      const key = config.keyGenerator ? config.keyGenerator(c) : getDefaultKey(c);
      const rateLimitKey = `rate_limit:${key}`;
      
      // 获取当前计数
      const currentCount = await kv.get(rateLimitKey);
      const count = currentCount ? parseInt(currentCount) : 0;
      
      // 检查是否超过限制
      if (count >= finalConfig.maxRequests) {
        return c.json<ApiResponse>({
          success: false,
          error: '请求过于频繁，请稍后再试',
          code: ErrorCodes.RATE_LIMIT_EXCEEDED
        }, 429);
      }
      
      // 增加计数
      const newCount = count + 1;
      const ttl = Math.ceil(finalConfig.windowMs / 1000);
      
      await kv.put(rateLimitKey, newCount.toString(), { expirationTtl: ttl });
      
      // 添加响应头
      c.header('X-RateLimit-Limit', finalConfig.maxRequests.toString());
      c.header('X-RateLimit-Remaining', (finalConfig.maxRequests - newCount).toString());
      c.header('X-RateLimit-Reset', (Date.now() + finalConfig.windowMs).toString());
      
      await next();
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // 限流失败时继续执行，避免影响正常请求
      await next();
    }
  };
}

// 默认的限流中间件
export const rateLimitMiddleware = createRateLimitMiddleware();

// 严格的限流中间件（用于AI API调用）
export const strictRateLimitMiddleware = createRateLimitMiddleware({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 10, // 每分钟10次请求
});

// 用户级别的限流中间件
export const userRateLimitMiddleware = createRateLimitMiddleware({
  windowMs: 60 * 1000, // 1分钟
  maxRequests: 30, // 每分钟30次请求
  keyGenerator: (c) => {
    const user = c.get('user');
    return user ? `user:${user.id}` : getDefaultKey(c);
  }
});

// 故事生成的限流中间件
export const storyGenerationRateLimitMiddleware = createRateLimitMiddleware({
  windowMs: 60 * 60 * 1000, // 1小时
  maxRequests: 5, // 每小时5个故事
  keyGenerator: (c) => {
    const user = c.get('user');
    return user ? `story_generation:${user.id}` : getDefaultKey(c);
  }
});

/**
 * 获取默认的限流key（基于IP地址）
 */
function getDefaultKey(c: Context): string {
  // 尝试获取真实IP地址
  const forwarded = c.req.header('CF-Connecting-IP') || 
                   c.req.header('X-Forwarded-For') || 
                   c.req.header('X-Real-IP');
  
  if (forwarded) {
    return `ip:${forwarded.split(',')[0].trim()}`;
  }
  
  // 如果无法获取IP，使用用户代理作为fallback
  const userAgent = c.req.header('User-Agent') || 'unknown';
  return `ua:${Buffer.from(userAgent).toString('base64').substring(0, 20)}`;
}

/**
 * 检查用户是否有足够的配额
 */
export async function checkUserQuota(
  userId: string,
  quotaType: 'story' | 'image' | 'audio',
  kv: KVNamespace
): Promise<{ allowed: boolean; remaining: number }> {
  const quotaLimits = {
    story: { free: 1, credits: 5, unlimited: -1 }, // -1表示无限制
    image: { free: 10, credits: 50, unlimited: -1 },
    audio: { free: 1, credits: 5, unlimited: -1 }
  };
  
  try {
    // 获取用户当前使用量
    const usageKey = `quota:${userId}:${quotaType}`;
    const currentUsage = await kv.get(usageKey);
    const usage = currentUsage ? parseInt(currentUsage) : 0;
    
    // 这里应该从数据库获取用户的订阅类型
    // 为了简化，假设用户是免费用户
    const userPlan = 'free'; // 实际应该从数据库获取
    const limit = quotaLimits[quotaType][userPlan as keyof typeof quotaLimits.story];
    
    if (limit === -1) {
      // 无限制
      return { allowed: true, remaining: -1 };
    }
    
    const remaining = Math.max(0, limit - usage);
    return { allowed: remaining > 0, remaining };
  } catch (error) {
    console.error('Check user quota failed:', error);
    // 出错时允许请求，避免影响用户体验
    return { allowed: true, remaining: 0 };
  }
}

/**
 * 增加用户配额使用量
 */
export async function incrementUserQuota(
  userId: string,
  quotaType: 'story' | 'image' | 'audio',
  kv: KVNamespace,
  increment: number = 1
): Promise<void> {
  try {
    const usageKey = `quota:${userId}:${quotaType}`;
    const currentUsage = await kv.get(usageKey);
    const usage = currentUsage ? parseInt(currentUsage) : 0;
    const newUsage = usage + increment;
    
    // 设置24小时过期时间（每日重置配额）
    await kv.put(usageKey, newUsage.toString(), { expirationTtl: 24 * 60 * 60 });
  } catch (error) {
    console.error('Increment user quota failed:', error);
  }
}