// @ts-nocheck
/**
 * 最小化测试服务器
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';

interface Env {
  GEMINI_API_KEY?: string;
  JWT_SECRET?: string;
  ENVIRONMENT?: string;
}

const app = new Hono<{ Bindings: Env }>();

// CORS 中间件
app.use('*', cors({
  origin: [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174',
    'https://storyweaver.pages.dev',
    'https://storyweaver.jamintextiles.com'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// 健康检查端点
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'StoryWeaver API is running!',
    timestamp: new Date().toISOString(),
    environment: c.env?.ENVIRONMENT || 'development'
  });
});

// API 状态端点
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    data: {
      status: 'healthy',
      version: '1.0.0',
      services: {
        gemini: !!c.env?.GEMINI_API_KEY,
        jwt: !!c.env?.JWT_SECRET,
      }
    }
  });
});

// 测试端点
app.get('/api/test', (c) => {
  return c.json({
    success: true,
    data: {
      message: 'Test endpoint working!',
      timestamp: new Date().toISOString()
    }
  });
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    path: c.req.path
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Server error:', err);
  return c.json({
    success: false,
    error: 'Internal server error',
    message: err.message
  }, 500);
});

export default app;
