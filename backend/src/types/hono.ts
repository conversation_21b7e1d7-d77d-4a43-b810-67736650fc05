// @ts-nocheck
/**
 * Hono 类型扩展
 */

import { Context } from 'hono';

// Cloudflare Workers 环境类型
export interface Env {
  // API 密钥
  GEMINI_API_KEY: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  STRIPE_WEBHOOK_SECRET: string;
  JWT_SECRET: string;
  
  // Cloudflare 绑定
  CACHE: KVNamespace;
  ASSETS: R2Bucket;
  DB: D1Database;

  // Durable Objects 绑定
  AI_TASK_QUEUE: DurableObjectNamespace;
  
  // 环境配置
  ENVIRONMENT?: string;
}

// 变量类型
export interface Variables {
  user?: {
    id: string;
    email: string;
  };
  jwtPayload?: any;
}

// 应用上下文类型
export type AppContext = Context<{ Bindings: Env }, any, Variables>;

// 认证上下文类型
export interface AuthenticatedVariables {
  user: {
    id: string;
    email: string;
  };
  jwtPayload: any;
}

export type AuthenticatedContext = Context<{ Bindings: Env }, any, AuthenticatedVariables>;
