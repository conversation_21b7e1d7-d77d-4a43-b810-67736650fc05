// @ts-nocheck
/**
 * 数据一致性监控工具
 * 用于检测和预防用户状态不一致问题
 */

import { StorageService } from '../services/storage';
import { User, UserSubscription } from '../types/api';

export interface ConsistencyCheckResult {
  isConsistent: boolean;
  issues: string[];
  warnings: string[];
  userId: string;
  timestamp: string;
}

export class DataConsistencyMonitor {
  private storageService: StorageService;

  constructor(storageService: StorageService) {
    this.storageService = storageService;
  }

  /**
   * 检查用户数据一致性
   */
  async checkUserConsistency(userId: string): Promise<ConsistencyCheckResult> {
    const result: ConsistencyCheckResult = {
      isConsistent: true,
      issues: [],
      warnings: [],
      userId,
      timestamp: new Date().toISOString()
    };

    try {
      // 1. 检查用户是否存在
      const user = await this.storageService.getUserById(userId);
      if (!user) {
        result.isConsistent = false;
        result.issues.push('用户不存在于数据库中');
        return result;
      }

      // 2. 检查用户ID格式
      if (userId === 'test-user' || userId.includes('debug')) {
        result.isConsistent = false;
        result.issues.push('检测到测试或调试用户ID');
      }

      // 3. 检查用户积分异常
      if (user.credits > 100000) {
        const subscription = await this.storageService.getUserSubscription(userId);
        if (!subscription || (subscription.plan !== 'unlimited_monthly' && subscription.plan !== 'unlimited')) {
          result.isConsistent = false;
          result.issues.push(`用户积分异常高(${user.credits})但没有无限订阅`);
        }
      }

      // 4. 检查订阅状态一致性
      const subscription = await this.storageService.getUserSubscription(userId);
      if (subscription) {
        const consistencyCheck = await this.checkSubscriptionConsistency(user, subscription);
        if (!consistencyCheck.isConsistent) {
          result.isConsistent = false;
          result.issues.push(...consistencyCheck.issues);
        }
        result.warnings.push(...consistencyCheck.warnings);
      }

      // 5. 检查用户邮箱格式
      if (user.email.includes('debug') || user.email.includes('test')) {
        result.isConsistent = false;
        result.issues.push('检测到测试或调试邮箱');
      }

      // 6. 检查用户名称
      if (user.name.includes('调试') || user.name.includes('debug')) {
        result.warnings.push('用户名称包含调试标识');
      }

      console.log(`🔍 用户一致性检查完成: ${userId}`, {
        isConsistent: result.isConsistent,
        issueCount: result.issues.length,
        warningCount: result.warnings.length
      });

    } catch (error) {
      result.isConsistent = false;
      result.issues.push(`检查过程中出错: ${error.message}`);
      console.error('🚨 用户一致性检查失败:', error);
    }

    return result;
  }

  /**
   * 检查订阅状态一致性
   */
  private async checkSubscriptionConsistency(user: User, subscription: UserSubscription): Promise<{
    isConsistent: boolean;
    issues: string[];
    warnings: string[];
  }> {
    const result = {
      isConsistent: true,
      issues: [] as string[],
      warnings: [] as string[]
    };

    // 检查无限订阅的积分（允许自动修复）
    if (subscription.plan === 'unlimited_monthly' || subscription.plan === 'unlimited') {
      if (user.credits !== 999999) {
        // 不再视为严重错误，而是警告并自动修复
        result.warnings.push(`无限订阅用户积分异常(${user.credits})，将自动重置为999999`);

        // 自动修复积分
        try {
          await this.storageService.updateUser(user.id, { credits: 999999 });
          console.log(`✅ 已自动修复用户 ${user.id} 的积分: ${user.credits} → 999999`);
        } catch (error) {
          console.error(`❌ 修复用户 ${user.id} 积分失败:`, error);
          result.isConsistent = false;
          result.issues.push(`无限订阅用户积分异常(${user.credits})且自动修复失败`);
        }
      }
    }

    // 检查订阅状态
    if (subscription.status !== 'active') {
      result.warnings.push(`订阅状态为${subscription.status}，不是active`);
    }

    // 检查订阅过期时间
    const now = new Date();
    const endDate = new Date(subscription.currentPeriodEnd);
    if (endDate < now) {
      result.warnings.push('订阅已过期');
    }

    return result;
  }

  /**
   * 检查故事归属一致性
   */
  async checkStoryOwnership(storyId: string, expectedUserId: string): Promise<boolean> {
    try {
      const story = await this.storageService.getStoryById(storyId);
      if (!story) {
        console.error(`🚨 故事不存在: ${storyId}`);
        return false;
      }

      if (story.userId !== expectedUserId) {
        console.error(`🚨 故事归属不匹配:`, {
          storyId,
          expectedUserId,
          actualUserId: story.userId
        });
        return false;
      }

      return true;
    } catch (error) {
      console.error('🚨 检查故事归属时出错:', error);
      return false;
    }
  }

  /**
   * 检测数据污染
   */
  async detectDataPollution(): Promise<{
    hasTestUserData: boolean;
    hasDebugUserData: boolean;
    highCreditsUsers: User[];
  }> {
    const result = {
      hasTestUserData: false,
      hasDebugUserData: false,
      highCreditsUsers: [] as User[]
    };

    try {
      // 检查test-user数据
      const testUser = await this.storageService.getUserById('test-user');
      if (testUser) {
        result.hasTestUserData = true;
        console.warn('🚨 检测到test-user数据污染');
      }

      // 这里可以添加更多的污染检测逻辑
      // 由于StorageService没有提供批量查询方法，暂时跳过其他检查

    } catch (error) {
      console.error('🚨 检测数据污染时出错:', error);
    }

    return result;
  }

  /**
   * 生成一致性报告
   */
  generateConsistencyReport(results: ConsistencyCheckResult[]): string {
    const totalChecks = results.length;
    const consistentCount = results.filter(r => r.isConsistent).length;
    const inconsistentCount = totalChecks - consistentCount;

    let report = `📊 数据一致性检查报告\n`;
    report += `检查时间: ${new Date().toISOString()}\n`;
    report += `总检查数: ${totalChecks}\n`;
    report += `一致: ${consistentCount}\n`;
    report += `不一致: ${inconsistentCount}\n\n`;

    if (inconsistentCount > 0) {
      report += `🚨 发现的问题:\n`;
      results.forEach(result => {
        if (!result.isConsistent) {
          report += `用户 ${result.userId}:\n`;
          result.issues.forEach(issue => {
            report += `  - ${issue}\n`;
          });
        }
      });
    }

    return report;
  }
}
