// @ts-nocheck
/**
 * 错误处理工具
 */

import { Context } from 'hono';
import { ApiResponse, ErrorCodes } from '../types/api';

export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code: string = ErrorCodes.INTERNAL_ERROR
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400, ErrorCodes.VALIDATION_ERROR);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, ErrorCodes.UNAUTHORIZED);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, ErrorCodes.FORBIDDEN);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = '资源未找到') {
    super(message, 404, ErrorCodes.NOT_FOUND);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409, ErrorCodes.CONFLICT);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = '请求过于频繁') {
    super(message, 429, ErrorCodes.RATE_LIMIT_EXCEEDED);
    this.name = 'RateLimitError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string = '外部服务错误') {
    super(message, 502, ErrorCodes.EXTERNAL_SERVICE_ERROR);
    this.name = 'ExternalServiceError';
  }
}

/**
 * 全局错误处理中间件
 */
export function errorHandler(error: Error, c: Context): Response {
  console.error('Error occurred:', {
    name: error.name,
    message: error.message,
    stack: error.stack,
    url: c.req.url,
    method: c.req.method,
  });

  // 如果是自定义错误
  if (error instanceof AppError) {
    return c.json<ApiResponse>({
      success: false,
      error: error.message,
      code: error.code
    }, error.statusCode);
  }

  // 处理 JWT 错误
  if (error.name === 'JwtTokenInvalid' || error.name === 'JwtTokenExpired') {
    return c.json<ApiResponse>({
      success: false,
      error: '令牌无效或已过期',
      code: ErrorCodes.INVALID_TOKEN
    }, 401);
  }

  // 处理 JSON 解析错误
  if (error instanceof SyntaxError && error.message.includes('JSON')) {
    return c.json<ApiResponse>({
      success: false,
      error: '请求数据格式错误',
      code: ErrorCodes.VALIDATION_ERROR
    }, 400);
  }

  // 默认内部服务器错误
  return c.json<ApiResponse>({
    success: false,
    error: '内部服务器错误',
    code: ErrorCodes.INTERNAL_ERROR
  }, 500);
}

/**
 * 异步错误包装器
 */
export function asyncHandler(
  fn: (c: Context) => Promise<Response>
) {
  return async (c: Context) => {
    try {
      return await fn(c);
    } catch (error) {
      return errorHandler(error as Error, c);
    }
  };
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  message: string,
  code: string = ErrorCodes.INTERNAL_ERROR,
  statusCode: number = 500
): ApiResponse {
  return {
    success: false,
    error: message,
    code
  };
}

/**
 * 验证错误响应
 */
export function createValidationErrorResponse(
  message: string,
  details?: string[]
): ApiResponse {
  return {
    success: false,
    error: message,
    code: ErrorCodes.VALIDATION_ERROR,
    ...(details && { details })
  };
}

/**
 * 成功响应
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string
): ApiResponse<T> {
  return {
    success: true,
    data,
    ...(message && { message })
  };
}

/**
 * 分页成功响应
 */
export function createPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number
): ApiResponse<T[]> & { pagination: any } {
  return {
    success: true,
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  };
}