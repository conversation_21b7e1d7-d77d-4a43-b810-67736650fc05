// @ts-nocheck
/**
 * 数据一致性检查和修复工具 - Cloudflare Workers兼容版本
 * 用于检查和修复数据库中的媒体文件URL与实际存储的一致性
 */

interface Story {
  id: string;
  title: string;
  pages: string; // JSON string
  audio_url: string | null;
  cover_image_url: string | null;
  status: string;
}

interface Page {
  pageNumber: number;
  text: string;
  imagePrompt: string;
  imageUrl?: string;
  audioUrl?: string;
}

interface ConsistencyCheckResult {
  totalStories: number;
  totalIssues: number;
  fixedIssues: number;
  remainingIssues: number;
  details: Array<{
    storyId: string;
    storyTitle: string;
    issues: string[];
    fixed: number;
  }>;
}

export class DataConsistencyChecker {
  private db: D1Database;
  private r2: R2Bucket;

  constructor(db: D1Database, r2: R2Bucket) {
    this.db = db;
    this.r2 = r2;
  }

  /**
   * 检查所有故事的数据一致性
   */
  async checkAllStories(): Promise<ConsistencyCheckResult> {
    console.log('🔍 [DataConsistencyChecker] 开始检查数据一致性...');

    try {
      // 获取所有故事
      const stories = await this.db.prepare(`
        SELECT id, title, pages, audio_url, cover_image_url, status
        FROM stories
        WHERE status = 'completed'
        ORDER BY created_at DESC
      `).all<Story>();

      console.log(`📊 [DataConsistencyChecker] 找到 ${stories.results.length} 个已完成的故事`);

      let totalIssues = 0;
      let fixedIssues = 0;
      const details: ConsistencyCheckResult['details'] = [];

      for (const story of stories.results) {
        console.log(`📖 [DataConsistencyChecker] 检查故事: ${story.title} (${story.id})`);
        
        const issues = await this.checkStoryConsistency(story);
        totalIssues += issues.length;

        if (issues.length > 0) {
          console.log(`   ❌ 发现 ${issues.length} 个问题:`);
          issues.forEach(issue => console.log(`      - ${issue}`));
          
          // 尝试修复问题
          const fixed = await this.fixStoryIssues(story);
          fixedIssues += fixed;
          
          details.push({
            storyId: story.id,
            storyTitle: story.title,
            issues,
            fixed
          });
          
          if (fixed > 0) {
            console.log(`   ✅ 修复了 ${fixed} 个问题`);
          }
        } else {
          console.log(`   ✅ 数据一致性正常`);
        }
      }

      const result: ConsistencyCheckResult = {
        totalStories: stories.results.length,
        totalIssues,
        fixedIssues,
        remainingIssues: totalIssues - fixedIssues,
        details
      };

      console.log(`\n📋 [DataConsistencyChecker] 检查完成:`);
      console.log(`   总故事数: ${result.totalStories}`);
      console.log(`   总问题数: ${result.totalIssues}`);
      console.log(`   已修复: ${result.fixedIssues}`);
      console.log(`   剩余问题: ${result.remainingIssues}`);

      return result;
    } catch (error) {
      console.error('❌ [DataConsistencyChecker] 检查过程中发生错误:', error);
      throw new Error(`数据一致性检查失败: ${error.message}`);
    }
  }

  /**
   * 检查单个故事的一致性
   */
  private async checkStoryConsistency(story: Story): Promise<string[]> {
    const issues: string[] = [];

    try {
      // 检查音频URL
      if (story.audio_url) {
        const audioExists = await this.checkUrlExists(story.audio_url);
        if (!audioExists) {
          issues.push(`音频文件不存在: ${story.audio_url}`);
        }
      }

      // 检查封面图片URL
      if (story.cover_image_url) {
        const coverExists = await this.checkUrlExists(story.cover_image_url);
        if (!coverExists) {
          issues.push(`封面图片不存在: ${story.cover_image_url}`);
        }
      }

      // 检查页面图片URL
      if (story.pages) {
        const pages: Page[] = JSON.parse(story.pages);
        for (const page of pages) {
          if (page.imageUrl) {
            const imageExists = await this.checkUrlExists(page.imageUrl);
            if (!imageExists) {
              issues.push(`第${page.pageNumber}页图片不存在: ${page.imageUrl}`);
            }
          }
        }
      }
    } catch (error) {
      issues.push(`数据解析失败: ${error.message}`);
    }

    return issues;
  }

  /**
   * 检查URL对应的文件是否存在
   */
  private async checkUrlExists(url: string): Promise<boolean> {
    try {
      if (url.startsWith('https://assets.storyweaver.com/')) {
        // 检查R2存储中的文件
        const key = url.replace('https://assets.storyweaver.com/', '');
        const object = await this.r2.head(key);
        return object !== null;
      } else if (url.startsWith('http')) {
        // 检查外部URL
        const response = await fetch(url, { 
          method: 'HEAD',
          signal: AbortSignal.timeout(5000) // 5秒超时
        });
        return response.ok;
      } else {
        // 无效的URL格式
        return false;
      }
    } catch (error) {
      console.warn(`   ⚠️ URL检查失败: ${url} - ${error.message}`);
      return false;
    }
  }

  /**
   * 修复故事的数据问题
   */
  private async fixStoryIssues(story: Story): Promise<number> {
    let fixedCount = 0;
    const updates: any = {};

    try {
      // 修复无效的音频URL
      if (story.audio_url && !(await this.checkUrlExists(story.audio_url))) {
        updates.audio_url = null;
        fixedCount++;
        console.log(`   🔧 移除无效音频URL: ${story.audio_url}`);
      }

      // 修复无效的封面图片URL
      if (story.cover_image_url && !(await this.checkUrlExists(story.cover_image_url))) {
        updates.cover_image_url = null;
        fixedCount++;
        console.log(`   🔧 移除无效封面URL: ${story.cover_image_url}`);
      }

      // 修复页面中的无效图片URL
      if (story.pages) {
        const pages: Page[] = JSON.parse(story.pages);
        let pagesUpdated = false;

        for (const page of pages) {
          if (page.imageUrl && !(await this.checkUrlExists(page.imageUrl))) {
            console.log(`   🔧 移除第${page.pageNumber}页无效图片URL: ${page.imageUrl}`);
            delete page.imageUrl;
            pagesUpdated = true;
            fixedCount++;
          }
        }

        if (pagesUpdated) {
          updates.pages = JSON.stringify(pages);
        }
      }

      // 应用更新
      if (Object.keys(updates).length > 0) {
        const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
        const values = Object.values(updates);
        values.push(new Date().toISOString(), story.id);

        await this.db.prepare(`
          UPDATE stories 
          SET ${setClause}, updated_at = ?
          WHERE id = ?
        `).bind(...values).run();

        console.log(`   💾 已更新故事数据库记录`);
      }
    } catch (error) {
      console.error(`   ❌ 修复故事 ${story.id} 时发生错误:`, error);
    }

    return fixedCount;
  }

  /**
   * 生成数据一致性报告
   */
  async generateReport(): Promise<{
    totalStories: number;
    completedStories: number;
    storiesWithAudio: number;
    storiesWithCover: number;
  }> {
    console.log('📊 [DataConsistencyChecker] 生成数据一致性报告...');

    try {
      const stats = await this.db.prepare(`
        SELECT
          COUNT(*) as total_stories,
          COUNT(CASE WHEN audio_url IS NOT NULL THEN 1 END) as stories_with_audio,
          COUNT(CASE WHEN cover_image_url IS NOT NULL THEN 1 END) as stories_with_cover,
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_stories
        FROM stories
      `).first() as any;

      const report = {
        totalStories: Number(stats.total_stories) || 0,
        completedStories: Number(stats.completed_stories) || 0,
        storiesWithAudio: Number(stats.stories_with_audio) || 0,
        storiesWithCover: Number(stats.stories_with_cover) || 0
      };

      console.log('📈 数据库统计:');
      console.log(`   总故事数: ${report.totalStories}`);
      console.log(`   已完成故事: ${report.completedStories}`);
      console.log(`   有音频的故事: ${report.storiesWithAudio}`);
      console.log(`   有封面的故事: ${report.storiesWithCover}`);

      return report;
    } catch (error) {
      console.error('❌ [DataConsistencyChecker] 生成报告时发生错误:', error);
      throw new Error(`生成报告失败: ${error.message}`);
    }
  }
}
