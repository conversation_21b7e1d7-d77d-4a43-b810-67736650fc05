// @ts-nocheck
/**
 * 数据验证工具
 */

import { CreateStoryRequest, StoryStyle, VoiceType } from '../types/api';

export interface ValidationResult {
  valid: boolean;
  error?: string;
  details?: string[];
}

/**
 * 验证故事创建请求
 */
export function validateStoryRequest(request: CreateStoryRequest): ValidationResult {
  const errors: string[] = [];

  // 验证角色名称
  if (!request.characterName || typeof request.characterName !== 'string') {
    errors.push('角色名称不能为空');
  } else if (request.characterName.length > 20) {
    errors.push('角色名称不能超过20个字符');
  } else if (!/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(request.characterName)) {
    errors.push('角色名称只能包含中文、英文和空格');
  }

  // 验证年龄
  if (!request.characterAge || typeof request.characterAge !== 'number') {
    errors.push('角色年龄必须是数字');
  } else if (request.characterAge < 3 || request.characterAge > 12) {
    errors.push('角色年龄必须在3-12岁之间');
  }

  // 验证性格特征
  if (!request.characterTraits || !Array.isArray(request.characterTraits)) {
    errors.push('性格特征必须是数组');
  } else if (request.characterTraits.length === 0) {
    errors.push('至少选择一个性格特征');
  } else if (request.characterTraits.length > 5) {
    errors.push('性格特征最多选择5个');
  } else {
    const validTraits = [
      '勇敢', '善良', '聪明', '活泼', '好奇', '爱冒险', '有趣', '友善', '创意', '坚强',
      '温柔', '幽默', '乐观', '诚实', '耐心', '独立', '热情', '细心', '勤奋', '宽容'
    ];
    
    for (const trait of request.characterTraits) {
      if (!validTraits.includes(trait)) {
        errors.push(`无效的性格特征: ${trait}`);
      }
    }
  }

  // 验证主题
  if (!request.theme || typeof request.theme !== 'string') {
    errors.push('故事主题不能为空');
  } else if (request.theme.length > 50) {
    errors.push('故事主题不能超过50个字符');
  }

  // 验证场景
  if (!request.setting || typeof request.setting !== 'string') {
    errors.push('故事场景不能为空');
  } else if (request.setting.length > 50) {
    errors.push('故事场景不能超过50个字符');
  }

  // 验证插图风格
  const validStyles: StoryStyle[] = ['cartoon', 'watercolor', 'sketch', 'fantasy', 'realistic', 'anime'];
  if (!request.style || !validStyles.includes(request.style)) {
    errors.push('无效的插图风格');
  }

  // 验证语音类型
  const validVoices: VoiceType[] = ['gentle_female', 'warm_male', 'child_friendly', 'storyteller'];
  if (!request.voice || !validVoices.includes(request.voice)) {
    errors.push('无效的语音类型');
  }

  // 内容安全检查
  const sensitiveWords = ['暴力', '恐怖', '血腥', '死亡', '杀害', '战争', '武器', '毒品'];
  const allText = [request.characterName, request.theme, request.setting].join(' ');
  
  for (const word of sensitiveWords) {
    if (allText.includes(word)) {
      errors.push(`内容包含不适宜词汇: ${word}`);
    }
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 验证邮箱格式
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * 验证用户名
 */
export function validateUsername(username: string): ValidationResult {
  const errors: string[] = [];

  if (!username || typeof username !== 'string') {
    errors.push('用户名不能为空');
  } else {
    if (username.length < 2) {
      errors.push('用户名至少2个字符');
    }
    if (username.length > 20) {
      errors.push('用户名不能超过20个字符');
    }
    if (!/^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$/.test(username)) {
      errors.push('用户名只能包含中文、英文、数字、下划线和空格');
    }
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 验证支付金额
 */
export function validatePaymentAmount(amount: number): ValidationResult {
  const errors: string[] = [];

  if (typeof amount !== 'number' || isNaN(amount)) {
    errors.push('金额必须是有效数字');
  } else {
    if (amount <= 0) {
      errors.push('金额必须大于0');
    }
    if (amount > 1000) {
      errors.push('单次支付金额不能超过1000美元');
    }
    if (amount % 0.01 !== 0) {
      errors.push('金额精度不能超过2位小数');
    }
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 验证分页参数
 */
export function validatePagination(page?: string, limit?: string): {
  page: number;
  limit: number;
  valid: boolean;
  error?: string;
} {
  let pageNum = 1;
  let limitNum = 10;

  if (page) {
    pageNum = parseInt(page);
    if (isNaN(pageNum) || pageNum < 1) {
      return { page: 1, limit: 10, valid: false, error: '页码必须是大于0的整数' };
    }
    if (pageNum > 1000) {
      return { page: 1, limit: 10, valid: false, error: '页码不能超过1000' };
    }
  }

  if (limit) {
    limitNum = parseInt(limit);
    if (isNaN(limitNum) || limitNum < 1) {
      return { page: pageNum, limit: 10, valid: false, error: '每页数量必须是大于0的整数' };
    }
    if (limitNum > 100) {
      return { page: pageNum, limit: 10, valid: false, error: '每页数量不能超过100' };
    }
  }

  return { page: pageNum, limit: limitNum, valid: true };
}

/**
 * 验证文件上传
 */
export function validateFileUpload(
  file: File | null,
  allowedTypes: string[],
  maxSize: number
): ValidationResult {
  const errors: string[] = [];

  if (!file) {
    errors.push('文件不能为空');
    return { valid: false, error: errors[0], details: errors };
  }

  // 检查文件类型
  if (!allowedTypes.includes(file.type)) {
    errors.push(`不支持的文件类型: ${file.type}`);
  }

  // 检查文件大小
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / 1024 / 1024);
    errors.push(`文件大小不能超过${maxSizeMB}MB`);
  }

  // 检查文件名
  if (file.name.length > 255) {
    errors.push('文件名过长');
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 验证实体书定制信息
 */
export function validateBookCustomization(customization: any): ValidationResult {
  const errors: string[] = [];

  // 验证封面标题
  if (customization.coverTitle && typeof customization.coverTitle === 'string') {
    if (customization.coverTitle.length > 30) {
      errors.push('封面标题不能超过30个字符');
    }
  }

  // 验证封面颜色
  if (!customization.coverColor || typeof customization.coverColor !== 'string') {
    errors.push('封面颜色不能为空');
  } else if (!/^#[0-9A-Fa-f]{6}$/.test(customization.coverColor)) {
    errors.push('封面颜色格式无效');
  }

  // 验证封面风格
  const validStyles = ['standard', 'premium', 'deluxe'];
  if (!customization.coverStyle || !validStyles.includes(customization.coverStyle)) {
    errors.push('无效的封面风格');
  }

  // 验证献词
  if (customization.dedication && typeof customization.dedication === 'string') {
    if (customization.dedication.length > 100) {
      errors.push('献词不能超过100个字符');
    }
  }

  // 验证礼品包装
  if (typeof customization.giftWrap !== 'boolean') {
    errors.push('礼品包装选项必须是布尔值');
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 验证配送信息
 */
export function validateShippingInfo(shippingInfo: any): ValidationResult {
  const errors: string[] = [];

  // 验证收件人姓名
  if (!shippingInfo.recipientName || typeof shippingInfo.recipientName !== 'string') {
    errors.push('收件人姓名不能为空');
  } else if (shippingInfo.recipientName.length > 50) {
    errors.push('收件人姓名不能超过50个字符');
  }

  // 验证地址
  if (!shippingInfo.address || typeof shippingInfo.address !== 'object') {
    errors.push('地址信息不能为空');
  } else {
    const { street, city, state, postalCode, country } = shippingInfo.address;
    
    if (!street || typeof street !== 'string' || street.length > 100) {
      errors.push('街道地址无效');
    }
    if (!city || typeof city !== 'string' || city.length > 50) {
      errors.push('城市名称无效');
    }
    if (!state || typeof state !== 'string' || state.length > 50) {
      errors.push('省/州名称无效');
    }
    if (!postalCode || typeof postalCode !== 'string' || postalCode.length > 20) {
      errors.push('邮政编码无效');
    }
    if (!country || typeof country !== 'string' || country.length > 50) {
      errors.push('国家名称无效');
    }
  }

  // 验证电话号码
  if (!shippingInfo.phone || typeof shippingInfo.phone !== 'string') {
    errors.push('电话号码不能为空');
  } else if (!/^[\d\s\-\+\(\)]{10,20}$/.test(shippingInfo.phone)) {
    errors.push('电话号码格式无效');
  }

  // 验证邮箱
  if (!shippingInfo.email || typeof shippingInfo.email !== 'string') {
    errors.push('邮箱地址不能为空');
  } else if (!validateEmail(shippingInfo.email)) {
    errors.push('邮箱地址格式无效');
  }

  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}

/**
 * 清理和转义用户输入
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .trim()
    .replace(/[<>\"'&]/g, (match) => {
      const escapeMap: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return escapeMap[match];
    });
}

/**
 * 验证内容安全性
 */
export function validateContentSafety(content: string): ValidationResult {
  const errors: string[] = [];
  
  // 敏感词检查
  const sensitiveWords = [
    '暴力', '恐怖', '血腥', '死亡', '杀害', '战争', '武器', '毒品', '赌博',
    '色情', '裸体', '性', '酒精', '烟草', '自杀', '伤害', '欺凌', '歧视'
  ];
  
  const lowerContent = content.toLowerCase();
  
  for (const word of sensitiveWords) {
    if (lowerContent.includes(word)) {
      errors.push(`内容包含不适宜词汇: ${word}`);
    }
  }
  
  // 检查内容长度
  if (content.length > 10000) {
    errors.push('内容过长');
  }
  
  return {
    valid: errors.length === 0,
    error: errors.length > 0 ? errors[0] : undefined,
    details: errors.length > 0 ? errors : undefined
  };
}