// @ts-nocheck
/**
 * StoryWeaver API - 最小化版本
 * 用于快速部署和测试
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';

// 环境变量类型
interface Env {
  GEMINI_API_KEY: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  STRIPE_SECRET_KEY: string;
  JWT_SECRET: string;
  CACHE: KVNamespace;
  ASSETS: R2Bucket;
  DB: D1Database;
  ENVIRONMENT?: string;
}

const app = new Hono<{ Bindings: Env }>();

// 全局中间件
app.use('*', logger());
app.use('*', cors({
  origin: ['https://storyweaver.jamintextiles.com', 'http://localhost:3000'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
}));

// 健康检查
app.get('/', (c) => {
  return c.json({
    success: false,
    error: 'API endpoint not found',
    code: 'NOT_FOUND',
    availableEndpoints: [
      'GET /health',
      'GET /api/info',
      'GET /api/test/env',
      'GET /api/test/db',
      'GET /api/test/kv',
      'GET /api/test/r2'
    ]
  });
});

app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: c.env.ENVIRONMENT || 'unknown'
  });
});

// API 信息
app.get('/api/info', (c) => {
  return c.json({
    success: true,
    data: {
      name: 'StoryWeaver API',
      version: '1.0.0',
      description: 'AI-powered children\'s storybook platform',
      endpoints: {
        health: 'GET /health',
        info: 'GET /api/info',
        test: {
          env: 'GET /api/test/env',
          db: 'GET /api/test/db',
          kv: 'GET /api/test/kv',
          r2: 'GET /api/test/r2'
        }
      }
    }
  });
});

// 测试端点
app.get('/api/test/env', (c) => {
  return c.json({
    success: true,
    data: {
      environment: c.env.ENVIRONMENT || 'unknown',
      hasGeminiKey: !!c.env.GEMINI_API_KEY,
      hasGoogleClientId: !!c.env.GOOGLE_CLIENT_ID,
      hasStripeKey: !!c.env.STRIPE_SECRET_KEY,
      hasJwtSecret: !!c.env.JWT_SECRET
    }
  });
});

app.get('/api/test/db', async (c) => {
  try {
    const result = await c.env.DB.prepare('SELECT 1 as test').first();
    return c.json({
      success: true,
      data: {
        connected: true,
        result
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'Database connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

app.get('/api/test/kv', async (c) => {
  try {
    const testKey = 'health-check';
    const testValue = { timestamp: Date.now(), test: true };
    
    await c.env.CACHE.put(testKey, JSON.stringify(testValue));
    const retrieved = await c.env.CACHE.get(testKey);
    
    return c.json({
      success: true,
      data: {
        connected: true,
        written: testValue,
        retrieved: retrieved ? JSON.parse(retrieved) : null
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'KV Store connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

app.get('/api/test/r2', async (c) => {
  try {
    const testKey = 'health-check.txt';
    const testContent = `Health check at ${new Date().toISOString()}`;
    
    await c.env.ASSETS.put(testKey, testContent);
    const retrieved = await c.env.ASSETS.get(testKey);
    const content = retrieved ? await retrieved.text() : null;
    
    return c.json({
      success: true,
      data: {
        connected: true,
        written: testContent,
        retrieved: content
      }
    });
  } catch (error) {
    return c.json({
      success: false,
      error: 'R2 Storage connection failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'Endpoint not found',
    code: 'NOT_FOUND'
  }, 404);
});

// 错误处理
app.onError((err, c) => {
  console.error('Unhandled error:', err);
  return c.json({
    success: false,
    error: 'Internal server error',
    code: 'INTERNAL_ERROR'
  }, 500);
});

export default app;
