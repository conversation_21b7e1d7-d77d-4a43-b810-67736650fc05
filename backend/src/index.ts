// @ts-nocheck
/**
 * StoryWeaver API - Cloudflare Workers 入口文件
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { upgradeWebSocket } from 'hono/cloudflare-workers';

// 导入路由处理器
import authHandler from './handlers/auth';
import storiesHandler from './handlers/stories';
import usersHandler from './handlers/users';
import paymentsHandler from './handlers/payments';
import booksHandler from './handlers/books';
import subscriptionSettingsHandler from './handlers/subscription-settings';
import charactersHandler from './handlers/characters';

// 导入中间件
import { authMiddleware } from './middleware/auth';
import { rateLimitMiddleware } from './middleware/rateLimit';
import { errorHandler } from './utils/errors';

// 导入类型
import type { Env, Variables } from './types/hono';
import type { ScheduledController, ExecutionContext } from '@cloudflare/workers-types';

// 导入 Durable Objects
import { AITaskQueueDO } from './durable-objects/AITaskQueueDO';

// 生产环境移除调试路由导入

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env }>();

// 全局中间件
app.use('*', logger());
app.use('*', prettyJSON());
app.use('*', cors({
  origin: [
    'https://storyweaver.pages.dev',
    'https://53434694.storyweaver.pages.dev', // 临时部署域名
    'https://storyweaver.com',
    'https://www.storyweaver.com',
    'http://localhost:3000',
    'http://localhost:5173',
    'http://localhost:5174'
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'X-File-Name'
  ],
  credentials: true,
  exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
}));

// 添加安全头，支持Stripe重定向
app.use('*', async (c, next) => {
  await next();

  // 设置Cross-Origin-Opener-Policy以支持Stripe Checkout
  c.header('Cross-Origin-Opener-Policy', 'same-origin-allow-popups');
  c.header('Cross-Origin-Embedder-Policy', 'unsafe-none');
});

// 添加额外的安全头部
app.use('*', async (c, next) => {
  // 设置 Cross-Origin-Opener-Policy 以支持 Google Identity Services
  // 使用 unsafe-none 来避免阻塞 postMessage 调用
  c.header('Cross-Origin-Opener-Policy', 'unsafe-none');
  c.header('Cross-Origin-Embedder-Policy', 'unsafe-none');

  // 添加其他必要的安全头部
  c.header('X-Content-Type-Options', 'nosniff');
  c.header('X-Frame-Options', 'DENY');
  c.header('Referrer-Policy', 'strict-origin-when-cross-origin');

  await next();
});

// 限流中间件
// app.use('/api/*', rateLimitMiddleware);

// 根路径健康检查
app.get('/', (c) => {
  return c.json({
    success: true,
    message: 'StoryWeaver API is running!',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 健康检查端点
app.get('/health', (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// API 健康检查端点
app.get('/api/health', (c) => {
  return c.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      api: 'healthy',
      database: 'healthy',
      ai: 'healthy'
    }
  });
});

// WebSocket 升级处理 - 必须在Durable Objects路由之前
app.get('/ai-queue/:storyId/websocket', upgradeWebSocket(async (c) => {
  const storyId = c.req.param('storyId');
  console.log('WebSocket upgrade request for story:', storyId);

  // 获取Durable Object实例
  const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
  const stub = c.env.AI_TASK_QUEUE.get(id);

  return {
    onOpen: async (event, ws) => {
      console.log('WebSocket opened for story:', storyId);

      // 通知Durable Object有新的WebSocket连接
      try {
        await stub.fetch(new Request(`http://localhost/websocket-connect`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ storyId, action: 'connect' })
        }));

        // 发送连接确认消息
        ws.send(JSON.stringify({
          type: 'connected',
          storyId: storyId,
          timestamp: Date.now()
        }));

        console.log('WebSocket connection registered with Durable Object');
      } catch (error) {
        console.error('Failed to register WebSocket with Durable Object:', error);
      }
    },
    onMessage: async (event, ws) => {
      console.log('WebSocket message received:', event.data);

      // 转发消息到Durable Object
      try {
        await stub.fetch(new Request(`http://localhost/websocket-message`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ storyId, message: event.data })
        }));
      } catch (error) {
        console.error('Failed to forward message to Durable Object:', error);
      }
    },
    onClose: async (event, ws) => {
      console.log('WebSocket closed for story:', storyId);

      // 通知Durable Object连接关闭
      try {
        await stub.fetch(new Request(`http://localhost/websocket-disconnect`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ storyId, action: 'disconnect' })
        }));
      } catch (error) {
        console.error('Failed to notify Durable Object of disconnect:', error);
      }
    },
    onError: (event, ws) => {
      console.error('WebSocket error for story:', storyId, event);
    }
  };
}));

// Durable Objects 路由
app.all('/ai-queue/:storyId/*', async (c) => {
  const storyId = c.req.param('storyId');
  const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
  const stub = c.env.AI_TASK_QUEUE.get(id);

  // 构建新的请求URL，移除前缀
  const url = new URL(c.req.url);
  const newPath = url.pathname.replace(`/ai-queue/${storyId}`, '');
  const newUrl = new URL(newPath || '/', url.origin);
  newUrl.search = url.search;

  // 创建新的请求
  const newRequest = new Request(newUrl.toString(), {
    method: c.req.method,
    headers: c.req.header(),
    body: c.req.method !== 'GET' && c.req.method !== 'HEAD' ? c.req.raw.body : undefined,
  });

  return stub.fetch(newRequest);
});

// 生产环境移除调试路由

// API 路由
app.route('/api/auth', authHandler);
app.route('/api/stories', storiesHandler);
app.route('/api/users', usersHandler);
app.route('/api/payments', paymentsHandler);
app.route('/api/books', booksHandler);
app.route('/api/subscription-settings', subscriptionSettingsHandler);
app.route('/api/characters', charactersHandler);

// 404 处理
app.notFound((c) => {
  return c.json({
    success: false,
    error: 'API endpoint not found',
    code: 'NOT_FOUND'
  }, 404);
});

// 全局错误处理
app.onError(errorHandler);

// 定时任务处理器
export async function scheduled(controller: ScheduledController, env: Env, ctx: ExecutionContext): Promise<void> {
  console.log('🕐 Scheduled task started:', new Date().toISOString());

  try {
    // 导入存储服务
    const { StorageService } = await import('./services/storage');
    const storageService = new StorageService(env.CACHE, env.ASSETS, env.DB);

    // 查找超时的任务（10分钟阈值）
    const timeoutThreshold = new Date(Date.now() - 10 * 60 * 1000).toISOString();

    const stuckTasks = await env.DB.prepare(`
      SELECT id, title, status, updated_at,
             ROUND((julianday('now') - julianday(updated_at)) * 24 * 60, 2) as minutes_stuck
      FROM stories
      WHERE status IN ('generating_text', 'generating_images', 'generating_audio')
      AND datetime(updated_at) < datetime('now', '-10 minutes')
      LIMIT 50
    `).all();

    if (stuckTasks.results && stuckTasks.results.length > 0) {
      console.log(`🔧 Found ${stuckTasks.results.length} stuck tasks, marking as failed`);

      for (const task of stuckTasks.results) {
        try {
          await storageService.updateStory(task.id as string, {
            status: 'failed',
            updated_at: new Date().toISOString()
          });

          console.log(`✅ Marked task ${task.id} as failed (stuck for ${task.minutes_stuck} minutes)`);
        } catch (error) {
          console.error(`❌ Failed to update task ${task.id}:`, error);
        }
      }
    } else {
      console.log('✅ No stuck tasks found');
    }

    // 清理旧的失败任务记录（可选）
    const oldFailedTasks = await env.DB.prepare(`
      DELETE FROM stories
      WHERE status = 'failed'
      AND datetime(updated_at) < datetime('now', '-7 days')
    `).run();

    if (oldFailedTasks.changes && oldFailedTasks.changes > 0) {
      console.log(`🗑️ Cleaned up ${oldFailedTasks.changes} old failed tasks`);
    }

    console.log('✅ Scheduled task completed successfully');

  } catch (error) {
    console.error('❌ Scheduled task failed:', error);
  }
}

// 导出 Durable Objects
export { AITaskQueueDO };

export default app;