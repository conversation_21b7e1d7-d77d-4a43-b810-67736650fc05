// @ts-nocheck
/**
 * 认证相关API处理器
 */

import { Hono } from 'hono';
import { sign, verify } from 'hono/jwt';
import { StorageService } from '../services/storage';
import { ApiResponse, AuthTokens, GoogleUserInfo, User, ErrorCodes } from '../types/api';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

/**
 * 调试用户信息（仅开发环境）
 * 这个端点已被弃用，使用 GET /api/auth/me 替代
 */
app.get('/debug-me', (c) => {
  // 🔒 CRITICAL: 仅在明确的开发环境中提供调试用户
  if (c.env.ENVIRONMENT !== 'development' || c.env.DEBUG_MODE !== 'true') {
    return c.json({
      success: false,
      error: '调试功能仅在开发环境中可用',
      code: 'DEBUG_DISABLED'
    }, 403);
  }

  // 在调试模式下返回模拟用户
  return c.json({
    success: true,
    data: {
      id: 'debug-user',
      email: '<EMAIL>',
      name: '调试用户',
      avatar: 'https://example.com/avatar.jpg',
      credits: 100,
      subscriptionType: 'free',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  });
});

/**
 * Google OAuth 登录
 * POST /api/auth/google
 */
app.post('/google', async (c) => {
  try {
    const body = await c.req.json();
    const { code, redirectUri, googleToken } = body;

    let googleUser: GoogleUserInfo;

    if (googleToken) {
      // 处理 Google Identity Services credential token
      console.log('🔧 Processing Google Identity Services token');

      // 检查环境变量
      if (!c.env.GOOGLE_CLIENT_ID) {
        console.error('❌ GOOGLE_CLIENT_ID environment variable not set');
        return c.json<ApiResponse>({
          success: false,
          error: 'Google OAuth 配置错误',
          code: ErrorCodes.CONFIGURATION_ERROR
        }, 500);
      }

      console.log('🔧 Environment check passed');

      console.log('🔧 GOOGLE_CLIENT_ID configured:', c.env.GOOGLE_CLIENT_ID.substring(0, 20) + '...');

      try {
        console.log('🔧 Verifying Google token with tokeninfo API');

        // 验证并解码 JWT token
        const tokeninfoUrl = `https://oauth2.googleapis.com/tokeninfo?id_token=${googleToken}`;
        const response = await fetch(tokeninfoUrl);

        console.log('🔧 Tokeninfo API response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ Tokeninfo API error:', response.status, errorText);
          throw new Error(`Google tokeninfo API failed: ${response.status} - ${errorText}`);
        }

        const tokenInfo = await response.json();
        console.log('🔧 Token info received:', {
          aud: tokenInfo.aud,
          sub: tokenInfo.sub,
          email: tokenInfo.email,
          name: tokenInfo.name,
          exp: tokenInfo.exp,
          iat: tokenInfo.iat
        });

        // 验证 token 的 audience (client_id)
        if (tokenInfo.aud !== c.env.GOOGLE_CLIENT_ID) {
          console.error('❌ Token audience mismatch:', {
            expected: c.env.GOOGLE_CLIENT_ID,
            received: tokenInfo.aud
          });
          throw new Error('Token audience mismatch');
        }

        // 检查token是否过期
        const now = Math.floor(Date.now() / 1000);
        if (tokenInfo.exp && tokenInfo.exp < now) {
          console.error('❌ Token expired:', {
            exp: tokenInfo.exp,
            now: now
          });
          throw new Error('Token expired');
        }

        // 构造用户信息对象
        googleUser = {
          id: tokenInfo.sub,
          email: tokenInfo.email,
          name: tokenInfo.name,
          picture: tokenInfo.picture,
          verified_email: tokenInfo.email_verified === 'true' || tokenInfo.email_verified === true
        };

        console.log('✅ Google token verification successful for user:', googleUser.email);

      } catch (error) {
        console.error('❌ Google token verification failed:', error);
        console.error('❌ Error details:', {
          message: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });

        return c.json<ApiResponse>({
          success: false,
          error: 'Google 认证失败',
          code: ErrorCodes.AUTHENTICATION_FAILED
        }, 400);
      }
    } else if (code) {
      // 处理传统的 OAuth2 authorization code flow
      if (!code) {
        return c.json<ApiResponse>({
          success: false,
          error: '缺少授权码',
          code: ErrorCodes.VALIDATION_ERROR
        }, 400);
      }

      // 1. 用授权码换取访问令牌
      const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: c.env.GOOGLE_CLIENT_ID,
          client_secret: c.env.GOOGLE_CLIENT_SECRET,
          code: code,
          grant_type: 'authorization_code',
          redirect_uri: redirectUri || 'https://storyweaver.pages.dev/auth/callback'
        })
      });

      if (!tokenResponse.ok) {
        throw new Error('Google token exchange failed');
      }

      const tokenData = await tokenResponse.json() as any;
      const accessToken = tokenData.access_token;

      // 2. 获取用户信息
      const userResponse = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (!userResponse.ok) {
        throw new Error('Failed to get user info from Google');
      }

      googleUser = await userResponse.json();
    } else {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少认证参数',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 3. 检查用户是否已存在
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    let user = await storageService.getUserByGoogleId(googleUser.id);

    if (!user) {
      // 创建新用户
      user = await storageService.createUser({
        email: googleUser.email,
        name: googleUser.name,
        avatar: googleUser.picture,
        googleId: googleUser.id,
        credits: 1 // 新用户赠送1个免费故事
      });
    } else {
      // 更新现有用户信息
      await storageService.updateUser(user.id, {
        name: googleUser.name,
        avatar: googleUser.picture
      });
      
      // 重新获取更新后的用户信息
      user = await storageService.getUserById(user.id);
    }

    // 4. 生成JWT令牌
    const tokens = await generateTokens(user!, c.env.JWT_SECRET);

    // 5. 缓存用户会话
    await storageService.cacheUserSession(user!.id, {
      userId: user!.id,
      email: user!.email,
      name: user!.name,
      loginTime: new Date().toISOString()
    }, 3600 * 24 * 7); // 7天

    return c.json<ApiResponse<{ user: User; tokens: AuthTokens }>>({
      success: true,
      data: {
        user: user!,
        tokens
      }
    });

  } catch (error) {
    console.error('Google auth failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '登录失败，请重试',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 刷新访问令牌
 * POST /api/auth/refresh
 */
app.post('/refresh', async (c) => {
  try {
    const { refreshToken } = await c.req.json();

    if (!refreshToken) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少刷新令牌',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 验证刷新令牌
    const payload = await verify(refreshToken, c.env.JWT_SECRET);
    
    if (!payload || payload.type !== 'refresh') {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的刷新令牌',
        code: ErrorCodes.INVALID_TOKEN
      }, 401);
    }

    // 获取用户信息
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const user = await storageService.getUserById(payload.userId as string);

    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: '用户不存在',
        code: ErrorCodes.USER_NOT_FOUND
      }, 404);
    }

    // 生成新的令牌
    const tokens = await generateTokens(user, c.env.JWT_SECRET);

    return c.json<ApiResponse<AuthTokens>>({
      success: true,
      data: tokens
    });

  } catch (error) {
    console.error('Token refresh failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '令牌刷新失败',
      code: ErrorCodes.TOKEN_EXPIRED
    }, 401);
  }
});

/**
 * 登出
 * POST /api/auth/logout
 */
app.post('/logout', async (c) => {
  try {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json<ApiResponse>({
        success: true,
        message: '已登出'
      });
    }

    const token = authHeader.substring(7);
    
    try {
      const payload = await verify(token, c.env.JWT_SECRET);
      
      if (payload && payload.userId) {
        // 删除用户会话缓存
        const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
        await storageService.deleteUserSession(payload.userId as string);
      }
    } catch (error) {
      // 忽略令牌验证错误，继续登出流程
    }

    return c.json<ApiResponse>({
      success: true,
      message: '登出成功'
    });

  } catch (error) {
    console.error('Logout failed:', error);
    return c.json<ApiResponse>({
      success: true,
      message: '已登出'
    });
  }
});

/**
 * 获取当前用户信息
 * GET /api/auth/me
 */
app.get('/me', async (c) => {
  try {
    const authHeader = c.req.header('Authorization');
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return c.json<ApiResponse>({
        success: false,
        error: '未提供认证令牌',
        code: ErrorCodes.UNAUTHORIZED
      }, 401);
    }

    const token = authHeader.substring(7);
    const payload = await verify(token, c.env.JWT_SECRET);
    
    if (!payload || payload.type !== 'access') {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的访问令牌',
        code: ErrorCodes.INVALID_TOKEN
      }, 401);
    }

    // 获取用户信息
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const user = await storageService.getUserById(payload.userId as string);

    if (!user) {
      return c.json<ApiResponse>({
        success: false,
        error: '用户不存在',
        code: ErrorCodes.USER_NOT_FOUND
      }, 404);
    }

    // 获取用户订阅信息
    const subscription = await storageService.getUserSubscription(user.id);
    
    return c.json<ApiResponse<User>>({
      success: true,
      data: {
        ...user,
        subscription
      }
    });

  } catch (error) {
    console.error('Get current user failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取用户信息失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 验证令牌
 * POST /api/auth/verify
 */
app.post('/verify', async (c) => {
  try {
    const { token } = await c.req.json();

    if (!token) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少令牌',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const payload = await verify(token, c.env.JWT_SECRET);
    
    if (!payload) {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的令牌',
        code: ErrorCodes.INVALID_TOKEN
      }, 401);
    }

    // 检查令牌是否过期
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      return c.json<ApiResponse>({
        success: false,
        error: '令牌已过期',
        code: ErrorCodes.TOKEN_EXPIRED
      }, 401);
    }

    return c.json<ApiResponse<{ valid: boolean; payload: any }>>({
      success: true,
      data: {
        valid: true,
        payload
      }
    });

  } catch (error) {
    console.error('Token verification failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '令牌验证失败',
      code: ErrorCodes.INVALID_TOKEN
    }, 401);
  }
});

/**
 * 生成访问令牌和刷新令牌
 */
async function generateTokens(user: User, secret: string): Promise<AuthTokens> {
  const now = Math.floor(Date.now() / 1000);
  const accessTokenExpiry = now + (60 * 60 * 24); // 24小时
  const refreshTokenExpiry = now + (60 * 60 * 24 * 7); // 7天

  const accessToken = await sign({
    userId: user.id,
    email: user.email,
    type: 'access',
    iat: now,
    exp: accessTokenExpiry
  }, secret);

  const refreshToken = await sign({
    userId: user.id,
    type: 'refresh',
    iat: now,
    exp: refreshTokenExpiry
  }, secret);

  return {
    accessToken,
    refreshToken,
    expiresIn: 86400, // 24小时（秒）
    expiresAt: accessTokenExpiry * 1000 // 前端需要的毫秒时间戳
  };
}

export default app;