// @ts-nocheck
/**
 * 订阅设置相关API处理器
 */

import { Hono } from 'hono';
import { StorageService } from '../services/storage';
import { SubscriptionService } from '../services/subscription';
import { authMiddleware } from '../middleware/auth';
import { ApiResponse, UserPreferences, UsageStats, ApiKey } from '../types/api';
import type { Env, Variables, AuthenticatedContext } from '../types/hono';
import { randomUUID } from 'node:crypto';

const app = new Hono<{ Bindings: Env }>();

// 应用认证中间件到所有路由
app.use('*', authMiddleware);

/**
 * 获取用户偏好设置
 * GET /api/subscription-settings/preferences
 */
app.get('/preferences', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 从数据库获取用户偏好设置
    const preferences = await storageService.getUserPreferences(user.id);
    
    // 如果没有偏好设置，返回默认值
    if (!preferences) {
      const defaultPreferences: UserPreferences = {
        aiModel: 'gemini-2.5-flash',
        imageQuality: 'standard',
        selectedVoice: 'gentle_female',
        audioQuality: 'standard',
        audioSpeed: 1.0,
        audioPitch: 0,
        permissions: {
          commercialUse: false,
          apiAccess: false,
          batchExport: false
        },
        language: 'zh',
        theme: 'light',
        updatedAt: new Date().toISOString()
      };

      return c.json<ApiResponse<UserPreferences>>({
        success: true,
        data: defaultPreferences
      });
    }

    return c.json<ApiResponse<UserPreferences>>({
      success: true,
      data: preferences
    });

  } catch (error) {
    console.error('获取用户偏好设置失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取偏好设置失败',
      code: 'PREFERENCES_FETCH_ERROR'
    }, 500);
  }
});

/**
 * 更新用户偏好设置
 * PUT /api/subscription-settings/preferences
 */
app.put('/preferences', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    
    const body = await c.req.json();
    
    // 验证请求数据
    if (!body || typeof body !== 'object') {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的请求数据',
        code: 'INVALID_REQUEST_DATA'
      }, 400);
    }

    // 获取用户订阅信息以验证权限
    const subscription = await storageService.getUserSubscription(user.id);
    const features = SubscriptionService.getFeatures(subscription);

    // 验证AI模型选择权限
    if (body.aiModel) {
      const allowedModels = ['gemini-2.5-flash'];
      if (subscription?.plan === 'pro_monthly' || subscription?.plan === 'pro_yearly') {
        allowedModels.push('gemini-2.5-pro');
      }
      if (subscription?.plan === 'unlimited_monthly') {
        allowedModels.push('gemini-2.5-pro', 'gemini-2.5-ultra');
      }
      
      if (!allowedModels.includes(body.aiModel)) {
        return c.json<ApiResponse>({
          success: false,
          error: '您的订阅计划不支持此AI模型',
          code: 'INSUFFICIENT_PERMISSIONS'
        }, 403);
      }
    }

    // 验证图片质量权限
    if (body.imageQuality) {
      const allowedQualities = ['standard'];
      if (subscription?.plan === 'pro_monthly' || subscription?.plan === 'pro_yearly') {
        allowedQualities.push('premium');
      }
      if (subscription?.plan === 'unlimited_monthly') {
        allowedQualities.push('premium', 'ultra');
      }
      
      if (!allowedQualities.includes(body.imageQuality)) {
        return c.json<ApiResponse>({
          success: false,
          error: '您的订阅计划不支持此图片质量',
          code: 'INSUFFICIENT_PERMISSIONS'
        }, 403);
      }
    }

    // 验证声音选择权限
    if (body.selectedVoice) {
      const availableVoices = features.audioGeneration.voiceOptions;
      if (!availableVoices.includes(body.selectedVoice)) {
        return c.json<ApiResponse>({
          success: false,
          error: '您的订阅计划不支持此声音选项',
          code: 'INSUFFICIENT_PERMISSIONS'
        }, 403);
      }
    }

    // 验证权限设置
    if (body.permissions) {
      if (body.permissions.commercialUse && !features.permissions.commercialUse) {
        return c.json<ApiResponse>({
          success: false,
          error: '您的订阅计划不支持商业使用权限',
          code: 'INSUFFICIENT_PERMISSIONS'
        }, 403);
      }
      
      if (body.permissions.apiAccess && !features.permissions.apiAccess) {
        return c.json<ApiResponse>({
          success: false,
          error: '您的订阅计划不支持API访问权限',
          code: 'INSUFFICIENT_PERMISSIONS'
        }, 403);
      }
    }

    // 构建更新的偏好设置
    const updatedPreferences: UserPreferences = {
      aiModel: body.aiModel || 'gemini-2.5-flash',
      imageQuality: body.imageQuality || 'standard',
      selectedVoice: body.selectedVoice || 'gentle_female',
      audioQuality: body.audioQuality || 'standard',
      audioSpeed: body.audioSpeed || 1.0,
      audioPitch: body.audioPitch || 0,
      permissions: {
        commercialUse: body.permissions?.commercialUse || false,
        apiAccess: body.permissions?.apiAccess || false,
        batchExport: body.permissions?.batchExport || false
      },
      language: body.language || 'zh',
      theme: body.theme || 'light',
      updatedAt: new Date().toISOString()
    };

    // 保存到数据库
    await storageService.updateUserPreferences(user.id, updatedPreferences);

    return c.json<ApiResponse<UserPreferences>>({
      success: true,
      data: updatedPreferences,
      message: '偏好设置已更新'
    });

  } catch (error) {
    console.error('更新用户偏好设置失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '更新偏好设置失败',
      code: 'PREFERENCES_UPDATE_ERROR'
    }, 500);
  }
});

/**
 * 获取用户使用统计
 * GET /api/subscription-settings/usage-stats
 */
app.get('/usage-stats', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 获取当前月份和日期
    const now = new Date();
    const currentMonth = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
    const currentDay = now.toISOString().split('T')[0];

    // 获取月度和日度统计
    const [monthlyStats, dailyStats] = await Promise.all([
      storageService.getUserUsageStats(user.id, currentMonth),
      storageService.getUserUsageStats(user.id, currentDay)
    ]);

    // 获取总体统计
    const totalStats = await storageService.getUserTotalStats(user.id);

    const usageData = {
      storiesThisMonth: monthlyStats?.storiesCreated || 0,
      storiesThisDay: dailyStats?.storiesCreated || 0,
      charactersUsed: totalStats?.charactersUsed || 0,
      pagesGenerated: totalStats?.pagesGenerated || 0,
      apiCallsUsed: monthlyStats?.apiCallsUsed || 0,
      storageUsed: totalStats?.storageUsed || 0
    };

    return c.json<ApiResponse>({
      success: true,
      data: usageData
    });

  } catch (error) {
    console.error('获取使用统计失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取使用统计失败',
      code: 'USAGE_STATS_ERROR'
    }, 500);
  }
});

/**
 * 生成API密钥
 * POST /api/subscription-settings/api-key
 */
app.post('/api-key', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查用户是否有API访问权限
    const subscription = await storageService.getUserSubscription(user.id);
    if (!SubscriptionService.hasAPIAccess(subscription)) {
      return c.json<ApiResponse>({
        success: false,
        error: '您的订阅计划不支持API访问',
        code: 'INSUFFICIENT_PERMISSIONS'
      }, 403);
    }

    // 生成新的API密钥
    const apiKey = `sk-${randomUUID()}-${randomUUID()}`;
    const keyHash = await crypto.subtle.digest('SHA-256', new TextEncoder().encode(apiKey)).then(
      buffer => Array.from(new Uint8Array(buffer)).map(b => b.toString(16).padStart(2, '0')).join('')
    );

    const apiKeyData: ApiKey = {
      id: randomUUID(),
      userId: user.id,
      keyHash,
      name: 'Default API Key',
      createdAt: new Date().toISOString(),
      isActive: true
    };

    // 保存到数据库
    await storageService.createApiKey(apiKeyData);

    return c.json<ApiResponse>({
      success: true,
      data: {
        apiKey, // 只在创建时返回完整密钥
        keyId: apiKeyData.id,
        createdAt: apiKeyData.createdAt
      },
      message: 'API密钥已生成'
    });

  } catch (error) {
    console.error('生成API密钥失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '生成API密钥失败',
      code: 'API_KEY_GENERATION_ERROR'
    }, 500);
  }
});

/**
 * 获取用户的API密钥列表（不包含完整密钥）
 * GET /api/subscription-settings/api-keys
 */
app.get('/api-keys', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查用户是否有API访问权限
    const subscription = await storageService.getUserSubscription(user.id);
    if (!SubscriptionService.hasAPIAccess(subscription)) {
      return c.json<ApiResponse>({
        success: false,
        error: '您的订阅计划不支持API访问',
        code: 'INSUFFICIENT_PERMISSIONS'
      }, 403);
    }

    const apiKeys = await storageService.getUserApiKeys(user.id);

    // 返回不包含完整密钥的信息
    const safeApiKeys = apiKeys.map(key => ({
      id: key.id,
      name: key.name,
      lastUsed: key.lastUsed,
      createdAt: key.createdAt,
      isActive: key.isActive,
      keyPreview: `sk-...${key.keyHash.slice(-8)}`
    }));

    return c.json<ApiResponse>({
      success: true,
      data: safeApiKeys
    });

  } catch (error) {
    console.error('获取API密钥列表失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取API密钥列表失败',
      code: 'API_KEYS_FETCH_ERROR'
    }, 500);
  }
});

/**
 * 删除API密钥
 * DELETE /api/subscription-settings/api-key/:keyId
 */
app.delete('/api-key/:keyId', async (c) => {
  try {
    const user = c.get('user');
    const keyId = c.req.param('keyId');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查用户是否有API访问权限
    const subscription = await storageService.getUserSubscription(user.id);
    if (!SubscriptionService.hasAPIAccess(subscription)) {
      return c.json<ApiResponse>({
        success: false,
        error: '您的订阅计划不支持API访问',
        code: 'INSUFFICIENT_PERMISSIONS'
      }, 403);
    }

    // 删除API密钥
    const deleted = await storageService.deleteApiKey(user.id, keyId);

    if (!deleted) {
      return c.json<ApiResponse>({
        success: false,
        error: 'API密钥不存在',
        code: 'API_KEY_NOT_FOUND'
      }, 404);
    }

    return c.json<ApiResponse>({
      success: true,
      message: 'API密钥已删除'
    });

  } catch (error) {
    console.error('删除API密钥失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '删除API密钥失败',
      code: 'API_KEY_DELETE_ERROR'
    }, 500);
  }
});

/**
 * 获取用户的订阅功能配置
 * GET /api/subscription-settings/features
 */
app.get('/features', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 获取用户订阅信息
    const subscription = await storageService.getUserSubscription(user.id);
    
    // 获取功能配置
    const features = SubscriptionService.getFeatures(subscription);
    
    // 获取当前使用统计
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM
    const currentUsage = await storageService.getUserUsageStats(user.id, currentMonth);
    
    return c.json<ApiResponse<any>>({
      success: true,
      data: {
        subscription: {
          plan: subscription?.plan || 'free',
          status: subscription?.status || 'inactive',
          currentPeriodEnd: subscription?.currentPeriodEnd
        },
        features,
        currentUsage: {
          storiesCreated: currentUsage?.storiesCreated || 0,
          pagesGenerated: currentUsage?.pagesGenerated || 0,
          charactersUsed: currentUsage?.charactersUsed || 0,
          apiCallsUsed: currentUsage?.apiCallsUsed || 0,
          storageUsed: currentUsage?.storageUsed || 0
        },
        limits: {
          storiesRemaining: Math.max(0, features.limits.maxStoriesPerMonth - (currentUsage?.storiesCreated || 0)),
          canCreateStory: SubscriptionService.checkLimit(subscription, 'maxStoriesPerMonth', currentUsage?.storiesCreated || 0)
        }
      }
    });

  } catch (error) {
    console.error('获取订阅功能配置失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取订阅功能配置失败',
      code: 'FEATURES_FETCH_ERROR'
    }, 500);
  }
});

/**
 * 验证用户权限
 * POST /api/subscription-settings/validate-permission
 */
app.post('/validate-permission', async (c) => {
  try {
    const user = c.get('user');
    const body = await c.req.json();
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    if (!body.permission) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少权限参数',
        code: 'MISSING_PERMISSION_PARAMETER'
      }, 400);
    }

    // 获取用户订阅信息
    const subscription = await storageService.getUserSubscription(user.id);
    
    // 验证权限
    const hasPermission = SubscriptionService.hasPermission(subscription, body.permission);
    
    return c.json<ApiResponse<any>>({
      success: true,
      data: {
        hasPermission,
        currentPlan: subscription?.plan || 'free',
        requiredPlan: body.permission === 'commercialUse' || body.permission === 'apiAccess' ? 'unlimited_monthly' : 'pro_monthly'
      }
    });

  } catch (error) {
    console.error('验证用户权限失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '验证用户权限失败',
      code: 'PERMISSION_VALIDATION_ERROR'
    }, 500);
  }
});

export default app;