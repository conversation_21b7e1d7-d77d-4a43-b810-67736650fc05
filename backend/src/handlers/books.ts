// @ts-nocheck
/**
 * 实体书相关API处理器
 */

import { Hono } from 'hono';
import { StorageService } from '../services/storage';
import { authMiddleware } from '../middleware/auth';
import { validateBookCustomization, validateShippingInfo, validatePagination } from '../utils/validation';
import { ApiResponse, PhysicalBook, ErrorCodes } from '../types/api';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

// 应用认证中间件到所有路由
app.use('*', authMiddleware);

/**
 * 创建实体书订单
 * POST /api/books
 */
app.post('/', async (c) => {
  try {
    const user = c.get('user');
    const { storyId, customization, shippingInfo } = await c.req.json();

    // 验证必要字段
    if (!storyId || !customization || !shippingInfo) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少必要字段',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 验证定制信息
    const customizationValidation = validateBookCustomization(customization);
    if (!customizationValidation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: customizationValidation.error,
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 验证配送信息
    const shippingValidation = validateShippingInfo(shippingInfo);
    if (!shippingValidation.valid) {
      return c.json<ApiResponse>({
        success: false,
        error: shippingValidation.error,
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 验证故事是否存在且属于当前用户
    const story = await storageService.getStoryById(storyId);
    if (!story) {
      return c.json<ApiResponse>({
        success: false,
        error: '故事不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    if (story.userId !== user.id) {
      return c.json<ApiResponse>({
        success: false,
        error: '无权访问此故事',
        code: ErrorCodes.UNAUTHORIZED
      }, 403);
    }

    if (story.status !== 'completed') {
      return c.json<ApiResponse>({
        success: false,
        error: '故事尚未完成，无法制作实体书',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 计算价格
    const basePrice = 29.99;
    let totalPrice = basePrice;

    // 根据定制选项调整价格
    if (customization.coverStyle === 'premium') {
      totalPrice += 10.00;
    } else if (customization.coverStyle === 'deluxe') {
      totalPrice += 20.00;
    }

    if (customization.giftWrap) {
      totalPrice += 5.00;
    }

    // 生成订单号
    const orderNumber = generateOrderNumber();

    // 创建实体书订单
    const bookId = crypto.randomUUID();
    const now = new Date().toISOString();

    await c.env.DB.prepare(`
      INSERT INTO physical_books (
        id, story_id, user_id, order_number, customization, 
        shipping_info, status, price, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      bookId,
      storyId,
      user.id,
      orderNumber,
      JSON.stringify(customization),
      JSON.stringify(shippingInfo),
      'pending',
      totalPrice,
      now,
      now
    ).run();

    // 创建Stripe支付意图
    const paymentIntent = await createBookPaymentIntent(
      totalPrice,
      {
        bookId,
        userId: user.id,
        storyId,
        orderNumber
      },
      c.env.STRIPE_SECRET_KEY
    );

    // 更新订单的支付意图ID
    await c.env.DB.prepare(`
      UPDATE physical_books 
      SET stripe_payment_intent_id = ? 
      WHERE id = ?
    `).bind(paymentIntent.id, bookId).run();

    // 记录用户活动
    await c.env.DB.prepare(`
      INSERT INTO user_activities (
        id, user_id, activity_type, activity_data, created_at
      ) VALUES (?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      user.id,
      'book_order_created',
      JSON.stringify({ bookId, orderNumber, storyId, price: totalPrice }),
      now
    ).run();

    return c.json<ApiResponse<{
      bookId: string;
      orderNumber: string;
      price: number;
      paymentIntent: any;
    }>>({
      success: true,
      data: {
        bookId,
        orderNumber,
        price: totalPrice,
        paymentIntent: {
          id: paymentIntent.id,
          clientSecret: paymentIntent.client_secret,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency
        }
      }
    });

  } catch (error) {
    console.error('Create book order failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '创建订单失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取用户的实体书订单列表
 * GET /api/books
 */
app.get('/', async (c) => {
  try {
    const user = c.get('user');
    const { page, limit, valid, error } = validatePagination(
      c.req.query('page'),
      c.req.query('limit')
    );

    if (!valid) {
      return c.json<ApiResponse>({
        success: false,
        error: error,
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const status = c.req.query('status');
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE pb.user_id = ?';
    const params = [user.id];

    if (status) {
      whereClause += ' AND pb.status = ?';
      params.push(status);
    }

    // 获取订单列表
    const books = await c.env.DB.prepare(`
      SELECT 
        pb.id,
        pb.order_number,
        pb.status,
        pb.price,
        pb.tracking_number,
        pb.created_at,
        pb.updated_at,
        s.title as story_title,
        s.character_name,
        s.cover_image_url
      FROM physical_books pb
      JOIN stories s ON pb.story_id = s.id
      ${whereClause}
      ORDER BY pb.created_at DESC 
      LIMIT ? OFFSET ?
    `).bind(...params, limit, offset).all();

    // 获取总数
    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count 
      FROM physical_books pb 
      ${whereClause}
    `).bind(...params).first();

    const total = countResult?.count || 0;

    return c.json<ApiResponse<any[]>>({
      success: true,
      data: books.results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get book orders failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取订单列表失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取实体书订单详情
 * GET /api/books/:id
 */
app.get('/:id', async (c) => {
  try {
    const bookId = c.req.param('id');
    const user = c.get('user');

    // 获取订单详情
    const book = await c.env.DB.prepare(`
      SELECT 
        pb.*,
        s.title as story_title,
        s.character_name,
        s.cover_image_url,
        s.pages
      FROM physical_books pb
      JOIN stories s ON pb.story_id = s.id
      WHERE pb.id = ? AND pb.user_id = ?
    `).bind(bookId, user.id).first();

    if (!book) {
      return c.json<ApiResponse>({
        success: false,
        error: '订单不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 解析JSON字段
    const bookData = {
      ...book,
      customization: JSON.parse(book.customization),
      shippingInfo: JSON.parse(book.shipping_info),
      storyPages: JSON.parse(book.pages || '[]')
    };

    // 移除敏感字段
    delete bookData.shipping_info;
    delete bookData.pages;

    return c.json<ApiResponse<any>>({
      success: true,
      data: bookData
    });

  } catch (error) {
    console.error('Get book order failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取订单详情失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 取消实体书订单
 * DELETE /api/books/:id
 */
app.delete('/:id', async (c) => {
  try {
    const bookId = c.req.param('id');
    const user = c.get('user');

    // 获取订单信息
    const book = await c.env.DB.prepare(`
      SELECT * FROM physical_books 
      WHERE id = ? AND user_id = ?
    `).bind(bookId, user.id).first();

    if (!book) {
      return c.json<ApiResponse>({
        success: false,
        error: '订单不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 检查订单状态
    if (!['pending', 'printing'].includes(book.status)) {
      return c.json<ApiResponse>({
        success: false,
        error: '订单已发货，无法取消',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 如果有支付意图，取消支付
    if (book.stripe_payment_intent_id) {
      try {
        await cancelStripePaymentIntent(
          book.stripe_payment_intent_id,
          c.env.STRIPE_SECRET_KEY
        );
      } catch (error) {
        console.error('Cancel payment intent failed:', error);
        // 继续执行取消订单流程
      }
    }

    // 更新订单状态
    await c.env.DB.prepare(`
      UPDATE physical_books 
      SET status = 'canceled', updated_at = ? 
      WHERE id = ?
    `).bind(new Date().toISOString(), bookId).run();

    // 记录用户活动
    await c.env.DB.prepare(`
      INSERT INTO user_activities (
        id, user_id, activity_type, activity_data, created_at
      ) VALUES (?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      user.id,
      'book_order_canceled',
      JSON.stringify({ bookId, orderNumber: book.order_number }),
      new Date().toISOString()
    ).run();

    return c.json<ApiResponse>({
      success: true,
      message: '订单取消成功'
    });

  } catch (error) {
    console.error('Cancel book order failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '取消订单失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 确认实体书支付
 * POST /api/books/:id/confirm-payment
 */
app.post('/:id/confirm-payment', async (c) => {
  try {
    const bookId = c.req.param('id');
    const user = c.get('user');
    const { paymentIntentId } = await c.req.json();

    if (!paymentIntentId) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少支付意图ID',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 获取订单信息
    const book = await c.env.DB.prepare(`
      SELECT * FROM physical_books 
      WHERE id = ? AND user_id = ?
    `).bind(bookId, user.id).first();

    if (!book) {
      return c.json<ApiResponse>({
        success: false,
        error: '订单不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 验证支付状态
    const paymentIntent = await getStripePaymentIntent(
      paymentIntentId,
      c.env.STRIPE_SECRET_KEY
    );

    if (paymentIntent.status !== 'succeeded') {
      return c.json<ApiResponse>({
        success: false,
        error: '支付未完成',
        code: ErrorCodes.PAYMENT_FAILED
      }, 400);
    }

    // 更新订单状态
    await c.env.DB.prepare(`
      UPDATE physical_books 
      SET status = 'printing', updated_at = ? 
      WHERE id = ?
    `).bind(new Date().toISOString(), bookId).run();

    // 记录支付记录
    await c.env.DB.prepare(`
      INSERT INTO payments (
        id, user_id, type, amount, currency, status, 
        stripe_payment_intent_id, metadata, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      user.id,
      'physical_book',
      book.price,
      'USD',
      'succeeded',
      paymentIntentId,
      JSON.stringify({ bookId, orderNumber: book.order_number }),
      new Date().toISOString(),
      new Date().toISOString()
    ).run();

    // 记录用户活动
    await c.env.DB.prepare(`
      INSERT INTO user_activities (
        id, user_id, activity_type, activity_data, created_at
      ) VALUES (?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      user.id,
      'book_payment_completed',
      JSON.stringify({ 
        bookId, 
        orderNumber: book.order_number, 
        amount: book.price 
      }),
      new Date().toISOString()
    ).run();

    return c.json<ApiResponse>({
      success: true,
      message: '支付确认成功，订单已进入制作流程'
    });

  } catch (error) {
    console.error('Confirm book payment failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '支付确认失败',
      code: ErrorCodes.PAYMENT_FAILED
    }, 500);
  }
});

/**
 * 跟踪实体书订单
 * GET /api/books/:id/tracking
 */
app.get('/:id/tracking', async (c) => {
  try {
    const bookId = c.req.param('id');
    const user = c.get('user');

    // 获取订单信息
    const book = await c.env.DB.prepare(`
      SELECT 
        id, order_number, status, tracking_number, 
        created_at, updated_at
      FROM physical_books 
      WHERE id = ? AND user_id = ?
    `).bind(bookId, user.id).first();

    if (!book) {
      return c.json<ApiResponse>({
        success: false,
        error: '订单不存在',
        code: ErrorCodes.STORY_NOT_FOUND
      }, 404);
    }

    // 构建跟踪信息
    const trackingInfo = {
      orderNumber: book.order_number,
      status: book.status,
      trackingNumber: book.tracking_number,
      timeline: getOrderTimeline(book.status, book.created_at, book.updated_at)
    };

    return c.json<ApiResponse<any>>({
      success: true,
      data: trackingInfo
    });

  } catch (error) {
    console.error('Get tracking info failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取跟踪信息失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

// ==================== 辅助函数 ====================

/**
 * 生成订单号
 */
function generateOrderNumber(): string {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `SW${timestamp.slice(-8)}${random}`;
}

/**
 * 创建实体书支付意图
 */
async function createBookPaymentIntent(
  amount: number,
  metadata: any,
  secretKey: string
): Promise<any> {
  const response = await fetch('https://api.stripe.com/v1/payment_intents', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      amount: Math.round(amount * 100).toString(), // 转换为分
      currency: 'usd',
      'metadata[bookId]': metadata.bookId,
      'metadata[userId]': metadata.userId,
      'metadata[storyId]': metadata.storyId,
      'metadata[orderNumber]': metadata.orderNumber,
      'metadata[type]': 'physical_book',
    })
  });

  if (!response.ok) {
    throw new Error('Failed to create payment intent');
  }

  return await response.json();
}

/**
 * 取消支付意图
 */
async function cancelStripePaymentIntent(
  paymentIntentId: string,
  secretKey: string
): Promise<void> {
  const response = await fetch(`https://api.stripe.com/v1/payment_intents/${paymentIntentId}/cancel`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
    }
  });

  if (!response.ok) {
    throw new Error('Failed to cancel payment intent');
  }
}

/**
 * 获取支付意图信息
 */
async function getStripePaymentIntent(
  paymentIntentId: string,
  secretKey: string
): Promise<any> {
  const response = await fetch(`https://api.stripe.com/v1/payment_intents/${paymentIntentId}`, {
    headers: {
      'Authorization': `Bearer ${secretKey}`,
    }
  });

  if (!response.ok) {
    throw new Error('Failed to get payment intent');
  }

  return await response.json();
}

/**
 * 获取订单时间线
 */
function getOrderTimeline(status: string, createdAt: string, updatedAt: string) {
  const timeline = [
    {
      status: 'pending',
      title: '订单创建',
      description: '订单已创建，等待支付',
      timestamp: createdAt,
      completed: true
    }
  ];

  if (['printing', 'shipped', 'delivered'].includes(status)) {
    timeline.push({
      status: 'printing',
      title: '开始制作',
      description: '支付成功，开始制作实体书',
      timestamp: updatedAt,
      completed: true
    });
  }

  if (['shipped', 'delivered'].includes(status)) {
    timeline.push({
      status: 'shipped',
      title: '已发货',
      description: '实体书已发货，正在配送中',
      timestamp: updatedAt,
      completed: true
    });
  }

  if (status === 'delivered') {
    timeline.push({
      status: 'delivered',
      title: '已送达',
      description: '实体书已成功送达',
      timestamp: updatedAt,
      completed: true
    });
  }

  return timeline;
}

export default app;