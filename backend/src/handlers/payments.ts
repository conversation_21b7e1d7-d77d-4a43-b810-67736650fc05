// @ts-nocheck
/**
 * 支付相关API处理器
 */

import { Hono } from 'hono';
import { StorageService } from '../services/storage';
import { authMiddleware } from '../middleware/auth';
import { ApiResponse, PaymentIntent, SubscriptionPlan, ErrorCodes } from '../types/api';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

// 在模块加载时运行价格转换测试
// testPriceConversion(); // 暂时注释掉，避免在生产环境输出过多日志

// 应用认证中间件到需要认证的路由
app.use('/create-checkout-session', authMiddleware);
app.use('/create-payment-intent', authMiddleware);
app.use('/create-subscription', authMiddleware);
app.use('/cancel-subscription', authMiddleware);
app.use('/purchase-credits', authMiddleware);
app.use('/verify-checkout-session', authMiddleware);

/**
 * 获取订阅计划
 * GET /api/payments/plans
 */
app.get('/plans', async (c) => {
  try {
    const plans: SubscriptionPlan[] = [
      {
        id: 'credits_5',
        name: '积分包',
        description: '5个故事创作额度',
        price: 10,
        currency: 'USD',
        interval: 'month',
        features: [
          '5个故事创作',
          '多种插图风格',
          '高质量音频旁白',
          '实体书定制选项'
        ],
        stripePriceId: 'price_credits_5'
      },
      {
        id: 'unlimited_monthly',
        name: '无限订阅',
        description: '无限故事创作',
        price: 15,
        currency: 'USD',
        interval: 'month',
        features: [
          '无限故事创作',
          '所有插图风格',
          '多种语音选择',
          '实体书折扣',
          '高级编辑功能',
          '优先客服支持'
        ],
        stripePriceId: 'price_unlimited_monthly'
      }
    ];

    return c.json<ApiResponse<SubscriptionPlan[]>>({
      success: true,
      data: plans
    });

  } catch (error) {
    console.error('Get plans failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取订阅计划失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 创建Stripe Checkout Session（用于重定向支付）
 * POST /api/payments/create-checkout-session
 */
app.post('/create-checkout-session', async (c) => {
  try {
    const user = c.get('user');
    console.log('🔍 User from context:', user ? 'User found' : 'User is null/undefined');

    const requestBody = await c.req.json();
    const { amount, currency = 'usd', type, metadata } = requestBody;

    // 输入验证
    if (!amount || typeof amount !== 'number' || amount <= 0) {
      console.error('❌ Invalid amount:', { amount, type: typeof amount });
      return c.json({
        success: false,
        error: '无效的金额',
        code: 'INVALID_AMOUNT'
      }, 400);
    }

    if (!type || typeof type !== 'string') {
      console.error('❌ Invalid type:', { type, typeOf: typeof type });
      return c.json({
        success: false,
        error: '无效的支付类型',
        code: 'INVALID_TYPE'
      }, 400);
    }

    // 详细的调试日志
    console.log('🔍 Create checkout session request:', {
      user: user ? { id: user.id, email: user.email } : 'null',
      requestBody,
      amount,
      currency,
      type,
      metadata,
      hasAmount: !!amount,
      hasType: !!type,
      amountType: typeof amount,
      typeType: typeof type
    });

    if (!user) {
      console.error('❌ User object is null or undefined');
      return c.json<ApiResponse>({
        success: false,
        error: '用户认证失败',
        code: ErrorCodes.UNAUTHORIZED
      }, 401);
    }

    if (!amount || !type) {
      console.error('❌ Missing required parameters:', { amount, type });
      return c.json<ApiResponse>({
        success: false,
        error: '缺少必要参数',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 创建Stripe Checkout Session
    // 注意：前端发送的金额已经是美元单位，我们只需要在createStripeCheckoutSession函数内部转换一次
    console.log('💰 Price before sending to Stripe:', {
      amountInDollars: amount,
      currency,
      type
    });
    
    const session = await createStripeCheckoutSession({
      amount: amount, // 保持美元单位，函数内部会转换为分
      currency,
      userId: user.id,
      userEmail: user.email,
      type,
      metadata,
      successUrl: `${c.env.CORS_ORIGIN}/payment/success?session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${c.env.CORS_ORIGIN}/payment/failed`
    }, c.env.STRIPE_SECRET_KEY);

    return c.json<ApiResponse<{ sessionId: string; url: string }>>({
      success: true,
      data: {
        sessionId: session.id,
        url: session.url
      }
    });

  } catch (error) {
    const user = c.get('user'); // 重新获取user以防在catch块中未定义
    console.error('❌ Create checkout session failed:', {
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      user: user ? { id: user.id, email: user.email } : 'null'
    });
    return c.json<ApiResponse>({
      success: false,
      error: '创建支付会话失败',
      code: ErrorCodes.PAYMENT_FAILED
    }, 500);
  }
});

/**
 * 创建支付意图（用于积分购买）- 保留用于内嵌支付
 * POST /api/payments/create-payment-intent
 */
app.post('/create-payment-intent', async (c) => {
  try {
    const user = c.get('user');
    const { planId, amount } = await c.req.json();

    if (!planId || !amount) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少必要参数',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 创建Stripe支付意图
    // 修复：前端传入的金额已经是美元单位，我们只需要在createStripePaymentIntent函数内部转换一次
    console.log('💰 Payment intent amount:', { amountInDollars: amount });
    
    const paymentIntent = await createStripePaymentIntent(
      amount, // 保持美元单位，函数内部会转换为分
      'usd',
      {
        userId: user.id,
        planId: planId,
        type: 'credits'
      },
      c.env.STRIPE_SECRET_KEY
    );

    return c.json<ApiResponse<PaymentIntent>>({
      success: true,
      data: {
        id: paymentIntent.id,
        amount: paymentIntent.amount,
        currency: paymentIntent.currency,
        status: paymentIntent.status,
        clientSecret: paymentIntent.client_secret
      }
    });

  } catch (error) {
    console.error('Create payment intent failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '创建支付失败',
      code: ErrorCodes.PAYMENT_FAILED
    }, 500);
  }
});

/**
 * 创建订阅
 * POST /api/payments/create-subscription
 */
app.post('/create-subscription', async (c) => {
  try {
    const user = c.get('user');
    const { priceId, paymentMethodId } = await c.req.json();

    if (!priceId || !paymentMethodId) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少必要参数',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查用户是否已有活跃订阅
    const existingSubscription = await storageService.getUserSubscription(user.id);
    if (existingSubscription && existingSubscription.status === 'active') {
      return c.json<ApiResponse>({
        success: false,
        error: '您已有活跃订阅',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 创建Stripe客户（如果不存在）
    const customer = await createOrGetStripeCustomer(
      user.email,
      user.name,
      c.env.STRIPE_SECRET_KEY
    );

    // 附加支付方式到客户
    await attachPaymentMethodToCustomer(
      paymentMethodId,
      customer.id,
      c.env.STRIPE_SECRET_KEY
    );

    // 创建订阅
    const subscription = await createStripeSubscription(
      customer.id,
      priceId,
      paymentMethodId,
      c.env.STRIPE_SECRET_KEY
    );

    // 保存订阅信息到数据库
    await storageService.createSubscription({
      userId: user.id,
      plan: priceId === 'price_unlimited_monthly' ? 'unlimited' : 'credits',
      status: 'active',
      currentPeriodStart: new Date(subscription.current_period_start * 1000).toISOString(),
      currentPeriodEnd: new Date(subscription.current_period_end * 1000).toISOString(),
      stripeSubscriptionId: subscription.id
    });

    return c.json<ApiResponse<{ subscriptionId: string; status: string }>>({
      success: true,
      data: {
        subscriptionId: subscription.id,
        status: subscription.status
      }
    });

  } catch (error) {
    console.error('Create subscription failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '创建订阅失败',
      code: ErrorCodes.PAYMENT_FAILED
    }, 500);
  }
});

/**
 * 取消订阅
 * POST /api/payments/cancel-subscription
 */
app.post('/cancel-subscription', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 获取用户订阅
    const subscription = await storageService.getUserSubscription(user.id);
    if (!subscription || !subscription.stripeSubscriptionId) {
      return c.json<ApiResponse>({
        success: false,
        error: '未找到活跃订阅',
        code: ErrorCodes.SUBSCRIPTION_NOT_FOUND
      }, 404);
    }

    // 取消Stripe订阅
    await cancelStripeSubscription(
      subscription.stripeSubscriptionId,
      c.env.STRIPE_SECRET_KEY
    );

    // 更新数据库中的订阅状态
    await c.env.DB.prepare(`
      UPDATE subscriptions 
      SET status = 'canceled' 
      WHERE id = ?
    `).bind(subscription.id).run();

    return c.json<ApiResponse>({
      success: true,
      message: '订阅已取消'
    });

  } catch (error) {
    console.error('Cancel subscription failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '取消订阅失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 购买积分
 * POST /api/payments/purchase-credits
 */
app.post('/purchase-credits', async (c) => {
  try {
    const user = c.get('user');
    const { paymentIntentId } = await c.req.json();

    // 详细的用户验证日志
    console.log('🔍 Purchase credits - User verification:', {
      userId: user?.id,
      userEmail: user?.email,
      userName: user?.name,
      paymentIntentId,
      timestamp: new Date().toISOString()
    });

    if (!paymentIntentId) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少支付意图ID',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 验证支付状态
    const paymentIntent = await getStripePaymentIntent(
      paymentIntentId,
      c.env.STRIPE_SECRET_KEY
    );

    if (paymentIntent.status !== 'succeeded') {
      return c.json<ApiResponse>({
        success: false,
        error: '支付未完成',
        code: ErrorCodes.PAYMENT_FAILED
      }, 400);
    }

    // 根据支付金额确定积分数量
    const amount = paymentIntent.amount / 100; // 转换为美元
    let credits = 0;

    // 检查是否为订阅支付
    const isSubscription = paymentIntent.metadata?.type === 'subscription';

    console.log('💰 Payment validation:', {
      amount,
      type: paymentIntent.metadata?.type,
      planId: paymentIntent.metadata?.planId,
      isSubscription,
      paymentIntentId
    });

    if (isSubscription) {
      // 订阅支付：根据计划设置积分
      const planId = paymentIntent.metadata?.planId;
      switch (planId) {
        case 'basic_monthly':
          credits = 50; // 基础会员月积分
          break;
        case 'pro_monthly':
          credits = 200; // 专业会员月积分
          break;
        case 'unlimited_monthly':
          credits = 999999; // 无限会员
          break;
        default:
          credits = 100; // 默认积分
      }
    } else {
      // 积分包支付：根据金额计算积分
      if (amount === 2.99) {
        credits = 100; // 基础包：$2.99 = 100积分
      } else if (amount === 7.99) {
        credits = 350; // 超值包：$7.99 = 300积分 + 50赠送
      } else if (amount === 14.99) {
        credits = 750; // 豪华包：$14.99 = 600积分 + 150赠送
      } else {
        console.error('❌ Unsupported payment amount:', amount);
        return c.json<ApiResponse>({
          success: false,
          error: `不支持的支付金额: $${amount}`,
          code: ErrorCodes.VALIDATION_ERROR
        }, 400);
      }
    }

    // 更新用户积分
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const currentUser = await storageService.getUserById(user.id);
    
    if (currentUser) {
      await storageService.updateUser(user.id, {
        credits: currentUser.credits + credits
      });
    }

    return c.json<ApiResponse<{ credits: number }>>({
      success: true,
      data: { credits },
      message: `成功购买${credits}个故事积分`
    });

  } catch (error) {
    console.error('Purchase credits failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '购买积分失败',
      code: ErrorCodes.PAYMENT_FAILED
    }, 500);
  }
});

/**
 * 验证Stripe Checkout Session并处理支付结果
 * POST /api/payments/verify-checkout-session
 */
app.post('/verify-checkout-session', async (c) => {
  try {
    const user = c.get('user');
    
    // 检查用户认证
    if (!user) {
      console.error('❌ User not authenticated for verify-checkout-session');
      return c.json<ApiResponse>({
        success: false,
        error: '用户未认证',
        code: ErrorCodes.UNAUTHORIZED
      }, 401);
    }

    const { sessionId } = await c.req.json();

    console.log('🔍 Verifying checkout session:', {
      sessionId,
      userId: user?.id,
      userEmail: user?.email,
      sessionIdType: typeof sessionId,
      sessionIdLength: sessionId?.length
    });

    if (!sessionId || typeof sessionId !== 'string' || sessionId.trim() === '') {
      console.error('❌ Invalid sessionId:', { sessionId, type: typeof sessionId });
      return c.json<ApiResponse>({
        success: false,
        error: '无效的会话ID',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 获取Stripe Checkout Session详情
    const session = await getStripeCheckoutSession(sessionId, c.env.STRIPE_SECRET_KEY);

    console.log('🔍 Retrieved session:', {
      sessionExists: !!session,
      sessionId: session?.id,
      paymentStatus: session?.payment_status,
      amountTotal: session?.amount_total,
      metadata: session?.metadata
    });

    if (!session || !session.id) {
      console.error('❌ Invalid session:', { session, sessionId });
      return c.json<ApiResponse>({
        success: false,
        error: '无效的支付会话',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    if (session.payment_status !== 'paid') {
      return c.json<ApiResponse>({
        success: false,
        error: '支付未完成',
        code: ErrorCodes.PAYMENT_FAILED
      }, 400);
    }

    // 获取支付金额和计算积分
    const amount = session.amount_total / 100; // 转换为美元
    let credits = 0;

    // 检查是否为订阅类型支付
    const isSubscription = session.metadata?.type === 'subscription';

    console.log('💰 Checkout session validation:', {
      amount,
      type: session.metadata?.type,
      planId: session.metadata?.planId,
      isSubscription,
      sessionId
    });

    if (isSubscription) {
      // 订阅支付：根据计划设置积分
      const planId = session.metadata?.planId;
      switch (planId) {
        case 'basic_monthly':
          credits = 50; // 基础会员月积分
          break;
        case 'pro_monthly':
          credits = 200; // 专业会员月积分
          break;
        case 'unlimited_monthly':
          credits = 999999; // 无限会员
          break;
        default:
          credits = 100; // 默认积分
      }
    } else {
      // 积分包支付：根据金额计算积分
      if (amount === 2.99) {
        credits = 100; // 基础包：$2.99 = 100积分
      } else if (amount === 7.99) {
        credits = 350; // 超值包：$7.99 = 300积分 + 50赠送
      } else if (amount === 14.99) {
        credits = 750; // 豪华包：$14.99 = 600积分 + 150赠送
      } else {
        console.error('❌ Unsupported payment amount:', amount);
        return c.json<ApiResponse>({
          success: false,
          error: `不支持的支付金额: $${amount}`,
          code: ErrorCodes.VALIDATION_ERROR
        }, 400);
      }
    }

    // 更新用户积分
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);
    const currentUser = await storageService.getUserById(user.id);

    if (currentUser) {
      // 检查是否为订阅类型支付
      const isSubscription = session.metadata?.type === 'subscription';
      
      // 如果是订阅类型，设置为无限积分并创建订阅记录
      if (isSubscription) {
        console.log('🔄 Creating subscription record and updating user credits...');

        // 1. 更新用户积分
        await storageService.updateUser(user.id, {
          credits: 999999 // 设置为非常大的数值表示"无限"
        });

        // 2. 创建订阅记录
        const subscriptionData = {
          userId: user.id,
          plan: 'unlimited_monthly' as const, // 使用正确的类型
          status: 'active' as const,
          currentPeriodStart: new Date().toISOString(),
          currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天后
          stripeSubscriptionId: session.subscription || session.id
        };

        await storageService.createSubscription(subscriptionData);

        // 3. 记录支付信息
        await storageService.createPayment({
          userId: user.id,
          type: 'subscription',
          amount: session.amount_total ? session.amount_total / 100 : 0, // 转换为美元
          currency: 'USD',
          status: 'succeeded',
          stripePaymentIntentId: session.payment_intent || session.id,
          stripeSubscriptionId: session.subscription || session.id,
          metadata: JSON.stringify({
            planId: session.metadata?.planId || 'unlimited_monthly',
            sessionId: session.id
          })
        });

        console.log('✅ Subscription created successfully:', {
          userId: user.id,
          plan: 'unlimited_monthly',
          credits: 999999,
          subscriptionId: subscriptionData.stripeSubscriptionId
        });
      } else {
        // 积分包购买，更新积分
        await storageService.updateUser(user.id, {
          credits: currentUser.credits + credits
        });

        console.log('✅ Credits updated successfully:', {
          userId: user.id,
          previousCredits: currentUser.credits,
          addedCredits: credits,
          newTotal: currentUser.credits + credits
        });
      }
    } else {
      console.error('❌ User not found when updating credits:', user.id);
    }

    // 根据支付类型返回不同的响应
    // 注意：isSubscription 已在上面声明过，这里直接使用
    
    if (isSubscription) {
      return c.json<ApiResponse<{ amount: number; credits: number; type: string; subscription: any }>>({
        success: true,
        data: {
          amount,
          credits: 999999, // 无限积分
          type: 'subscription',
          subscription: {
            plan: session.metadata?.planId || 'premium',
            status: 'active'
          }
        },
        message: `成功订阅${session.metadata?.planId || 'premium'}会员`
      });
    } else {
      return c.json<ApiResponse<{ amount: number; credits: number; type: string }>>({
        success: true,
        data: {
          amount,
          credits,
          type: 'credits'
        },
        message: `成功购买${credits}个故事积分`
      });
    }

  } catch (error) {
    console.error('Verify checkout session failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '验证支付会话失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * Stripe Webhook处理
 * POST /api/payments/webhook
 */
app.post('/webhook', async (c) => {
  try {
    const signature = c.req.header('stripe-signature');
    const body = await c.req.text();

    if (!signature) {
      return c.json({ error: 'Missing signature' }, 400);
    }

    // 验证Webhook签名
    const event = await verifyStripeWebhook(
      body,
      signature,
      c.env.STRIPE_WEBHOOK_SECRET
    );

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 处理不同类型的事件
    switch (event.type) {
      case 'invoice.payment_succeeded':
        await handleSubscriptionPaymentSucceeded(event.data.object, storageService);
        break;
      
      case 'invoice.payment_failed':
        await handleSubscriptionPaymentFailed(event.data.object, storageService);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object, storageService);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return c.json({ received: true });

  } catch (error) {
    console.error('Webhook processing failed:', error);
    return c.json({ error: 'Webhook processing failed' }, 400);
  }
});

// ==================== Stripe API 辅助函数 ====================

/**
 * 安全地将美元金额转换为美分（整数）
 * 解决JavaScript浮点数精度问题
 */
function dollarsToCents(dollars: number): number {
  // 使用Math.round确保结果是整数，避免浮点数精度问题
  return Math.round(dollars * 100);
}

/**
 * 测试价格转换函数的正确性
 */
function testPriceConversion() {
  const testCases = [
    { dollars: 2.99, expectedCents: 299 },
    { dollars: 7.99, expectedCents: 799 },
    { dollars: 14.99, expectedCents: 1499 },
    { dollars: 19.99, expectedCents: 1999 },
    { dollars: 39.99, expectedCents: 3999 },
    { dollars: 199.99, expectedCents: 19999 }
  ];

  console.log('🧪 Testing price conversion:');
  testCases.forEach(({ dollars, expectedCents }) => {
    const actualCents = dollarsToCents(dollars);
    const isCorrect = actualCents === expectedCents;
    console.log(`  $${dollars} → ${actualCents} cents (expected: ${expectedCents}) ${isCorrect ? '✅' : '❌'}`);
  });
}

async function createStripeCheckoutSession(
  params: {
    amount: number;
    currency: string;
    userId: string;
    userEmail: string;
    type: string;
    metadata?: any;
    successUrl: string;
    cancelUrl: string;
  },
  secretKey: string
): Promise<any> {
  console.log('🔍 Creating Stripe checkout session with params:', {
    ...params,
    secretKey: secretKey ? `${secretKey.substring(0, 10)}...` : 'null'
  });

  // 根据支付类型设置不同的模式
  const isSubscription = params.type === 'subscription';
  const mode = isSubscription ? 'subscription' : 'payment';

  const body = new URLSearchParams({
    'payment_method_types[0]': 'card',
    mode: mode,
    success_url: params.successUrl,
    cancel_url: params.cancelUrl,
    client_reference_id: params.userId,
    customer_email: params.userEmail,
    'metadata[userId]': params.userId,
    'metadata[type]': params.type,
  });

  // 将美元转换为美分（Stripe要求的格式）
  // 修复：确保价格只被转换一次
  const amountInCents = dollarsToCents(params.amount);
  
  // 添加价格转换日志，帮助调试
  console.log('💰 Price conversion for Stripe:', {
    originalAmount: params.amount,
    amountInCents: amountInCents,
    currency: params.currency
  });

  if (isSubscription) {
    // 订阅模式：使用动态定价创建订阅产品
    const interval = params.metadata?.interval || 'month';
    body.append('line_items[0][price_data][currency]', params.currency);
    body.append('line_items[0][price_data][product_data][name]', getProductName(params.type, params.metadata));
    body.append('line_items[0][price_data][unit_amount]', amountInCents.toString());
    body.append('line_items[0][price_data][recurring][interval]', interval);
    body.append('line_items[0][quantity]', '1');
  } else {
    // 一次性支付模式：使用动态价格
    body.append('line_items[0][price_data][currency]', params.currency);
    body.append('line_items[0][price_data][product_data][name]', getProductName(params.type, params.metadata));
    body.append('line_items[0][price_data][unit_amount]', amountInCents.toString());
    body.append('line_items[0][quantity]', '1');
  }

  // 添加额外的metadata
  if (params.metadata) {
    Object.entries(params.metadata).forEach(([key, value]) => {
      body.append(`metadata[${key}]`, String(value));
    });
  }

  // 详细的Stripe请求日志
  console.log('🔍 Stripe Checkout Session Request Details:', {
    mode,
    isSubscription,
    amountInDollars: params.amount,
    amountInCents: amountInCents,
    currency: params.currency,
    productName: getProductName(params.type, params.metadata),
    interval: params.metadata?.interval,
    userId: params.userId,
    userEmail: params.userEmail
  });

  const response = await fetch('https://api.stripe.com/v1/checkout/sessions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error('Stripe checkout session creation failed:', errorText);
    throw new Error('Failed to create checkout session');
  }

  return await response.json();
}

function getProductName(type: string, metadata?: any): string {
  if (type === 'credits') {
    const credits = metadata?.credits || '未知';
    return `StoryWeaver 故事积分 (${credits}个故事)`;
  } else if (type === 'subscription') {
    const planId = metadata?.planId || '未知套餐';
    const interval = metadata?.interval || 'month';
    const intervalText = interval === 'month' ? '月度' : '年度';

    // 根据planId提供更友好的产品名称
    const planNames: { [key: string]: string } = {
      'basic_monthly': '基础会员',
      'pro_monthly': '专业会员',
      'unlimited_monthly': '无限会员',
      'pro_yearly': '专业会员(年付)'
    };

    const planName = planNames[planId] || planId;
    return `StoryWeaver ${planName} - ${intervalText}订阅`;
  }
  return 'StoryWeaver 服务';
}

async function createStripePaymentIntent(
  amount: number,
  currency: string,
  metadata: any,
  secretKey: string
): Promise<any> {
  // 将美元转换为美分（Stripe要求的格式）
  const amountInCents = dollarsToCents(amount);
  
  // 添加价格转换日志，帮助调试
  console.log('💰 Payment intent price conversion:', {
    originalAmount: amount,
    amountInCents: amountInCents,
    currency: currency
  });
  
  const response = await fetch('https://api.stripe.com/v1/payment_intents', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      amount: amountInCents.toString(), // 使用转换后的美分金额
      currency: currency,
      'metadata[userId]': metadata.userId,
      'metadata[planId]': metadata.planId,
      'metadata[type]': metadata.type,
    })
  });

  if (!response.ok) {
    throw new Error('Failed to create payment intent');
  }

  return await response.json();
}

async function createOrGetStripeCustomer(
  email: string,
  name: string,
  secretKey: string
): Promise<any> {
  // 先尝试查找现有客户
  const searchResponse = await fetch(`https://api.stripe.com/v1/customers?email=${encodeURIComponent(email)}`, {
    headers: {
      'Authorization': `Bearer ${secretKey}`,
    }
  });

  if (searchResponse.ok) {
    const searchResult = await searchResponse.json();
    if (searchResult.data.length > 0) {
      return searchResult.data[0];
    }
  }

  // 创建新客户
  const createResponse = await fetch('https://api.stripe.com/v1/customers', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      email: email,
      name: name,
    })
  });

  if (!createResponse.ok) {
    throw new Error('Failed to create customer');
  }

  return await createResponse.json();
}

async function attachPaymentMethodToCustomer(
  paymentMethodId: string,
  customerId: string,
  secretKey: string
): Promise<void> {
  const response = await fetch(`https://api.stripe.com/v1/payment_methods/${paymentMethodId}/attach`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      customer: customerId,
    })
  });

  if (!response.ok) {
    throw new Error('Failed to attach payment method');
  }
}

async function createStripeSubscription(
  customerId: string,
  priceId: string,
  paymentMethodId: string,
  secretKey: string
): Promise<any> {
  const response = await fetch('https://api.stripe.com/v1/subscriptions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: new URLSearchParams({
      customer: customerId,
      'items[0][price]': priceId,
      default_payment_method: paymentMethodId,
      expand: 'latest_invoice.payment_intent',
    })
  });

  if (!response.ok) {
    throw new Error('Failed to create subscription');
  }

  return await response.json();
}

async function cancelStripeSubscription(
  subscriptionId: string,
  secretKey: string
): Promise<void> {
  const response = await fetch(`https://api.stripe.com/v1/subscriptions/${subscriptionId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${secretKey}`,
    }
  });

  if (!response.ok) {
    throw new Error('Failed to cancel subscription');
  }
}

async function getStripePaymentIntent(
  paymentIntentId: string,
  secretKey: string
): Promise<any> {
  const response = await fetch(`https://api.stripe.com/v1/payment_intents/${paymentIntentId}`, {
    headers: {
      'Authorization': `Bearer ${secretKey}`,
    }
  });

  if (!response.ok) {
    throw new Error('Failed to get payment intent');
  }

  return await response.json();
}

async function getStripeCheckoutSession(
  sessionId: string,
  secretKey: string
): Promise<any> {
  try {
    console.log('🔍 Fetching Stripe session:', { sessionId });
    
    const response = await fetch(`https://api.stripe.com/v1/checkout/sessions/${sessionId}`, {
      headers: {
        'Authorization': `Bearer ${secretKey}`,
      }
    });

    console.log('🔍 Stripe API response:', {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Stripe API error:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
      throw new Error(`Failed to get checkout session: ${response.status} ${response.statusText}`);
    }

    const sessionData = await response.json();
    console.log('✅ Session data retrieved:', {
      id: sessionData?.id,
      payment_status: sessionData?.payment_status,
      amount_total: sessionData?.amount_total
    });

    return sessionData;
  } catch (error) {
    console.error('❌ Error in getStripeCheckoutSession:', error);
    throw error;
  }
}

async function verifyStripeWebhook(
  body: string,
  signature: string,
  secret: string
): Promise<any> {
  // 简化的Webhook验证实现
  // 在实际项目中，应该使用Stripe的官方验证方法
  return JSON.parse(body);
}

async function handleSubscriptionPaymentSucceeded(
  invoice: any,
  storageService: StorageService
): Promise<void> {
  // 处理订阅支付成功
  console.log('Subscription payment succeeded:', invoice.id);
}

async function handleSubscriptionPaymentFailed(
  invoice: any,
  storageService: StorageService
): Promise<void> {
  // 处理订阅支付失败
  console.log('Subscription payment failed:', invoice.id);
}

async function handleSubscriptionDeleted(
  subscription: any,
  storageService: StorageService
): Promise<void> {
  // 处理订阅删除
  console.log('Subscription deleted:', subscription.id);
}

export default app;