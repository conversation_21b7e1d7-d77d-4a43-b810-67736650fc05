// @ts-nocheck
/**
 * 用户相关API处理器
 */

import { Hono } from 'hono';
import { StorageService } from '../services/storage';
import { authMiddleware } from '../middleware/auth';
import { validateUsername, validatePagination } from '../utils/validation';
import { ApiResponse, PaginatedResponse, User, ErrorCodes } from '../types/api';
import type { Env, Variables, AuthenticatedContext } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

// 应用认证中间件到所有路由
app.use('*', authMiddleware);

/**
 * 获取当前用户信息
 * GET /api/users/me
 */
app.get('/me', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    const userInfo = await storageService.getUserById(user.id);
    
    if (!userInfo) {
      return c.json<ApiResponse>({
        success: false,
        error: '用户不存在',
        code: ErrorCodes.USER_NOT_FOUND
      }, 404);
    }

    // 获取用户订阅信息
    const subscription = await storageService.getUserSubscription(user.id);

    return c.json<ApiResponse<User>>({
      success: true,
      data: {
        ...userInfo,
        subscription
      }
    });

  } catch (error) {
    console.error('Get user info failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取用户信息失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 更新用户信息
 * PUT /api/users/me
 */
app.put('/me', async (c) => {
  try {
    const user = c.get('user');
    const updates = await c.req.json();
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 验证用户名
    if (updates.name) {
      const nameValidation = validateUsername(updates.name);
      if (!nameValidation.valid) {
        return c.json<ApiResponse>({
          success: false,
          error: nameValidation.error,
          code: ErrorCodes.VALIDATION_ERROR
        }, 400);
      }
    }

    // 只允许更新特定字段
    const allowedUpdates: Partial<User> = {};
    if (updates.name) allowedUpdates.name = updates.name;
    if (updates.avatar) allowedUpdates.avatar = updates.avatar;

    await storageService.updateUser(user.id, allowedUpdates);

    // 获取更新后的用户信息
    const updatedUser = await storageService.getUserById(user.id);

    return c.json<ApiResponse<User>>({
      success: true,
      data: updatedUser!,
      message: '用户信息更新成功'
    });

  } catch (error) {
    console.error('Update user failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '更新用户信息失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取用户统计信息
 * GET /api/users/stats
 */
app.get('/stats', async (c) => {
  try {
    const user = c.get('user');

    // 从数据库获取用户统计信息
    const stats = await c.env.DB.prepare(`
      SELECT 
        COUNT(s.id) as total_stories,
        COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_stories,
        COUNT(CASE WHEN s.status = 'generating' THEN 1 END) as generating_stories,
        COUNT(CASE WHEN s.status = 'failed' THEN 1 END) as failed_stories,
        COUNT(pb.id) as total_books_ordered,
        COUNT(CASE WHEN pb.status = 'delivered' THEN 1 END) as delivered_books,
        u.credits,
        u.created_at as join_date
      FROM users u
      LEFT JOIN stories s ON u.id = s.user_id
      LEFT JOIN physical_books pb ON u.id = pb.user_id
      WHERE u.id = ?
      GROUP BY u.id, u.credits, u.created_at
    `).bind(user.id).first();

    if (!stats) {
      return c.json<ApiResponse>({
        success: false,
        error: '用户不存在',
        code: ErrorCodes.USER_NOT_FOUND
      }, 404);
    }

    // 获取最近的故事
    const recentStories = await c.env.DB.prepare(`
      SELECT id, title, character_name, status, created_at
      FROM stories 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT 5
    `).bind(user.id).all();

    return c.json<ApiResponse<any>>({
      success: true,
      data: {
        totalStories: stats.total_stories || 0,
        completedStories: stats.completed_stories || 0,
        generatingStories: stats.generating_stories || 0,
        failedStories: stats.failed_stories || 0,
        totalBooksOrdered: stats.total_books_ordered || 0,
        deliveredBooks: stats.delivered_books || 0,
        credits: stats.credits || 0,
        joinDate: stats.join_date,
        recentStories: recentStories.results || []
      }
    });

  } catch (error) {
    console.error('Get user stats failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取用户统计失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取用户活动日志
 * GET /api/users/activities
 */
app.get('/activities', async (c) => {
  try {
    const user = c.get('user');
    const { page, limit, valid, error } = validatePagination(
      c.req.query('page'),
      c.req.query('limit')
    );

    if (!valid) {
      return c.json<ApiResponse>({
        success: false,
        error: error,
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const offset = (page - 1) * limit;

    // 获取活动日志
    const activities = await c.env.DB.prepare(`
      SELECT 
        activity_type,
        activity_data,
        created_at
      FROM user_activities 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `).bind(user.id, limit, offset).all();

    // 获取总数
    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM user_activities WHERE user_id = ?
    `).bind(user.id).first();

    const total = countResult?.count || 0;

    return c.json<ApiResponse<any[]>>({
      success: true,
      data: activities.results.map((activity: any) => ({
        type: activity.activity_type,
        data: activity.activity_data ? JSON.parse(activity.activity_data) : null,
        createdAt: activity.created_at
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get user activities failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取活动日志失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 删除用户账户
 * DELETE /api/users/me
 */
app.delete('/me', async (c) => {
  try {
    const user = c.get('user');
    const { confirmPassword } = await c.req.json();

    // 在实际应用中，这里应该验证用户密码
    // 由于我们使用Google OAuth，可以要求用户重新认证

    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 获取用户的所有故事，删除相关文件
    const userStories = await c.env.DB.prepare(`
      SELECT id, audio_url, pages FROM stories WHERE user_id = ?
    `).bind(user.id).all();

    // 删除用户的所有文件
    for (const story of userStories.results) {
      try {
        // 删除音频文件
        if (story.audio_url) {
          const audioKey = story.audio_url.split('/').pop();
          if (audioKey) {
            await storageService.deleteFile(`audio/${audioKey}`);
          }
        }

        // 删除图片文件
        const pages = JSON.parse(story.pages || '[]');
        for (const page of pages) {
          if (page.imageUrl) {
            const imageKey = page.imageUrl.split('/').pop();
            if (imageKey) {
              await storageService.deleteFile(`images/${imageKey}`);
            }
          }
        }
      } catch (error) {
        console.error(`Failed to delete files for story ${story.id}:`, error);
      }
    }

    // 删除用户数据（级联删除会自动删除相关记录）
    await c.env.DB.prepare('DELETE FROM users WHERE id = ?').bind(user.id).run();

    // 删除用户会话
    await storageService.deleteUserSession(user.id);

    return c.json<ApiResponse>({
      success: true,
      message: '账户删除成功'
    });

  } catch (error) {
    console.error('Delete user account failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '删除账户失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 提交用户反馈
 * POST /api/users/feedback
 */
app.post('/feedback', async (c) => {
  try {
    const user = c.get('user');
    const { type, subject, content } = await c.req.json();

    // 验证输入
    if (!type || !subject || !content) {
      return c.json<ApiResponse>({
        success: false,
        error: '缺少必要字段',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const validTypes = ['bug_report', 'feature_request', 'general_feedback'];
    if (!validTypes.includes(type)) {
      return c.json<ApiResponse>({
        success: false,
        error: '无效的反馈类型',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    if (subject.length > 100 || content.length > 1000) {
      return c.json<ApiResponse>({
        success: false,
        error: '标题或内容过长',
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    // 保存反馈
    const feedbackId = crypto.randomUUID();
    await c.env.DB.prepare(`
      INSERT INTO user_feedbacks (
        id, user_id, type, subject, content, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      feedbackId,
      user.id,
      type,
      subject,
      content,
      new Date().toISOString(),
      new Date().toISOString()
    ).run();

    // 记录用户活动
    await c.env.DB.prepare(`
      INSERT INTO user_activities (
        id, user_id, activity_type, activity_data, created_at
      ) VALUES (?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      user.id,
      'feedback_submitted',
      JSON.stringify({ feedbackId, type, subject }),
      new Date().toISOString()
    ).run();

    return c.json<ApiResponse<{ feedbackId: string }>>({
      success: true,
      data: { feedbackId },
      message: '反馈提交成功，我们会尽快处理'
    });

  } catch (error) {
    console.error('Submit feedback failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '提交反馈失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 获取用户反馈列表
 * GET /api/users/feedback
 */
app.get('/feedback', async (c) => {
  try {
    const user = c.get('user');
    const { page, limit, valid, error } = validatePagination(
      c.req.query('page'),
      c.req.query('limit')
    );

    if (!valid) {
      return c.json<ApiResponse>({
        success: false,
        error: error,
        code: ErrorCodes.VALIDATION_ERROR
      }, 400);
    }

    const offset = (page - 1) * limit;

    // 获取反馈列表
    const feedbacks = await c.env.DB.prepare(`
      SELECT 
        id, type, subject, content, status, created_at, updated_at
      FROM user_feedbacks 
      WHERE user_id = ? 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `).bind(user.id, limit, offset).all();

    // 获取总数
    const countResult = await c.env.DB.prepare(`
      SELECT COUNT(*) as count FROM user_feedbacks WHERE user_id = ?
    `).bind(user.id).first();

    const total = countResult?.count || 0;

    return c.json<ApiResponse<any[]>>({
      success: true,
      data: feedbacks.results,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get user feedback failed:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '获取反馈列表失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

/**
 * 导出用户数据
 * POST /api/users/export
 */
app.post('/export', async (c) => {
  try {
    const user = c.get('user');
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 获取用户基本信息
    const userInfo = await c.env.DB.prepare(`
      SELECT id, email, name, avatar, credits, created_at, updated_at
      FROM users WHERE id = ?
    `).bind(user.id).first();

    if (!userInfo) {
      return c.json<ApiResponse>({
        success: false,
        error: '用户不存在',
        code: ErrorCodes.USER_NOT_FOUND
      }, 404);
    }

    // 获取用户故事数据
    const stories = await c.env.DB.prepare(`
      SELECT id, title, character_name, character_age, character_traits,
             theme, setting, style, voice, pages, status, created_at, updated_at
      FROM stories WHERE user_id = ?
      ORDER BY created_at DESC
    `).bind(user.id).all();

    // 获取用户订阅信息
    const subscription = await storageService.getUserSubscription(user.id);

    // 获取用户偏好设置
    const preferences = await storageService.getUserPreferences(user.id);

    // 获取使用统计
    const currentMonth = new Date().toISOString().slice(0, 7);
    const usageStats = await storageService.getUserUsageStats(user.id, currentMonth);

    // 构建导出数据
    const exportData = {
      exportInfo: {
        exportedAt: new Date().toISOString(),
        exportVersion: '1.0',
        userId: user.id
      },
      userInfo: {
        id: userInfo.id,
        email: userInfo.email,
        name: userInfo.name,
        avatar: userInfo.avatar,
        credits: userInfo.credits,
        createdAt: userInfo.created_at,
        updatedAt: userInfo.updated_at
      },
      subscription: subscription ? {
        plan: subscription.plan,
        status: subscription.status,
        currentPeriodStart: subscription.currentPeriodStart,
        currentPeriodEnd: subscription.currentPeriodEnd
      } : null,
      preferences: preferences || null,
      usageStats: usageStats || null,
      stories: stories.results?.map((story: any) => ({
        id: story.id,
        title: story.title,
        characterName: story.character_name,
        characterAge: story.character_age,
        characterTraits: story.character_traits ? JSON.parse(story.character_traits) : null,
        theme: story.theme,
        setting: story.setting,
        style: story.style,
        voice: story.voice,
        pages: story.pages ? JSON.parse(story.pages) : [],
        status: story.status,
        createdAt: story.created_at,
        updatedAt: story.updated_at
      })) || [],
      summary: {
        totalStories: stories.results?.length || 0,
        completedStories: stories.results?.filter((s: any) => s.status === 'completed').length || 0,
        currentCredits: userInfo.credits,
        memberSince: userInfo.created_at
      }
    };

    return c.json<ApiResponse<any>>({
      success: true,
      data: exportData,
      message: '数据导出成功'
    });

  } catch (error) {
    console.error('导出用户数据失败:', error);
    return c.json<ApiResponse>({
      success: false,
      error: '导出用户数据失败',
      code: ErrorCodes.INTERNAL_ERROR
    }, 500);
  }
});

export default app;