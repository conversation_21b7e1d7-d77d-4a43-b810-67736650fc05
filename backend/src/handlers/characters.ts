// @ts-nocheck
import { Hono } from 'hono';
import { GeminiService } from '../services/gemini';
import { StorageService } from '../services/storage';
import type { Env } from '../types/hono';

const app = new Hono<{ Bindings: Env }>();

/**
 * 生成角色形象
 * POST /api/characters/generate
 */
app.post('/generate', async (c) => {
  try {
    const body = await c.req.json();
    const { characterName, characterAge, characterTraits, style, gender = 'neutral', userId } = body;

    // 验证输入参数
    if (!characterName || !characterAge || !characterTraits) {
      return c.json({
        success: false,
        error: '缺少必要参数'
      }, 400);
    }

    if (!Array.isArray(characterTraits) || characterTraits.length === 0) {
      return c.json({
        success: false,
        error: '角色特征不能为空'
      }, 400);
    }

    console.log(`🎭 [角色生成] 开始生成角色形象: ${characterName}, ${characterAge}岁, 性别: ${gender}, 风格: anime`);

    // 初始化服务
    const geminiService = new GeminiService(c.env.GEMINI_API_KEY);
    const storageService = new StorageService(c.env.CACHE, c.env.ASSETS, c.env.DB);

    // 检查用户积分（如果提供了userId）
    if (userId) {
      const user = await storageService.getUserById(userId);
      if (!user) {
        return c.json({
          success: false,
          error: '用户不存在'
        }, 404);
      }

      if (user.credits < 1) {
        return c.json({
          success: false,
          error: '积分不足，生成角色形象需要1积分'
        }, 400);
      }
    }

    // 生成角色形象（固定使用动漫风格）
    const characterImageUrl = await generateCharacterImage(
      geminiService,
      storageService,
      {
        characterName,
        characterAge,
        characterTraits,
        style: 'anime', // 固定使用动漫风格
        gender
      }
    );

    console.log(`✅ [角色生成] 角色形象生成成功: ${characterImageUrl}`);

    // 扣除用户积分（如果提供了userId，无限订阅用户除外）
    if (userId) {
      const user = await storageService.getUserById(userId);
      if (user) {
        // 检查用户是否有无限订阅
        const userSubscription = await storageService.getUserSubscription(userId);
        const hasUnlimitedSubscription = userSubscription &&
          (userSubscription.plan === 'unlimited_monthly' || userSubscription.plan === 'unlimited') &&
          userSubscription.status === 'active';

        if (hasUnlimitedSubscription) {
          console.log(`💰 [角色生成] 无限订阅用户，跳过积分扣除`);
        } else {
          const newCredits = Math.max(0, user.credits - 1);
          await storageService.updateUser(userId, { credits: newCredits });
          console.log(`💰 [角色生成] 积分扣除: 1, 剩余: ${newCredits}`);
        }
      }
    }

    return c.json({
      success: true,
      data: {
        imageUrl: characterImageUrl,
        characterName,
        characterAge,
        characterTraits,
        style: 'anime', // 固定返回动漫风格
        gender
      }
    });

  } catch (error) {
    console.error('❌ [角色生成] 角色形象生成失败:', error);
    return c.json({
      success: false,
      error: error instanceof Error ? error.message : '角色形象生成失败'
    }, 500);
  }
});

/**
 * 角色形象生成核心逻辑
 */
async function generateCharacterImage(
  geminiService: GeminiService,
  storageService: StorageService,
  params: {
    characterName: string;
    characterAge: number;
    characterTraits: string[];
    style: string;
    gender: string;
  }
): Promise<string> {
  const { characterName, characterAge, characterTraits, style, gender } = params;

  // 构建角色描述
  const traitsDescription = characterTraits.join('、');

  // 性别描述映射
  const genderDescriptions = {
    male: '男孩',
    female: '女孩',
    neutral: '儿童'
  };

  const genderDescription = genderDescriptions[gender as keyof typeof genderDescriptions] || genderDescriptions.neutral;

  // 增强的动漫风格描述
  const styleDescription = "High-quality Japanese anime style, exquisite and delicate artwork, large and expressive eyes, soft facial lines, vibrant and saturated colors, cute and fresh overall atmosphere, similar to the visual style of Miyazaki or Shinkai works";

  // 将style转换为StoryStyle类型
  const storyStyle = style as 'cartoon' | 'watercolor' | 'sketch' | 'fantasy' | 'realistic' | 'anime';

  // 构建角色生成提示词
  const characterPrompt = `
You are a professional character designer creating a protagonist for a children's picture book.

**Character Information:**
- Name: ${characterName}
- Age: ${characterAge} years old
- Gender: ${genderDescription}
- Personality traits: ${traitsDescription}

**Generation Requirements:**
1. **Character Design**: Create an adorable ${characterAge}-year-old ${genderDescription} character that embodies the personality traits: ${traitsDescription}
2. **Appearance Features**:
   - ${gender === 'male' ? 'Boy characteristics: short or medium-short hair, lively and sunny image, wearing boy-appropriate clothing (such as T-shirts, shorts, etc.)' :
      gender === 'female' ? 'Girl characteristics: cute hairstyle (such as twin tails, short hair, or long hair), gentle and sweet image, wearing girl-appropriate clothing (such as dresses, cute tops, etc.)' :
      'Neutral characteristics: child-friendly image, gender features not obvious, wearing neutral clothing'}
   - Friendly facial expression, large and expressive anime-style eyes, warm smile
   - Age-appropriate clothing for ${characterAge} years old, simple but distinctive, bright colors
   - Overall image should be cute, approachable, and easily loved by children, reflecting the personality traits: ${traitsDescription}
3. **Composition Requirements**:
   - Full-body portrait, character centered
   - Front-facing view, standing pose
   - Pure white background, no other elements
   - High resolution, clear details
4. **Art Style**: ${styleDescription}

**Important Notes:**
- This character will maintain consistent appearance throughout the entire story
- Please ensure the character image is suitable for children to view, positive and uplifting
- The character should have strong recognizability and memorability
- CRITICAL: Do not include any text, logos, or written content in the image

Please generate a high-quality character reference sheet.
  `.trim();

  try {
    // 使用Gemini生成角色形象
    const timestamp = new Date().toISOString();
    console.log(`👤 [角色生成] ${timestamp} - 开始生成角色形象`);
    console.log(`📝 [角色信息] 角色详情:`);
    console.log(`   - 姓名: ${characterName}`);
    console.log(`   - 年龄: ${characterAge}岁`);
    console.log(`   - 性别: ${genderDescription} (${gender})`);
    console.log(`   - 性格: ${traitsDescription}`);
    console.log(`   - 风格: ${style} (${styleDescription})`);

    console.log(`📝 [角色提示词] 长度: ${characterPrompt.length} 字符`);
    console.log(`================== 角色生成提示词开始 ==================`);
    console.log(characterPrompt);
    console.log(`================== 角色生成提示词结束 ==================`);

    console.log(`🎨 [角色生成] 调用Gemini生成角色形象`);
    const imageDataUrl = await geminiService.generateSingleImage(characterPrompt, storyStyle);
    
    if (!imageDataUrl) {
      throw new Error('Gemini返回的图像数据为空');
    }

    console.log(`🖼️ [角色生成] Gemini生成完成，开始上传到存储`);

    // 生成唯一的文件名
    const fileTimestamp = Date.now();
    const fileName = `characters/${characterName}-${fileTimestamp}.jpg`;

    // 上传到存储服务
    const uploadedUrl = await storageService.uploadImage(fileName, imageDataUrl);

    if (!uploadedUrl) {
      throw new Error('角色形象上传失败');
    }

    console.log(`✅ [角色生成] 角色形象上传成功: ${uploadedUrl}`);
    return uploadedUrl;

  } catch (error) {
    console.error(`❌ [角色生成] 生成失败:`, error);
    
    // 生成占位符角色形象
    console.log(`🔄 [角色生成] 使用占位符角色形象`);
    return generatePlaceholderCharacter(characterName, characterAge, storyStyle);
  }
}

/**
 * 生成占位符角色形象
 */
function generatePlaceholderCharacter(
  characterName: string, 
  characterAge: number, 
  style: string
): string {
  // 根据风格选择颜色
  const styleColors = {
    cartoon: '#FF6B6B',
    watercolor: '#4ECDC4',
    sketch: '#95A5A6',
    fantasy: '#9B59B6',
    realistic: '#3498DB',
    anime: '#E74C3C'
  };

  const color = styleColors[style as keyof typeof styleColors] || styleColors.cartoon;

  // 生成SVG占位符
  const svgContent = `
    <svg width="400" height="500" xmlns="http://www.w3.org/2000/svg">
      <rect width="400" height="500" fill="#f8f9fa"/>
      <circle cx="200" cy="150" r="60" fill="${color}" opacity="0.3"/>
      <rect x="150" y="220" width="100" height="120" fill="${color}" opacity="0.2" rx="10"/>
      <text x="200" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" fill="${color}">
        ${characterName}
      </text>
      <text x="200" y="410" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" fill="#666">
        ${characterAge}岁
      </text>
      <text x="200" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#999">
        ${style}风格
      </text>
    </svg>
  `;

  // 转换为base64
  const base64Svg = btoa(unescape(encodeURIComponent(svgContent)));
  return `data:image/svg+xml;base64,${base64Svg}`;
}

export default app;
