{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "types": ["@cloudflare/workers-types", "node"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strictNullChecks": false, "strictFunctionTypes": false, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true, "noErrorTruncation": true, "skipDefaultLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}