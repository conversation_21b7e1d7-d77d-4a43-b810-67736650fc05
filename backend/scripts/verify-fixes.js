#!/usr/bin/env node

/**
 * StoryWeaver 修复验证脚本
 * 验证数据不一致问题的修复效果
 */

const fs = require('fs');
const path = require('path');

// 验证后端修复
function verifyBackendFixes() {
  console.log('🔍 验证后端修复...\n');
  
  // 1. 验证故事创建API的fallback逻辑已移除
  const storiesHandlerPath = path.join(__dirname, '../src/handlers/stories.ts');
  const storiesContent = fs.readFileSync(storiesHandlerPath, 'utf8');
  
  const hasFallback = storiesContent.includes("|| { id: 'test-user', credits: 100 }");
  console.log(`✓ 故事创建fallback逻辑移除: ${hasFallback ? '❌ 未移除' : '✅ 已移除'}`);
  
  const hasAuthCheck = storiesContent.includes('if (!user)') && storiesContent.includes('UNAUTHORIZED');
  console.log(`✓ 认证检查增强: ${hasAuthCheck ? '✅ 已添加' : '❌ 未添加'}`);
  
  // 2. 验证订阅查询增强
  const storageServicePath = path.join(__dirname, '../src/services/storage.ts');
  const storageContent = fs.readFileSync(storageServicePath, 'utf8');
  
  const hasDetailedLogging = storageContent.includes('所有订阅记录') && storageContent.includes('console.log');
  console.log(`✓ 订阅查询日志增强: ${hasDetailedLogging ? '✅ 已添加' : '❌ 未添加'}`);
  
  // 3. 验证认证中间件增强
  const authMiddlewarePath = path.join(__dirname, '../src/middleware/auth.ts');
  const authContent = fs.readFileSync(authMiddlewarePath, 'utf8');
  
  const hasUserExistenceCheck = authContent.includes('getUserById') && authContent.includes('数据库中不存在');
  console.log(`✓ 认证中间件用户存在性检查: ${hasUserExistenceCheck ? '✅ 已添加' : '❌ 未添加'}`);
  
  // 4. 验证数据一致性监控
  const monitorPath = path.join(__dirname, '../src/utils/dataConsistencyMonitor.ts');
  const hasMonitor = fs.existsSync(monitorPath);
  console.log(`✓ 数据一致性监控工具: ${hasMonitor ? '✅ 已创建' : '❌ 未创建'}`);
  
  if (hasMonitor) {
    const monitorContent = fs.readFileSync(monitorPath, 'utf8');
    const hasConsistencyCheck = monitorContent.includes('checkUserConsistency');
    console.log(`✓ 一致性检查功能: ${hasConsistencyCheck ? '✅ 已实现' : '❌ 未实现'}`);
  }
  
  return {
    fallbackRemoved: !hasFallback,
    authCheckAdded: hasAuthCheck,
    loggingEnhanced: hasDetailedLogging,
    userExistenceCheck: hasUserExistenceCheck,
    monitorCreated: hasMonitor
  };
}

// 验证前端修复
function verifyFrontendFixes() {
  console.log('\n🔍 验证前端修复...\n');
  
  // 1. 验证用户状态重置工具
  const resetUtilPath = path.join(__dirname, '../../frontend/src/utils/userStateReset.ts');
  const hasResetUtil = fs.existsSync(resetUtilPath);
  console.log(`✓ 用户状态重置工具: ${hasResetUtil ? '✅ 已创建' : '❌ 未创建'}`);
  
  if (hasResetUtil) {
    const resetContent = fs.readFileSync(resetUtilPath, 'utf8');
    const hasForceReset = resetContent.includes('forceResetUserState');
    const hasConsistencyDetection = resetContent.includes('detectAndFixUserStateInconsistency');
    const hasAuthValidation = resetContent.includes('validateUserAuthState');
    
    console.log(`✓ 强制重置功能: ${hasForceReset ? '✅ 已实现' : '❌ 未实现'}`);
    console.log(`✓ 一致性检测功能: ${hasConsistencyDetection ? '✅ 已实现' : '❌ 未实现'}`);
    console.log(`✓ 认证状态验证: ${hasAuthValidation ? '✅ 已实现' : '❌ 未实现'}`);
  }
  
  // 2. 验证App.tsx集成
  const appPath = path.join(__dirname, '../../frontend/src/App.tsx');
  const hasAppIntegration = fs.existsSync(appPath);
  
  if (hasAppIntegration) {
    const appContent = fs.readFileSync(appPath, 'utf8');
    const hasResetImport = appContent.includes('userStateReset');
    const hasConsistencyCheck = appContent.includes('detectAndFixUserStateInconsistency');
    const hasValidationCheck = appContent.includes('validateUserAuthState');
    
    console.log(`✓ App.tsx重置工具导入: ${hasResetImport ? '✅ 已添加' : '❌ 未添加'}`);
    console.log(`✓ App.tsx一致性检查: ${hasConsistencyCheck ? '✅ 已添加' : '❌ 未添加'}`);
    console.log(`✓ App.tsx验证检查: ${hasValidationCheck ? '✅ 已添加' : '❌ 未添加'}`);
  }
  
  return {
    resetUtilCreated: hasResetUtil,
    appIntegrated: hasAppIntegration
  };
}

// 验证数据检查脚本
function verifyDataCheckScripts() {
  console.log('\n🔍 验证数据检查脚本...\n');
  
  const sqlScriptPath = path.join(__dirname, 'check-data-consistency.sql');
  const jsScriptPath = path.join(__dirname, 'run-data-check.js');
  
  const hasSQLScript = fs.existsSync(sqlScriptPath);
  const hasJSScript = fs.existsSync(jsScriptPath);
  
  console.log(`✓ SQL检查脚本: ${hasSQLScript ? '✅ 已创建' : '❌ 未创建'}`);
  console.log(`✓ JS执行脚本: ${hasJSScript ? '✅ 已创建' : '❌ 未创建'}`);
  
  if (hasSQLScript) {
    const sqlContent = fs.readFileSync(sqlScriptPath, 'utf8');
    const hasUserCheck = sqlContent.includes('32d476ae-ecf6-4ecf-a4e0-2532626d7f2b');
    const hasStoryCheck = sqlContent.includes('ef143350-d5f5-425b-b4dd-f8e70d797505');
    const hasTestUserCheck = sqlContent.includes('test-user');
    
    console.log(`✓ 特定用户检查: ${hasUserCheck ? '✅ 已包含' : '❌ 未包含'}`);
    console.log(`✓ 特定故事检查: ${hasStoryCheck ? '✅ 已包含' : '❌ 未包含'}`);
    console.log(`✓ test-user污染检查: ${hasTestUserCheck ? '✅ 已包含' : '❌ 未包含'}`);
  }
  
  return {
    sqlScriptCreated: hasSQLScript,
    jsScriptCreated: hasJSScript
  };
}

// 生成修复报告
function generateFixReport(backendResults, frontendResults, scriptResults) {
  console.log('\n📋 修复验证报告');
  console.log('=' .repeat(50));
  
  const allChecks = [
    backendResults.fallbackRemoved,
    backendResults.authCheckAdded,
    backendResults.loggingEnhanced,
    backendResults.userExistenceCheck,
    backendResults.monitorCreated,
    frontendResults.resetUtilCreated,
    frontendResults.appIntegrated,
    scriptResults.sqlScriptCreated,
    scriptResults.jsScriptCreated
  ];
  
  const passedChecks = allChecks.filter(check => check).length;
  const totalChecks = allChecks.length;
  const successRate = Math.round((passedChecks / totalChecks) * 100);
  
  console.log(`\n📊 总体修复进度: ${passedChecks}/${totalChecks} (${successRate}%)`);
  
  if (successRate === 100) {
    console.log('🎉 所有修复措施已成功实施！');
  } else if (successRate >= 80) {
    console.log('✅ 大部分修复措施已实施，还有少量工作需要完成');
  } else {
    console.log('⚠️ 还有重要的修复措施需要实施');
  }
  
  console.log('\n🎯 下一步行动:');
  console.log('1. 执行数据库一致性检查脚本');
  console.log('2. 根据检查结果修复数据不一致问题');
  console.log('3. 部署修复后的代码到生产环境');
  console.log('4. 监控用户反馈和系统日志');
  console.log('5. 建立定期数据一致性检查机制');
}

// 主函数
async function main() {
  console.log('🔧 StoryWeaver 修复验证开始...\n');
  
  try {
    const backendResults = verifyBackendFixes();
    const frontendResults = verifyFrontendFixes();
    const scriptResults = verifyDataCheckScripts();
    
    generateFixReport(backendResults, frontendResults, scriptResults);
    
    console.log('\n✅ 修复验证完成');
    
  } catch (error) {
    console.error('❌ 修复验证过程中出错:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  verifyBackendFixes,
  verifyFrontendFixes,
  verifyDataCheckScripts,
  generateFixReport
};
