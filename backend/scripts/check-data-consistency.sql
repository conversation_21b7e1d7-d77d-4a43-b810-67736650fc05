-- StoryWeaver 数据一致性检查脚本
-- 用于诊断用户订阅状态和故事创建的数据不一致问题

-- ==================== 用户数据检查 ====================

-- 1. 检查特定用户的基本信息
SELECT 
  'USER_INFO' as check_type,
  id,
  email,
  name,
  credits,
  created_at,
  updated_at
FROM users 
WHERE id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b';

-- 2. 检查该用户的所有订阅记录（不限制状态）
SELECT
  'USER_SUBSCRIPTIONS' as check_type,
  id,
  user_id,
  plan_id,
  status,
  current_period_start,
  current_period_end,
  stripe_subscription_id,
  created_at,
  updated_at
FROM subscriptions
WHERE user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b'
ORDER BY created_at DESC;

-- 3. 检查该用户的活跃订阅
SELECT
  'ACTIVE_SUBSCRIPTIONS' as check_type,
  COUNT(*) as active_count,
  GROUP_CONCAT(plan_id) as active_plans,
  GROUP_CONCAT(status) as statuses
FROM subscriptions
WHERE user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b'
  AND status = 'active';

-- ==================== 故事数据检查 ====================

-- 4. 检查特定故事的归属
SELECT 
  'STORY_INFO' as check_type,
  id,
  user_id,
  title,
  character_name,
  status,
  created_at,
  updated_at
FROM stories 
WHERE id = 'ef143350-d5f5-425b-b4dd-f8e70d797505';

-- 5. 检查该用户的所有故事
SELECT 
  'USER_STORIES' as check_type,
  COUNT(*) as total_stories,
  COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_stories,
  COUNT(CASE WHEN status = 'generating' THEN 1 END) as generating_stories,
  COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_stories
FROM stories 
WHERE user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b';

-- 6. 检查该用户最近的故事列表
SELECT 
  'RECENT_STORIES' as check_type,
  id,
  title,
  character_name,
  status,
  created_at
FROM stories 
WHERE user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b'
ORDER BY created_at DESC 
LIMIT 10;

-- ==================== 数据污染检查 ====================

-- 7. 检查是否存在test-user的数据
SELECT 
  'TEST_USER_POLLUTION' as check_type,
  'users' as table_name,
  COUNT(*) as count
FROM users 
WHERE id = 'test-user'
UNION ALL
SELECT 
  'TEST_USER_POLLUTION' as check_type,
  'stories' as table_name,
  COUNT(*) as count
FROM stories 
WHERE user_id = 'test-user'
UNION ALL
SELECT 
  'TEST_USER_POLLUTION' as check_type,
  'subscriptions' as table_name,
  COUNT(*) as count
FROM subscriptions 
WHERE user_id = 'test-user';

-- 8. 检查是否存在调试用户数据
SELECT 
  'DEBUG_USER_POLLUTION' as check_type,
  'users' as table_name,
  id,
  email,
  name,
  credits
FROM users 
WHERE id LIKE '%debug%' 
   OR email LIKE '%debug%'
   OR name LIKE '%调试%'
   OR name LIKE '%debug%';

-- 9. 检查异常高积分的用户（可能是调试数据）
SELECT 
  'HIGH_CREDITS_USERS' as check_type,
  id,
  email,
  name,
  credits,
  created_at
FROM users 
WHERE credits > 100000
ORDER BY credits DESC;

-- ==================== 数据一致性检查 ====================

-- 10. 检查订阅状态与用户积分的一致性
SELECT
  'SUBSCRIPTION_CREDITS_CONSISTENCY' as check_type,
  u.id,
  u.email,
  u.credits,
  s.plan_id,
  s.status,
  CASE
    WHEN s.plan_id = 'unlimited_monthly' AND u.credits != 999999 THEN 'INCONSISTENT'
    WHEN s.plan_id IS NULL AND u.credits > 100 THEN 'SUSPICIOUS'
    ELSE 'OK'
  END as consistency_status
FROM users u
LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
WHERE u.id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b';

-- 11. 检查孤立的故事（用户不存在）
SELECT 
  'ORPHANED_STORIES' as check_type,
  s.id as story_id,
  s.user_id,
  s.title,
  s.created_at
FROM stories s
LEFT JOIN users u ON s.user_id = u.id
WHERE u.id IS NULL
LIMIT 10;

-- 12. 检查最近创建的故事分布
SELECT 
  'RECENT_STORY_DISTRIBUTION' as check_type,
  user_id,
  COUNT(*) as story_count,
  MAX(created_at) as latest_story
FROM stories 
WHERE created_at > datetime('now', '-7 days')
GROUP BY user_id
ORDER BY story_count DESC
LIMIT 10;
