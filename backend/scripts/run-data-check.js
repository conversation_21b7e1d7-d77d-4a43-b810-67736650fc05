#!/usr/bin/env node

/**
 * StoryWeaver 数据一致性检查工具
 * 用于诊断用户订阅状态和故事创建的数据不一致问题
 */

const fs = require('fs');
const path = require('path');

// 模拟D1数据库查询（实际环境中需要连接到真实数据库）
async function runDataConsistencyCheck() {
  console.log('🔍 StoryWeaver 数据一致性检查开始...\n');
  
  const sqlFile = path.join(__dirname, 'check-data-consistency.sql');
  const sqlContent = fs.readFileSync(sqlFile, 'utf8');
  
  console.log('📋 执行的SQL查询:');
  console.log('=' .repeat(50));
  console.log(sqlContent);
  console.log('=' .repeat(50));
  
  console.log('\n⚠️  注意: 这是SQL查询脚本，需要在Cloudflare D1数据库中执行');
  console.log('💡 执行方法:');
  console.log('   1. 登录Cloudflare Dashboard');
  console.log('   2. 进入D1数据库管理页面');
  console.log('   3. 选择storyweaver数据库');
  console.log('   4. 在查询界面中执行上述SQL语句');
  
  console.log('\n🎯 重点检查项目:');
  console.log('   ✓ 用户32d476ae-ecf6-4ecf-a4e0-2532626d7f2b的真实订阅状态');
  console.log('   ✓ 故事ef143350-d5f5-425b-b4dd-f8e70d797505的用户归属');
  console.log('   ✓ 是否存在test-user数据污染');
  console.log('   ✓ 是否存在调试用户数据残留');
  console.log('   ✓ 订阅状态与积分的一致性');
  
  // 生成Wrangler命令
  console.log('\n🛠️  使用Wrangler CLI执行查询:');
  console.log('   wrangler d1 execute storyweaver --file=./scripts/check-data-consistency.sql');
  
  return {
    success: true,
    message: 'SQL查询脚本已生成，请在D1数据库中执行'
  };
}

// 生成修复建议
function generateFixRecommendations() {
  console.log('\n🔧 基于ACE分析的修复建议:');
  console.log('=' .repeat(50));
  
  console.log('\n1. 如果发现用户订阅状态不一致:');
  console.log('   - 检查subscriptions表中的status字段');
  console.log('   - 确认current_period_end是否过期');
  console.log('   - 验证stripe_subscription_id的有效性');
  
  console.log('\n2. 如果发现故事归属错误:');
  console.log('   - 检查stories表中的user_id字段');
  console.log('   - 确认是否被错误保存到test-user');
  console.log('   - 需要手动修正user_id字段');
  
  console.log('\n3. 如果发现test-user数据污染:');
  console.log('   - 立即删除或修正test-user相关数据');
  console.log('   - 检查认证中间件的fallback逻辑');
  console.log('   - 确保生产环境不使用fallback用户');
  
  console.log('\n4. 如果发现调试用户数据残留:');
  console.log('   - 清理所有包含debug标识的用户数据');
  console.log('   - 检查前端localStorage缓存');
  console.log('   - 强制用户重新登录');
  
  console.log('\n5. 如果发现积分异常:');
  console.log('   - 999999积分应该只出现在unlimited_monthly订阅中');
  console.log('   - 检查支付处理逻辑的正确性');
  console.log('   - 验证订阅状态与积分的匹配关系');
}

// 生成数据修复SQL
function generateFixSQL() {
  console.log('\n🛠️  数据修复SQL模板:');
  console.log('=' .repeat(50));
  
  const fixSQL = `
-- 修复故事归属（如果发现故事被错误保存到test-user）
-- UPDATE stories 
-- SET user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b' 
-- WHERE id = 'ef143350-d5f5-425b-b4dd-f8e70d797505' 
--   AND user_id = 'test-user';

-- 清理test-user数据（如果存在）
-- DELETE FROM stories WHERE user_id = 'test-user';
-- DELETE FROM subscriptions WHERE user_id = 'test-user';
-- DELETE FROM users WHERE id = 'test-user';

-- 修复用户积分（如果发现异常）
-- UPDATE users 
-- SET credits = 100 
-- WHERE id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b' 
--   AND credits = 999999 
--   AND NOT EXISTS (
--     SELECT 1 FROM subscriptions 
--     WHERE user_id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b' 
--       AND plan = 'unlimited_monthly' 
--       AND status = 'active'
--   );

-- 验证修复结果
-- SELECT 'AFTER_FIX' as status, u.id, u.credits, s.plan, s.status
-- FROM users u
-- LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
-- WHERE u.id = '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b';
`;
  
  console.log(fixSQL);
  console.log('⚠️  注意: 执行修复SQL前请先备份数据！');
}

// 主函数
async function main() {
  try {
    const result = await runDataConsistencyCheck();
    generateFixRecommendations();
    generateFixSQL();
    
    console.log('\n✅ 数据一致性检查脚本执行完成');
    console.log('📝 请将SQL查询结果保存并分析，然后根据建议进行修复');
    
  } catch (error) {
    console.error('❌ 执行数据检查时出错:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  runDataConsistencyCheck,
  generateFixRecommendations,
  generateFixSQL
};
