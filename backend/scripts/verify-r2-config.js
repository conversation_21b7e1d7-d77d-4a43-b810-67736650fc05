#!/usr/bin/env node

/**
 * R2存储配置验证脚本
 * 用于检查开发环境和生产环境的R2存储配置是否正确
 */

const { execSync } = require('child_process');

console.log('🔍 验证R2存储配置...\n');

// 检查wrangler是否已安装
try {
  execSync('wrangler --version', { stdio: 'pipe' });
  console.log('✅ Wrangler CLI 已安装');
} catch (error) {
  console.error('❌ Wrangler CLI 未安装，请运行: npm install -g wrangler');
  process.exit(1);
}

// 检查是否已登录
try {
  execSync('wrangler whoami', { stdio: 'pipe' });
  console.log('✅ 已登录Cloudflare账户');
} catch (error) {
  console.error('❌ 未登录Cloudflare账户，请运行: wrangler login');
  process.exit(1);
}

console.log('\n📋 检查R2存储桶配置...');

// 检查开发环境R2存储桶
console.log('\n🔧 开发环境:');
try {
  const devBuckets = execSync('wrangler r2 bucket list', { encoding: 'utf8' });
  if (devBuckets.includes('storyweaver-assets-dev')) {
    console.log('✅ 开发环境R2存储桶 (storyweaver-assets-dev) 存在');
  } else {
    console.log('❌ 开发环境R2存储桶 (storyweaver-assets-dev) 不存在');
    console.log('💡 创建存储桶: wrangler r2 bucket create storyweaver-assets-dev');
  }
} catch (error) {
  console.error('❌ 无法检查R2存储桶:', error.message);
}

// 检查生产环境R2存储桶
console.log('\n🚀 生产环境:');
try {
  const prodBuckets = execSync('wrangler r2 bucket list', { encoding: 'utf8' });
  if (prodBuckets.includes('storyweaver-assets')) {
    console.log('✅ 生产环境R2存储桶 (storyweaver-assets) 存在');
  } else {
    console.log('❌ 生产环境R2存储桶 (storyweaver-assets) 不存在');
    console.log('💡 创建存储桶: wrangler r2 bucket create storyweaver-assets');
  }
} catch (error) {
  console.error('❌ 无法检查R2存储桶:', error.message);
}

console.log('\n🔐 检查环境变量配置...');

// 检查必要的环境变量
const requiredSecrets = [
  'GEMINI_API_KEY',
  'GOOGLE_CLIENT_ID', 
  'GOOGLE_CLIENT_SECRET',
  'STRIPE_SECRET_KEY',
  'JWT_SECRET'
];

console.log('\n📝 必要的环境变量:');
requiredSecrets.forEach(secret => {
  console.log(`   - ${secret}`);
});

console.log('\n💡 设置环境变量命令:');
requiredSecrets.forEach(secret => {
  console.log(`   wrangler secret put ${secret} --env development`);
  console.log(`   wrangler secret put ${secret} --env production`);
});

console.log('\n🧪 测试R2存储连接...');
console.log('💡 可以使用以下命令测试R2存储:');
console.log('   # 上传测试文件');
console.log('   echo "test" | wrangler r2 object put storyweaver-assets-dev/test.txt');
console.log('   # 列出文件');
console.log('   wrangler r2 object list storyweaver-assets-dev');
console.log('   # 删除测试文件');
console.log('   wrangler r2 object delete storyweaver-assets-dev/test.txt');

console.log('\n✨ R2配置验证完成!');
console.log('\n📚 相关文档:');
console.log('   - Cloudflare R2: https://developers.cloudflare.com/r2/');
console.log('   - Wrangler CLI: https://developers.cloudflare.com/workers/wrangler/');
