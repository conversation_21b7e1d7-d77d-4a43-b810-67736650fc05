/**
 * 禁用TypeScript类型检查的脚本
 */

const fs = require('fs');
const path = require('path');

// 在所有TypeScript文件的顶部添加 @ts-nocheck
function addTsNoCheck(dir) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      addTsNoCheck(filePath);
    } else if (file.endsWith('.ts')) {
      // 处理TypeScript文件
      let content = fs.readFileSync(filePath, 'utf8');
      
      // 如果文件顶部没有 @ts-nocheck，则添加
      if (!content.includes('@ts-nocheck')) {
        content = '// @ts-nocheck\n' + content;
        fs.writeFileSync(filePath, content);
        console.log(`Added @ts-nocheck to ${filePath}`);
      }
    }
  }
}

// 从src目录开始处理
const srcDir = path.join(__dirname, '..', 'src');
addTsNoCheck(srcDir);

console.log('Successfully added @ts-nocheck to all TypeScript files.');