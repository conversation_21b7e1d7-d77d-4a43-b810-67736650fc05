name = "storyweaver-api-dev"
main = "src/test-server.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[vars]
ENVIRONMENT = "development"
CORS_ORIGIN = "http://localhost:3000,https://storyweaver.jamintextiles.com"

[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets-dev"

[[d1_databases]]
binding = "DB"
database_name = "storyweaver-dev"
database_id = "fdf376a8-b068-49f3-89c5-440b89bb7e5f"

# 免费计划不支持CPU限制
# [limits]
# cpu_ms = 30000