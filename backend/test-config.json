{"environments": {"local": {"apiUrl": "http://localhost:8787", "timeout": 30000, "description": "本地开发环境"}, "staging": {"apiUrl": "https://api-staging.storyweaver.com", "timeout": 60000, "description": "预发布环境"}, "production": {"apiUrl": "https://api.storyweaver.com", "timeout": 60000, "description": "生产环境"}}, "testData": {"validStoryRequest": {"characterName": "小明", "characterAge": 6, "characterTraits": ["勇敢", "善良", "好奇"], "theme": "adventure", "setting": "神秘的森林", "style": "cartoon", "voice": "gentle_female", "customPrompt": "希望故事能教会孩子勇敢面对困难"}, "edgeCases": [{"name": "最小年龄", "data": {"characterName": "小宝", "characterAge": 3, "characterTraits": ["可爱"], "theme": "family", "setting": "家里", "style": "cartoon", "voice": "child_friendly"}}, {"name": "最大年龄", "data": {"characterName": "小华", "characterAge": 12, "characterTraits": ["聪明", "独立"], "theme": "learning", "setting": "学校", "style": "realistic", "voice": "storyteller"}}, {"name": "长名字", "data": {"characterName": "非常非常长的角色名字测试", "characterAge": 7, "characterTraits": ["特别", "独特"], "theme": "magic", "setting": "魔法世界", "style": "fantasy", "voice": "warm_male"}}, {"name": "多个性格特征", "data": {"characterName": "小红", "characterAge": 8, "characterTraits": ["勇敢", "善良", "聪明", "活泼", "乐观", "友善"], "theme": "friendship", "setting": "学校操场", "style": "watercolor", "voice": "gentle_female"}}], "invalidRequests": [{"name": "空名字", "data": {"characterName": "", "characterAge": 6, "characterTraits": ["勇敢"], "theme": "adventure", "setting": "森林", "style": "cartoon", "voice": "gentle_female"}, "expectedError": "character<PERSON>ame不能为空"}, {"name": "无效年龄", "data": {"characterName": "小明", "characterAge": 0, "characterTraits": ["勇敢"], "theme": "adventure", "setting": "森林", "style": "cartoon", "voice": "gentle_female"}, "expectedError": "年龄必须大于0"}, {"name": "缺少性格特征", "data": {"characterName": "小明", "characterAge": 6, "characterTraits": [], "theme": "adventure", "setting": "森林", "style": "cartoon", "voice": "gentle_female"}, "expectedError": "至少需要一个性格特征"}, {"name": "无效主题", "data": {"characterName": "小明", "characterAge": 6, "characterTraits": ["勇敢"], "theme": "invalid_theme", "setting": "森林", "style": "cartoon", "voice": "gentle_female"}, "expectedError": "无效的主题"}]}, "timeouts": {"apiCall": 30000, "storyGeneration": 300000, "pollingInterval": 5000, "maxPollingTime": 600000}, "expectedResults": {"minStoryPages": 3, "maxStoryPages": 15, "minPageTextLength": 20, "maxPageTextLength": 200, "requiredFields": ["id", "title", "<PERSON><PERSON><PERSON>", "characterAge", "pages", "status", "createdAt", "updatedAt"], "pageRequiredFields": ["pageNumber", "text", "imagePrompt"]}, "performance": {"maxResponseTime": {"health": 1000, "themes": 2000, "styles": 2000, "voices": 2000, "createStory": 5000, "getStory": 3000, "getStatus": 2000}, "concurrency": {"maxConcurrentRequests": 5, "requestInterval": 100}}}