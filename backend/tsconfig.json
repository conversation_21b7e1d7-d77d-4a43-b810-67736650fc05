{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "types": ["@cloudflare/workers-types", "node"], "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "strictNullChecks": false, "strictFunctionTypes": false, "noEmit": true, "verbatimModuleSyntax": false, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}