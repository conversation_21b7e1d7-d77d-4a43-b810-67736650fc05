name = "storyweaver-api-minimal"
main = "src/index-minimal.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量
[vars]
ENVIRONMENT = "production"

# 资源绑定
[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"

# 路由配置
[[routes]]
pattern = "api-storyweaver.jamintextiles.com/*"
zone_name = "jamintextiles.com"

# CPU 限制在免费计划中不支持
