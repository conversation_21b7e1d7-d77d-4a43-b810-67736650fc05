#!/bin/bash

echo "🔐 设置StoryWeaver生产环境密钥..."

# 确保在正确的目录中运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前工作目录: $(pwd)"

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "❌ Wrangler CLI未安装，请先安装: npm install -g wrangler"
  exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
  echo "❌ 未登录Cloudflare账户，请先运行: wrangler login"
  exit 1
fi

# 检查wrangler.toml文件是否存在
if [ ! -f "wrangler.toml" ]; then
  echo "❌ 未找到wrangler.toml文件，请确保在backend目录中运行此脚本"
  exit 1
fi

echo "📋 设置生产环境密钥..."

# 设置API密钥
echo "🔑 设置Gemini API密钥..."
echo "请输入Gemini API密钥:"
read -s GEMINI_API_KEY
wrangler secret put GEMINI_API_KEY --env production <<< "$GEMINI_API_KEY"

echo "🔑 设置Google OAuth密钥..."
echo "请输入Google Client Secret:"
read -s GOOGLE_CLIENT_SECRET
wrangler secret put GOOGLE_CLIENT_SECRET --env production <<< "$GOOGLE_CLIENT_SECRET"

echo "🔑 设置Stripe密钥..."
echo "请输入Stripe Secret Key:"
read -s STRIPE_SECRET_KEY
wrangler secret put STRIPE_SECRET_KEY --env production <<< "$STRIPE_SECRET_KEY"

echo "🔑 设置Stripe Webhook Secret:"
read -s STRIPE_WEBHOOK_SECRET
wrangler secret put STRIPE_WEBHOOK_SECRET --env production <<< "$STRIPE_WEBHOOK_SECRET"

echo "🔑 设置JWT Secret:"
read -s JWT_SECRET
wrangler secret put JWT_SECRET --env production <<< "$JWT_SECRET"

echo "✅ 所有生产环境密钥设置完成！"

echo "📋 验证密钥设置..."
wrangler secret list --env production

echo "🚀 现在可以部署到生产环境了！"
echo "运行: npm run deploy:production"
