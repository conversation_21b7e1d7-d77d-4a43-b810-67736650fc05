-- StoryWeaver 数据库Schema
-- 适用于 Cloudflare D1 SQLite 数据库

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    name TEXT NOT NULL,
    avatar TEXT,
    google_id TEXT UNIQUE NOT NULL,
    credits INTEGER DEFAULT 1,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 为用户表创建索引
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);

-- 故事表
CREATE TABLE IF NOT EXISTS stories (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    character_name TEXT NOT NULL,
    character_age INTEGER NOT NULL,
    character_traits TEXT NOT NULL, -- JSON array
    theme TEXT NOT NULL,
    setting TEXT NOT NULL,
    style TEXT NOT NULL,
    voice TEXT NOT NULL,
    pages TEXT NOT NULL, -- JSON array
    audio_url TEXT,
    cover_image_url TEXT,
    status TEXT NOT NULL DEFAULT 'generating', -- generating, completed, failed
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为故事表创建索引
CREATE INDEX IF NOT EXISTS idx_stories_user_id ON stories(user_id);
CREATE INDEX IF NOT EXISTS idx_stories_status ON stories(status);
CREATE INDEX IF NOT EXISTS idx_stories_created_at ON stories(created_at);
CREATE INDEX IF NOT EXISTS idx_stories_user_created ON stories(user_id, created_at);

-- 订阅表
CREATE TABLE IF NOT EXISTS subscriptions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    plan TEXT NOT NULL, -- free, credits, unlimited
    status TEXT NOT NULL, -- active, canceled, expired
    current_period_start TEXT NOT NULL,
    current_period_end TEXT NOT NULL,
    stripe_subscription_id TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为订阅表创建索引
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_stripe_id ON subscriptions(stripe_subscription_id);

-- 实体书订单表
CREATE TABLE IF NOT EXISTS physical_books (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    order_number TEXT UNIQUE NOT NULL,
    customization TEXT NOT NULL, -- JSON object
    shipping_info TEXT NOT NULL, -- JSON object
    status TEXT NOT NULL DEFAULT 'pending', -- pending, printing, shipped, delivered, canceled
    price REAL NOT NULL,
    stripe_payment_intent_id TEXT,
    tracking_number TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为实体书订单表创建索引
CREATE INDEX IF NOT EXISTS idx_physical_books_user_id ON physical_books(user_id);
CREATE INDEX IF NOT EXISTS idx_physical_books_story_id ON physical_books(story_id);
CREATE INDEX IF NOT EXISTS idx_physical_books_status ON physical_books(status);
CREATE INDEX IF NOT EXISTS idx_physical_books_order_number ON physical_books(order_number);
CREATE INDEX IF NOT EXISTS idx_physical_books_created_at ON physical_books(created_at);

-- 支付记录表
CREATE TABLE IF NOT EXISTS payments (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- credits, subscription, physical_book
    amount REAL NOT NULL,
    currency TEXT NOT NULL DEFAULT 'USD',
    status TEXT NOT NULL, -- pending, succeeded, failed, canceled
    stripe_payment_intent_id TEXT,
    stripe_subscription_id TEXT,
    metadata TEXT, -- JSON object for additional data
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为支付记录表创建索引
CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_type ON payments(type);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_stripe_payment_intent ON payments(stripe_payment_intent_id);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);

-- 用户活动日志表
CREATE TABLE IF NOT EXISTS user_activities (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    activity_type TEXT NOT NULL, -- login, story_created, payment, subscription_changed
    activity_data TEXT, -- JSON object
    ip_address TEXT,
    user_agent TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为用户活动日志表创建索引
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activities_created_at ON user_activities(created_at);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    description TEXT,
    created_at TEXT NOT NULL DEFAULT (datetime('now')),
    updated_at TEXT NOT NULL DEFAULT (datetime('now'))
);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_configs (key, value, description) VALUES
('max_free_stories', '1', '免费用户最大故事数量'),
('credits_per_purchase', '5', '每次购买获得的积分数'),
('credit_price_usd', '10.00', '积分包价格（美元）'),
('subscription_price_usd', '15.00', '月订阅价格（美元）'),
('physical_book_price_usd', '29.99', '实体书价格（美元）'),
('max_story_pages', '12', '故事最大页数'),
('min_story_pages', '6', '故事最小页数'),
('ai_generation_timeout', '300', 'AI生成超时时间（秒）'),
('content_safety_enabled', 'true', '是否启用内容安全检查'),
('rate_limit_per_minute', '60', '每分钟API调用限制');

-- 内容审核表
CREATE TABLE IF NOT EXISTS content_moderations (
    id TEXT PRIMARY KEY,
    content_type TEXT NOT NULL, -- story, image, audio
    content_id TEXT NOT NULL, -- story_id or other content identifier
    user_id TEXT NOT NULL,
    status TEXT NOT NULL, -- pending, approved, rejected
    moderator_id TEXT,
    reason TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为内容审核表创建索引
CREATE INDEX IF NOT EXISTS idx_content_moderations_content_type ON content_moderations(content_type);
CREATE INDEX IF NOT EXISTS idx_content_moderations_content_id ON content_moderations(content_id);
CREATE INDEX IF NOT EXISTS idx_content_moderations_status ON content_moderations(status);
CREATE INDEX IF NOT EXISTS idx_content_moderations_user_id ON content_moderations(user_id);
CREATE INDEX IF NOT EXISTS idx_content_moderations_created_at ON content_moderations(created_at);

-- 用户反馈表
CREATE TABLE IF NOT EXISTS user_feedbacks (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    type TEXT NOT NULL, -- bug_report, feature_request, general_feedback
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'open', -- open, in_progress, resolved, closed
    priority TEXT NOT NULL DEFAULT 'medium', -- low, medium, high, urgent
    assigned_to TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为用户反馈表创建索引
CREATE INDEX IF NOT EXISTS idx_user_feedbacks_user_id ON user_feedbacks(user_id);
CREATE INDEX IF NOT EXISTS idx_user_feedbacks_type ON user_feedbacks(type);
CREATE INDEX IF NOT EXISTS idx_user_feedbacks_status ON user_feedbacks(status);
CREATE INDEX IF NOT EXISTS idx_user_feedbacks_priority ON user_feedbacks(priority);
CREATE INDEX IF NOT EXISTS idx_user_feedbacks_created_at ON user_feedbacks(created_at);

-- 故事分享表
CREATE TABLE IF NOT EXISTS story_shares (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    share_token TEXT UNIQUE NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    view_count INTEGER DEFAULT 0,
    expires_at TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (story_id) REFERENCES stories(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 为故事分享表创建索引
CREATE INDEX IF NOT EXISTS idx_story_shares_story_id ON story_shares(story_id);
CREATE INDEX IF NOT EXISTS idx_story_shares_user_id ON story_shares(user_id);
CREATE INDEX IF NOT EXISTS idx_story_shares_token ON story_shares(share_token);
CREATE INDEX IF NOT EXISTS idx_story_shares_public ON story_shares(is_public);
CREATE INDEX IF NOT EXISTS idx_story_shares_created_at ON story_shares(created_at);

-- 创建触发器：自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
BEGIN
    UPDATE users SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_stories_updated_at
    AFTER UPDATE ON stories
    FOR EACH ROW
BEGIN
    UPDATE stories SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_subscriptions_updated_at
    AFTER UPDATE ON subscriptions
    FOR EACH ROW
BEGIN
    UPDATE subscriptions SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_physical_books_updated_at
    AFTER UPDATE ON physical_books
    FOR EACH ROW
BEGIN
    UPDATE physical_books SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_payments_updated_at
    AFTER UPDATE ON payments
    FOR EACH ROW
BEGIN
    UPDATE payments SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_system_configs_updated_at
    AFTER UPDATE ON system_configs
    FOR EACH ROW
BEGIN
    UPDATE system_configs SET updated_at = datetime('now') WHERE key = NEW.key;
END;

CREATE TRIGGER IF NOT EXISTS update_content_moderations_updated_at
    AFTER UPDATE ON content_moderations
    FOR EACH ROW
BEGIN
    UPDATE content_moderations SET updated_at = datetime('now') WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_user_feedbacks_updated_at
    AFTER UPDATE ON user_feedbacks
    FOR EACH ROW
BEGIN
    UPDATE user_feedbacks SET updated_at = datetime('now') WHERE id = NEW.id;
END;

-- 创建视图：用户统计信息
CREATE VIEW IF NOT EXISTS user_stats AS
SELECT 
    u.id,
    u.email,
    u.name,
    u.credits,
    u.created_at,
    COUNT(s.id) as total_stories,
    COUNT(CASE WHEN s.status = 'completed' THEN 1 END) as completed_stories,
    COUNT(pb.id) as total_books_ordered,
    COALESCE(sub.plan, 'free') as current_plan,
    COALESCE(sub.status, 'none') as subscription_status
FROM users u
LEFT JOIN stories s ON u.id = s.user_id
LEFT JOIN physical_books pb ON u.id = pb.user_id
LEFT JOIN subscriptions sub ON u.id = sub.user_id AND sub.status = 'active'
GROUP BY u.id, u.email, u.name, u.credits, u.created_at, sub.plan, sub.status;

-- 创建视图：故事统计信息
CREATE VIEW IF NOT EXISTS story_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as total_stories,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_stories,
    COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_stories,
    AVG(CASE WHEN status = 'completed' THEN 
        (julianday(updated_at) - julianday(created_at)) * 24 * 60 
    END) as avg_generation_time_minutes
FROM stories
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- 创建视图：收入统计信息
CREATE VIEW IF NOT EXISTS revenue_stats AS
SELECT 
    DATE(created_at) as date,
    type,
    COUNT(*) as transaction_count,
    SUM(amount) as total_amount,
    AVG(amount) as avg_amount
FROM payments
WHERE status = 'succeeded'
GROUP BY DATE(created_at), type
ORDER BY date DESC, type;