#!/bin/bash

# StoryWeaver Cloudflare 资源创建脚本
# 创建 KV、R2、D1 等必要资源

echo "🚀 创建 StoryWeaver Cloudflare 资源..."

# 检查是否已登录 Cloudflare
echo "检查 Cloudflare 登录状态..."
if ! wrangler whoami > /dev/null 2>&1; then
    echo "❌ 请先登录 Cloudflare:"
    echo "wrangler login"
    exit 1
fi

echo "✅ Cloudflare 已登录"

# 创建开发环境资源
echo ""
echo "📦 创建开发环境资源..."

# 创建开发环境 KV 命名空间
echo "创建开发环境 KV 命名空间..."
DEV_KV_ID=$(wrangler kv:namespace create "CACHE" --preview | grep -o 'id = "[^"]*"' | cut -d'"' -f2)
DEV_KV_PREVIEW_ID=$(wrangler kv:namespace create "CACHE" --preview | grep -o 'preview_id = "[^"]*"' | cut -d'"' -f2)

# 创建开发环境 R2 存储桶
echo "创建开发环境 R2 存储桶..."
wrangler r2 bucket create storyweaver-assets-dev

# 创建开发环境 D1 数据库
echo "创建开发环境 D1 数据库..."
DEV_D1_OUTPUT=$(wrangler d1 create storyweaver-dev)
DEV_D1_ID=$(echo "$DEV_D1_OUTPUT" | grep -o 'database_id = "[^"]*"' | cut -d'"' -f2)

# 创建生产环境资源
echo ""
echo "🏭 创建生产环境资源..."

# 创建生产环境 KV 命名空间
echo "创建生产环境 KV 命名空间..."
PROD_KV_ID=$(wrangler kv:namespace create "CACHE" | grep -o 'id = "[^"]*"' | cut -d'"' -f2)

# 创建生产环境 R2 存储桶
echo "创建生产环境 R2 存储桶..."
wrangler r2 bucket create storyweaver-assets

# 创建生产环境 D1 数据库
echo "创建生产环境 D1 数据库..."
PROD_D1_OUTPUT=$(wrangler d1 create storyweaver)
PROD_D1_ID=$(echo "$PROD_D1_OUTPUT" | grep -o 'database_id = "[^"]*"' | cut -d'"' -f2)

# 更新 wrangler.toml 文件
echo ""
echo "📝 更新 wrangler.toml 配置文件..."

# 备份原文件
cp wrangler.toml wrangler.toml.backup

# 替换开发环境 ID
sed -i.tmp "s/your-dev-kv-namespace-id/$DEV_KV_ID/g" wrangler.toml
sed -i.tmp "s/your-dev-kv-preview-id/$DEV_KV_PREVIEW_ID/g" wrangler.toml
sed -i.tmp "s/your-dev-d1-database-id/$DEV_D1_ID/g" wrangler.toml

# 替换生产环境 ID
sed -i.tmp "s/your-prod-kv-namespace-id/$PROD_KV_ID/g" wrangler.toml
sed -i.tmp "s/your-prod-d1-database-id/$PROD_D1_ID/g" wrangler.toml

# 清理临时文件
rm wrangler.toml.tmp

echo ""
echo "✅ Cloudflare 资源创建完成!"
echo ""
echo "📋 创建的资源:"
echo "开发环境:"
echo "  - KV 命名空间: $DEV_KV_ID"
echo "  - R2 存储桶: storyweaver-assets-dev"
echo "  - D1 数据库: $DEV_D1_ID"
echo ""
echo "生产环境:"
echo "  - KV 命名空间: $PROD_KV_ID"
echo "  - R2 存储桶: storyweaver-assets"
echo "  - D1 数据库: $PROD_D1_ID"
echo ""
echo "🔧 下一步:"
echo "1. 运行数据库迁移: npm run migrate:local"
echo "2. 设置环境变量: ./setup-secrets.sh"
echo "3. 启动开发服务器: npm run dev"
