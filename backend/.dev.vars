# StoryWeaver Backend Development Environment Variables
# 这些变量用于本地开发环境，确保所有功能正常工作

# AI 服务配置
GEMINI_API_KEY=AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw

# 认证相关配置
JWT_SECRET=storyweaver-jwt-secret-key-2024
DEBUG_MODE=true

# Google OAuth 配置
GOOGLE_CLIENT_ID=463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Stripe 支付配置（测试环境）
STRIPE_SECRET_KEY=sk_test_51RfGiA2azdJC9VD2pw6iqqZ7YslQYTCwvksZnrM4ESJwOGvMMPwq19WhvM3B6i0qwOxKqNyR868D69y3lK0e0qxC00CccfMW6o
STRIPE_WEBHOOK_SECRET=whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA