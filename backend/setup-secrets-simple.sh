#!/bin/bash

echo "🔐 设置StoryWeaver生产环境密钥（简化版）..."

# 确保在正确的目录中运行
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前工作目录: $(pwd)"

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "❌ Wrangler CLI未安装，请先安装: npm install -g wrangler"
  exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
  echo "❌ 未登录Cloudflare账户，请先运行: wrangler login"
  exit 1
fi

# 检查wrangler.toml文件是否存在
if [ ! -f "wrangler.toml" ]; then
  echo "❌ 未找到wrangler.toml文件，请确保在backend目录中运行此脚本"
  exit 1
fi

echo "📋 当前将为以下Worker设置密钥:"
echo "   Worker名称: storyweaver-api-production"
echo "   环境: production"
echo ""

# 显示需要设置的密钥列表
echo "🔑 需要设置的密钥列表:"
echo "   1. GEMINI_API_KEY"
echo "   2. GOOGLE_CLIENT_SECRET"
echo "   3. STRIPE_SECRET_KEY"
echo "   4. STRIPE_WEBHOOK_SECRET"
echo "   5. JWT_SECRET"
echo ""

echo "💡 使用以下命令手动设置密钥:"
echo ""
echo "wrangler secret put GEMINI_API_KEY --env production"
echo "wrangler secret put GOOGLE_CLIENT_SECRET --env production"
echo "wrangler secret put STRIPE_SECRET_KEY --env production"
echo "wrangler secret put STRIPE_WEBHOOK_SECRET --env production"
echo "wrangler secret put JWT_SECRET --env production"
echo ""

echo "📋 或者逐个运行以下命令:"

# 提供选择菜单
while true; do
  echo ""
  echo "请选择要设置的密钥:"
  echo "1) GEMINI_API_KEY"
  echo "2) GOOGLE_CLIENT_SECRET"
  echo "3) STRIPE_SECRET_KEY"
  echo "4) STRIPE_WEBHOOK_SECRET"
  echo "5) JWT_SECRET"
  echo "6) 查看已设置的密钥"
  echo "7) 退出"
  echo ""
  read -p "请输入选择 (1-7): " choice

  case $choice in
    1)
      echo "🔑 设置 GEMINI_API_KEY..."
      wrangler secret put GEMINI_API_KEY --env production
      ;;
    2)
      echo "🔑 设置 GOOGLE_CLIENT_SECRET..."
      wrangler secret put GOOGLE_CLIENT_SECRET --env production
      ;;
    3)
      echo "🔑 设置 STRIPE_SECRET_KEY..."
      wrangler secret put STRIPE_SECRET_KEY --env production
      ;;
    4)
      echo "🔑 设置 STRIPE_WEBHOOK_SECRET..."
      wrangler secret put STRIPE_WEBHOOK_SECRET --env production
      ;;
    5)
      echo "🔑 设置 JWT_SECRET..."
      wrangler secret put JWT_SECRET --env production
      ;;
    6)
      echo "📋 查看已设置的密钥..."
      wrangler secret list --env production
      ;;
    7)
      echo "👋 退出设置"
      break
      ;;
    *)
      echo "❌ 无效选择，请输入 1-7"
      ;;
  esac
done

echo ""
echo "✅ 密钥设置完成！"
echo "🚀 现在可以测试生产环境功能了"
