name = "storyweaver-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[observability.logs]
enabled = true

# 默认环境配置（生产环境）
[vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.pages.dev"
# 认证相关配置
JWT_SECRET = "storyweaver-jwt-secret-key-2024"
# Google OAuth 配置
GOOGLE_CLIENT_ID = "463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
# Stripe 支付配置（测试环境）
STRIPE_SECRET_KEY = "sk_test_51RfGiA2azdJC9VD2pw6iqqZ7YslQYTCwvksZnrM4ESJwOGvMMPwq19WhvM3B6i0qwOxKqNyR868D69y3lK0e0qxC00CccfMW6o"
STRIPE_WEBHOOK_SECRET = "whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA"
# AI 服务配置
GEMINI_API_KEY = "AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw"

# 默认环境 Durable Objects 配置
[durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" }
]

# 默认环境 KV 命名空间
[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

# 默认环境 R2 存储桶
[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

# 默认环境 D1 数据库
[[d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"

# 开发环境配置
[env.development]
[env.development.vars]
ENVIRONMENT = "development"
CORS_ORIGIN = "http://localhost:3000,http://localhost:5173,http://localhost:5174,https://storyweaver.jamintextiles.com"
# 注意：开发环境的敏感变量通过 .dev.vars 文件提供

# 开发环境 Durable Objects 配置
[env.development.durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" }
]

# 开发服务器配置
[dev]
port = 8787
local_protocol = "http"

[[env.development.kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[env.development.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets-dev"

[[env.development.d1_databases]]
binding = "DB"
database_name = "storyweaver-dev"
database_id = "fdf376a8-b068-49f3-89c5-440b89bb7e5f"

# 生产环境配置
[env.production]
name = "storyweaver-api"
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.pages.dev"
# 认证相关配置
JWT_SECRET = "storyweaver-jwt-secret-key-2024"
# Google OAuth 配置
GOOGLE_CLIENT_ID = "463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
# Stripe 支付配置（测试环境）
STRIPE_SECRET_KEY = "sk_test_51RfGiA2azdJC9VD2pw6iqqZ7YslQYTCwvksZnrM4ESJwOGvMMPwq19WhvM3B6i0qwOxKqNyR868D69y3lK0e0qxC00CccfMW6o"
STRIPE_WEBHOOK_SECRET = "whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA"
# AI 服务配置
GEMINI_API_KEY = "AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw"

# 生产环境 Durable Objects 配置
[env.production.durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" }
]

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[env.production.d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"

# 环境变量（通过 wrangler secret 命令设置）
# wrangler secret put GEMINI_API_KEY
# wrangler secret put GOOGLE_CLIENT_ID
# wrangler secret put GOOGLE_CLIENT_SECRET
# wrangler secret put STRIPE_SECRET_KEY
# wrangler secret put STRIPE_WEBHOOK_SECRET
# wrangler secret put JWT_SECRET

[[migrations]]
tag = "v1"
new_classes = ["AITaskQueueDO"]

# 路由配置（使用默认workers.dev域名）
# [[routes]]
# pattern = "api-storyweaver.jamintextiles.com/*"
# zone_name = "jamintextiles.com"

# CPU limits不支持免费计划，已移除
# [limits]
# cpu_ms = 30000  # 30秒CPU时间限制（适合AI生成任务）

# 构建配置
[build]
command = "npm run build"

# 部署配置
[triggers]
crons = ["*/5 * * * *"]  # 每5分钟检查超时任务