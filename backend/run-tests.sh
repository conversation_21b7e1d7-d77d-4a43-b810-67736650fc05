#!/bin/bash

# StoryWeaver 测试运行脚本
# 用于运行各种类型的测试

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "StoryWeaver 测试运行脚本"
    echo ""
    echo "用法: ./run-tests.sh [选项] [测试类型]"
    echo ""
    echo "测试类型:"
    echo "  local     - 运行本地管道测试 (不需要服务器)"
    echo "  mock      - 运行模拟API测试"
    echo "  real      - 运行真实API测试"
    echo "  all       - 运行所有测试"
    echo ""
    echo "选项:"
    echo "  --url <url>       指定API URL (仅用于real测试)"
    echo "  --timeout <ms>    设置超时时间"
    echo "  --verbose         显示详细输出"
    echo "  --help, -h        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./run-tests.sh local"
    echo "  ./run-tests.sh real --url http://localhost:8787"
    echo "  ./run-tests.sh real --url https://api.storyweaver.com"
    echo "  ./run-tests.sh all --verbose"
    echo ""
    echo "环境变量:"
    echo "  API_BASE_URL      默认API URL"
    echo "  GEMINI_API_KEY    Gemini API密钥 (用于真实API测试)"
    echo "  USE_REAL_GEMINI   设置为'true'启用真实Gemini API"
}

# 检查Node.js是否安装
check_node() {
    if ! command -v node &> /dev/null; then
        print_message $RED "❌ Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    local node_version=$(node --version)
    print_message $BLUE "📦 Node.js 版本: $node_version"
}

# 检查依赖
check_dependencies() {
    print_message $BLUE "🔍 检查依赖..."
    
    if [ ! -f "package.json" ]; then
        print_message $RED "❌ 未找到 package.json，请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查是否需要安装依赖
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "📦 安装依赖..."
        npm install
    fi
}

# 运行本地管道测试
run_local_test() {
    print_message $GREEN "🧪 运行本地管道测试..."
    print_message $BLUE "📝 测试内容: 存储服务、Gemini服务、完整故事管道"
    echo ""
    
    node test-local-pipeline.js
}

# 运行模拟API测试
run_mock_test() {
    print_message $GREEN "🧪 运行模拟API测试..."
    print_message $BLUE "📝 测试内容: API端点、请求响应、错误处理"
    echo ""
    
    node test-story-pipeline.js
}

# 运行真实API测试
run_real_test() {
    local api_url=${API_URL:-${API_BASE_URL:-"http://localhost:8787"}}
    
    print_message $GREEN "🧪 运行真实API测试..."
    print_message $BLUE "📝 测试目标: $api_url"
    print_message $BLUE "📝 测试内容: 健康检查、故事创建、生成监控"
    echo ""
    
    # 检查服务是否运行
    print_message $BLUE "🔍 检查服务状态..."
    if curl -s --connect-timeout 5 "$api_url/health" > /dev/null 2>&1; then
        print_message $GREEN "✅ 服务正在运行"
    else
        print_message $YELLOW "⚠️  无法连接到服务，测试可能失败"
        print_message $BLUE "💡 提示: 确保服务正在运行: npm run dev"
    fi
    
    echo ""
    node test-real-api.js --url "$api_url"
}

# 运行所有测试
run_all_tests() {
    print_message $GREEN "🧪 运行所有测试..."
    echo ""
    
    local failed_tests=()
    
    # 本地测试
    print_message $BLUE "=== 1/3: 本地管道测试 ==="
    if run_local_test; then
        print_message $GREEN "✅ 本地测试通过"
    else
        print_message $RED "❌ 本地测试失败"
        failed_tests+=("local")
    fi
    
    echo ""
    
    # 模拟测试
    print_message $BLUE "=== 2/3: 模拟API测试 ==="
    if run_mock_test; then
        print_message $GREEN "✅ 模拟测试通过"
    else
        print_message $RED "❌ 模拟测试失败"
        failed_tests+=("mock")
    fi
    
    echo ""
    
    # 真实API测试
    print_message $BLUE "=== 3/3: 真实API测试 ==="
    if run_real_test; then
        print_message $GREEN "✅ 真实API测试通过"
    else
        print_message $RED "❌ 真实API测试失败"
        failed_tests+=("real")
    fi
    
    echo ""
    print_message $BLUE "=== 测试总结 ==="
    
    if [ ${#failed_tests[@]} -eq 0 ]; then
        print_message $GREEN "🎉 所有测试通过！"
        return 0
    else
        print_message $RED "⚠️  以下测试失败: ${failed_tests[*]}"
        return 1
    fi
}

# 启动开发服务器
start_dev_server() {
    print_message $BLUE "🚀 启动开发服务器..."
    
    if command -v wrangler &> /dev/null; then
        print_message $GREEN "✅ 找到 Wrangler CLI"
        print_message $BLUE "💡 运行: wrangler dev"
        echo ""
        exec wrangler dev
    else
        print_message $YELLOW "⚠️  未找到 Wrangler CLI"
        print_message $BLUE "💡 安装命令: npm install -g wrangler"
        print_message $BLUE "💡 或使用: npm run dev"
        exit 1
    fi
}

# 生成测试报告
generate_report() {
    print_message $BLUE "📊 生成测试报告..."
    
    local report_dir="test-reports"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    
    mkdir -p "$report_dir"
    
    # 收集测试结果文件
    local report_file="$report_dir/test_report_$timestamp.md"
    
    cat > "$report_file" << EOF
# StoryWeaver 测试报告

**生成时间**: $(date)
**测试环境**: $(uname -s) $(uname -r)
**Node.js版本**: $(node --version)

## 测试结果

EOF
    
    # 如果存在测试结果JSON文件，添加到报告中
    if [ -f "test-report.json" ]; then
        echo "### 详细结果" >> "$report_file"
        echo '```json' >> "$report_file"
        cat test-report.json >> "$report_file"
        echo '```' >> "$report_file"
    fi
    
    print_message $GREEN "📄 报告已生成: $report_file"
}

# 清理测试文件
cleanup() {
    print_message $BLUE "🧹 清理测试文件..."
    
    # 删除临时测试文件
    rm -f test-report.json
    rm -f *.log
    
    print_message $GREEN "✅ 清理完成"
}

# 主函数
main() {
    local test_type=""
    local api_url=""
    local timeout=""
    local verbose=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --url)
                api_url="$2"
                shift 2
                ;;
            --timeout)
                timeout="$2"
                shift 2
                ;;
            --verbose)
                verbose=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            --dev)
                start_dev_server
                exit 0
                ;;
            --report)
                generate_report
                exit 0
                ;;
            --cleanup)
                cleanup
                exit 0
                ;;
            local|mock|real|all)
                test_type="$1"
                shift
                ;;
            *)
                print_message $RED "❌ 未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定测试类型，显示帮助
    if [ -z "$test_type" ]; then
        show_help
        exit 1
    fi
    
    # 设置环境变量
    if [ -n "$api_url" ]; then
        export API_URL="$api_url"
    fi
    
    if [ -n "$timeout" ]; then
        export TEST_TIMEOUT="$timeout"
    fi
    
    if [ "$verbose" = true ]; then
        export VERBOSE=true
        set -x  # 显示执行的命令
    fi
    
    # 显示测试信息
    print_message $BLUE "🚀 StoryWeaver 测试运行器"
    print_message $BLUE "================================"
    
    # 检查环境
    check_node
    check_dependencies
    
    echo ""
    
    # 运行指定的测试
    case $test_type in
        local)
            run_local_test
            ;;
        mock)
            run_mock_test
            ;;
        real)
            run_real_test
            ;;
        all)
            run_all_tests
            ;;
        *)
            print_message $RED "❌ 未知测试类型: $test_type"
            exit 1
            ;;
    esac
}

# 捕获中断信号
trap 'print_message $YELLOW "⚠️  测试被中断"; exit 130' INT

# 运行主函数
main "$@"