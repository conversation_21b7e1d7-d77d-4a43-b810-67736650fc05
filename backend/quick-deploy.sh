#!/bin/bash

echo "🚀 快速部署StoryWeaver后端修复..."

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "❌ Wrangler CLI未安装，请先安装: npm install -g wrangler"
  exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
  echo "❌ 未登录Cloudflare账户，请先运行: wrangler login"
  exit 1
fi

echo "🔧 修复内容:"
echo "  ✅ 添加认证中间件到 /create-checkout-session 端点"
echo "  ✅ 优化JWT验证逻辑"
echo "  ✅ 添加JWT_SECRET到生产环境配置"
echo "  ✅ 修复前端认证检查逻辑"

# 部署到生产环境
echo "🌐 部署到Cloudflare Workers生产环境..."
wrangler deploy --env production

if [ $? -eq 0 ]; then
  echo "✅ 后端部署成功！"
  echo "🔗 API地址: https://storyweaver-api.stawky.workers.dev"
  
  echo "🧪 测试API连接..."
  curl -s https://storyweaver-api.stawky.workers.dev/health
  
  echo ""
  echo "🎯 修复验证:"
  echo "1. 访问前端: https://storyweaver.pages.dev"
  echo "2. 登录用户账户"
  echo "3. 尝试购买积分"
  echo "4. 应该能成功创建Stripe Checkout会话"
  
else
  echo "❌ 部署失败，请检查错误信息"
  exit 1
fi

echo "🎉 修复部署完成！"
