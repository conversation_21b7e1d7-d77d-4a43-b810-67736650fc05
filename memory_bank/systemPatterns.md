# 系统模式

## 技术栈分析 (由 NexusCore 更新 @ 2025-07-09)

根据对项目 `package.json` 文件的分析，总结出以下技术栈和系统模式：

### 核心架构
- **运行时**: Cloudflare Workers
- **语言**: TypeScript
- **项目结构**: Monorepo 风格

### 后端 (`backend/`)
- **框架**: Hono
- **API 集成**:
    - Google Gemini (AI 功能)
    - Stripe (支付)
- **主要依赖**:
    - `hono`: 核心框架
    - `@google/generative-ai`: Gemini AI
    - `stripe`: Stripe 支付集成
    - `zod`: 数据验证

### 前端 (`frontend/` & `frontend-production/`)
- **框架**: React
- **构建工具**: Vite
- **状态管理**:
    - `zustand`: 全局状态管理
    - `@tanstack/react-query`: 服务器状态和缓存
- **主要依赖**:
    - `react`: UI 库
    - `vite`: 构建工具
    - `zustand`: 状态管理
    - `@tanstack/react-query`: 数据请求
    - `axios`: HTTP 客户端

### 管理后台 (`admin-panel/`)
- **框架**: React & Hono
- **数据可视化**:
    - `recharts`: 图表库
    - `@tanstack/react-table`: 表格库
- **主要依赖**:
    - `react`: UI 库
    - `hono`: 后端框架
    - `recharts`: 图表
    - `@tanstack/react-table`: 表格
