# Admin Panel 前端架构知识摘要

本文档是根据 `code-developer` 模式在 [2025-07-06] 生成的分析报告 `admin-panel/ADMIN_PANEL_FRONTEND_ANALYSIS.md` 整合而成的核心知识。

## 核心技术栈

*   **框架:** React 18.2.0
*   **路由:** `react-router-dom` 6.8.0
*   **状态管理:** `zustand` (客户端全局状态) & `react-query` (服务器状态)
*   **UI & 样式:** `tailwindcss`, `lucide-react`, `recharts`
*   **表单:** `react-hook-form` & `zod`
*   **构建:** Vite 4.2.0

## 架构特点

1.  **混合状态管理:**
    *   使用 `Zustand` 管理持久化的全局认证状态。
    *   使用 `React Query` 高效处理所有后端数据交互，包括缓存和同步。

2.  **清晰的路由系统:**
    *   在 `App.tsx` 中通过 `react-router-dom` 集中管理。
    *   实现了基于认证状态的路由保护。
    *   所有核心页面共享统一的 `Layout` 组件。

3.  **高度模块化:**
    *   遵循功能驱动的目录结构 (`pages`, `components`, `stores`, `hooks`)。
    *   拥有丰富的可重用 UI 组件库 (`components/ui`) 和布局组件 (`components/layout`)。

4.  **现代化的数据流:**
    *   API 调用通过 `useQuery` 触发，使用原生 `fetch`。
    *   认证 Token 通过 `Zustand` 的持久化状态从 `localStorage` 获取，并附加到请求头。

该架构设计健壮、现代且易于维护，为后续的功能开发和扩展奠定了坚实的基础。