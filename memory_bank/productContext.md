# 产品上下文

## 项目概述 (由 NexusCore 更新 @ 2025-07-09)

这是一个基于 **Cloudflare Workers** 构建的全栈 Web 应用程序，采用 monorepo 风格的项目结构。

### 主要组件

*   **`backend/`**: 核心后端服务，负责处理 API 请求、业务逻辑和与第三方服务（如 Google Gemini 和 Stripe）的集成。
*   **`frontend/`**: 面向用户的主要前端应用程序，使用 React 构建。
*   **`admin-panel/`**: 一个独立的管理后台，用于数据分析、用户管理和内容管理。
*   **`docs/`**: 存放项目相关的各类文档。


## 核心业务分析 (由 NexusCore 整合 @ 2025-07-09)

基于对前后端源代码的深度分析，推断出以下核心业务逻辑、功能和目标用户：

### 核心功能

1.  **AI 多媒体故事生成：**
    *   用户可通过自定义参数（角色、主题、画风），利用 Google Gemini AI 服务，一键生成包含情节、插图和音频旁白的多媒体故事。
    *   后端通过 Durable Objects 实现异步、可追踪的生成任务队列。

2.  **全面的用户与认证系统：**
    *   提供基于 Google OAuth 的安全认证流程和完整的用户个人空间。

3.  **灵活的支付与订阅系统：**
    *   集成 Stripe 支付，支持一次性购买和订阅两种商业模式。

4.  **实体书定制与销售：**
    *   提供将数字故事转化为实体书的增值服务，提升产品价值。

### 目标用户

*   **内容创作者 (核心用户):** 希望为孩子创作个性化故事的父母、教育工作者。
*   **付费订阅者 (价值用户):** 对故事创作有高频需求，愿意为高级功能付费的用户。
*   **礼品购买者/收藏者 (延伸用户):** 希望将故事作为独特礼品或纪念品的用户。
*   **平台管理员 (内部用户):** 项目的运营和管理人员。

### 核心价值主张

> **一个为家庭和创作者打造的 AI 驱动的一站式故事创作与变现平台，让用户能够轻松地将创意转化为包含精美插图和动人音频的多媒体故事，并可进一步定制为可永久珍藏的实体书籍。**
