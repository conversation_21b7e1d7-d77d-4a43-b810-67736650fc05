#!/bin/bash

# StoryWeaver Frontend Development Startup Script

echo "🚀 Starting StoryWeaver Frontend Development Server..."

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Copy environment file if it doesn't exist
if [ ! -f ".env" ]; then
    echo "📝 Creating environment file..."
    cp .env.development .env
fi

# Start development server
echo "🌟 Starting Vite development server..."
npm run dev
