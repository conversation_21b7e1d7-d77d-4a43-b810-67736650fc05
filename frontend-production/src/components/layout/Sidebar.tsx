import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  Home, 
  PlusCircle, 
  BookOpen, 
  CreditCard, 
  User, 
  Settings,
  HelpCircle,
  X,
  Crown
} from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { useUIStore } from '@/stores/uiStore';
import { cn } from '@/utils/cn';

interface SidebarProps {
  className?: string;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  requireAuth?: boolean;
  badge?: string | number;
}

const navigation: NavItem[] = [
  {
    name: '首页',
    href: '/',
    icon: Home,
  },
  {
    name: '创作故事',
    href: '/create',
    icon: PlusCircle,
    requireAuth: true,
  },
  {
    name: '我的故事',
    href: '/my-stories',
    icon: BookOpen,
    requireAuth: true,
  },
  {
    name: '定价方案',
    href: '/pricing',
    icon: CreditCard,
  },
];

const secondaryNavigation: NavItem[] = [
  {
    name: '个人资料',
    href: '/profile',
    icon: User,
    requireAuth: true,
  },
  {
    name: '订阅设置',
    href: '/subscription-settings',
    icon: Crown,
    requireAuth: true,
  },
  {
    name: '设置',
    href: '/settings',
    icon: Settings,
    requireAuth: true,
  },
  {
    name: '帮助中心',
    href: '/help',
    icon: HelpCircle,
  },
];

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation();
  const { isAuthenticated, user } = useAuthStore();
  const { sidebarOpen, setSidebarOpen } = useUIStore();

  const closeSidebar = () => {
    setSidebarOpen(false);
  };

  const isCurrentPath = (href: string) => {
    if (href === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(href);
  };

  const filteredNavigation = navigation.filter(item => 
    !item.requireAuth || isAuthenticated
  );

  const filteredSecondaryNavigation = secondaryNavigation.filter(item => 
    !item.requireAuth || isAuthenticated
  );

  return (
    <>
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full',
          className
        )}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <Link to="/" className="flex items-center space-x-2" onClick={closeSidebar}>
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gradient">StoryWeaver</span>
            </Link>
            <button
              onClick={closeSidebar}
              className="p-1 text-gray-400 hover:text-gray-600 lg:hidden"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* User info */}
          {isAuthenticated && user && (
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-primary-600" />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {user.credits} 积分
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <nav className="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {/* Primary navigation */}
            <div className="space-y-1">
              {filteredNavigation.map((item) => {
                const Icon = item.icon;
                const current = isCurrentPath(item.href);
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={closeSidebar}
                    className={cn(
                      'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                      current
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    )}
                  >
                    <Icon
                      className={cn(
                        'mr-3 h-5 w-5 flex-shrink-0',
                        current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                      )}
                    />
                    {item.name}
                    {item.badge && (
                      <span className="ml-auto bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                );
              })}
            </div>

            {/* Divider */}
            {filteredSecondaryNavigation.length > 0 && (
              <div className="border-t border-gray-200 my-4" />
            )}

            {/* Secondary navigation */}
            <div className="space-y-1">
              {filteredSecondaryNavigation.map((item) => {
                const Icon = item.icon;
                const current = isCurrentPath(item.href);
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    onClick={closeSidebar}
                    className={cn(
                      'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                      current
                        ? 'bg-primary-100 text-primary-700'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    )}
                  >
                    <Icon
                      className={cn(
                        'mr-3 h-5 w-5 flex-shrink-0',
                        current ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'
                      )}
                    />
                    {item.name}
                    {item.badge && (
                      <span className="ml-auto bg-primary-100 text-primary-600 text-xs px-2 py-0.5 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                );
              })}
            </div>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200">
            <div className="text-xs text-gray-500 text-center">
              <p>© 2024 StoryWeaver</p>
              <p className="mt-1">版本 1.0.0</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

// Sidebar toggle button for mobile
export const SidebarToggle: React.FC<{ className?: string }> = ({ className }) => {
  const { toggleSidebar } = useUIStore();

  return (
    <button
      onClick={toggleSidebar}
      className={cn(
        'p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors lg:hidden',
        className
      )}
      aria-label="Toggle sidebar"
    >
      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>
  );
};

// Sidebar with layout wrapper
export const SidebarLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col overflow-hidden lg:ml-64">
        {children}
      </div>
    </div>
  );
};
