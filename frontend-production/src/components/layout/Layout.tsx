import React from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { NotificationContainer } from '@/components/ui/NotificationContainer';
import { DebugBanner } from '@/components/debug/DebugBanner';
import { isDebugMode } from '@/utils/debug';

interface LayoutProps {
  children: React.ReactNode;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  showHeader = true,
  showFooter = true,
  className = '',
}) => {
  return (
    <div className={`min-h-screen flex flex-col ${className}`}>
      {/* Debug banner - only shows in development */}
      <DebugBanner />

      {/* Adjust top padding when debug banner is visible */}
      <div className={isDebugMode() ? 'pt-12' : ''}>
        {showHeader && <Header />}

        <ErrorBoundary>
          <main className="flex-1">
            {children}
          </main>
        </ErrorBoundary>

        {showFooter && <Footer />}
      </div>
      
      {/* Global notification container */}
      <NotificationContainer />
    </div>
  );
};

// Specialized layout variants
export const AuthLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Layout showHeader={false} showFooter={false} className="gradient-bg">
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          {children}
        </div>
      </div>
    </Layout>
  );
};

export const DashboardLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </div>
    </Layout>
  );
};

export const FullWidthLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Layout>
      <div className="w-full">
        {children}
      </div>
    </Layout>
  );
};

export const CenteredLayout: React.FC<{ 
  children: React.ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}> = ({ children, maxWidth = 'lg' }) => {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
  };

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center p-4">
        <div className={`w-full ${maxWidthClasses[maxWidth]}`}>
          {children}
        </div>
      </div>
    </Layout>
  );
};
