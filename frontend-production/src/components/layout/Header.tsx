import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Menu, X, User, LogOut, Settings, BookOpen, CreditCard } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/stores/authStore';
import { Button } from '@/components/ui/Button';
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher';

export const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, isAuthenticated, logout } = useAuthStore();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
    setIsUserMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gradient">StoryWeaver</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/create"
              className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
            >
              {t('nav.create')}
            </Link>
            <Link
              to="/my-stories"
              className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
            >
              {t('nav.myStories')}
            </Link>
            <Link
              to="/pricing"
              className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
            >
              {t('nav.pricing')}
            </Link>
          </nav>

          {/* Desktop Auth Section */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                {/* Language Switcher */}
                <LanguageSwitcher />

                {/* Credits Display */}
                <div className="flex items-center space-x-2 bg-gray-50 px-3 py-1 rounded-full">
                  <CreditCard className="w-4 h-4 text-primary-500" />
                  <span className="text-sm font-medium text-gray-800">
                    {user?.credits || 0} {t('nav.credits')}
                  </span>
                </div>

                {/* User Menu */}
                <div className="relative">
                  <button
                    onClick={toggleUserMenu}
                    className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    {user?.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                        <User className="w-4 h-4 text-primary-600" />
                      </div>
                    )}
                    <span className="text-sm font-medium text-gray-800">
                      {user?.name}
                    </span>
                  </button>

                  {/* User Dropdown */}
                  {isUserMenuOpen && (
                    <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-50">
                      <Link
                        to="/profile"
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-800 hover:bg-gray-50"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Settings className="w-4 h-4" />
                        <span>{t('nav.profile')}</span>
                      </Link>
                      <button
                        onClick={handleLogout}
                        className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-800 hover:bg-gray-50 w-full text-left"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>{t('nav.logout')}</span>
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <LanguageSwitcher />
                <Button
                  variant="outline"
                  onClick={() => navigate('/auth')}
                >
                  {t('nav.login')}
                </Button>
                <Button
                  onClick={() => navigate('/auth')}
                >
                  {t('nav.startCreating')}
                </Button>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={toggleMobileMenu}
              className="p-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6 text-gray-600" />
              ) : (
                <Menu className="w-6 h-6 text-gray-700" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-100 py-4">
            <nav className="flex flex-col space-y-4">
              <Link
                to="/create"
                className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
                onClick={closeMobileMenu}
              >
                {t('nav.create')}
              </Link>
              <Link
                to="/my-stories"
                className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
                onClick={closeMobileMenu}
              >
                {t('nav.myStories')}
              </Link>
              <Link
                to="/pricing"
                className="text-gray-800 hover:text-primary-600 font-medium transition-colors"
                onClick={closeMobileMenu}
              >
                {t('nav.pricing')}
              </Link>

              {isAuthenticated ? (
                <div className="pt-4 border-t border-gray-100">
                  <div className="flex items-center space-x-3 mb-4">
                    {user?.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-primary-600" />
                      </div>
                    )}
                    <div>
                      <p className="font-medium text-gray-900">{user?.name}</p>
                      <p className="text-sm text-gray-700">{user?.credits || 0} {t('nav.credits')}</p>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Link
                      to="/profile"
                      className="flex items-center space-x-2 text-gray-800 hover:text-primary-600 transition-colors"
                      onClick={closeMobileMenu}
                    >
                      <Settings className="w-4 h-4" />
                      <span>{t('nav.profile')}</span>
                    </Link>
                    <button
                      onClick={handleLogout}
                      className="flex items-center space-x-2 text-gray-800 hover:text-primary-600 transition-colors text-left"
                    >
                      <LogOut className="w-4 h-4" />
                      <span>{t('nav.logout')}</span>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="pt-4 border-t border-gray-100 flex flex-col space-y-3">
                  <div className="mb-3">
                    <LanguageSwitcher />
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => {
                      navigate('/auth');
                      closeMobileMenu();
                    }}
                    className="w-full"
                  >
                    {t('nav.login')}
                  </Button>
                  <Button
                    onClick={() => {
                      navigate('/auth');
                      closeMobileMenu();
                    }}
                    className="w-full"
                  >
                    {t('nav.startCreating')}
                  </Button>
                </div>
              )}
            </nav>
          </div>
        )}
      </div>

      {/* Overlay for mobile menu */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-25 z-40 md:hidden"
          onClick={closeMobileMenu}
        />
      )}

      {/* Overlay for user menu */}
      {isUserMenuOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsUserMenuOpen(false)}
        />
      )}
    </header>
  );
};
