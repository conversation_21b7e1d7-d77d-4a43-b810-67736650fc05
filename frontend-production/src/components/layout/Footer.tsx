import React from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { BookOpen, Mail, Heart } from 'lucide-react';

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-50 border-t border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gradient">StoryWeaver</span>
            </div>
            <p className="text-gray-800 mb-4 max-w-md">
              {t('footer.description')}
            </p>
            <div className="flex items-center space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="flex items-center space-x-2 text-gray-800 hover:text-primary-600 transition-colors"
              >
                <Mail className="w-4 h-4" />
                <span className="text-sm"><EMAIL></span>
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">{t('footer.product')}</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/create"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('nav.create')}
                </Link>
              </li>
              <li>
                <Link
                  to="/my-stories"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('nav.myStories')}
                </Link>
              </li>
              <li>
                <Link
                  to="/pricing"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('nav.pricing')}
                </Link>
              </li>
              <li>
                <Link
                  to="/physical-books"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('footer.physicalBooks')}
                </Link>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">{t('footer.support')}</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/help"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('footer.helpCenter')}
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('footer.contact')}
                </Link>
              </li>
              <li>
                <Link
                  to="/faq"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('footer.faq')}
                </Link>
              </li>
              <li>
                <Link
                  to="/tutorials"
                  className="text-gray-800 hover:text-primary-600 transition-colors text-sm"
                >
                  {t('footer.tutorials')}
                </Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6">
              <p className="text-sm text-gray-700">
                © {currentYear} StoryWeaver. {t('footer.copyright')}
              </p>
              <div className="flex items-center space-x-4">
                <Link
                  to="/privacy"
                  className="text-sm text-gray-700 hover:text-primary-600 transition-colors"
                >
                  {t('footer.privacy')}
                </Link>
                <Link
                  to="/terms"
                  className="text-sm text-gray-700 hover:text-primary-600 transition-colors"
                >
                  {t('footer.terms')}
                </Link>
                <Link
                  to="/cookies"
                  className="text-sm text-gray-700 hover:text-primary-600 transition-colors"
                >
                  {t('footer.cookies')}
                </Link>
              </div>
            </div>

            <div className="flex items-center space-x-2 text-sm text-gray-700">
              <span>{t('footer.madeWith')}</span>
              <Heart className="w-4 h-4 text-red-500" />
              <span>{t('footer.madeWithLove')}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
