import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Settings, 
  User, 
  CreditCard, 
  RefreshCw, 
  ChevronDown, 
  ChevronUp,
  Plus,
  Minus
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useAuthStore } from '@/stores/authStore';
import { 
  isDebugMode, 
  debugActions, 
  DEBUG_USER_TYPES,
  debugLog 
} from '@/utils/debug';

export const DebugBanner: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { user, isAuthenticated, setDebugUser, addDebugCredits, resetDebugState } = useAuthStore();

  // Only show in debug mode
  if (!isDebugMode()) {
    return null;
  }

  const handleUserTypeChange = (userType: keyof typeof DEBUG_USER_TYPES) => {
    setDebugUser(userType);
    debugLog.info(`Switched to ${userType} user`);
  };

  const handleAddCredits = (amount: number) => {
    addDebugCredits(amount);
    debugLog.info(`Added ${amount} credits`);
  };

  const handleReset = () => {
    resetDebugState();
    debugLog.info('Reset debug state');
  };

  return (
    <motion.div
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-orange-500 to-amber-500 text-white shadow-lg"
    >
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between py-2">
          {/* Main debug info */}
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span className="font-medium text-sm">🔧 调试模式</span>
            </div>
            
            {isAuthenticated && user && (
              <>
                <div className="flex items-center space-x-1 text-sm">
                  <User className="w-3 h-3" />
                  <span>{user.name}</span>
                </div>
                
                <div className="flex items-center space-x-1 text-sm">
                  <CreditCard className="w-3 h-3" />
                  <span>{user.credits} 积分</span>
                </div>
                
                <div className="text-xs bg-white/20 px-2 py-1 rounded">
                  {(user as any).subscriptionPlan || 'free'}
                </div>
              </>
            )}
          </div>

          {/* Controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-white hover:bg-white/20 h-8 px-2"
            >
              {isExpanded ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>

        {/* Expanded controls */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-t border-white/20 py-3"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* User Type Controls */}
                <Card className="p-3 bg-white/10 border-white/20">
                  <h4 className="text-sm font-medium mb-2 text-white">用户类型</h4>
                  <div className="grid grid-cols-2 gap-1">
                    {Object.entries(DEBUG_USER_TYPES).map(([key, userType]) => (
                      <Button
                        key={key}
                        variant="ghost"
                        size="sm"
                        onClick={() => handleUserTypeChange(key as keyof typeof DEBUG_USER_TYPES)}
                        className={`text-xs h-8 ${
                          (user as any)?.subscriptionPlan === userType.subscription
                            ? 'bg-white/30 text-white'
                            : 'text-white/80 hover:bg-white/20'
                        }`}
                      >
                        {userType.name}
                      </Button>
                    ))}
                  </div>
                </Card>

                {/* Credits Controls */}
                <Card className="p-3 bg-white/10 border-white/20">
                  <h4 className="text-sm font-medium mb-2 text-white">积分操作</h4>
                  <div className="flex items-center space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAddCredits(-50)}
                      className="text-white hover:bg-white/20 h-8 px-2"
                      disabled={!user || user.credits <= 0}
                    >
                      <Minus className="w-3 h-3" />
                      50
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAddCredits(100)}
                      className="text-white hover:bg-white/20 h-8 px-2"
                    >
                      <Plus className="w-3 h-3" />
                      100
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleAddCredits(500)}
                      className="text-white hover:bg-white/20 h-8 px-2"
                    >
                      <Plus className="w-3 h-3" />
                      500
                    </Button>
                  </div>
                </Card>

                {/* System Controls */}
                <Card className="p-3 bg-white/10 border-white/20">
                  <h4 className="text-sm font-medium mb-2 text-white">系统操作</h4>
                  <div className="flex space-x-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleReset}
                      className="text-white hover:bg-white/20 h-8 px-2 text-xs"
                    >
                      <RefreshCw className="w-3 h-3 mr-1" />
                      重置
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        localStorage.clear();
                        window.location.reload();
                      }}
                      className="text-white hover:bg-white/20 h-8 px-2 text-xs"
                    >
                      清除缓存
                    </Button>
                  </div>
                </Card>
              </div>
              
              <div className="mt-3 text-xs text-white/70">
                💡 提示：调试模式仅在开发环境中可用，生产构建会自动移除所有调试代码
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
};

export default DebugBanner;
