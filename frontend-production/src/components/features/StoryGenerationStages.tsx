import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Settings,
  FileText,
  Image,
  Volume2,
  VolumeX,
  Sparkles,
  CheckCircle,
  Loader2,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { StoryGenerationProgress, STORY_GENERATION_STAGES, StoryStatus, Story } from '@/types/story';
import { cn } from '@/utils/cn';

interface StoryGenerationStagesProps {
  storyStatus: StoryStatus;
  progress?: StoryGenerationProgress;
  story?: Story; // 添加story参数以获取skipAudio信息
  onPreviewContent?: (type: 'text' | 'image' | 'audio', taskId: string) => void;
  onRetry?: () => void; // 添加重试回调
  className?: string;
}

// 图标映射
const iconMap = {
  Settings,
  FileText,
  Image,
  Volume2,
  Sparkles,
  CheckCircle
};

export const StoryGenerationStages: React.FC<StoryGenerationStagesProps> = ({
  storyStatus,
  progress,
  story,
  onPreviewContent,
  onRetry,
  className
}) => {
  const [timeoutWarning, setTimeoutWarning] = useState(false);
  const [elapsedTime, setElapsedTime] = useState(0);

  // 超时检查 - 10分钟阈值
  useEffect(() => {
    if (!story || !['generating_text', 'generating_images', 'generating_audio'].includes(storyStatus)) {
      setTimeoutWarning(false);
      setElapsedTime(0);
      return;
    }

    // 使用当前时间作为基准，而不是story.updatedAt（可能是很久以前的时间）
    const startTime = Date.now();
    const timeoutThreshold = 10 * 60 * 1000; // 10分钟

    console.log(`⏰ [时间监控] 开始监控故事生成时间，状态: ${storyStatus}, 开始时间: ${new Date(startTime).toISOString()}`);

    const timer = setInterval(() => {
      const now = Date.now();
      const elapsed = now - startTime;
      setElapsedTime(elapsed);

      // 每30秒记录一次时间进度
      if (elapsed % 30000 < 1000) {
        console.log(`⏰ [时间监控] 已运行: ${Math.floor(elapsed / 1000)}秒 (${Math.floor(elapsed / 60000)}:${Math.floor((elapsed % 60000) / 1000).toString().padStart(2, '0')})`);
      }

      if (elapsed > timeoutThreshold) {
        console.warn(`⚠️ [时间监控] 生成时间超过阈值: ${Math.floor(elapsed / 60000)}分钟`);
        setTimeoutWarning(true);
      }
    }, 1000);

    return () => {
      console.log(`⏰ [时间监控] 停止监控，状态变更为: ${storyStatus}`);
      clearInterval(timer);
    };
  }, [story, storyStatus]);

  // 格式化时间显示
  const formatElapsedTime = (ms: number) => {
    // 防止负数时间
    if (ms < 0) {
      console.warn(`⚠️ [时间格式化] 检测到负数时间: ${ms}ms，重置为0`);
      return '0:00';
    }

    const totalSeconds = Math.floor(ms / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // 如果超过1小时，显示小时
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
  };
  // 根据当前状态计算整体进度
  const calculateOverallProgress = () => {
    if (progress?.progress) {
      return progress.progress;
    }
    
    // 根据状态估算进度
    switch (storyStatus) {
      case 'preparing': return 5;
      case 'generating_text': return 25;
      case 'generating_images': return 55;
      case 'generating_audio': return 80;
      case 'composing': return 95;
      case 'completed': return 100;
      case 'failed': return 0;
      default: return 0;
    }
  };

  // 获取当前阶段索引
  const getCurrentStageIndex = () => {
    const stageMap = {
      'preparing': 0,
      'generating_text': 1,
      'generating_images': 2,
      'generating_audio': 3,
      'composing': 4,
      'completed': 5
    };
    return stageMap[storyStatus as keyof typeof stageMap] ?? 0;
  };

  const overallProgress = calculateOverallProgress();
  const currentStageIndex = getCurrentStageIndex();

  return (
    <div className={cn('w-full', className)}>
      {/* 整体进度条 */}
      <Card className="p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              故事生成进度
            </h3>
            <p className="text-sm text-gray-600">
              {progress?.currentStep || STORY_GENERATION_STAGES[currentStageIndex]?.description || '正在处理中...'}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-primary-600">
              {overallProgress}%
            </div>
            {progress?.estimatedTimeRemaining && progress.estimatedTimeRemaining > 0 && (
              <div className="text-xs text-gray-500 flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                预计剩余 {Math.ceil(progress.estimatedTimeRemaining / 60)} 分钟
              </div>
            )}
          </div>
        </div>
        
        {/* 进度条 */}
        <div className="w-full bg-gray-200 rounded-full h-3">
          <motion.div
            className="bg-gradient-to-r from-primary-500 to-secondary-500 h-3 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${overallProgress}%` }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          />
        </div>

        {/* 超时警告 */}
        {timeoutWarning && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg"
          >
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-orange-600 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-orange-800">
                  生成时间较长
                </h4>
                <p className="text-sm text-orange-700 mt-1">
                  当前任务已运行 {formatElapsedTime(elapsedTime)}，超过了预期时间（10分钟）。
                  这可能是由于网络延迟或服务繁忙导致的。
                </p>
                <div className="mt-3 flex space-x-3">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => window.location.reload()}
                    className="text-orange-700 border-orange-300 hover:bg-orange-100"
                  >
                    刷新页面
                  </Button>
                  {onRetry && (
                    <Button
                      size="sm"
                      onClick={onRetry}
                      className="bg-orange-600 hover:bg-orange-700 text-white"
                    >
                      重新生成
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </Card>

      {/* 6阶段详细进度 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {STORY_GENERATION_STAGES.map((stage, index) => {
          const IconComponent = iconMap[stage.icon as keyof typeof iconMap];
          const isCompleted = index < currentStageIndex;
          const isCurrent = index === currentStageIndex && storyStatus !== 'completed' && storyStatus !== 'failed';
          const isUpcoming = index > currentStageIndex;
          const isFailed = storyStatus === 'failed' && index === currentStageIndex;

          // 检查是否跳过音频生成
          const isAudioStage = stage.id === 'generating_audio';
          const isAudioSkipped = isAudioStage && (
            !story?.voice ||
            story?.voice === '' ||
            (story as any)?.skipAudio === true
          );

          return (
            <motion.div
              key={stage.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card className={cn(
                'p-4 transition-all duration-300',
                {
                  'ring-2 ring-primary-500 bg-primary-50': isCurrent && !isAudioSkipped,
                  'bg-green-50 border-green-200': isCompleted,
                  'bg-gray-50': isUpcoming,
                  'bg-red-50 border-red-200': isFailed,
                  'bg-orange-50 border-orange-200': isAudioSkipped
                }
              )}>
                <div className="flex items-start space-x-3">
                  {/* 图标 */}
                  <div className={cn(
                    'w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0',
                    {
                      'bg-primary-100 text-primary-600': isCurrent && !isAudioSkipped,
                      'bg-green-100 text-green-600': isCompleted,
                      'bg-gray-100 text-gray-400': isUpcoming,
                      'bg-red-100 text-red-600': isFailed,
                      'bg-orange-100 text-orange-600': isAudioSkipped
                    }
                  )}>
                    {isFailed ? (
                      <AlertCircle className="w-5 h-5" />
                    ) : isAudioSkipped ? (
                      <VolumeX className="w-5 h-5" />
                    ) : isCompleted ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : isCurrent ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <IconComponent className="w-5 h-5" />
                    )}
                  </div>

                  {/* 内容 */}
                  <div className="flex-1 min-w-0">
                    <h4 className={cn(
                      'font-medium text-sm mb-1',
                      {
                        'text-primary-900': isCurrent && !isAudioSkipped,
                        'text-green-900': isCompleted,
                        'text-gray-600': isUpcoming,
                        'text-red-900': isFailed,
                        'text-orange-900': isAudioSkipped
                      }
                    )}>
                      {isAudioSkipped ? '跳过配音' : stage.name}
                    </h4>
                    <p className={cn(
                      'text-xs leading-relaxed',
                      {
                        'text-primary-700': isCurrent && !isAudioSkipped,
                        'text-green-700': isCompleted,
                        'text-gray-500': isUpcoming,
                        'text-red-700': isFailed,
                        'text-orange-700': isAudioSkipped
                      }
                    )}>
                      {isFailed ? '生成失败，请重试' :
                       isAudioSkipped ? '用户选择跳过语音生成，故事将不包含朗读音频' :
                       stage.description}
                    </p>
                    
                    {/* 状态标签 */}
                    <div className="mt-2">
                      <span className={cn(
                        'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                        {
                          'bg-primary-100 text-primary-800': isCurrent && !isAudioSkipped,
                          'bg-green-100 text-green-800': isCompleted,
                          'bg-gray-100 text-gray-600': isUpcoming,
                          'bg-red-100 text-red-800': isFailed,
                          'bg-orange-100 text-orange-800': isAudioSkipped
                        }
                      )}>
                        {isFailed ? '失败' :
                         isAudioSkipped ? '已跳过' :
                         isCompleted ? '已完成' :
                         isCurrent ? '进行中' : '等待中'}
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* 错误信息显示 */}
      {storyStatus === 'failed' && progress?.error && (
        <Card className="mt-6 p-4 bg-red-50 border-red-200">
          <div className="flex items-start space-x-3">
            <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0 mt-0.5" />
            <div>
              <h4 className="font-medium text-red-900 mb-1">生成失败</h4>
              <p className="text-sm text-red-700">{progress.error}</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
