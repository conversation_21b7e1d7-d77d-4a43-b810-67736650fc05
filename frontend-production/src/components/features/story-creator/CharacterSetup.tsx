import React, { useState, useEffect, useCallback } from 'react';
import { User, Heart, Star, Zap, Coins } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { CreditBalance } from '@/components/ui/CreditBalance';
import { CharacterPreview } from '@/components/features/CharacterPreview';
import { CreateStoryRequest } from '@/types';
import { useAuthStore } from '@/stores/authStore';
import { cn } from '@/utils/cn';

interface CharacterSetupProps {
  data: Partial<CreateStoryRequest>;
  onChange: (updates: Partial<CreateStoryRequest>) => void;
  onValidationChange: (isValid: boolean) => void;
}

const characterTraits = [
  { id: 'brave', icon: '🦁', category: 'personality' },
  { id: 'kind', icon: '💝', category: 'personality' },
  { id: 'curious', icon: '🔍', category: 'personality' },
  { id: 'funny', icon: '😄', category: 'personality' },
  { id: 'smart', icon: '🧠', category: 'personality' },
  { id: 'creative', icon: '🎨', category: 'personality' },
  { id: 'adventurous', icon: '🗺️', category: 'personality' },
  { id: 'helpful', icon: '🤝', category: 'personality' },
  { id: 'musical', icon: '🎵', category: 'ability' },
  { id: 'athletic', icon: '⚽', category: 'ability' },
  { id: 'artistic', icon: '🖌️', category: 'ability' },
  { id: 'magical', icon: '✨', category: 'ability' },
  { id: 'animals', icon: '🐾', category: 'interest' },
  { id: 'nature', icon: '🌿', category: 'interest' },
  { id: 'books', icon: '📚', category: 'interest' },
  { id: 'science', icon: '🔬', category: 'interest' },
];

const ageRanges = [3, 4, 5, 6, 7, 8];

export const CharacterSetup: React.FC<CharacterSetupProps> = ({
  data,
  onChange,
  onValidationChange,
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const [selectedTraits, setSelectedTraits] = useState<string[]>(data.characterTraits || []);
  const [characterName, setCharacterName] = useState(data.characterName || '');
  const [characterAge, setCharacterAge] = useState(data.characterAge || 5);
  const [characterGender, setCharacterGender] = useState(data.characterGender || 'neutral');
  const [characterImageUrl, setCharacterImageUrl] = useState<string | null>(data.characterImageUrl || null);
  const [showCharacterPreview, setShowCharacterPreview] = useState(false);

  useEffect(() => {
    const isValid = !!(characterName.trim() && characterAge && selectedTraits.length > 0);
    onValidationChange(isValid);

    if (isValid) {
      onChange({
        characterName: characterName.trim(),
        characterAge,
        characterTraits: selectedTraits,
        characterGender,
        characterImageUrl,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [characterName, characterAge, selectedTraits, characterGender, characterImageUrl]); // 移除函数依赖以避免无限循环

  // 处理角色形象生成完成
  const handleCharacterGenerated = useCallback((imageUrl: string) => {
    setCharacterImageUrl(imageUrl);
  }, []);

  // 检查是否可以显示角色预览
  const canShowPreview = characterName.trim() && characterAge && selectedTraits.length > 0;

  const handleTraitToggle = (traitId: string) => {
    setSelectedTraits(prev => {
      if (prev.includes(traitId)) {
        return prev.filter(id => id !== traitId);
      } else if (prev.length < 5) { // 最多选择5个特征
        return [...prev, traitId];
      }
      return prev;
    });
  };

  const getTraitsByCategory = (category: string) => {
    return characterTraits.filter(trait => trait.category === category);
  };

  return (
    <div className="space-y-8">
      {/* Character Name */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <User className="w-5 h-5 text-primary-500 mr-2" />
          {t('create.character.characterName')}
        </h3>
        <Input
          label={t('create.character.characterNamePlaceholder')}
          placeholder={t('create.character.characterNamePlaceholder')}
          value={characterName}
          onChange={(e) => setCharacterName(e.target.value)}
          helperText={t('create.character.characterNameHelper', '这个名字将贯穿整个故事')}
          maxLength={20}
        />
      </div>

      {/* Character Age */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Star className="w-5 h-5 text-primary-500 mr-2" />
          {t('create.character.characterAge')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('create.character.ageDescription', '选择角色年龄将影响故事的语言复杂度和内容深度')}
        </p>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {ageRanges.map((age) => (
            <Card
              key={age}
              className={cn(
                'cursor-pointer transition-all duration-200 hover:shadow-md',
                characterAge === age
                  ? 'ring-2 ring-primary-500 bg-primary-50'
                  : 'hover:bg-gray-50'
              )}
              onClick={() => setCharacterAge(age)}
              padding="sm"
            >
              <div className="text-center">
                <div className="text-lg font-semibold text-gray-900 mb-1">
                  {t('create.character.ageLabel', { age })}
                </div>
                <div className="text-xs text-gray-500">
                  {t(`create.character.ageDescriptions.${age}`)}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Character Gender */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <User className="w-5 h-5 text-primary-500 mr-2" />
          角色性别
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          选择角色的性别，这将影响生成的角色形象
        </p>
        <div className="grid grid-cols-3 gap-3">
          {[
            { id: 'male', label: '男孩', icon: '👦', description: '男性角色' },
            { id: 'female', label: '女孩', icon: '👧', description: '女性角色' },
            { id: 'neutral', label: '中性', icon: '🧒', description: '中性角色' }
          ].map((gender) => (
            <Card
              key={gender.id}
              className={cn(
                'cursor-pointer transition-all duration-200 hover:shadow-md',
                characterGender === gender.id
                  ? 'ring-2 ring-primary-500 bg-primary-50'
                  : 'hover:bg-gray-50'
              )}
              onClick={() => setCharacterGender(gender.id)}
              padding="sm"
            >
              <div className="text-center">
                <div className="text-2xl mb-1">{gender.icon}</div>
                <div className="text-sm font-medium text-gray-900 mb-1">
                  {gender.label}
                </div>
                <div className="text-xs text-gray-500">
                  {gender.description}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Character Traits */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Heart className="w-5 h-5 text-primary-500 mr-2" />
          {t('create.character.characterTraits')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('create.character.selectTraitsDescription', { selected: selectedTraits.length, max: 5 })}
        </p>

        {/* Personality Traits */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3">{t('create.character.personalityTraits', '性格特点')}</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {getTraitsByCategory('personality').map((trait) => (
              <Card
                key={trait.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md',
                  selectedTraits.includes(trait.id)
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50',
                  selectedTraits.length >= 5 && !selectedTraits.includes(trait.id) && 'opacity-50 cursor-not-allowed'
                )}
                onClick={() => handleTraitToggle(trait.id)}
                padding="sm"
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{trait.icon}</div>
                  <div className="text-sm font-medium text-gray-900">
                    {t(`create.character.traits.${trait.id}`)}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Abilities */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3">{t('create.character.abilities', '特殊能力')}</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {getTraitsByCategory('ability').map((trait) => (
              <Card
                key={trait.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md',
                  selectedTraits.includes(trait.id)
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50',
                  selectedTraits.length >= 5 && !selectedTraits.includes(trait.id) && 'opacity-50 cursor-not-allowed'
                )}
                onClick={() => handleTraitToggle(trait.id)}
                padding="sm"
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{trait.icon}</div>
                  <div className="text-sm font-medium text-gray-900">
                    {t(`create.character.traits.${trait.id}`)}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Interests */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">{t('create.character.interests', '兴趣爱好')}</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {getTraitsByCategory('interest').map((trait) => (
              <Card
                key={trait.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md',
                  selectedTraits.includes(trait.id)
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50',
                  selectedTraits.length >= 5 && !selectedTraits.includes(trait.id) && 'opacity-50 cursor-not-allowed'
                )}
                onClick={() => handleTraitToggle(trait.id)}
                padding="sm"
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{trait.icon}</div>
                  <div className="text-sm font-medium text-gray-900">
                    {t(`create.character.traits.${trait.id}`)}
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* 简单角色预览 */}
      {characterName && selectedTraits.length > 0 && !showCharacterPreview && (
        <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
          <div className="flex items-start space-x-4">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
              <User className="w-8 h-8 text-primary-600" />
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                {t('create.character.characterPreview', '角色预览')}
              </h4>
              <p className="text-gray-700 mb-2">
                <strong>{characterName}</strong>，{t('create.character.ageLabel', { age: characterAge })}
              </p>
              <div className="flex flex-wrap gap-2 mb-3">
                {selectedTraits.map(traitId => {
                  const trait = characterTraits.find(t => t.id === traitId);
                  return trait ? (
                    <span
                      key={traitId}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                    >
                      <span className="mr-1">{trait.icon}</span>
                      {t(`create.character.traits.${trait.id}`)}
                    </span>
                  ) : null;
                })}
              </div>

              {canShowPreview && (
                <div className="mt-4 space-y-3">
                  {/* 积分消耗提示 */}
                  <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                    <Coins className="w-4 h-4 text-amber-500" />
                    <span>生成角色形象需要消耗 1 积分</span>
                  </div>

                  {/* 积分余额显示 */}
                  {user && (
                    <div className="flex justify-center">
                      <CreditBalance
                        current={user.credits}
                        required={1}
                        size="sm"
                        showPurchaseButton={user.credits < 1}
                        onPurchaseClick={() => window.open('/pricing', '_blank')}
                      />
                    </div>
                  )}

                  {/* 生成按钮 */}
                  <div className="flex justify-center">
                    <button
                      onClick={() => setShowCharacterPreview(true)}
                      disabled={user && user.credits < 1}
                      className={cn(
                        "px-6 py-2 rounded-md font-medium transition-colors",
                        user && user.credits >= 1
                          ? "bg-primary-500 text-white hover:bg-primary-600"
                          : "bg-gray-300 text-gray-500 cursor-not-allowed"
                      )}
                    >
                      生成角色形象
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* 角色形象生成预览 */}
      {showCharacterPreview && (
        <CharacterPreview
          characterName={characterName}
          characterAge={characterAge}
          characterTraits={selectedTraits}
          style="anime" // 限制为动漫风格
          gender={characterGender} // 传递性别参数
          onCharacterGenerated={handleCharacterGenerated}
          className="mt-6"
        />
      )}
    </div>
  );
};
