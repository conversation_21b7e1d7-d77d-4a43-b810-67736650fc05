import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import {
  FileText,
  Image,
  Volume2,
  CheckCircle,
  AlertCircle,
  Loader2,
  <PERSON>rk<PERSON>,
  Clock
} from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { ProgressBar } from '@/components/ui/LoadingSpinner';
import { StoryGenerationProgress, CreateStoryRequest } from '@/types';
import { cn } from '@/utils/cn';
import { useNotifications } from '@/stores/uiStore';

interface GenerationProgressProps {
  progress: StoryGenerationProgress | null;
  storyData?: CreateStoryRequest;
}

const generationSteps = [
  {
    id: 'generating_text',
    name: '创作故事文本',
    description: 'AI正在根据您的设定创作精彩的故事内容...',
    icon: FileText,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
  },
  {
    id: 'generating_images',
    name: '绘制精美插图',
    description: 'AI正在为故事绘制生动的插图...',
    icon: Image,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
  },
  {
    id: 'generating_audio',
    name: '合成语音朗读',
    description: 'AI正在生成温暖的语音朗读...',
    icon: Volume2,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
  },
];

const motivationalMessages = [
  '正在为您的小主角编织奇妙的冒险...',
  '每一个细节都在精心雕琢中...',
  '故事的魔法正在发生...',
  '即将为您呈现独一无二的作品...',
  '最后的润色让故事更加完美...',
];

export const GenerationProgress: React.FC<GenerationProgressProps> = ({
  progress,
  storyData,
}) => {
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const [currentMessage, setCurrentMessage] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  useEffect(() => {
    // 轮换激励消息
    const messageInterval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % motivationalMessages.length);
    }, 3000);

    // 计时器
    const timeInterval = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => {
      clearInterval(messageInterval);
      clearInterval(timeInterval);
    };
  }, []);

  // 监听生成完成状态
  useEffect(() => {
    if (!progress) return;

    // 如果生成完成，自动跳转到故事详情页面
    if (progress.stage === 'completed' && progress.storyId) {
      showSuccess('故事生成完成', '您的专属故事已经准备好了！');
      // 延迟一秒后跳转，让用户看到完成状态
      setTimeout(() => {
        navigate(`/stories/${progress.storyId}`);
      }, 1000);
    }

    // 如果生成失败，显示错误信息
    if (progress.error) {
      showError('故事生成失败', progress.error);
    }
  }, [progress, navigate, showSuccess, showError]);

  const getOverallProgress = () => {
    if (!progress) return 0;
    
    return progress.progress;
  };

  const getCurrentStep = () => {
    if (!progress) return 0;

    switch (progress.stage) {
      case 'preparing': return 0;
      case 'generating_text': return 0;
      case 'generating_images': return 1;
      case 'generating_audio': return 2;
      case 'composing': return 2;
      case 'completed': return 3;
      default: return 0;
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStepStatus = (stepId: string) => {
    if (!progress) return 'pending';

    // 检查是否有progress.progress对象（新格式）
    if (progress.progress && typeof progress.progress === 'object') {
      if (progress.progress && progress.progress[stepId as keyof typeof progress.progress]) {
        return 'completed';
      }
    }

    // 根据stage判断当前状态（兼容旧格式）
    const currentStepIndex = getCurrentStep();
    const stepIndex = generationSteps.findIndex(step => step.id === stepId);

    if (stepIndex < currentStepIndex) {
      return 'completed';
    } else if (stepIndex === currentStepIndex) {
      return 'active';
    }

    return 'pending';
  };

  if (progress?.error) {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="text-center border-red-200 bg-red-50">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="text-xl font-bold text-red-900 mb-2">
            创作过程中遇到了问题
          </h3>
          <p className="text-red-700 mb-6">
            {progress.error || '很抱歉，AI创作过程中出现了意外错误，请稍后重试。'}
          </p>
          <div className="flex justify-center space-x-3">
            <button className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              重新创作
            </button>
            <button className="px-6 py-2 border border-red-300 text-red-700 rounded-lg hover:bg-red-100 transition-colors">
              返回编辑
            </button>
          </div>
        </Card>
      </div>
    );
  }

  if (progress?.stage === 'completed') {
    return (
      <div className="max-w-2xl mx-auto">
        <Card className="text-center border-green-200 bg-green-50">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", duration: 0.6 }}
            className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4"
          >
            <CheckCircle className="w-8 h-8 text-green-600" />
          </motion.div>
          <h3 className="text-xl font-bold text-green-900 mb-2">
            🎉 故事创作完成！
          </h3>
          <p className="text-green-700 mb-6">
            您的专属故事《{storyData.characterName}的冒险》已经创作完成，
            快去欣赏这个独一无二的作品吧！
          </p>
          <button className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
            查看我的故事
          </button>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <Card className="text-center mb-8 bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <Sparkles className="w-8 h-8 text-primary-600" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          正在为您创作专属故事
        </h2>
        <p className="text-gray-600 mb-4">
          《{storyData.characterName}的{storyData.theme}》
        </p>
        
        {/* Overall Progress */}
        <div className="max-w-md mx-auto mb-4">
          <ProgressBar
            value={getOverallProgress()}
            max={100}
            showLabel
            label="总体进度"
            className="mb-2"
          />
        </div>
        
        {/* Time and Message */}
        <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>已用时 {formatTime(elapsedTime)}</span>
          </div>
          {progress?.estimatedTimeRemaining && (
            <div>
              <span>预计还需 {Math.ceil(progress.estimatedTimeRemaining / 60)} 分钟</span>
            </div>
          )}
        </div>
      </Card>

      {/* Generation Steps */}
      <div className="space-y-6 mb-8">
        {generationSteps.map((step, index) => {
          const status = getStepStatus(step.id);
          const Icon = step.icon;
          
          return (
            <motion.div
              key={step.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.2 }}
            >
              <Card className={cn(
                'transition-all duration-300',
                status === 'active' && 'ring-2 ring-primary-500 shadow-lg',
                status === 'completed' && 'border-green-200 bg-green-50'
              )}>
                <div className="flex items-center space-x-4">
                  <div className={cn(
                    'w-12 h-12 rounded-lg flex items-center justify-center',
                    status === 'completed' ? 'bg-green-100' : step.bgColor
                  )}>
                    {status === 'completed' ? (
                      <CheckCircle className="w-6 h-6 text-green-600" />
                    ) : status === 'active' ? (
                      <Loader2 className={cn('w-6 h-6 animate-spin', step.color)} />
                    ) : (
                      <Icon className={cn('w-6 h-6', 
                        status === 'pending' ? 'text-gray-400' : step.color
                      )} />
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className={cn(
                      'font-semibold mb-1',
                      status === 'completed' ? 'text-green-900' : 'text-gray-900'
                    )}>
                      {step.name}
                      {status === 'completed' && (
                        <span className="ml-2 text-sm text-green-600">✓ 已完成</span>
                      )}
                      {status === 'active' && (
                        <span className="ml-2 text-sm text-primary-600">进行中...</span>
                      )}
                    </h4>
                    <p className={cn(
                      'text-sm',
                      status === 'completed' ? 'text-green-700' : 
                      status === 'active' ? 'text-gray-700' : 'text-gray-500'
                    )}>
                      {step.description}
                    </p>
                  </div>
                  
                  {status === 'active' && (
                    <div className="text-right">
                      <div className="w-8 h-8 border-2 border-primary-200 border-t-primary-500 rounded-full animate-spin" />
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Motivational Message */}
      <Card className="text-center bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
        <motion.div
          key={currentMessage}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.5 }}
        >
          <p className="text-purple-700 font-medium">
            {motivationalMessages[currentMessage]}
          </p>
        </motion.div>
      </Card>

      {/* Tips */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">
          💡 小贴士：创作过程中请保持页面开启，这样您就能第一时间看到精彩的故事了！
        </p>
      </div>
    </div>
  );
};
