import React, { useEffect } from 'react';
import { User, MapPin, <PERSON>lette, Volume2, <PERSON><PERSON><PERSON>, <PERSON>, CreditCard, Coins } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import i18n from '@/i18n';
import { Card } from '@/components/ui/Card';
import { CreditBalance } from '@/components/ui/CreditBalance';
import { CreateStoryRequest } from '@/types';
import { useAuthStore } from '@/stores/authStore';

interface StoryPreviewProps {
  data: CreateStoryRequest;
  onValidationChange: (isValid: boolean) => void;
}

const estimatedCosts = {
  text: 5,
  images: 15,
  audio: 10,
  total: 30,
};

const getEstimatedTime = (t: any) => ({
  text: t('create.preview.estimatedTimes.text', '1-2分钟'),
  images: t('create.preview.estimatedTimes.images', '3-5分钟'),
  audio: t('create.preview.estimatedTimes.audio', '2-3分钟'),
  total: t('create.preview.estimatedTimes.total', '6-10分钟'),
});

export const StoryPreview: React.FC<StoryPreviewProps> = ({
  data,
  onValidationChange,
}) => {
  const { user } = useAuthStore();
  const { t } = useTranslation();

  useEffect(() => {
    // 根据是否跳过音频计算所需积分
    const requiredCredits = data.skipAudio ?
      estimatedCosts.text + estimatedCosts.images :
      estimatedCosts.total;
    const hasEnoughCredits = user && user.credits >= requiredCredits;
    onValidationChange(!!hasEnoughCredits);
  }, [user, data.skipAudio, onValidationChange]);

  const requiredCredits = data.skipAudio ?
    estimatedCosts.text + estimatedCosts.images :
    estimatedCosts.total;
  const hasEnoughCredits = user && user.credits >= requiredCredits;
  const estimatedTime = getEstimatedTime(t);

  // 使用翻译系统获取选项名称
  const getVoiceName = (voice: string | undefined) => {
    if (!voice || data.skipAudio) {
      return t('create.preview.noVoice', '不配置朗读声音');
    }
    return t(`create.style.voices.${voice}`, voice);
  };

  const getThemeName = (theme: string) => {
    return t(`create.theme.themes.${theme}`, theme);
  };

  const getStyleName = (style: string) => {
    return t(`create.style.styles.${style}`, style);
  };

  const getSettingName = (setting: string) => {
    return t(`create.theme.settings.${setting}`, setting);
  };

  const getTraitName = (trait: string) => {
    return t(`create.character.traits.${trait}`, trait);
  };

  return (
    <div className="space-y-6">
      {/* Story Summary */}
      <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-primary-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            {t('create.preview.title')}
          </h3>
          <p className="text-gray-600">
            {t('create.preview.subtitle')}
          </p>
        </div>

        {/* Story Details Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Character Info */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <User className="w-5 h-5 text-primary-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">{t('create.preview.characterInfo')}</h4>
                <p className="text-gray-700">
                  <strong>{data.characterName}</strong>，{t('create.preview.ageYears', { age: data.characterAge })}
                </p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {data.characterTraits.slice(0, 3).map((trait, index) => (
                    <span
                      key={index}
                      className="inline-block px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full"
                    >
                      {getTraitName(trait)}
                    </span>
                  ))}
                  {data.characterTraits.length > 3 && (
                    <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                      {t('create.preview.moreTraits', { count: data.characterTraits.length - 3 })}
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <MapPin className="w-5 h-5 text-secondary-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">{t('create.preview.storyBackground')}</h4>
                <p className="text-gray-700">
                  {t('create.preview.theme')}：<strong>{getThemeName(data.theme)}</strong>
                </p>
                <p className="text-gray-700">
                  {t('create.preview.setting')}：<strong>{getSettingName(data.setting)}</strong>
                </p>
              </div>
            </div>
          </div>

          {/* Style Info */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-accent-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Palette className="w-5 h-5 text-accent-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">{t('create.preview.storyStyle')}</h4>
                <p className="text-gray-700">
                  <strong>{getStyleName(data.style)}</strong>
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                <Volume2 className="w-5 h-5 text-green-600" />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-gray-900 mb-1">{t('create.preview.voiceNarration')}</h4>
                <p className="text-gray-700">
                  <strong>{getVoiceName(data.voice)}</strong>
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>

      {/* Generation Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Time Estimate */}
        <Card>
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">{t('create.preview.estimatedTime')}</h4>
              <p className="text-sm text-gray-600">{t('create.preview.estimatedTimeSubtitle')}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.storyText')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedTime.text}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.illustrations')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedTime.images}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.audioSynthesis')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedTime.audio}</span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">{t('create.preview.totalTime')}</span>
                <span className="font-bold text-primary-600">{estimatedTime.total}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Cost Breakdown */}
        <Card>
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <CreditCard className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">{t('create.preview.creditConsumption')}</h4>
              <p className="text-sm text-gray-600">{t('create.preview.creditConsumptionSubtitle')}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.storyText')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedCosts.text} {t('create.preview.credits')}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.illustrations')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedCosts.images} {t('create.preview.credits')}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">{t('create.preview.audioSynthesis')}</span>
              <span className="text-sm font-medium text-gray-900">{estimatedCosts.audio} {t('create.preview.credits')}</span>
            </div>
            <div className="border-t pt-3">
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">{t('create.preview.totalConsumption')}</span>
                <span className="font-bold text-primary-600">{estimatedCosts.total} {t('create.preview.credits')}</span>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Credit Status */}
      <Card className={hasEnoughCredits ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
        <div className="space-y-4">
          {/* 标题 */}
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              hasEnoughCredits ? 'bg-green-100' : 'bg-red-100'
            }`}>
              <Coins className={`w-5 h-5 ${
                hasEnoughCredits ? 'text-green-600' : 'text-red-600'
              }`} />
            </div>
            <div className="flex-1">
              <h4 className={`font-semibold ${
                hasEnoughCredits ? 'text-green-900' : 'text-red-900'
              }`}>
                {hasEnoughCredits ? '积分充足' : '积分不足'}
              </h4>
              <p className={`text-sm ${
                hasEnoughCredits ? 'text-green-700' : 'text-red-700'
              }`}>
                {hasEnoughCredits
                  ? `您当前有 ${user?.credits || 0} 积分，足够创作此故事`
                  : `还需要 ${requiredCredits - (user?.credits || 0)} 积分才能创作此故事`
                }
              </p>
            </div>
          </div>

          {/* 积分余额显示 */}
          <div className="flex justify-center">
            <CreditBalance
              current={user?.credits || 0}
              required={requiredCredits}
              showWarning={!hasEnoughCredits}
              showPurchaseButton={!hasEnoughCredits}
              onPurchaseClick={() => window.open('/pricing', '_blank')}
            />
          </div>
        </div>
      </Card>

      {/* Story Preview */}
      <Card>
        <h4 className="font-semibold text-gray-900 mb-4">{t('create.preview.storyPreview')}</h4>
        <div className="bg-gray-50 rounded-lg p-4">
          <p className="text-gray-700 leading-relaxed">
            {t('create.preview.storyDescription', {
              characterName: data.characterName,
              theme: getThemeName(data.theme),
              age: data.characterAge,
              traits: data.characterTraits.length > 0 ?
                `${t('common.with')} ${data.characterTraits.slice(0, 2).map(getTraitName).join(t('common.and'))} ${t('common.traits')}。` : '',
              setting: getSettingName(data.setting),
              style: getStyleName(data.style),
              voice: getVoiceName(data.voice)
            })}
          </p>
        </div>
      </Card>

      {/* Terms Notice */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          {t('create.preview.termsNotice')}
        </p>
      </div>
    </div>
  );
};
