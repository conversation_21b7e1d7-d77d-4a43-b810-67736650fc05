import React, { useState, useEffect } from 'react';
import { Brush, Volume2 } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { CreateStoryRequest } from '@/types';
import { cn } from '@/utils/cn';


interface StyleConfigurationProps {
  data: Partial<CreateStoryRequest>;
  onChange: (updates: Partial<CreateStoryRequest>) => void;
  onValidationChange: (isValid: boolean) => void;
}

const storyStyles = [
  {
    id: 'fairy-tale',
    icon: '📚',
    color: 'from-pink-400 to-purple-500',
    popular: true,
  },
  {
    id: 'adventure',
    icon: '🗺️',
    color: 'from-orange-400 to-red-500',
    popular: true,
  },
  {
    id: 'educational',
    icon: '🎓',
    color: 'from-blue-400 to-cyan-500',
    popular: true,
  },
  {
    id: 'rhyme',
    icon: '🎵',
    color: 'from-green-400 to-teal-500',
    popular: false,
  },
  {
    id: 'modern',
    icon: '🏙️',
    color: 'from-gray-400 to-slate-500',
    popular: false,
  },
  {
    id: 'fantasy',
    icon: '✨',
    color: 'from-purple-400 to-indigo-500',
    popular: false,
  },
];



export const StyleConfiguration: React.FC<StyleConfigurationProps> = ({
  data,
  onChange,
  onValidationChange,
}) => {
  const { t } = useTranslation();
  const [selectedStyle, setSelectedStyle] = useState(data.style || '');

  useEffect(() => {
    const isValid = !!selectedStyle;
    onValidationChange(isValid);

    if (isValid) {
      onChange({
        style: selectedStyle,
      });
    }
  }, [selectedStyle, onChange, onValidationChange]);

  const popularStyles = storyStyles.filter(style => style.popular);
  const otherStyles = storyStyles.filter(style => !style.popular);

  return (
    <div className="space-y-8">
      {/* Story Style Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Brush className="w-5 h-5 text-primary-500 mr-2" />
          {t('create.style.visualStyle')}
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          {t('create.style.styleDescription', '选择故事的叙述风格，这将影响语言表达和情节展开方式')}
        </p>

        {/* Popular Styles */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3 flex items-center">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
            {t('create.style.recommendedStyles', '推荐风格')}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {popularStyles.map((style) => (
              <Card
                key={style.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-lg relative overflow-hidden',
                  selectedStyle === style.id
                    ? 'ring-2 ring-primary-500 shadow-lg'
                    : 'hover:shadow-md'
                )}
                onClick={() => setSelectedStyle(style.id)}
                padding="none"
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${style.color} opacity-10`} />
                <div className="relative p-4">
                  <div className="text-3xl mb-2">{style.icon}</div>
                  <h5 className="font-semibold text-gray-900 mb-1">{t(`create.style.styles.${style.id}`)}</h5>
                  <p className="text-sm text-gray-600 mb-3">{t(`create.style.styleDescriptions.${style.id}`)}</p>
                  <div className="bg-gray-50 rounded-lg p-2">
                    <p className="text-xs text-gray-700 italic">"{t(`create.style.stylePreviews.${style.id}`)}"</p>
                  </div>
                  {selectedStyle === style.id && (
                    <div className="absolute top-2 right-2">
                      <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Other Styles */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">{t('create.style.moreStyles', '更多风格')}</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
            {otherStyles.map((style) => (
              <Card
                key={style.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md relative',
                  selectedStyle === style.id
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50'
                )}
                onClick={() => setSelectedStyle(style.id)}
                padding="sm"
              >
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{style.icon}</div>
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{t(`create.style.styles.${style.id}`)}</h5>
                    <p className="text-xs text-gray-600">{t(`create.style.styleDescriptions.${style.id}`)}</p>
                  </div>
                  {selectedStyle === style.id && (
                    <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Preview */}
      {selectedStyle && (
        <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">
                {storyStyles.find(s => s.id === selectedStyle)?.icon}
              </span>
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-gray-900 mb-1">
                {t('create.style.preview.title', '风格预览')}
              </h4>
              <p className="text-gray-700">
                {t('create.style.preview.styleOnly', `已选择 ${t(`create.style.styles.${selectedStyle}`)} 风格`)}
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};