import React, { useState, useEffect } from 'react';
import { MapPin, Palette } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { CreateStoryRequest } from '@/types';
import { cn } from '@/utils/cn';

interface ThemeSelectionProps {
  data: Partial<CreateStoryRequest>;
  onChange: (updates: Partial<CreateStoryRequest>) => void;
  onValidationChange: (isValid: boolean) => void;
}

const themes = [
  {
    id: 'adventure',
    icon: '🗺️',
    color: 'from-orange-400 to-red-500',
    popular: true,
  },
  {
    id: 'friendship',
    icon: '🤝',
    color: 'from-pink-400 to-purple-500',
    popular: true,
  },
  {
    id: 'magic',
    icon: '✨',
    color: 'from-purple-400 to-indigo-500',
    popular: true,
  },
  {
    id: 'animals',
    icon: '🐾',
    color: 'from-green-400 to-teal-500',
    popular: false,
  },
  {
    id: 'family',
    icon: '🏠',
    color: 'from-yellow-400 to-orange-500',
    popular: false,
  },
  {
    id: 'learning',
    icon: '📚',
    color: 'from-blue-400 to-cyan-500',
    popular: false,
  },
  {
    id: 'nature',
    icon: '🌿',
    color: 'from-green-400 to-emerald-500',
    popular: false,
  },
  {
    id: 'space',
    icon: '🚀',
    color: 'from-indigo-400 to-purple-500',
    popular: false,
  },
];

const settings = [
  {
    id: 'forest',
    icon: '🌲',
    themes: ['adventure', 'magic', 'animals', 'nature'],
  },
  {
    id: 'castle',
    icon: '🏰',
    themes: ['magic', 'adventure', 'friendship'],
  },
  {
    id: 'ocean',
    icon: '🌊',
    themes: ['adventure', 'magic', 'animals'],
  },
  {
    id: 'village',
    icon: '🏘️',
    themes: ['friendship', 'family', 'learning'],
  },
  {
    id: 'school',
    icon: '🏫',
    themes: ['friendship', 'learning', 'family'],
  },
  {
    id: 'garden',
    icon: '🌺',
    themes: ['nature', 'animals', 'magic'],
  },
  {
    id: 'mountain',
    icon: '⛰️',
    themes: ['adventure', 'nature', 'animals'],
  },
  {
    id: 'space',
    icon: '🛸',
    themes: ['space', 'adventure', 'learning'],
  },
];

export const ThemeSelection: React.FC<ThemeSelectionProps> = ({
  data,
  onChange,
  onValidationChange,
}) => {
  const { t } = useTranslation();
  const [selectedTheme, setSelectedTheme] = useState(data.theme || '');
  const [selectedSetting, setSelectedSetting] = useState(data.setting || '');

  useEffect(() => {
    const isValid = !!(selectedTheme && selectedSetting);
    onValidationChange(isValid);
    
    if (isValid) {
      onChange({
        theme: selectedTheme,
        setting: selectedSetting,
      });
    }
  }, [selectedTheme, selectedSetting, onChange, onValidationChange]);

  const getAvailableSettings = () => {
    if (!selectedTheme) return settings;
    return settings.filter(setting => setting.themes.includes(selectedTheme));
  };

  const popularThemes = themes.filter(theme => theme.popular);
  const otherThemes = themes.filter(theme => !theme.popular);

  return (
    <div className="space-y-8">
      {/* Theme Selection */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Palette className="w-5 h-5 text-primary-500 mr-2" />
          {t('create.theme.storyTheme')}
        </h3>
        <p className="text-sm text-gray-600 mb-6">
          {t('create.theme.themeDescription', '选择一个主题来确定故事的核心内容和价值观')}
        </p>

        {/* Popular Themes */}
        <div className="mb-6">
          <h4 className="text-md font-medium text-gray-800 mb-3 flex items-center">
            <span className="w-2 h-2 bg-primary-500 rounded-full mr-2"></span>
            {t('create.theme.popularThemes', '热门主题')}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {popularThemes.map((theme) => (
              <Card
                key={theme.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-lg relative overflow-hidden',
                  selectedTheme === theme.id
                    ? 'ring-2 ring-primary-500 shadow-lg'
                    : 'hover:shadow-md'
                )}
                onClick={() => setSelectedTheme(theme.id)}
                padding="none"
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${theme.color} opacity-10`} />
                <div className="relative p-4">
                  <div className="text-3xl mb-2">{theme.icon}</div>
                  <h5 className="font-semibold text-gray-900 mb-1">{t(`create.theme.themes.${theme.id}`)}</h5>
                  <p className="text-sm text-gray-600">{t(`create.theme.themeDescriptions.${theme.id}`)}</p>
                  {selectedTheme === theme.id && (
                    <div className="absolute top-2 right-2">
                      <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Other Themes */}
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">{t('create.theme.moreThemes', '更多主题')}</h4>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {otherThemes.map((theme) => (
              <Card
                key={theme.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md relative overflow-hidden',
                  selectedTheme === theme.id
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50'
                )}
                onClick={() => setSelectedTheme(theme.id)}
                padding="sm"
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{theme.icon}</div>
                  <div className="text-sm font-medium text-gray-900">{t(`create.theme.themes.${theme.id}`)}</div>
                  {selectedTheme === theme.id && (
                    <div className="absolute top-1 right-1">
                      <div className="w-4 h-4 bg-primary-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Setting Selection */}
      {selectedTheme && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <MapPin className="w-5 h-5 text-primary-500 mr-2" />
            {t('create.theme.storySetting')}
          </h3>
          <p className="text-sm text-gray-600 mb-6">
            {t('create.theme.settingDescription', '选择故事发生的地点，为角色创造合适的环境')}
          </p>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {getAvailableSettings().map((setting) => (
              <Card
                key={setting.id}
                className={cn(
                  'cursor-pointer transition-all duration-200 hover:shadow-md',
                  selectedSetting === setting.id
                    ? 'ring-2 ring-primary-500 bg-primary-50'
                    : 'hover:bg-gray-50'
                )}
                onClick={() => setSelectedSetting(setting.id)}
                padding="sm"
              >
                <div className="text-center">
                  <div className="text-3xl mb-2">{setting.icon}</div>
                  <h5 className="font-medium text-gray-900 mb-1">{t(`create.theme.settings.${setting.id}`)}</h5>
                  <p className="text-xs text-gray-600">{t(`create.theme.settingDescriptions.${setting.id}`)}</p>
                  {selectedSetting === setting.id && (
                    <div className="absolute top-2 right-2">
                      <div className="w-5 h-5 bg-primary-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Preview */}
      {selectedTheme && selectedSetting && (
        <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 border-primary-200">
          <div className="flex items-center space-x-4">
            <div className="flex space-x-2">
              <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">
                  {themes.find(t => t.id === selectedTheme)?.icon}
                </span>
              </div>
              <div className="w-12 h-12 bg-secondary-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">
                  {settings.find(s => s.id === selectedSetting)?.icon}
                </span>
              </div>
            </div>
            <div className="flex-1">
              <h4 className="text-lg font-semibold text-gray-900 mb-1">
                {t('create.theme.preview.title', '主题预览')}
              </h4>
              <p className="text-gray-700">
                {t('create.theme.preview.description', {
                  theme: t(`create.theme.themes.${selectedTheme}`),
                  setting: t(`create.theme.settings.${selectedSetting}`)
                })}
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
