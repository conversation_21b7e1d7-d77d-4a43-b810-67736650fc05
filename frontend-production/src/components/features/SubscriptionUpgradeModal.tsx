/**
 * 订阅升级模态框
 * 当用户权限不足时显示升级提示
 */

import React from 'react';
import { motion } from 'framer-motion';
import { Crown, X, Check, Zap, Image, Volume2, Shield } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Modal } from '@/components/ui/Modal';

interface SubscriptionUpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  requiredPlan: string;
  feature: string;
  reason: string;
  onUpgrade: () => void;
}

const PLAN_FEATURES = {
  basic_monthly: {
    name: '基础版',
    price: '¥29/月',
    features: [
      '50个故事/月',
      '基础AI模型',
      '标准图片质量',
      '3种声音选择',
      'PDF导出'
    ],
    color: 'from-blue-500 to-blue-600'
  },
  pro_monthly: {
    name: '专业版',
    price: '¥99/月',
    features: [
      '200个故事/月',
      '高级AI模型',
      '高清图片质量',
      '4种声音选择',
      '多格式导出',
      '优先客服支持'
    ],
    color: 'from-purple-500 to-purple-600'
  },
  unlimited_monthly: {
    name: '无限版',
    price: '¥299/月',
    features: [
      '无限故事创作',
      '顶级AI模型',
      '超高清图片',
      '6种声音选择',
      '商业使用授权',
      'API访问权限',
      '专属客服支持'
    ],
    color: 'from-gold-500 to-gold-600'
  }
};

export const SubscriptionUpgradeModal: React.FC<SubscriptionUpgradeModalProps> = ({
  isOpen,
  onClose,
  requiredPlan,
  feature,
  reason,
  onUpgrade
}) => {
  const planInfo = PLAN_FEATURES[requiredPlan as keyof typeof PLAN_FEATURES];
  
  if (!planInfo) {
    return null;
  }

  const getFeatureIcon = (feature: string) => {
    if (feature.includes('AI') || feature.includes('模型')) return <Zap className="w-5 h-5" />;
    if (feature.includes('图片') || feature.includes('质量')) return <Image className="w-5 h-5" />;
    if (feature.includes('音频') || feature.includes('声音')) return <Volume2 className="w-5 h-5" />;
    if (feature.includes('商业') || feature.includes('API')) return <Shield className="w-5 h-5" />;
    return <Crown className="w-5 h-5" />;
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-md">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-lg bg-gradient-to-r ${planInfo.color} text-white`}>
              {getFeatureIcon(feature)}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                功能升级
              </h3>
              <p className="text-sm text-gray-600">
                解锁更多创作可能
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        {/* Reason */}
        <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <p className="text-orange-800 text-sm">
            {reason}
          </p>
        </div>

        {/* Plan Info */}
        <div className={`mb-6 p-4 bg-gradient-to-r ${planInfo.color} rounded-lg text-white`}>
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-lg font-semibold">{planInfo.name}</h4>
            <div className="text-right">
              <div className="text-2xl font-bold">{planInfo.price}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            {planInfo.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <Check className="w-4 h-4 flex-shrink-0" />
                <span>{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            稍后升级
          </Button>
          <Button
            onClick={onUpgrade}
            className={`flex-1 bg-gradient-to-r ${planInfo.color} hover:opacity-90`}
          >
            <Crown className="w-4 h-4 mr-2" />
            立即升级
          </Button>
        </div>
      </div>
    </Modal>
  );
};