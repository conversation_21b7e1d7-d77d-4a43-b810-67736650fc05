import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { User, Sparkles, RefreshCw, Check, AlertCircle, Coins } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';
import { useAuthStore } from '@/stores/authStore';

interface CharacterPreviewProps {
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  style: string;
  gender?: string; // 添加性别参数
  onCharacterGenerated?: (characterImageUrl: string) => void;
  className?: string;
}

interface CharacterGenerationState {
  isGenerating: boolean;
  imageUrl: string | null;
  error: string | null;
  isConfirmed: boolean;
}

export const CharacterPreview: React.FC<CharacterPreviewProps> = ({
  characterName,
  characterAge,
  characterTraits,
  style,
  gender = 'neutral', // 默认为中性
  onCharacterGenerated,
  className
}) => {
  const { user } = useAuthStore();
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [generationState, setGenerationState] = useState<CharacterGenerationState>({
    isGenerating: false,
    imageUrl: null,
    error: null,
    isConfirmed: false
  });

  // 显示确认对话框
  const handleShowConfirmDialog = () => {
    if (!characterName.trim() || characterTraits.length === 0) {
      setGenerationState(prev => ({
        ...prev,
        error: '请先完善角色信息'
      }));
      return;
    }

    // 检查用户积分是否足够
    if (user && user.credits < 1) {
      setGenerationState(prev => ({
        ...prev,
        error: '积分不足，生成角色形象需要1积分'
      }));
      return;
    }

    setShowConfirmDialog(true);
  };

  // 关闭确认对话框
  const handleCloseConfirmDialog = () => {
    setShowConfirmDialog(false);
  };

  // 生成角色形象
  const handleGenerateCharacter = async () => {
    setShowConfirmDialog(false);

    setGenerationState(prev => ({
      ...prev,
      isGenerating: true,
      error: null
    }));

    try {
      // 使用完整的后端API URL
      const response = await fetch('https://storyweaver-api.stawky.workers.dev/api/characters/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          characterName,
          characterAge,
          characterTraits,
          style: 'anime', // 限制风格为动漫风格
          gender: gender || 'neutral', // 添加性别参数
          userId: user?.id // 添加用户ID用于积分扣除
        }),
      });

      if (!response.ok) {
        throw new Error('角色形象生成失败');
      }

      const data = await response.json();
      
      if (data.success && data.data?.imageUrl) {
        setGenerationState(prev => ({
          ...prev,
          isGenerating: false,
          imageUrl: data.data.imageUrl,
          error: null
        }));
      } else {
        throw new Error(data.error || '角色形象生成失败');
      }
    } catch (error) {
      console.error('Character generation failed:', error);
      setGenerationState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : '角色形象生成失败'
      }));
    }
  };

  // 重新生成角色形象
  const handleRegenerate = () => {
    setGenerationState(prev => ({
      ...prev,
      imageUrl: null,
      isConfirmed: false
    }));
    handleGenerateCharacter();
  };

  // 确认角色形象
  const handleConfirmCharacter = () => {
    if (generationState.imageUrl) {
      setGenerationState(prev => ({
        ...prev,
        isConfirmed: true
      }));
      onCharacterGenerated?.(generationState.imageUrl);
    }
  };

  return (
    <>
    <Card className={cn('p-6', className)}>
      <div className="space-y-6">
        {/* 标题 */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-primary-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              角色形象预览
            </h3>
            <p className="text-sm text-gray-600">
              生成并确认角色的视觉形象，确保故事中的一致性
            </p>
          </div>
        </div>

        {/* 角色信息摘要 */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <User className="w-5 h-5 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium text-gray-900">{characterName}</p>
              <p className="text-sm text-gray-600">{characterAge}岁</p>
              <div className="flex flex-wrap gap-1 mt-2">
                {characterTraits.map((trait, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full"
                  >
                    {trait}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* 角色形象显示区域 */}
        <div className="space-y-4">
          {!generationState.imageUrl && !generationState.isGenerating && (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">
                点击下方按钮生成角色形象
              </p>
              <div className="space-y-2">
                <Button
                  onClick={handleShowConfirmDialog}
                  className="bg-primary-500 hover:bg-primary-600"
                  disabled={user && user.credits < 1}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  生成角色形象
                </Button>
                <div className="flex items-center justify-center text-sm text-gray-600">
                  <Coins className="w-4 h-4 mr-1 text-amber-500" />
                  <span>生成角色形象将消耗1积分</span>
                </div>
                {user && (
                  <div className="text-sm text-center">
                    当前积分: <span className="font-medium">{user.credits}</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {generationState.isGenerating && (
            <div className="border-2 border-dashed border-primary-300 rounded-lg p-8 text-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-16 h-16 mx-auto mb-4"
              >
                <Sparkles className="w-16 h-16 text-primary-500" />
              </motion.div>
              <p className="text-primary-600 font-medium">
                正在生成角色形象...
              </p>
              <p className="text-sm text-gray-500 mt-1">
                这可能需要几秒钟时间
              </p>
            </div>
          )}

          {generationState.imageUrl && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="space-y-4"
            >
              <div className="relative">
                <img
                  src={generationState.imageUrl}
                  alt={`${characterName}的形象`}
                  className="w-full max-w-sm mx-auto rounded-lg shadow-lg"
                />
                {generationState.isConfirmed && (
                  <div className="absolute top-2 right-2 bg-green-500 text-white rounded-full p-1">
                    <Check className="w-4 h-4" />
                  </div>
                )}
              </div>

              <div className="flex justify-center space-x-3">
                {!generationState.isConfirmed ? (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleRegenerate}
                      disabled={generationState.isGenerating}
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      重新生成
                    </Button>
                    <Button
                      onClick={handleConfirmCharacter}
                      className="bg-green-500 hover:bg-green-600"
                    >
                      <Check className="w-4 h-4 mr-2" />
                      确认使用
                    </Button>
                  </>
                ) : (
                  <div className="flex items-center text-green-600">
                    <Check className="w-5 h-5 mr-2" />
                    <span className="font-medium">角色形象已确认</span>
                  </div>
                )}
              </div>
            </motion.div>
          )}

          {generationState.error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
                <div>
                  <p className="text-red-800 font-medium">生成失败</p>
                  <p className="text-red-700 text-sm mt-1">
                    {generationState.error}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleGenerateCharacter}
                    className="mt-2 text-red-600 border-red-300 hover:bg-red-50"
                  >
                    重试
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </Card>

    {/* 确认对话框 */}
    {showConfirmDialog && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">确认生成角色形象</h3>
          <p className="text-gray-600 mb-4">
            生成角色形象将消耗1积分，当前积分: {user?.credits || 0}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleCloseConfirmDialog}
            >
              取消
            </Button>
            <Button
              onClick={handleGenerateCharacter}
              className="bg-primary-500 hover:bg-primary-600"
            >
              确认生成
            </Button>
          </div>
        </div>
      </div>
    )}
    </>
  );
};
