import React from 'react';
import { X, Check, Star, Zap, Crown, Gift } from 'lucide-react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { cn } from '@/utils/cn';

interface PlanDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  plan: {
    id: string;
    name: string;
    description: string;
    price: { monthly: number; yearly: number };
    credits: { monthly: number; yearly: number };
    features: string[];
    popular: boolean;
    color: string;
    icon: any;
  } | null;
  billingCycle: 'monthly' | 'yearly';
  onProceedToPayment: () => void;
}

export const PlanDetailsModal: React.FC<PlanDetailsModalProps> = ({
  isOpen,
  onClose,
  plan,
  billingCycle,
  onProceedToPayment,
}) => {
  if (!plan) return null;

  const IconComponent = plan.icon;
  const currentPrice = plan.price[billingCycle];
  const currentCredits = plan.credits[billingCycle];
  const isYearly = billingCycle === 'yearly';
  const monthlyEquivalent = isYearly ? (currentPrice / 12).toFixed(2) : currentPrice;

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'primary':
        return {
          bg: 'bg-primary-50',
          border: 'border-primary-200',
          icon: 'text-primary-600',
          badge: 'bg-primary-500',
          button: 'bg-primary-600 hover:bg-primary-700'
        };
      case 'blue':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          icon: 'text-blue-600',
          badge: 'bg-blue-500',
          button: 'bg-blue-600 hover:bg-blue-700'
        };
      case 'purple':
        return {
          bg: 'bg-purple-50',
          border: 'border-purple-200',
          icon: 'text-purple-600',
          badge: 'bg-purple-500',
          button: 'bg-purple-600 hover:bg-purple-700'
        };
      case 'gray':
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          icon: 'text-gray-600',
          badge: 'bg-gray-500',
          button: 'bg-gray-600 hover:bg-gray-700'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          icon: 'text-gray-600',
          badge: 'bg-gray-500',
          button: 'bg-gray-600 hover:bg-gray-700'
        };
    }
  };

  const colorClasses = getColorClasses(plan.color);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="套餐详情"
      size="lg"
    >
      <div className="p-6">
        {/* 套餐头部 */}
        <Card className={cn(
          'relative p-6 mb-6 border-2',
          colorClasses.bg,
          colorClasses.border
        )}>
          {plan.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <span className={cn(
                'text-white text-sm px-4 py-1 rounded-full',
                colorClasses.badge
              )}>
                最受欢迎
              </span>
            </div>
          )}

          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className={cn(
                'w-12 h-12 rounded-xl flex items-center justify-center',
                colorClasses.bg
              )}>
                <IconComponent className={cn('w-6 h-6', colorClasses.icon)} />
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                <p className="text-gray-600">{plan.description}</p>
              </div>
            </div>
          </div>

          {/* 价格显示 */}
          <div className="mb-4">
            <div className="flex items-baseline space-x-2">
              <span className="text-3xl font-bold text-gray-900">
                ${currentPrice === 0 ? '0' : currentPrice.toFixed(2)}
              </span>
              <span className="text-gray-600">
                /{isYearly ? '年' : '月'}
              </span>
            </div>
            {isYearly && currentPrice > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                相当于每月 ${monthlyEquivalent}
              </p>
            )}
          </div>

          {/* 积分显示 */}
          <div className="mb-4">
            <div className="flex items-center space-x-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="text-gray-700">
                {currentCredits === 999999 ? '无限' : currentCredits} 故事生成额度
                {isYearly && currentCredits !== 999999 && ' (全年)'}
              </span>
            </div>
          </div>
        </Card>

        {/* 功能列表 */}
        <div className="mb-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">包含功能</h4>
          <div className="space-y-3">
            {plan.features.map((feature, index) => (
              <div key={index} className="flex items-start space-x-3">
                <Check className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </div>
            ))}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={onClose}
            className="flex-1"
          >
            取消
          </Button>
          <Button
            onClick={onProceedToPayment}
            className={cn(
              'flex-1 text-white',
              colorClasses.button
            )}
            disabled={plan.id === 'free'}
          >
            {plan.id === 'free' ? '当前套餐' : '立即购买'}
          </Button>
        </div>

        {/* 免费套餐说明 */}
        {plan.id === 'free' && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-700">
              您当前使用的是免费套餐。升级到付费套餐可获得更多功能和积分。
            </p>
          </div>
        )}
      </div>
    </Modal>
  );
};
