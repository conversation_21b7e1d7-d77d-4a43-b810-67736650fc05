import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { CreditCard, Star, Award, Calendar, CheckCircle } from 'lucide-react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import SubscriptionFeatures from './SubscriptionFeatures';
import { useAuthStore } from '@/stores/authStore';

interface SubscriptionCardProps {
  showDetails?: boolean;
}

const SubscriptionCard: React.FC<SubscriptionCardProps> = ({ showDetails = true }) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { user } = useAuthStore();
  const subscription = user?.subscription;
  
  // 获取订阅计划名称
  const getPlanName = () => {
    if (!subscription) return t('subscription.plans.free');
    
    const planId = typeof subscription.plan === 'string' ? subscription.plan : 'free';
    
    switch (planId) {
      case 'basic_monthly': return t('subscription.plans.basic_monthly');
      case 'pro_monthly': return t('subscription.plans.pro_monthly');
      case 'pro_yearly': return t('subscription.plans.pro_monthly') + ' (' + t('pricing.yearly') + ')';
      case 'unlimited_monthly': return t('subscription.plans.unlimited_monthly');
      default: return t('subscription.plans.free');
    }
  };
  
  // 获取订阅状态标签
  const getStatusBadge = () => {
    if (!subscription) {
      return (
        <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
          {t('subscription.plans.free')}
        </span>
      );
    }
    
    switch (subscription.status) {
      case 'active':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-green-100 text-green-800 flex items-center">
            <CheckCircle className="w-3 h-3 mr-1" />
            {t('subscription.status.active', 'Active')}
          </span>
        );
      case 'canceled':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
            {t('subscription.status.canceled', 'Canceled')}
          </span>
        );
      case 'past_due':
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
            {t('subscription.status.pastDue', 'Past Due')}
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
            {t('subscription.status.unknown', 'Unknown')}
          </span>
        );
    }
  };
  
  // 获取订阅图标
  const getPlanIcon = () => {
    if (!subscription) return <Star className="w-5 h-5 text-gray-400" />;
    
    switch (subscription.plan) {
      case 'basic_monthly': return <Star className="w-5 h-5 text-blue-500" />;
      case 'pro_monthly': 
      case 'pro_yearly': return <Award className="w-5 h-5 text-primary" />;
      case 'unlimited_monthly': return <Award className="w-5 h-5 text-purple-600" />;
      default: return <Star className="w-5 h-5 text-gray-400" />;
    }
  };
  
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
          <CreditCard className="w-5 h-5 mr-2" />
          {t('subscription.title', 'Subscription Info')}
        </h2>
        {getStatusBadge()}
      </div>
      
      <div className="flex items-center mb-4">
        {getPlanIcon()}
        <div className="ml-3">
          <h3 className="font-medium text-gray-900">{getPlanName()}</h3>
          {subscription && (
            <p className="text-sm text-gray-500 flex items-center">
              <Calendar className="w-3 h-3 mr-1" />
              {t('subscription.validUntil')}: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </p>
          )}
        </div>
      </div>
      
      {showDetails && (
        <div className="mt-4 border-t pt-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">{t('subscription.currentPlanFeatures', 'Current Plan Features')}</h3>
          <SubscriptionFeatures compact />
        </div>
      )}
      
      <div className="mt-6 flex flex-wrap gap-2">
        <Button 
          onClick={() => navigate('/pricing')}
          className="flex-1"
        >
          {subscription?.status === 'active' ? t('subscription.upgrade', 'Upgrade Plan') : t('subscription.subscribe', 'Subscribe')}
        </Button>
        
        {subscription?.status === 'active' && (
          <Button 
            variant="outline" 
            className="flex-1"
            onClick={() => navigate('/subscription-settings')}
          >
            {t('subscription.manage', 'Manage Subscription')}
          </Button>
        )}
      </div>
    </Card>
  );
};

export default SubscriptionCard;