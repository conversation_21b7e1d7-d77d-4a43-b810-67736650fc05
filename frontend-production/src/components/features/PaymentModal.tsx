import React, { useState, useEffect } from 'react';
import { X, CreditCard, Check, AlertCircle, Sparkles, ArrowRight, Crown } from 'lucide-react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNotifications } from '@/stores/uiStore';
import { useAuthStore } from '@/stores/authStore';
import { paymentService, loadStripe } from '@/services/payments';
import { shouldMockPayments, shouldUseRealStripe, debugLog } from '@/utils/debug';
import { getSubscriptionPlans, getPlanById, getPlanPrice, SubscriptionPlan } from '@/config/pricing';
import { cn } from '@/utils/cn';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'credits' | 'subscription' | 'physical_book';
  amount?: number;
  currency?: string;
  metadata?: Record<string, string>;
  onSuccess?: (result: any) => void;
  initialSelectedPlan?: string;
}

interface CreditPackage {
  id: string;
  name: string;
  credits: number;
  price: number;
  currency: string;
  bonus: number;
  popular: boolean;
  savings?: string;
}

// SubscriptionPlan接口现在从统一配置中导入

const creditPackages: CreditPackage[] = [
  {
    id: 'basic',
    name: '基础包',
    credits: 100,
    price: 2.99, // 美元价格
    currency: 'usd',
    bonus: 0,
    popular: false,
  },
  {
    id: 'popular',
    name: '超值包',
    credits: 300,
    price: 7.99, // 美元价格
    currency: 'usd',
    bonus: 50,
    popular: true,
    savings: '节省 $1.98',
  },
  {
    id: 'premium',
    name: '豪华包',
    credits: 600,
    price: 14.99, // 美元价格
    currency: 'usd',
    bonus: 150,
    popular: false,
    savings: '节省 $4.98',
  },
];

// 使用统一的价格配置
const subscriptionPlans = getSubscriptionPlans();

export const PaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  type,
  amount,
  currency = 'usd',
  metadata = {},
  onSuccess,
  initialSelectedPlan,
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string>(
    type === 'credits' ? 'popular' : (initialSelectedPlan || 'pro_monthly')
  );
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'select' | 'payment' | 'success'>(
    initialSelectedPlan ? 'payment' : 'select'
  );

  // 调试日志
  React.useEffect(() => {
    if (isOpen) {
      debugLog.info('PaymentModal opened with params:', {
        type,
        initialSelectedPlan,
        selectedPackage,
        paymentStep,
        isOpen
      });
    }
  }, [isOpen, type, initialSelectedPlan, selectedPackage, paymentStep]);
  const [stripe, setStripe] = useState<any>(null);
  const [elements, setElements] = useState<any>(null);
  const [cardElement, setCardElement] = useState<any>(null);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const { showSuccess, showError } = useNotifications();
  const { addDebugCredits } = useAuthStore();

  // Stripe Elements不再需要，因为我们使用Checkout重定向
  // useEffect(() => {
  //   if (isOpen && shouldUseRealStripe()) {
  //     loadStripe().then((stripeInstance) => {
  //       setStripe(stripeInstance);
  //       if (stripeInstance) {
  //         const elementsInstance = stripeInstance.elements();
  //         setElements(elementsInstance);
  //       }
  //     });
  //   }
  // }, [isOpen]);

  const handlePackageSelect = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const resetPaymentState = () => {
    setPaymentError(null);
    setIsProcessing(false);
    if (cardElement) {
      try {
        cardElement.unmount();
      } catch (error) {
        // Element might already be unmounted
        debugLog.warn('Card element unmount warning during reset:', error);
      }
      setCardElement(null);
    }
  };

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      resetPaymentState();
      setPaymentStep('select');
    }
  }, [isOpen]);

  const handlePayment = async () => {
    if (isProcessing) return;

    // 检查用户认证状态（除非是模拟支付模式）
    if (!shouldMockPayments()) {
      const { isAuthenticated, user } = useAuthStore.getState();

      if (!isAuthenticated || !user) {
        showError('请先登录', '支付前需要登录您的账户');
        onClose(); // 关闭支付模态框
        window.location.href = '/auth';
        return;
      }

      debugLog.info('Starting payment process', {
        user: { id: user.id, email: user.email },
        isAuthenticated
      });
    }

    // Check if we should mock payments (only when explicitly enabled)
    if (shouldMockPayments()) {
      debugLog.info('Mocking payment in debug mode');
      setIsProcessing(true);

      try {
        // Simulate payment processing delay
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Mock successful payment
        if (type === 'credits') {
          const selectedPkg = creditPackages.find(pkg => pkg.id === selectedPackage);
          if (selectedPkg) {
            addDebugCredits(selectedPkg.credits);
            debugLog.info(`Added ${selectedPkg.credits} credits via debug payment`);
          }
        } else if (type === 'subscription') {
          const selectedPlan = subscriptionPlans.find(plan => plan.id === selectedPackage);
          if (selectedPlan) {
            debugLog.info(`Activated ${selectedPlan.name} subscription via debug payment`);
          }
        }

        setPaymentStep('success');
        showSuccess('支付成功', '您的订单已处理完成（调试模式）');
        onSuccess?.({ success: true, debug: true });

        setTimeout(() => {
          onClose();
          setPaymentStep('select');
        }, 2000);

        return;
      } catch (error) {
        showError('支付失败', '调试模式支付模拟失败');
      } finally {
        setIsProcessing(false);
      }
      return;
    }

    // Use Stripe Checkout redirect payment flow
    if (shouldUseRealStripe()) {
      setIsProcessing(true);
      setPaymentError(null);

      try {
        let checkoutSession;

        if (type === 'credits') {
          const selectedPkg = creditPackages.find(pkg => pkg.id === selectedPackage);
          if (!selectedPkg) throw new Error('未选择积分包');

          const requestPayload = {
            amount: selectedPkg.price, // 以美元为单位，后端会转换为分
            currency: selectedPkg.currency,
            type: 'credits' as const,
            metadata: {
              packageId: selectedPkg.id,
              credits: selectedPkg.credits.toString(),
              ...metadata,
            },
          };

          debugLog.info('🔍 Sending checkout session request:', requestPayload);
          checkoutSession = await paymentService.createCheckoutSession(requestPayload);
        } else if (type === 'subscription') {
          const selectedPlan = subscriptionPlans.find(plan => plan.id === selectedPackage);
          if (!selectedPlan) throw new Error('未选择订阅套餐');

          const requestPayload = {
            amount: selectedPlan.price,
            currency: selectedPlan.currency,
            type: 'subscription' as const,
            metadata: {
              planId: selectedPlan.id,
              interval: selectedPlan.interval,
              ...metadata,
            },
          };

          debugLog.info('🔍 Sending subscription checkout session request:', requestPayload);
          checkoutSession = await paymentService.createCheckoutSession(requestPayload);
        } else {
          checkoutSession = await paymentService.createCheckoutSession({
            amount: amount || 0, // 以美元为单位，后端会转换为分
            currency,
            type,
            metadata,
          });
        }

        debugLog.info('Redirecting to Stripe Checkout...', { checkoutSession });

        // 重定向到Stripe Checkout页面
        window.location.href = checkoutSession.url;

      } catch (error) {
        debugLog.error('Stripe checkout error:', error);

        let errorMessage = '支付失败，请重试';
        let errorTitle = '支付失败';

        if (error instanceof Error) {
          // 检查是否是认证错误
          if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            errorTitle = '认证失败';
            errorMessage = '登录状态已过期，请重新登录后再试';
          } else if (error.message.includes('400') || error.message.includes('Bad Request')) {
            errorTitle = '请求错误';
            errorMessage = '支付参数有误，请重试';
          } else if (error.message.includes('500') || error.message.includes('Internal Server Error')) {
            errorTitle = '服务器错误';
            errorMessage = '服务器暂时不可用，请稍后重试';
          } else {
            errorMessage = error.message;
          }
        }

        setPaymentError(errorMessage);
        showError(errorTitle, errorMessage);
        setIsProcessing(false);
      }
      return;
    }

    // 如果既不是模拟支付也不是真实Stripe，则显示错误
    showError('支付系统配置错误', '请联系管理员');
    return;
  };

  const renderSubscriptionSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          选择订阅套餐
        </h3>
        <p className="text-gray-600">
          选择适合您的会员计划，享受更多创作权益
        </p>
      </div>

      {/* 订阅套餐选择 */}
      <div className="space-y-3">
        {subscriptionPlans.map((plan) => (
          <Card
            key={plan.id}
            className={cn(
              'relative cursor-pointer transition-all duration-200 border-2',
              selectedPackage === plan.id
                ? 'ring-2 ring-primary-500 shadow-lg'
                : 'hover:shadow-md',
              plan.popular && 'border-primary-200'
            )}
            onClick={() => handlePackageSelect(plan.id)}
          >
            {plan.popular && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary-500 text-white text-xs px-3 py-1 rounded-full">
                  最受欢迎
                </span>
              </div>
            )}

            <div className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'w-10 h-10 rounded-lg flex items-center justify-center',
                    plan.popular ? 'bg-primary-100' : 'bg-gray-100'
                  )}>
                    <Crown className={cn(
                      'w-5 h-5',
                      plan.popular ? 'text-primary-600' : 'text-gray-600'
                    )} />
                  </div>

                  <div>
                    <h4 className="font-semibold text-gray-900">{plan.name}</h4>
                    <p className="text-sm text-gray-600">{plan.description}</p>
                    {plan.savings && (
                      <p className="text-xs text-green-600 mt-1">{plan.savings}</p>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-xl font-bold text-gray-900">
                    ${plan.price}
                  </div>
                  <div className="text-sm text-gray-500">
                    /{plan.interval === 'month' ? '月' : '年'}
                  </div>
                </div>
              </div>

              {/* 功能列表 */}
              <div className="space-y-1">
                {plan.features.slice(0, 3).map((feature, index) => (
                  <div key={index} className="flex items-center text-sm text-gray-600">
                    <Check className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                    <span>{feature}</span>
                  </div>
                ))}
                {plan.features.length > 3 && (
                  <div className="text-xs text-gray-500 mt-1">
                    +{plan.features.length - 3} 更多功能
                  </div>
                )}
              </div>

              {selectedPackage === plan.id && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* 继续支付按钮 */}
      <div className="flex justify-end pt-4">
        <Button
          onClick={() => setPaymentStep('payment')}
          disabled={!selectedPackage}
          className="px-8"
        >
          继续支付
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );

  const renderCreditSelection = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          选择积分包
        </h3>
        <p className="text-gray-600">
          购买积分来创作更多精彩故事
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {creditPackages.map((pkg) => (
          <Card
            key={pkg.id}
            className={cn(
              'cursor-pointer transition-all duration-200 relative',
              selectedPackage === pkg.id
                ? 'ring-2 ring-primary-500 shadow-lg'
                : 'hover:shadow-md',
              pkg.popular && 'border-primary-200'
            )}
            onClick={() => handlePackageSelect(pkg.id)}
          >
            {pkg.popular && (
              <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary-500 text-white text-xs px-3 py-1 rounded-full">
                  最受欢迎
                </span>
              </div>
            )}
            
            <div className="flex items-center justify-between p-4">
              <div className="flex items-center space-x-4">
                <div className={cn(
                  'w-12 h-12 rounded-lg flex items-center justify-center',
                  pkg.popular ? 'bg-primary-100' : 'bg-gray-100'
                )}>
                  <Sparkles className={cn(
                    'w-6 h-6',
                    pkg.popular ? 'text-primary-600' : 'text-gray-600'
                  )} />
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900">{pkg.name}</h4>
                  <p className="text-sm text-gray-600">
                    {pkg.credits} 积分
                    {pkg.bonus > 0 && (
                      <span className="text-green-600 ml-1">
                        + {pkg.bonus} 赠送
                      </span>
                    )}
                  </p>
                  {pkg.savings && (
                    <p className="text-xs text-green-600">{pkg.savings}</p>
                  )}
                </div>
              </div>
              
              <div className="text-right">
                <div className="text-xl font-bold text-gray-900">
                  ${pkg.price}
                </div>
                <div className="text-sm text-gray-500">
                  ≈ ${(pkg.price / (pkg.credits + pkg.bonus)).toFixed(3)}/积分
                </div>
              </div>
              
              {selectedPackage === pkg.id && (
                <div className="absolute top-4 right-4">
                  <div className="w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                </div>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* 继续支付按钮 */}
      <div className="flex justify-end pt-4">
        <Button
          onClick={() => setPaymentStep('payment')}
          disabled={!selectedPackage}
          className="px-8"
        >
          继续支付
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
      </div>
    </div>
  );

  // Stripe Elements不再需要，因为我们使用Checkout重定向
  // useEffect(() => {
  //   // Stripe Elements mounting logic removed
  // }, []);

  const renderPaymentForm = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          确认支付
        </h3>
        {type === 'credits' && (
          <p className="text-gray-600">
            {creditPackages.find(pkg => pkg.id === selectedPackage)?.name} -
            ${creditPackages.find(pkg => pkg.id === selectedPackage)?.price}
          </p>
        )}
        {type === 'subscription' && (
          <p className="text-gray-600">
            {subscriptionPlans.find(pkg => pkg.id === selectedPackage)?.name} -
            ${subscriptionPlans.find(pkg => pkg.id === selectedPackage)?.price}
          </p>
        )}
      </div>

      {/* 支付表单 */}
      <Card className="p-6 bg-white border border-gray-200">
        <div className="space-y-4">
          <div className="text-center mb-4">
            <CreditCard className="w-12 h-12 mx-auto mb-2 text-primary-500" />
            <p className="text-sm font-medium text-gray-900">
              安全支付
            </p>
            <p className="text-xs text-gray-500 mt-1">
              {shouldUseRealStripe() ? '使用Stripe安全支付' : '安全支付处理'}
            </p>
          </div>

          {shouldUseRealStripe() ? (
            // Stripe Checkout redirect payment
            <div className="space-y-4">
              <div className="p-4 border border-primary-200 rounded-lg bg-primary-50">
                <div className="flex items-center justify-center space-x-2">
                  <CreditCard className="w-5 h-5 text-primary-600" />
                  <span className="text-sm font-medium text-primary-900">Stripe 安全支付</span>
                </div>
                <p className="text-xs text-primary-700 text-center mt-2">
                  点击"确认支付"将跳转到Stripe官方支付页面
                </p>
              </div>

              {paymentError && (
                <div className="mt-2 text-sm text-red-600 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {paymentError}
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded p-3">
                <div className="flex items-start">
                  <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="text-xs text-blue-700">
                    <p className="font-medium mb-1">🧪 测试环境</p>
                    <p>将跳转到Stripe测试支付页面</p>
                    <p>使用测试卡号: 4242 4242 4242 4242</p>
                  </div>
                </div>
              </div>
            </div>
          ) : shouldMockPayments() ? (
            // Debug mode mock payment
            <div className="bg-orange-50 border border-orange-200 rounded p-3">
              <div className="flex items-start">
                <AlertCircle className="w-4 h-4 text-orange-600 mt-0.5 mr-2 flex-shrink-0" />
                <div className="text-xs text-orange-700">
                  <p className="font-medium mb-1">🔧 调试模式</p>
                  <p>点击"确认支付"将模拟支付成功并自动添加积分</p>
                </div>
              </div>
            </div>
          ) : (
            // Production mode - Stripe only
            <div className="space-y-4">
              <div className="p-4 border border-primary-200 rounded-lg bg-primary-50">
                <div className="flex items-center justify-center space-x-2">
                  <CreditCard className="w-5 h-5 text-primary-600" />
                  <span className="text-sm font-medium text-primary-900">Stripe 安全支付</span>
                </div>
                <p className="text-xs text-primary-700 text-center mt-2">
                  支持所有主流银行卡，采用银行级安全加密
                </p>
              </div>

              <div className="bg-red-50 border border-red-200 rounded p-3">
                <div className="flex items-start">
                  <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 mr-2 flex-shrink-0" />
                  <div className="text-xs text-red-700">
                    <p className="font-medium mb-1">⚠️ 支付系统未就绪</p>
                    <p>请联系管理员配置支付系统</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* 支付错误提示和重试 */}
      {paymentError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-600 mt-0.5 mr-3 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800 mb-1">支付失败</h4>
              <p className="text-sm text-red-700">{paymentError}</p>
              <div className="mt-3 flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setPaymentError(null);
                    // Remount Stripe Elements if needed
                    if (shouldUseRealStripe() && cardElement) {
                      setTimeout(() => {
                        const cardContainer = document.getElementById('card-element');
                        if (cardContainer && !cardContainer.hasChildNodes()) {
                          try {
                            cardElement.mount('#card-element');
                          } catch (error) {
                            debugLog.warn('Card element remount warning:', error);
                          }
                        }
                      }, 100);
                    }
                  }}
                  className="text-red-700 border-red-300 hover:bg-red-100"
                >
                  重试支付
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    setPaymentError(null);
                    setPaymentStep('select');
                  }}
                  className="text-red-700 border-red-300 hover:bg-red-100"
                >
                  重新选择
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex space-x-3">
        <Button
          variant="outline"
          onClick={() => {
            setPaymentError(null);
            setPaymentStep('select');
          }}
          disabled={isProcessing}
          className="flex-1"
        >
          返回
        </Button>
        <Button
          onClick={handlePayment}
          loading={isProcessing}
          loadingText={shouldUseRealStripe() ? "正在跳转到支付页面..." : "处理中..."}
          disabled={isProcessing}
          className="flex-1"
        >
          {shouldUseRealStripe() ? "确认支付" : "确认支付"}
        </Button>
      </div>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center space-y-6">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <Check className="w-8 h-8 text-green-600" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          支付成功！
        </h3>
        <p className="text-gray-600">
          {type === 'credits' 
            ? '积分已添加到您的账户，现在可以创作更多故事了！'
            : '您的订单已处理完成'
          }
        </p>
      </div>
      
      <LoadingSpinner size="sm" label="正在跳转..." />
    </div>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        paymentStep === 'select' ? (type === 'credits' ? '购买积分' : '选择订阅') :
        paymentStep === 'payment' ? '支付确认' :
        '支付完成'
      }
      size="md"
      closeOnOverlayClick={!isProcessing}
      closeOnEscape={!isProcessing}
      showCloseButton={paymentStep !== 'success'}
    >
      <div className="p-6">
        {paymentStep === 'select' && (
          type === 'credits' ? renderCreditSelection() : renderSubscriptionSelection()
        )}
        {paymentStep === 'payment' && renderPaymentForm()}
        {paymentStep === 'success' && renderSuccess()}
      </div>
    </Modal>
  );
};

// 快速购买积分按钮
export const QuickBuyCreditsButton: React.FC<{
  className?: string;
  variant?: 'button' | 'link';
}> = ({ className, variant = 'button' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  if (variant === 'link') {
    return (
      <>
        <button
          onClick={() => setIsModalOpen(true)}
          className={cn('text-primary-600 hover:text-primary-700 underline', className)}
        >
          购买积分
        </button>
        <PaymentModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          type="credits"
        />
      </>
    );
  }

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className={className}
      >
        <CreditCard className="w-4 h-4 mr-2" />
        购买积分
      </Button>
      <PaymentModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        type="credits"
      />
    </>
  );
};
