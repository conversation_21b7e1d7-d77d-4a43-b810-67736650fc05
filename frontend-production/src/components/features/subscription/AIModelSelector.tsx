import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Zap, Crown, Lock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface AIModelOption {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  requiresPlan: string[];
  performance: {
    creativity: number;
    accuracy: number;
    speed: number;
  };
}

interface AIModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  userPlan: string;
  availableModels: string[];
  features: {
    model: string;
    temperature: number;
    topK: number;
    maxOutputTokens: number;
  };
  disabled?: boolean;
  className?: string;
}

const AI_MODELS: AIModelOption[] = [
  {
    id: 'gemini-2.5-flash',
    name: 'Gemini Flash',
    description: '快速响应的基础AI模型，适合日常创作',
    icon: <Zap className="w-5 h-5" />,
    features: ['快速生成', '基础创意', '标准质量'],
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    performance: {
      creativity: 70,
      accuracy: 75,
      speed: 90
    }
  },
  {
    id: 'gemini-2.5-pro',
    name: 'Gemini Pro',
    description: '高级AI模型，提供更强的创意和更好的故事质量',
    icon: <Brain className="w-5 h-5" />,
    features: ['高级创意', '优质内容', '更好理解力'],
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    performance: {
      creativity: 85,
      accuracy: 90,
      speed: 75
    }
  },
  {
    id: 'gemini-2.5-ultra',
    name: 'Gemini Ultra',
    description: '顶级AI模型，最强创意能力和最高质量输出',
    icon: <Crown className="w-5 h-5" />,
    features: ['顶级创意', '最高质量', '深度理解', '长文本支持'],
    requiresPlan: ['unlimited_monthly'],
    performance: {
      creativity: 95,
      accuracy: 95,
      speed: 60
    }
  }
];

export const AIModelSelector: React.FC<AIModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  userPlan,
  availableModels,
  features,
  disabled = false,
  className = ''
}) => {
  const { t } = useTranslation();

  const isModelAvailable = (model: AIModelOption) => {
    return availableModels.includes(model.id);
  };

  const handleModelChange = (modelId: string) => {
    if (disabled) return;
    onModelChange(modelId);
  };

  const PerformanceBar = ({ label, value, color }: { label: string; value: number; color: string }) => (
    <div className="flex items-center justify-between text-xs">
      <span className="text-gray-600">{label}</span>
      <div className="flex items-center space-x-2">
        <div className="w-16 h-1.5 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className={`h-full ${color}`}
            initial={{ width: 0 }}
            animate={{ width: `${value}%` }}
            transition={{ duration: 0.5, delay: 0.2 }}
          />
        </div>
        <span className="text-gray-500 w-8">{value}%</span>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('aiModel.title', 'AI模型选择')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('aiModel.description', '选择适合您需求的AI模型，不同模型在创意度、准确性和速度方面各有特色')}
        </p>
      </div>

      <div className="grid gap-4">
        {AI_MODELS.map((model) => {
          const isAvailable = isModelAvailable(model);
          const isSelected = selectedModel === model.id;

          return (
            <motion.div
              key={model.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card
                className={`p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                    : isAvailable
                    ? 'hover:border-gray-300 hover:shadow-md'
                    : 'opacity-60 cursor-not-allowed bg-gray-50'
                }`}
                onClick={() => isAvailable && onModelChange(model.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      {model.icon}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{model.name}</h4>
                        {!isAvailable && (
                          <Lock className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{model.description}</p>
                      
                      <div className="flex flex-wrap gap-1 mb-3">
                        {model.features.map((feature, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                          >
                            {feature}
                          </span>
                        ))}
                      </div>

                      <div className="space-y-2">
                        <PerformanceBar 
                          label="创意度" 
                          value={model.performance.creativity} 
                          color="bg-purple-500" 
                        />
                        <PerformanceBar 
                          label="准确性" 
                          value={model.performance.accuracy} 
                          color="bg-green-500" 
                        />
                        <PerformanceBar 
                          label="速度" 
                          value={model.performance.speed} 
                          color="bg-blue-500" 
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                    
                    {!isAvailable && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          // 触发升级流程
                        }}
                      >
                        升级解锁
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {selectedModel && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
        >
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            <span className="text-sm font-medium text-blue-900">
              已选择: {AI_MODELS.find(m => m.id === selectedModel)?.name}
            </span>
          </div>
          <p className="text-xs text-blue-700">
            此模型将用于生成您的故事内容，您可以随时在设置中更改。
          </p>
        </motion.div>
      )}
    </div>
  );
};