import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Volume2, Mic, Star, Crown, Lock, Play, Pause } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface VoiceOption {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'professional' | 'premium';
  requiresPlan: string[];
  sampleUrl?: string;
  gender: 'female' | 'male' | 'child';
  language: string[];
}

interface AudioQuality {
  id: string;
  name: string;
  description: string;
  bitrate: string;
  requiresPlan: string[];
}

interface AudioOptionsSelectorProps {
  selectedVoice: string;
  selectedQuality: string;
  audioSpeed: number;
  audioPitch: number;
  onVoiceChange: (voice: string) => void;
  onQualityChange: (quality: string) => void;
  onSpeedChange: (speed: number) => void;
  onPitchChange: (pitch: number) => void;
  userPlan: string;
  className?: string;
}

const VOICE_OPTIONS: VoiceOption[] = [
  {
    id: 'gentle_female',
    name: '温柔女声',
    description: '温和亲切的女性声音，适合睡前故事',
    category: 'basic',
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    gender: 'female',
    language: ['zh', 'en']
  },
  {
    id: 'warm_male',
    name: '温暖男声',
    description: '温暖有力的男性声音，富有感染力',
    category: 'basic',
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    gender: 'male',
    language: ['zh', 'en']
  },
  {
    id: 'child_friendly',
    name: '儿童友好',
    description: '活泼可爱的声音，深受孩子们喜爱',
    category: 'basic',
    requiresPlan: ['basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    gender: 'child',
    language: ['zh', 'en']
  },
  {
    id: 'storyteller',
    name: '专业讲述者',
    description: '专业的故事讲述声音，富有表现力',
    category: 'professional',
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    gender: 'female',
    language: ['zh', 'en']
  },
  {
    id: 'dramatic',
    name: '戏剧化声音',
    description: '富有戏剧张力的声音，适合冒险故事',
    category: 'premium',
    requiresPlan: ['unlimited_monthly'],
    gender: 'male',
    language: ['zh', 'en']
  },
  {
    id: 'whispering',
    name: '轻声细语',
    description: '轻柔细腻的声音，营造温馨氛围',
    category: 'premium',
    requiresPlan: ['unlimited_monthly'],
    gender: 'female',
    language: ['zh', 'en']
  }
];

const AUDIO_QUALITIES: AudioQuality[] = [
  {
    id: 'standard',
    name: '标准音质',
    description: '清晰的音质，适合日常收听',
    bitrate: '128kbps',
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly']
  },
  {
    id: 'high',
    name: '高保真音质',
    description: '更高的音质，更好的听觉体验',
    bitrate: '256kbps',
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly']
  },
  {
    id: 'premium',
    name: '无损音质',
    description: '顶级音质，专业级音频体验',
    bitrate: '320kbps',
    requiresPlan: ['unlimited_monthly']
  }
];

export const AudioOptionsSelector: React.FC<AudioOptionsSelectorProps> = ({
  selectedVoice,
  selectedQuality,
  audioSpeed,
  audioPitch,
  onVoiceChange,
  onQualityChange,
  onSpeedChange,
  onPitchChange,
  userPlan,
  className = ''
}) => {
  const { t } = useTranslation();
  const [playingVoice, setPlayingVoice] = useState<string | null>(null);

  const isVoiceAvailable = (voice: VoiceOption) => {
    return voice.requiresPlan.includes(userPlan);
  };

  const isQualityAvailable = (quality: AudioQuality) => {
    return quality.requiresPlan.includes(userPlan);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'basic':
        return <Volume2 className="w-4 h-4" />;
      case 'professional':
        return <Star className="w-4 h-4" />;
      case 'premium':
        return <Crown className="w-4 h-4" />;
      default:
        return <Volume2 className="w-4 h-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic':
        return 'text-gray-600 bg-gray-100';
      case 'professional':
        return 'text-blue-600 bg-blue-100';
      case 'premium':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const handlePlaySample = (voiceId: string) => {
    if (playingVoice === voiceId) {
      setPlayingVoice(null);
      // 停止播放逻辑
    } else {
      setPlayingVoice(voiceId);
      // 播放示例音频逻辑
      setTimeout(() => setPlayingVoice(null), 3000); // 模拟播放完成
    }
  };

  const canAdjustSettings = ['pro_monthly', 'unlimited_monthly', 'pro_yearly'].includes(userPlan);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 声音选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('audioOptions.voiceTitle', '声音选择')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('audioOptions.voiceDescription', '选择适合您故事的声音类型')}
        </p>

        <div className="grid gap-3">
          {VOICE_OPTIONS.map((voice) => {
            const isAvailable = isVoiceAvailable(voice);
            const isSelected = selectedVoice === voice.id;
            const isPlaying = playingVoice === voice.id;

            return (
              <motion.div
                key={voice.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card
                  className={`p-4 cursor-pointer transition-all duration-200 ${
                    isSelected
                      ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                      : isAvailable
                      ? 'hover:border-gray-300 hover:shadow-md'
                      : 'opacity-60 cursor-not-allowed bg-gray-50'
                  }`}
                  onClick={() => isAvailable && onVoiceChange(voice.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1">
                      <div className={`p-2 rounded-lg ${
                        isSelected ? getCategoryColor(voice.category) : 'bg-gray-100 text-gray-600'
                      }`}>
                        {getCategoryIcon(voice.category)}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-medium text-gray-900">{voice.name}</h4>
                          <span className={`px-2 py-0.5 text-xs rounded-full ${
                            voice.category === 'premium' ? 'bg-purple-100 text-purple-700' :
                            voice.category === 'professional' ? 'bg-blue-100 text-blue-700' :
                            'bg-gray-100 text-gray-700'
                          }`}>
                            {voice.category === 'premium' ? '高级' : 
                             voice.category === 'professional' ? '专业' : '基础'}
                          </span>
                          {!isAvailable && (
                            <Lock className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{voice.description}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {isAvailable && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePlaySample(voice.id);
                          }}
                          className="p-2"
                        >
                          {isPlaying ? (
                            <Pause className="w-4 h-4" />
                          ) : (
                            <Play className="w-4 h-4" />
                          )}
                        </Button>
                      )}
                      
                      {isSelected && (
                        <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                          <div className="w-2 h-2 bg-white rounded-full" />
                        </div>
                      )}
                      
                      {!isAvailable && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            // 触发升级流程
                          }}
                        >
                          升级解锁
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* 音质选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('audioOptions.qualityTitle', '音质设置')}
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          {t('audioOptions.qualityDescription', '选择音频质量等级')}
        </p>

        <div className="grid gap-3">
          {AUDIO_QUALITIES.map((quality) => {
            const isAvailable = isQualityAvailable(quality);
            const isSelected = selectedQuality === quality.id;

            return (
              <Card
                key={quality.id}
                className={`p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                    : isAvailable
                    ? 'hover:border-gray-300 hover:shadow-md'
                    : 'opacity-60 cursor-not-allowed bg-gray-50'
                }`}
                onClick={() => isAvailable && onQualityChange(quality.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-medium text-gray-900">{quality.name}</h4>
                      <span className="text-xs text-gray-500">({quality.bitrate})</span>
                      {!isAvailable && (
                        <Lock className="w-4 h-4 text-gray-400" />
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{quality.description}</p>
                  </div>

                  <div className="flex items-center space-x-2">
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                    
                    {!isAvailable && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          // 触发升级流程
                        }}
                      >
                        升级解锁
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 高级音频设置 */}
      {canAdjustSettings && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="space-y-4"
        >
          <h3 className="text-lg font-semibold text-gray-900">
            {t('audioOptions.advancedTitle', '高级音频设置')}
          </h3>

          <Card className="p-4">
            <div className="space-y-4">
              {/* 语速调节 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-900">
                    语速调节
                  </label>
                  <span className="text-sm text-gray-600">{audioSpeed}x</span>
                </div>
                <input
                  type="range"
                  min="0.5"
                  max="2.0"
                  step="0.1"
                  value={audioSpeed}
                  onChange={(e) => onSpeedChange(parseFloat(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>慢速 (0.5x)</span>
                  <span>正常 (1.0x)</span>
                  <span>快速 (2.0x)</span>
                </div>
              </div>

              {/* 音调调节 */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-900">
                    音调调节
                  </label>
                  <span className="text-sm text-gray-600">
                    {audioPitch > 0 ? '+' : ''}{audioPitch}
                  </span>
                </div>
                <input
                  type="range"
                  min="-5"
                  max="5"
                  step="1"
                  value={audioPitch}
                  onChange={(e) => onPitchChange(parseInt(e.target.value))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>低音调 (-5)</span>
                  <span>标准 (0)</span>
                  <span>高音调 (+5)</span>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>
      )}

      {!canAdjustSettings && (
        <Card className="p-4 bg-gray-50 border-dashed">
          <div className="text-center">
            <Lock className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <h4 className="text-sm font-medium text-gray-900 mb-1">高级音频设置</h4>
            <p className="text-xs text-gray-600 mb-3">
              升级到专业版或更高版本以解锁语速和音调调节功能
            </p>
            <Button variant="outline" size="sm">
              立即升级
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
};