import React from 'react';
import { motion } from 'framer-motion';
import { Image, Sparkles, Crown, Lock, RotateCcw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface ImageQualityOption {
  id: string;
  name: string;
  description: string;
  resolution: string;
  quality: string;
  retries: number;
  requiresPlan: string[];
  features: string[];
  sampleImage?: string;
}

interface ImageQualitySelectorProps {
  selectedQuality: string;
  onQualityChange: (quality: string) => void;
  userPlan: string;
  availableQualities: string[];
  features: {
    model: string;
    resolution: string;
    quality: string;
    maxRetries: number;
    enhancedPrompt: boolean;
  };
  disabled?: boolean;
  className?: string;
}

const IMAGE_QUALITY_OPTIONS: ImageQualityOption[] = [
  {
    id: 'standard',
    name: '标准质量',
    description: '适合日常使用的基础图片质量',
    resolution: '512x512',
    quality: 'standard',
    retries: 1,
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    features: ['快速生成', '基础细节', '标准分辨率']
  },
  {
    id: 'premium',
    name: '高级质量',
    description: '更高分辨率和更丰富的细节',
    resolution: '1024x1024',
    quality: 'premium',
    retries: 3,
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly'],
    features: ['高分辨率', '丰富细节', '增强提示词', '多次重试']
  },
  {
    id: 'ultra',
    name: '超高质量',
    description: '顶级分辨率和专业级图片质量',
    resolution: '2048x2048',
    quality: 'ultra',
    retries: 5,
    requiresPlan: ['unlimited_monthly'],
    features: ['超高分辨率', '专业级细节', '智能优化', '最多重试', '商业级质量']
  }
];

export const ImageQualitySelector: React.FC<ImageQualitySelectorProps> = ({
  selectedQuality,
  onQualityChange,
  userPlan,
  availableQualities,
  features,
  disabled = false,
  className = ''
}) => {
  const { t } = useTranslation();

  const isQualityAvailable = (option: ImageQualityOption) => {
    return availableQualities.includes(option.id);
  };

  const handleQualityChange = (qualityId: string) => {
    if (disabled) return;
    onQualityChange(qualityId);
  };

  const getQualityIcon = (qualityId: string) => {
    switch (qualityId) {
      case 'standard':
        return <Image className="w-5 h-5" />;
      case 'premium':
        return <Sparkles className="w-5 h-5" />;
      case 'ultra':
        return <Crown className="w-5 h-5" />;
      default:
        return <Image className="w-5 h-5" />;
    }
  };

  const getQualityColor = (qualityId: string) => {
    switch (qualityId) {
      case 'standard':
        return 'text-gray-600 bg-gray-100';
      case 'premium':
        return 'text-blue-600 bg-blue-100';
      case 'ultra':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('imageQuality.title', '图片质量设置')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('imageQuality.description', '选择图片生成质量，更高质量的图片需要更多时间但效果更佳')}
        </p>
      </div>

      <div className="grid gap-4">
        {IMAGE_QUALITY_OPTIONS.map((option) => {
          const isAvailable = isQualityAvailable(option);
          const isSelected = selectedQuality === option.id;

          return (
            <motion.div
              key={option.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card
                className={`p-4 cursor-pointer transition-all duration-200 ${
                  isSelected
                    ? 'ring-2 ring-blue-500 border-blue-200 bg-blue-50'
                    : isAvailable
                    ? 'hover:border-gray-300 hover:shadow-md'
                    : 'opacity-60 cursor-not-allowed bg-gray-50'
                }`}
                onClick={() => isAvailable && onQualityChange(option.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? getQualityColor(option.id) : 'bg-gray-100 text-gray-600'
                    }`}>
                      {getQualityIcon(option.id)}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{option.name}</h4>
                        {!isAvailable && (
                          <Lock className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">{option.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 mb-3">
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500">分辨率</div>
                          <div className="text-sm font-medium text-gray-900">{option.resolution}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="text-xs text-gray-500 flex items-center space-x-1">
                            <RotateCcw className="w-3 h-3" />
                            <span>重试次数</span>
                          </div>
                          <div className="text-sm font-medium text-gray-900">{option.retries}次</div>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-1">
                        {option.features.map((feature, index) => (
                          <span
                            key={index}
                            className={`px-2 py-1 text-xs rounded-full ${
                              isSelected 
                                ? 'bg-blue-100 text-blue-700' 
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    {isSelected && (
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-2 h-2 bg-white rounded-full" />
                      </div>
                    )}
                    
                    {!isAvailable && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          // 触发升级流程
                        }}
                      >
                        升级解锁
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* 质量对比预览 */}
      <Card className="p-4 bg-gray-50">
        <h4 className="text-sm font-medium text-gray-900 mb-3">质量对比</h4>
        <div className="grid grid-cols-3 gap-4 text-center">
          <div className="space-y-2">
            <div className="w-full h-20 bg-gray-200 rounded-lg flex items-center justify-center">
              <span className="text-xs text-gray-500">512x512</span>
            </div>
            <div className="text-xs text-gray-600">标准质量</div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-20 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-xs text-blue-600">1024x1024</span>
            </div>
            <div className="text-xs text-gray-600">高级质量</div>
          </div>
          <div className="space-y-2">
            <div className="w-full h-20 bg-purple-100 rounded-lg flex items-center justify-center">
              <span className="text-xs text-purple-600">2048x2048</span>
            </div>
            <div className="text-xs text-gray-600">超高质量</div>
          </div>
        </div>
      </Card>

      {selectedQuality && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200"
        >
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full" />
            <span className="text-sm font-medium text-blue-900">
              已选择: {IMAGE_QUALITY_OPTIONS.find(o => o.id === selectedQuality)?.name}
            </span>
          </div>
          <p className="text-xs text-blue-700">
            图片将以 {IMAGE_QUALITY_OPTIONS.find(o => o.id === selectedQuality)?.resolution} 分辨率生成，
            失败时最多重试 {IMAGE_QUALITY_OPTIONS.find(o => o.id === selectedQuality)?.retries} 次。
          </p>
        </motion.div>
      )}
    </div>
  );
};