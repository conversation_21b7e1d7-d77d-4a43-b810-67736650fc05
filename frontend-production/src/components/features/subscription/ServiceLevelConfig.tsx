import React from 'react';
import { motion } from 'framer-motion';
import { Headphones, FileText, Cloud, Crown, Check, Lock } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface ServiceLevel {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  requiresPlan: string[];
}

interface ServiceLevelConfigProps {
  userPlan: string;
  currentServices: {
    supportLevel: string;
    exportFormats: string[];
    storageSpace: number;
  };
  className?: string;
}

const SUPPORT_LEVELS: ServiceLevel[] = [
  {
    id: 'basic',
    name: '基础邮件支持',
    description: '工作日内24小时响应',
    icon: <Headphones className="w-5 h-5" />,
    features: ['邮件支持', '常见问题解答', '用户手册'],
    requiresPlan: ['free', 'basic_monthly', 'pro_monthly', 'unlimited_monthly', 'pro_yearly']
  },
  {
    id: 'priority',
    name: '优先客服支持',
    description: '12小时内优先响应',
    icon: <Crown className="w-5 h-5" />,
    features: ['优先邮件支持', '在线客服', '电话支持', '专属客服'],
    requiresPlan: ['pro_monthly', 'unlimited_monthly', 'pro_yearly']
  },
  {
    id: 'dedicated',
    name: '专属客服经理',
    description: '1对1专属服务',
    icon: <Crown className="w-5 h-5" />,
    features: ['专属客服经理', '即时响应', '定制化服务', 'VIP通道'],
    requiresPlan: ['unlimited_monthly']
  }
];

export const ServiceLevelConfig: React.FC<ServiceLevelConfigProps> = ({
  userPlan,
  currentServices,
  className = ''
}) => {
  const { t } = useTranslation();

  const getStorageLimit = (plan: string) => {
    const limits = {
      free: 0.1, // 100MB
      basic_monthly: 1, // 1GB
      pro_monthly: 5, // 5GB
      unlimited_monthly: 10, // 10GB
      pro_yearly: 5 // 5GB
    };
    return limits[plan as keyof typeof limits] || 0.1;
  };

  const getExportFormats = (plan: string) => {
    const formats = {
      free: ['PDF'],
      basic_monthly: ['PDF', 'EPUB'],
      pro_monthly: ['PDF', 'EPUB', 'MP3'],
      unlimited_monthly: ['PDF', 'EPUB', 'MP3', 'DOCX', 'JSON'],
      pro_yearly: ['PDF', 'EPUB', 'MP3']
    };
    return formats[plan as keyof typeof formats] || ['PDF'];
  };

  const isServiceAvailable = (service: ServiceLevel) => {
    return service.requiresPlan.includes(userPlan);
  };

  const getCurrentSupportLevel = () => {
    if (userPlan === 'unlimited_monthly') return 'dedicated';
    if (['pro_monthly', 'pro_yearly'].includes(userPlan)) return 'priority';
    return 'basic';
  };

  const formatStorageSize = (gb: number) => {
    if (gb < 1) return `${Math.round(gb * 1000)}MB`;
    return `${gb}GB`;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('serviceLevel.title', '服务等级配置')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('serviceLevel.description', '查看您当前的服务等级和可用功能')}
        </p>
      </div>

      {/* 客服支持等级 */}
      <div>
        <h4 className="text-md font-medium text-gray-900 mb-3">客服支持等级</h4>
        <div className="space-y-3">
          {SUPPORT_LEVELS.map((level) => {
            const isAvailable = isServiceAvailable(level);
            const isCurrent = getCurrentSupportLevel() === level.id;

            return (
              <Card
                key={level.id}
                className={`p-4 ${
                  isCurrent 
                    ? 'ring-2 ring-green-500 border-green-200 bg-green-50' 
                    : isAvailable 
                    ? 'border-gray-200' 
                    : 'opacity-60 bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className={`p-2 rounded-lg ${
                      isCurrent 
                        ? 'bg-green-100 text-green-600' 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {level.icon}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h5 className="font-medium text-gray-900">{level.name}</h5>
                        {isCurrent && (
                          <span className="px-2 py-0.5 text-xs bg-green-100 text-green-700 rounded-full">
                            当前等级
                          </span>
                        )}
                        {!isAvailable && (
                          <Lock className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{level.description}</p>
                      <div className="flex flex-wrap gap-1">
                        {level.features.map((feature, index) => (
                          <span
                            key={index}
                            className={`px-2 py-1 text-xs rounded-full ${
                              isCurrent 
                                ? 'bg-green-100 text-green-700' 
                                : 'bg-gray-100 text-gray-700'
                            }`}
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center">
                    {isCurrent && (
                      <Check className="w-6 h-6 text-green-600" />
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* 导出格式 */}
      <Card className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <FileText className="w-5 h-5 text-blue-600" />
          <h4 className="text-md font-medium text-gray-900">可用导出格式</h4>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
          {getExportFormats(userPlan).map((format) => (
            <div
              key={format}
              className="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg"
            >
              <Check className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">{format}</span>
            </div>
          ))}
        </div>
      </Card>

      {/* 云存储空间 */}
      <Card className="p-4">
        <div className="flex items-center space-x-2 mb-3">
          <Cloud className="w-5 h-5 text-purple-600" />
          <h4 className="text-md font-medium text-gray-900">云存储空间</h4>
        </div>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">已使用</span>
            <span className="text-sm font-medium">
              {formatStorageSize(currentServices.storageSpace)} / {formatStorageSize(getStorageLimit(userPlan))}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              className="h-2 bg-purple-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ 
                width: `${Math.min((currentServices.storageSpace / getStorageLimit(userPlan)) * 100, 100)}%` 
              }}
              transition={{ duration: 0.5 }}
            />
          </div>
          <p className="text-xs text-gray-500">
            存储您的故事、图片和音频文件
          </p>
        </div>
      </Card>
    </div>
  );
};