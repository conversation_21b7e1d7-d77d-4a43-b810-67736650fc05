import React, { useState, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Play, Pause, Volume2 } from 'lucide-react';

interface ContentPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentType: 'text' | 'image' | 'audio';
  storyId: string;
  taskId: string;
}

interface StoryContent {
  text?: {
    pages: Array<{
      pageNumber: number;
      content: string;
    }>;
  };
  images?: Array<{
    pageNumber: number;
    url: string;
    description?: string;
  }>;
  audio?: {
    url: string;
    duration?: number;
  };
}

export const ContentPreviewModal: React.FC<ContentPreviewModalProps> = ({
  isOpen,
  onClose,
  contentType,
  storyId,
  taskId
}) => {
  const [content, setContent] = useState<StoryContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null);

  useEffect(() => {
    if (isOpen && storyId && taskId) {
      fetchContent();
    }
  }, [isOpen, storyId, taskId, contentType]);

  useEffect(() => {
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
      }
    };
  }, [audioElement]);

  const fetchContent = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 首先尝试从DO获取内容
      const doResponse = await fetch(`/ai-queue/${storyId}/content?taskId=${taskId}&type=${contentType}`);
      
      if (doResponse.ok) {
        const doContent = await doResponse.json();
        if (doContent.success && doContent.content) {
          setContent(doContent.content);
          return;
        }
      }
      
      // 降级到数据库查询
      const dbResponse = await fetch(`/api/stories/${storyId}/content?type=${contentType}`);
      
      if (!dbResponse.ok) {
        throw new Error('Failed to fetch content');
      }
      
      const dbContent = await dbResponse.json();
      if (dbContent.success) {
        setContent(dbContent.data);
      } else {
        throw new Error(dbContent.error || 'Content not available');
      }
      
    } catch (err) {
      console.error('Error fetching content:', err);
      setError(err instanceof Error ? err.message : 'Failed to load content');
    } finally {
      setLoading(false);
    }
  };

  const handleAudioPlay = () => {
    if (!content?.audio?.url) return;
    
    if (!audioElement) {
      const audio = new Audio(content.audio.url);
      audio.addEventListener('ended', () => setIsPlaying(false));
      audio.addEventListener('error', () => {
        setError('Audio playback failed');
        setIsPlaying(false);
      });
      setAudioElement(audio);
      audio.play();
      setIsPlaying(true);
    } else {
      if (isPlaying) {
        audioElement.pause();
        setIsPlaying(false);
      } else {
        audioElement.play();
        setIsPlaying(true);
      }
    }
  };

  const renderTextContent = () => {
    if (!content?.text?.pages || content.text.pages.length === 0) {
      return <div className="text-gray-500 text-center py-8">暂无文本内容</div>;
    }

    const currentPageData = content.text.pages[currentPage];
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">故事文本预览</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={currentPage === 0}
              className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <span className="text-sm text-gray-600">
              {currentPage + 1} / {content.text.pages.length}
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(content.text.pages.length - 1, currentPage + 1))}
              disabled={currentPage === content.text.pages.length - 1}
              className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-6 min-h-[300px]">
          <div className="text-sm text-gray-500 mb-2">第 {currentPageData.pageNumber} 页</div>
          <div className="text-gray-900 leading-relaxed whitespace-pre-wrap">
            {currentPageData.content}
          </div>
        </div>
      </div>
    );
  };

  const renderImageContent = () => {
    if (!content?.images || content.images.length === 0) {
      return <div className="text-gray-500 text-center py-8">暂无图片内容</div>;
    }

    const currentImage = content.images[currentPage];
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">故事插图预览</h3>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
              disabled={currentPage === 0}
              className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <span className="text-sm text-gray-600">
              {currentPage + 1} / {content.images.length}
            </span>
            <button
              onClick={() => setCurrentPage(Math.min(content.images.length - 1, currentPage + 1))}
              disabled={currentPage === content.images.length - 1}
              className="p-1 rounded-full hover:bg-gray-100 disabled:opacity-50"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="text-sm text-gray-500 mb-2">第 {currentImage.pageNumber} 页插图</div>
          <div className="flex justify-center">
            <img
              src={currentImage.url}
              alt={currentImage.description || `第${currentImage.pageNumber}页插图`}
              className="max-w-full max-h-96 rounded-lg shadow-sm"
              onError={(e) => {
                e.currentTarget.src = '/placeholder-image.png';
              }}
            />
          </div>
          {currentImage.description && (
            <div className="text-sm text-gray-600 mt-2 text-center">
              {currentImage.description}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderAudioContent = () => {
    if (!content?.audio?.url) {
      return <div className="text-gray-500 text-center py-8">暂无音频内容</div>;
    }

    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">语音旁白预览</h3>

        <div className="bg-gray-50 rounded-lg p-8 text-center">
          <div className="mb-6">
            <Volume2 className="w-16 h-16 text-blue-500 mx-auto mb-4" />
            <div className="text-gray-600 mb-2">故事语音旁白</div>
            {content.audio.duration && (
              <div className="text-sm text-gray-500">
                时长: {Math.floor(content.audio.duration / 60)}:{(content.audio.duration % 60).toFixed(0).padStart(2, '0')}
              </div>
            )}
          </div>

          <div className="space-y-4">
            <button
              onClick={handleAudioPlay}
              className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              <span>{isPlaying ? '暂停' : '播放'}</span>
            </button>

            {/* 音频URL信息 */}
            <div className="text-xs text-gray-400 break-all">
              音频地址: {content.audio.url}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">内容预览</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              <span className="ml-3 text-gray-600">加载中...</span>
            </div>
          )}
          
          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-2">加载失败</div>
              <div className="text-gray-500 text-sm">{error}</div>
              <button
                onClick={fetchContent}
                className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
            </div>
          )}
          
          {!loading && !error && content && (
            <>
              {contentType === 'text' && renderTextContent()}
              {contentType === 'image' && renderImageContent()}
              {contentType === 'audio' && renderAudioContent()}
            </>
          )}
        </div>
      </div>
    </div>
  );
};
