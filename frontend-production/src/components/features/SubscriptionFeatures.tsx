import React from 'react';
import { useTranslation } from 'react-i18next';
import { Check, X } from 'lucide-react';
import { useAuthStore } from '../../stores/authStore';

interface SubscriptionFeaturesProps {
  compact?: boolean;
  showTitle?: boolean;
}

const SubscriptionFeatures: React.FC<SubscriptionFeaturesProps> = ({ 
  compact = false,
  showTitle = true
}) => {
  const { t } = useTranslation();
  const { user } = useAuthStore();
  
  // 获取用户当前订阅计划
  const subscription = user?.subscription;
  const plan = typeof subscription?.plan === 'string' ? subscription?.plan : 'free';
  
  // 根据订阅计划获取特色功能
  const getFeatures = () => {
    switch (plan) {
      case 'basic_monthly':
        return {
          aiModel: t('subscription.features.basic.aiModel'),
          imageQuality: t('subscription.features.basic.imageQuality'),
          audioQuality: t('subscription.features.basic.audioQuality'),
          maxStories: t('subscription.features.basic.maxStories', { count: 50 }),
          maxPages: t('subscription.features.basic.maxPages', { count: 10 }),
          support: t('subscription.features.basic.support'),
          commercialUse: false,
          apiAccess: false,
          prioritySupport: false,
          customCharacters: t('subscription.features.basic.customCharacters', { count: 2 }),
          exportFormats: ['PDF', 'EPUB']
        };
      case 'pro_monthly':
      case 'pro_yearly':
        return {
          aiModel: t('subscription.features.pro.aiModel'),
          imageQuality: t('subscription.features.pro.imageQuality'),
          audioQuality: t('subscription.features.pro.audioQuality'),
          maxStories: t('subscription.features.pro.maxStories', { count: 200 }),
          maxPages: t('subscription.features.pro.maxPages', { count: 15 }),
          support: t('subscription.features.pro.support'),
          commercialUse: false,
          apiAccess: false,
          prioritySupport: true,
          customCharacters: t('subscription.features.pro.customCharacters', { count: 5 }),
          exportFormats: ['PDF', 'EPUB', 'MP3']
        };
      case 'unlimited_monthly':
        return {
          aiModel: t('subscription.features.unlimited.aiModel'),
          imageQuality: t('subscription.features.unlimited.imageQuality'),
          audioQuality: t('subscription.features.unlimited.audioQuality'),
          maxStories: t('subscription.features.unlimited.maxStories'),
          maxPages: t('subscription.features.unlimited.maxPages', { count: 30 }),
          support: t('subscription.features.unlimited.support'),
          commercialUse: true,
          apiAccess: true,
          prioritySupport: true,
          customCharacters: t('subscription.features.unlimited.customCharacters', { count: 20 }),
          exportFormats: ['PDF', 'EPUB', 'MP3', 'DOCX', 'JSON']
        };
      default: // free
        return {
          aiModel: t('subscription.features.free.aiModel'),
          imageQuality: t('subscription.features.free.imageQuality'),
          audioQuality: t('subscription.features.free.audioQuality'),
          maxStories: t('subscription.features.free.maxStories', { count: 5 }),
          maxPages: t('subscription.features.free.maxPages', { count: 8 }),
          support: t('subscription.features.free.support'),
          commercialUse: false,
          apiAccess: false,
          prioritySupport: false,
          customCharacters: t('subscription.features.free.customCharacters', { count: 0 }),
          exportFormats: ['PDF']
        };
    }
  };
  
  const features = getFeatures();
  
  // 紧凑模式只显示核心功能
  const coreFeatures = compact ? [
    { label: t('subscription.aiModel'), value: features.aiModel },
    { label: t('subscription.imageQuality'), value: features.imageQuality },
    { label: t('subscription.maxStories'), value: features.maxStories }
  ] : [
    { label: t('subscription.aiModel'), value: features.aiModel },
    { label: t('subscription.imageQuality'), value: features.imageQuality },
    { label: t('subscription.audioQuality'), value: features.audioQuality },
    { label: t('subscription.maxStories'), value: features.maxStories },
    { label: t('subscription.maxPages'), value: features.maxPages },
    { label: t('subscription.customCharacters'), value: features.customCharacters },
    { label: t('subscription.support'), value: features.support }
  ];
  
  // 布尔特性（有/无）
  const booleanFeatures = compact ? [] : [
    { label: t('subscription.commercialUse'), value: features.commercialUse },
    { label: t('subscription.apiAccess'), value: features.apiAccess },
    { label: t('subscription.prioritySupport'), value: features.prioritySupport }
  ];
  
  // 获取计划名称
  const getPlanName = () => {
    switch (plan) {
      case 'basic_monthly': return t('plans.basic');
      case 'pro_monthly': return t('plans.pro');
      case 'pro_yearly': return t('plans.proYearly');
      case 'unlimited_monthly': return t('plans.unlimited');
      default: return t('plans.free');
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-100">
      {showTitle && (
        <div className="mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            {t('subscription.currentPlan')}: <span className="text-primary">{getPlanName()}</span>
          </h3>
          {subscription?.status === 'active' && (
            <p className="text-sm text-gray-500">
              {t('subscription.validUntil')}: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}
            </p>
          )}
        </div>
      )}
      
      <div className="space-y-3">
        {/* 核心功能 */}
        {coreFeatures.map((feature, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{feature.label}</span>
            <span className="text-sm font-medium text-gray-900">{feature.value}</span>
          </div>
        ))}
        
        {/* 布尔特性 */}
        {booleanFeatures.map((feature, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{feature.label}</span>
            {feature.value ? (
              <Check className="w-5 h-5 text-green-500" />
            ) : (
              <X className="w-5 h-5 text-red-400" />
            )}
          </div>
        ))}
        
        {/* 导出格式 */}
        {!compact && (
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{t('subscription.exportFormats')}</span>
            <span className="text-sm font-medium text-gray-900">{features.exportFormats.join(', ')}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default SubscriptionFeatures;