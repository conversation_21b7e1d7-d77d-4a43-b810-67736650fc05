import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageCircle, 
  X, 
  Send, 
  Star, 
  ThumbsUp, 
  ThumbsDown,
  Bug,
  Lightbulb,
  Heart
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { useNotifications } from '@/stores/uiStore';

interface FeedbackData {
  type: 'bug' | 'feature' | 'general' | 'rating';
  rating?: number;
  message: string;
  email?: string;
  page: string;
  userAgent: string;
  timestamp: string;
}

interface FeedbackSystemProps {
  className?: string;
}

export const FeedbackSystem: React.FC<FeedbackSystemProps> = ({ className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<'type' | 'form' | 'success'>('type');
  const [feedbackType, setFeedbackType] = useState<FeedbackData['type']>('general');
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { showSuccess, showError } = useNotifications();

  const feedbackTypes = [
    {
      type: 'bug' as const,
      title: '报告问题',
      description: '发现了bug或错误',
      icon: Bug,
      color: 'text-red-600 bg-red-100',
    },
    {
      type: 'feature' as const,
      title: '功能建议',
      description: '建议新功能或改进',
      icon: Lightbulb,
      color: 'text-yellow-600 bg-yellow-100',
    },
    {
      type: 'general' as const,
      title: '一般反馈',
      description: '其他意见或建议',
      icon: MessageCircle,
      color: 'text-blue-600 bg-blue-100',
    },
    {
      type: 'rating' as const,
      title: '评价体验',
      description: '为我们的服务打分',
      icon: Star,
      color: 'text-purple-600 bg-purple-100',
    },
  ];

  const handleTypeSelect = (type: FeedbackData['type']) => {
    setFeedbackType(type);
    setStep('form');
  };

  const handleSubmit = async () => {
    if (!message.trim() && feedbackType !== 'rating') {
      showError('请填写反馈内容', '');
      return;
    }

    if (feedbackType === 'rating' && rating === 0) {
      showError('请选择评分', '');
      return;
    }

    setIsSubmitting(true);

    try {
      const feedbackData: FeedbackData = {
        type: feedbackType,
        rating: feedbackType === 'rating' ? rating : undefined,
        message: message.trim(),
        email: email.trim(),
        page: window.location.pathname,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString(),
      };

      // TODO: 发送反馈到后端
      await submitFeedback(feedbackData);

      setStep('success');
      showSuccess('反馈提交成功', '感谢您的宝贵意见！');
      
      // Auto close after success
      setTimeout(() => {
        handleClose();
      }, 2000);
      
    } catch (error) {
      showError('提交失败', '请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const submitFeedback = async (data: FeedbackData): Promise<void> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In real implementation, send to backend
    console.log('Feedback submitted:', data);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(() => {
      setStep('type');
      setFeedbackType('general');
      setRating(0);
      setMessage('');
      setEmail('');
    }, 300);
  };

  const renderStarRating = () => (
    <div className="flex items-center justify-center space-x-2 mb-4">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          onClick={() => setRating(star)}
          className={`p-1 transition-colors ${
            star <= rating ? 'text-yellow-400' : 'text-gray-300 hover:text-yellow-300'
          }`}
        >
          <Star className="w-8 h-8 fill-current" />
        </button>
      ))}
    </div>
  );

  const renderTypeSelection = () => (
    <div className="space-y-3">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        选择反馈类型
      </h3>
      {feedbackTypes.map((type) => {
        const Icon = type.icon;
        return (
          <button
            key={type.type}
            onClick={() => handleTypeSelect(type.type)}
            className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
          >
            <div className="flex items-center space-x-3">
              <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${type.color}`}>
                <Icon className="w-5 h-5" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900">{type.title}</h4>
                <p className="text-sm text-gray-600">{type.description}</p>
              </div>
            </div>
          </button>
        );
      })}
    </div>
  );

  const renderForm = () => {
    const selectedType = feedbackTypes.find(t => t.type === feedbackType);
    const Icon = selectedType?.icon || MessageCircle;

    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-3 mb-4">
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${selectedType?.color}`}>
            <Icon className="w-5 h-5" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {selectedType?.title}
            </h3>
            <p className="text-sm text-gray-600">{selectedType?.description}</p>
          </div>
        </div>

        {feedbackType === 'rating' && renderStarRating()}

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {feedbackType === 'rating' ? '详细评价 (可选)' : '详细描述'}
          </label>
          <textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder={
              feedbackType === 'bug' 
                ? '请描述遇到的问题，包括操作步骤和预期结果...'
                : feedbackType === 'feature'
                ? '请描述您希望的功能或改进建议...'
                : '请告诉我们您的想法...'
            }
            className="w-full h-24 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
            required={feedbackType !== 'rating'}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            邮箱 (可选)
          </label>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="如需回复，请留下邮箱"
          />
        </div>

        <div className="flex space-x-3 pt-4">
          <Button
            variant="outline"
            onClick={() => setStep('type')}
            disabled={isSubmitting}
            className="flex-1"
          >
            返回
          </Button>
          <Button
            onClick={handleSubmit}
            loading={isSubmitting}
            loadingText="提交中..."
            className="flex-1"
          >
            <Send className="w-4 h-4 mr-2" />
            提交反馈
          </Button>
        </div>
      </div>
    );
  };

  const renderSuccess = () => (
    <div className="text-center py-8">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <Heart className="w-8 h-8 text-green-600" />
      </div>
      <h3 className="text-lg font-semibold text-gray-900 mb-2">
        感谢您的反馈！
      </h3>
      <p className="text-gray-600">
        我们已收到您的意见，将认真考虑并持续改进我们的服务。
      </p>
    </div>
  );

  return (
    <>
      {/* Feedback Button */}
      <motion.button
        onClick={() => setIsOpen(true)}
        className={`fixed bottom-6 right-6 w-14 h-14 bg-primary-500 hover:bg-primary-600 text-white rounded-full shadow-lg flex items-center justify-center z-40 transition-colors ${className}`}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <MessageCircle className="w-6 h-6" />
      </motion.button>

      {/* Feedback Modal */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={(e) => e.target === e.currentTarget && handleClose()}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="w-full max-w-md"
            >
              <Card className="relative">
                <button
                  onClick={handleClose}
                  className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>

                <div className="p-6">
                  {step === 'type' && renderTypeSelection()}
                  {step === 'form' && renderForm()}
                  {step === 'success' && renderSuccess()}
                </div>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Quick feedback buttons for specific actions
export const QuickFeedback: React.FC<{
  onPositive?: () => void;
  onNegative?: () => void;
  className?: string;
}> = ({ onPositive, onNegative, className = '' }) => {
  const [feedback, setFeedback] = useState<'positive' | 'negative' | null>(null);

  const handleFeedback = (type: 'positive' | 'negative') => {
    setFeedback(type);
    if (type === 'positive') {
      onPositive?.();
    } else {
      onNegative?.();
    }
  };

  if (feedback) {
    return (
      <div className={`text-center py-2 ${className}`}>
        <p className="text-sm text-gray-600">
          {feedback === 'positive' ? '感谢您的反馈！' : '我们会努力改进'}
        </p>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center space-x-4 ${className}`}>
      <span className="text-sm text-gray-600">这个功能有用吗？</span>
      <div className="flex space-x-2">
        <button
          onClick={() => handleFeedback('positive')}
          className="p-2 text-gray-400 hover:text-green-500 transition-colors"
        >
          <ThumbsUp className="w-4 h-4" />
        </button>
        <button
          onClick={() => handleFeedback('negative')}
          className="p-2 text-gray-400 hover:text-red-500 transition-colors"
        >
          <ThumbsDown className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};
