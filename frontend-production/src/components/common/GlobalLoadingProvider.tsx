import React, { createContext, useContext, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface LoadingState {
  isLoading: boolean;
  message?: string;
  progress?: number;
}

interface GlobalLoadingContextType {
  loadingStates: Record<string, LoadingState>;
  setLoading: (key: string, loading: boolean, message?: string, progress?: number) => void;
  clearLoading: (key: string) => void;
  clearAllLoading: () => void;
  isAnyLoading: boolean;
}

const GlobalLoadingContext = createContext<GlobalLoadingContextType | undefined>(undefined);

export const useGlobalLoading = () => {
  const context = useContext(GlobalLoadingContext);
  if (!context) {
    throw new Error('useGlobalLoading must be used within a GlobalLoadingProvider');
  }
  return context;
};

interface GlobalLoadingProviderProps {
  children: React.ReactNode;
}

export const GlobalLoadingProvider: React.FC<GlobalLoadingProviderProps> = ({ children }) => {
  const [loadingStates, setLoadingStates] = useState<Record<string, LoadingState>>({});

  const setLoading = useCallback((key: string, loading: boolean, message?: string, progress?: number) => {
    setLoadingStates(prev => {
      if (!loading) {
        const { [key]: removed, ...rest } = prev;
        return rest;
      }
      return {
        ...prev,
        [key]: { isLoading: loading, message, progress }
      };
    });
  }, []);

  const clearLoading = useCallback((key: string) => {
    setLoadingStates(prev => {
      const { [key]: removed, ...rest } = prev;
      return rest;
    });
  }, []);

  const clearAllLoading = useCallback(() => {
    setLoadingStates({});
  }, []);

  const isAnyLoading = Object.keys(loadingStates).length > 0;

  // Get the most important loading state to display
  const primaryLoadingState = Object.values(loadingStates)[0];

  return (
    <GlobalLoadingContext.Provider
      value={{
        loadingStates,
        setLoading,
        clearLoading,
        clearAllLoading,
        isAnyLoading,
      }}
    >
      {children}
      
      {/* Global Loading Overlay */}
      <AnimatePresence>
        {isAnyLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            style={{ backdropFilter: 'blur(4px)' }}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              className="bg-white rounded-lg p-8 max-w-sm w-full mx-4 text-center shadow-xl"
            >
              <LoadingSpinner size="lg" className="mb-4" />
              
              {primaryLoadingState?.message && (
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {primaryLoadingState.message}
                </h3>
              )}
              
              {primaryLoadingState?.progress !== undefined && (
                <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                  <motion.div
                    className="bg-primary-500 h-2 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${primaryLoadingState.progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              )}
              
              <p className="text-gray-600 text-sm">
                请稍候，正在处理您的请求...
              </p>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </GlobalLoadingContext.Provider>
  );
};

// Hook for managing loading states with automatic cleanup
export const useLoadingState = (key: string) => {
  const { setLoading, clearLoading } = useGlobalLoading();

  const startLoading = useCallback((message?: string, progress?: number) => {
    setLoading(key, true, message, progress);
  }, [key, setLoading]);

  const stopLoading = useCallback(() => {
    clearLoading(key);
  }, [key, clearLoading]);

  const updateProgress = useCallback((progress: number, message?: string) => {
    setLoading(key, true, message, progress);
  }, [key, setLoading]);

  // Cleanup on unmount
  React.useEffect(() => {
    return () => {
      clearLoading(key);
    };
  }, [key, clearLoading]);

  return {
    startLoading,
    stopLoading,
    updateProgress,
  };
};

// Hook for async operations with loading state
export const useAsyncOperation = <T extends any[], R>(
  operation: (...args: T) => Promise<R>,
  loadingKey: string,
  options?: {
    loadingMessage?: string;
    successMessage?: string;
    errorMessage?: string;
    onSuccess?: (result: R) => void;
    onError?: (error: Error) => void;
  }
) => {
  const { startLoading, stopLoading } = useLoadingState(loadingKey);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(async (...args: T): Promise<R | undefined> => {
    try {
      setIsLoading(true);
      setError(null);
      startLoading(options?.loadingMessage);

      const result = await operation(...args);

      options?.onSuccess?.(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error');
      setError(error);
      options?.onError?.(error);
      throw error;
    } finally {
      setIsLoading(false);
      stopLoading();
    }
  }, [operation, startLoading, stopLoading, options]);

  return {
    execute,
    isLoading,
    error,
  };
};

// Component for inline loading states
export const InlineLoading: React.FC<{
  isLoading: boolean;
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}> = ({ isLoading, message, size = 'md', className = '' }) => {
  if (!isLoading) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={`flex items-center justify-center space-x-3 ${className}`}
    >
      <LoadingSpinner size={size} />
      {message && (
        <span className="text-gray-600 text-sm">{message}</span>
      )}
    </motion.div>
  );
};

// Component for section loading states
export const SectionLoading: React.FC<{
  isLoading: boolean;
  message?: string;
  height?: string;
  className?: string;
}> = ({ isLoading, message = '加载中...', height = 'h-64', className = '' }) => {
  if (!isLoading) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`flex flex-col items-center justify-center ${height} ${className}`}
    >
      <LoadingSpinner size="lg" className="mb-4" />
      <p className="text-gray-600">{message}</p>
    </motion.div>
  );
};

// Component for button loading states
export const ButtonLoading: React.FC<{
  isLoading: boolean;
  children: React.ReactNode;
  loadingText?: string;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
}> = ({ isLoading, children, loadingText, disabled, className = '', onClick }) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`relative ${className} ${isLoading ? 'cursor-not-allowed' : ''}`}
    >
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" className="mr-2" />
          {loadingText && <span>{loadingText}</span>}
        </div>
      )}
    </button>
  );
};
