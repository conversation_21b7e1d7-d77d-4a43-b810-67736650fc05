/**
 * 权限守卫组件
 * 用于包装需要权限检查的组件
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Lock, Crown, AlertCircle } from 'lucide-react';
import { Button } from './Button';
import { Card } from './Card';
import { useSubscriptionMiddleware } from '@/middleware/subscriptionMiddleware';

interface PermissionGuardProps {
  children: React.ReactNode;
  permission: 'createStory' | 'aiModel' | 'imageQuality' | 'voice' | 'commercial' | 'api';
  value?: string; // 用于检查特定值的权限，如AI模型ID
  fallback?: React.ReactNode;
  onUpgrade?: (requiredPlan: string) => void;
  className?: string;
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  permission,
  value,
  fallback,
  onUpgrade,
  className = ''
}) => {
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [reason, setReason] = useState<string>('');
  const [upgradeRequired, setUpgradeRequired] = useState<string>('');
  const [loading, setLoading] = useState(true);
  
  const {
    canCreateStory,
    canUseAIModel,
    canUseImageQuality,
    canUseVoice,
    canUseCommercially,
    canAccessAPI
  } = useSubscriptionMiddleware();

  useEffect(() => {
    checkPermission();
  }, [permission, value]);

  const checkPermission = async () => {
    setLoading(true);
    try {
      let result;
      
      switch (permission) {
        case 'createStory':
          result = await canCreateStory();
          break;
        case 'aiModel':
          result = value ? await canUseAIModel(value) : { allowed: true };
          break;
        case 'imageQuality':
          result = value ? await canUseImageQuality(value) : { allowed: true };
          break;
        case 'voice':
          result = value ? await canUseVoice(value) : { allowed: true };
          break;
        case 'commercial':
          result = await canUseCommercially();
          break;
        case 'api':
          result = await canAccessAPI();
          break;
        default:
          result = { allowed: true };
      }
      
      setHasPermission(result.allowed);
      setReason(result.reason || '');
      setUpgradeRequired(result.upgradeRequired || '');
    } catch (error) {
      console.error('权限检查失败:', error);
      setHasPermission(false);
      setReason('权限检查失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = () => {
    if (onUpgrade && upgradeRequired) {
      onUpgrade(upgradeRequired);
    }
  };

  const getPlanDisplayName = (plan: string): string => {
    switch (plan) {
      case 'basic_monthly':
        return '基础版';
      case 'pro_monthly':
        return '专业版';
      case 'unlimited_monthly':
        return '无限版';
      case 'pro_yearly':
        return '专业版（年付）';
      default:
        return '高级版本';
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg h-32"></div>
      </div>
    );
  }

  if (hasPermission) {
    return <>{children}</>;
  }

  // 如果提供了fallback，使用fallback
  if (fallback) {
    return <>{fallback}</>;
  }

  // 默认的权限不足提示
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <Card className="p-6 border-2 border-dashed border-gray-300 bg-gray-50">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-orange-100 rounded-full">
              <Lock className="w-6 h-6 text-orange-600" />
            </div>
          </div>
          
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            功能需要升级
          </h3>
          
          <p className="text-gray-600 mb-4">
            {reason}
          </p>
          
          {upgradeRequired && (
            <div className="space-y-3">
              <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
                <Crown className="w-4 h-4" />
                <span>需要 {getPlanDisplayName(upgradeRequired)} 或更高版本</span>
              </div>
              
              <Button
                onClick={handleUpgrade}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Crown className="w-4 h-4 mr-2" />
                立即升级
              </Button>
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
};

/**
 * 权限检查Hook
 */
export const usePermissionCheck = () => {
  const middleware = useSubscriptionMiddleware();
  
  const checkPermission = async (
    permission: PermissionGuardProps['permission'],
    value?: string
  ): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: string }> => {
    switch (permission) {
      case 'createStory':
        return await middleware.canCreateStory();
      case 'aiModel':
        return value ? await middleware.canUseAIModel(value) : { allowed: true };
      case 'imageQuality':
        return value ? await middleware.canUseImageQuality(value) : { allowed: true };
      case 'voice':
        return value ? await middleware.canUseVoice(value) : { allowed: true };
      case 'commercial':
        return await middleware.canUseCommercially();
      case 'api':
        return await middleware.canAccessAPI();
      default:
        return { allowed: true };
    }
  };
  
  return { checkPermission };
};