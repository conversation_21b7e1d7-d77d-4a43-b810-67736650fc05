import React from 'react';
import { Coins, AlertTriangle, X, ShoppingCart } from 'lucide-react';
import { Button } from './Button';
import { CreditBalance } from './CreditBalance';
import { cn } from '@/utils/cn';

interface CreditConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  cost: number;
  action: string;
  description?: string;
  currentCredits: number;
  loading?: boolean;
  onPurchaseClick?: () => void;
}

export const CreditConfirmDialog: React.FC<CreditConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  cost,
  action,
  description,
  currentCredits,
  loading = false,
  onPurchaseClick
}) => {
  const insufficient = currentCredits < cost;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Coins className="w-5 h-5 text-amber-500 mr-2" />
            确认消耗积分
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* 内容 */}
        <div className="space-y-4">
          {/* 操作描述 */}
          <div className="text-center">
            <p className="text-gray-700 font-medium">{action}</p>
            {description && (
              <p className="text-sm text-gray-500 mt-1">{description}</p>
            )}
          </div>

          {/* 积分消耗信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-gray-600">消耗积分</span>
              <span className="text-lg font-bold text-red-600">-{cost}</span>
            </div>
            
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm text-gray-600">当前积分</span>
              <span className="text-lg font-medium text-gray-900">{currentCredits}</span>
            </div>
            
            <hr className="border-gray-200 my-2" />
            
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">操作后余额</span>
              <span className={cn(
                'text-lg font-bold',
                insufficient ? 'text-red-600' : 'text-green-600'
              )}>
                {insufficient ? '不足' : Math.max(0, currentCredits - cost)}
              </span>
            </div>
          </div>

          {/* 积分状态 */}
          <CreditBalance
            current={currentCredits}
            required={cost}
            showWarning={insufficient}
            className="justify-center"
          />

          {/* 积分不足警告 */}
          {insufficient && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <div className="flex items-start space-x-2">
                <AlertTriangle className="w-4 h-4 text-red-500 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <p className="text-sm text-red-800 font-medium">积分不足</p>
                  <p className="text-xs text-red-700 mt-1">
                    您需要 {cost - currentCredits} 个积分才能完成此操作
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 按钮区域 */}
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            取消
          </Button>
          
          {insufficient ? (
            <Button
              onClick={onPurchaseClick}
              className="bg-amber-500 hover:bg-amber-600"
              disabled={loading}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              购买积分
            </Button>
          ) : (
            <Button
              onClick={onConfirm}
              className="bg-primary-500 hover:bg-primary-600"
              disabled={loading}
            >
              {loading ? '处理中...' : '确认操作'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

// 简化版积分确认对话框
export const SimpleCreditConfirm: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  cost: number;
  currentCredits: number;
  title: string;
}> = ({ isOpen, onClose, onConfirm, cost, currentCredits, title }) => {
  if (!isOpen) return null;

  const insufficient = currentCredits < cost;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-4 max-w-sm w-full mx-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">{title}</h3>
        <p className="text-gray-600 mb-4">
          此操作将消耗 {cost} 积分，当前积分: {currentCredits}
        </p>
        
        {insufficient && (
          <p className="text-red-600 text-sm mb-4">积分不足，无法完成操作</p>
        )}
        
        <div className="flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button 
            onClick={onConfirm} 
            disabled={insufficient}
            className="bg-primary-500 hover:bg-primary-600"
          >
            确认
          </Button>
        </div>
      </div>
    </div>
  );
};
