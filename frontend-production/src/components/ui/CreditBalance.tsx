import React from 'react';
import { Coins, AlertTriangle, ShoppingCart } from 'lucide-react';
import { Button } from './Button';
import { cn } from '@/utils/cn';

interface CreditBalanceProps {
  current: number;
  required?: number;
  showWarning?: boolean;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showPurchaseButton?: boolean;
  onPurchaseClick?: () => void;
}

export const CreditBalance: React.FC<CreditBalanceProps> = ({
  current,
  required,
  showWarning = false,
  className = '',
  size = 'md',
  showPurchaseButton = false,
  onPurchaseClick
}) => {
  const insufficient = required !== undefined && current < required;
  const isWarning = showWarning || insufficient;

  const sizeClasses = {
    sm: {
      container: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      button: 'text-xs px-2 py-1'
    },
    md: {
      container: 'px-3 py-2 text-sm',
      icon: 'w-4 h-4',
      button: 'text-sm px-3 py-1.5'
    },
    lg: {
      container: 'px-4 py-3 text-base',
      icon: 'w-5 h-5',
      button: 'text-sm px-4 py-2'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div className={cn(
      'inline-flex items-center rounded-lg border transition-colors',
      classes.container,
      isWarning 
        ? 'bg-red-50 border-red-200 text-red-800' 
        : 'bg-amber-50 border-amber-200 text-amber-800',
      className
    )}>
      <div className="flex items-center space-x-2">
        {isWarning ? (
          <AlertTriangle className={cn(classes.icon, 'text-red-600')} />
        ) : (
          <Coins className={cn(classes.icon, 'text-amber-600')} />
        )}
        
        <div className="flex items-center space-x-1">
          <span className="font-medium">{current}</span>
          <span className="text-gray-600">积分</span>
          
          {required !== undefined && (
            <>
              <span className="text-gray-400">/</span>
              <span className={cn(
                'font-medium',
                insufficient ? 'text-red-600' : 'text-green-600'
              )}>
                需要 {required}
              </span>
            </>
          )}
        </div>

        {showPurchaseButton && insufficient && (
          <Button
            size="sm"
            variant="outline"
            onClick={onPurchaseClick}
            className={cn(
              classes.button,
              'ml-2 border-red-300 text-red-600 hover:bg-red-50'
            )}
          >
            <ShoppingCart className="w-3 h-3 mr-1" />
            购买
          </Button>
        )}
      </div>
    </div>
  );
};

// 简化版本，用于导航栏等空间有限的地方
export const CreditBadge: React.FC<{
  current: number;
  className?: string;
  onClick?: () => void;
}> = ({ current, className, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={cn(
        'inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium',
        'bg-amber-100 text-amber-800 hover:bg-amber-200 transition-colors',
        className
      )}
    >
      <Coins className="w-3 h-3" />
      <span>{current}</span>
    </button>
  );
};

// 积分状态指示器
export const CreditStatus: React.FC<{
  current: number;
  required: number;
  className?: string;
}> = ({ current, required, className }) => {
  const sufficient = current >= required;
  
  return (
    <div className={cn(
      'flex items-center space-x-2 text-sm',
      className
    )}>
      <div className={cn(
        'w-2 h-2 rounded-full',
        sufficient ? 'bg-green-500' : 'bg-red-500'
      )} />
      <span className={sufficient ? 'text-green-700' : 'text-red-700'}>
        {sufficient ? '积分充足' : '积分不足'}
      </span>
    </div>
  );
};
