import React from 'react';
import { createPortal } from 'react-dom';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '@/stores/uiStore';
import { cn } from '@/utils/cn';
import { Notification, NotificationType } from '@/types';

const notificationIcons = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
};

const notificationColors = {
  success: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    icon: 'text-green-400',
    title: 'text-green-800',
    message: 'text-green-700',
  },
  error: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    icon: 'text-red-400',
    title: 'text-red-800',
    message: 'text-red-700',
  },
  warning: {
    bg: 'bg-yellow-50',
    border: 'border-yellow-200',
    icon: 'text-yellow-400',
    title: 'text-yellow-800',
    message: 'text-yellow-700',
  },
  info: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    icon: 'text-blue-400',
    title: 'text-blue-800',
    message: 'text-blue-700',
  },
};

interface NotificationItemProps {
  notification: Notification;
  onRemove: (id: string) => void;
}

const NotificationItem: React.FC<NotificationItemProps> = ({
  notification,
  onRemove,
}) => {
  const { type, title, message, action } = notification;
  const Icon = notificationIcons[type];
  const colors = notificationColors[type];

  return (
    <motion.div
      initial={{ opacity: 0, y: -50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.95 }}
      transition={{ duration: 0.2 }}
      className={cn(
        'max-w-sm w-full shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden',
        colors.bg,
        colors.border
      )}
    >
      <div className="p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <Icon className={cn('h-6 w-6', colors.icon)} />
          </div>
          <div className="ml-3 w-0 flex-1 pt-0.5">
            <p className={cn('text-sm font-medium', colors.title)}>
              {title}
            </p>
            {message && (
              <p className={cn('mt-1 text-sm', colors.message)}>
                {message}
              </p>
            )}
            {action && (
              <div className="mt-3">
                <button
                  onClick={action.onClick}
                  className={cn(
                    'text-sm font-medium underline hover:no-underline',
                    colors.title
                  )}
                >
                  {action.label}
                </button>
              </div>
            )}
          </div>
          <div className="ml-4 flex-shrink-0 flex">
            <button
              onClick={() => onRemove(notification.id)}
              className={cn(
                'rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2',
                type === 'success' && 'focus:ring-green-500',
                type === 'error' && 'focus:ring-red-500',
                type === 'warning' && 'focus:ring-yellow-500',
                type === 'info' && 'focus:ring-blue-500'
              )}
            >
              <span className="sr-only">Close</span>
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export const NotificationContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();

  if (notifications.length === 0) return null;

  const notificationContent = (
    <div
      aria-live="assertive"
      className="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start z-50"
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        <AnimatePresence>
          {notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onRemove={removeNotification}
            />
          ))}
        </AnimatePresence>
      </div>
    </div>
  );

  return createPortal(notificationContent, document.body);
};

// Toast notification hook
export const useToast = () => {
  const { showSuccess, showError, showWarning, showInfo } = useNotifications();

  const toast = {
    success: (title: string, message?: string, duration?: number) => {
      showSuccess(title, message, duration);
    },
    error: (title: string, message?: string, duration?: number) => {
      showError(title, message, duration);
    },
    warning: (title: string, message?: string, duration?: number) => {
      showWarning(title, message, duration);
    },
    info: (title: string, message?: string, duration?: number) => {
      showInfo(title, message, duration);
    },
  };

  return toast;
};

// Notification banner component (for persistent notifications)
export interface NotificationBannerProps {
  type: NotificationType;
  title: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  onDismiss?: () => void;
  className?: string;
}

export const NotificationBanner: React.FC<NotificationBannerProps> = ({
  type,
  title,
  message,
  action,
  onDismiss,
  className,
}) => {
  const Icon = notificationIcons[type];
  const colors = notificationColors[type];

  return (
    <div className={cn('rounded-md p-4', colors.bg, colors.border, className)}>
      <div className="flex">
        <div className="flex-shrink-0">
          <Icon className={cn('h-5 w-5', colors.icon)} />
        </div>
        <div className="ml-3 flex-1">
          <h3 className={cn('text-sm font-medium', colors.title)}>
            {title}
          </h3>
          {message && (
            <div className={cn('mt-2 text-sm', colors.message)}>
              <p>{message}</p>
            </div>
          )}
          {action && (
            <div className="mt-4">
              <div className="-mx-2 -my-1.5 flex">
                <button
                  onClick={action.onClick}
                  className={cn(
                    'px-2 py-1.5 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                    type === 'success' && 'bg-green-100 text-green-800 hover:bg-green-200 focus:ring-green-600',
                    type === 'error' && 'bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-600',
                    type === 'warning' && 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 focus:ring-yellow-600',
                    type === 'info' && 'bg-blue-100 text-blue-800 hover:bg-blue-200 focus:ring-blue-600'
                  )}
                >
                  {action.label}
                </button>
                {onDismiss && (
                  <button
                    onClick={onDismiss}
                    className={cn(
                      'ml-3 px-2 py-1.5 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2',
                      type === 'success' && 'bg-green-100 text-green-800 hover:bg-green-200 focus:ring-green-600',
                      type === 'error' && 'bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-600',
                      type === 'warning' && 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 focus:ring-yellow-600',
                      type === 'info' && 'bg-blue-100 text-blue-800 hover:bg-blue-200 focus:ring-blue-600'
                    )}
                  >
                    Dismiss
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
        {onDismiss && !action && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                onClick={onDismiss}
                className={cn(
                  'inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2',
                  type === 'success' && 'text-green-500 hover:bg-green-100 focus:ring-green-600',
                  type === 'error' && 'text-red-500 hover:bg-red-100 focus:ring-red-600',
                  type === 'warning' && 'text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600',
                  type === 'info' && 'text-blue-500 hover:bg-blue-100 focus:ring-blue-600'
                )}
              >
                <span className="sr-only">Dismiss</span>
                <X className="h-5 w-5" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
