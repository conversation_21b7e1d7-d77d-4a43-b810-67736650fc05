import React, { forwardRef, useState } from 'react';
import { Eye, EyeOff, AlertCircle, Check } from 'lucide-react';
import { cn } from '@/utils/cn';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  success?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
  variant?: 'default' | 'filled' | 'outlined';
  inputSize?: 'sm' | 'md' | 'lg';
}

const inputVariants = {
  default: 'border border-gray-300 bg-white focus:border-primary-500 focus:ring-primary-500',
  filled: 'border-0 bg-gray-100 focus:bg-white focus:ring-primary-500',
  outlined: 'border-2 border-gray-300 bg-transparent focus:border-primary-500 focus:ring-0',
};

const inputSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-3 py-2 text-sm',
  lg: 'px-4 py-3 text-base',
};

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = 'text',
      label,
      error,
      success,
      helperText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      variant = 'default',
      inputSize = 'md',
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(false);
    const isPassword = type === 'password';
    const inputType = isPassword && showPassword ? 'text' : type;

    const hasError = !!error;
    const hasSuccess = !!success && !hasError;

    return (
      <div className={cn('relative', fullWidth && 'w-full')}>
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              'block text-sm font-medium mb-1',
              hasError ? 'text-red-600' : 'text-gray-700',
              disabled && 'text-gray-400'
            )}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <div className={cn(
                'w-4 h-4',
                hasError ? 'text-red-400' : hasSuccess ? 'text-green-400' : 'text-gray-400'
              )}>
                {leftIcon}
              </div>
            </div>
          )}

          <input
            type={inputType}
            className={cn(
              // Base styles
              'block w-full rounded-lg transition-colors duration-200',
              'focus:outline-none focus:ring-2 focus:ring-offset-0',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'placeholder:text-gray-400',
              
              // Variant styles
              inputVariants[variant],
              
              // Size styles
              inputSizes[inputSize],
              
              // Icon padding
              leftIcon && 'pl-10',
              (rightIcon || isPassword) && 'pr-10',
              
              // State styles
              hasError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
              hasSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
              
              // Custom className
              className
            )}
            disabled={disabled}
            ref={ref}
            {...props}
          />

          {(rightIcon || isPassword || hasError || hasSuccess) && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {isPassword ? (
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="text-gray-400 hover:text-gray-600 focus:outline-none"
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="w-4 h-4" />
                  ) : (
                    <Eye className="w-4 h-4" />
                  )}
                </button>
              ) : hasError ? (
                <AlertCircle className="w-4 h-4 text-red-400" />
              ) : hasSuccess ? (
                <Check className="w-4 h-4 text-green-400" />
              ) : (
                rightIcon && (
                  <div className="w-4 h-4 text-gray-400">
                    {rightIcon}
                  </div>
                )
              )}
            </div>
          )}
        </div>

        {(error || success || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1 flex-shrink-0" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <Check className="w-3 h-3 mr-1 flex-shrink-0" />
                {success}
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500">{helperText}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

// Textarea component
export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  success?: string;
  helperText?: string;
  fullWidth?: boolean;
  variant?: 'default' | 'filled' | 'outlined';
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      label,
      error,
      success,
      helperText,
      fullWidth = false,
      variant = 'default',
      resize = 'vertical',
      disabled,
      ...props
    },
    ref
  ) => {
    const hasError = !!error;
    const hasSuccess = !!success && !hasError;

    const resizeClasses = {
      none: 'resize-none',
      vertical: 'resize-y',
      horizontal: 'resize-x',
      both: 'resize',
    };

    return (
      <div className={cn('relative', fullWidth && 'w-full')}>
        {label && (
          <label
            htmlFor={props.id}
            className={cn(
              'block text-sm font-medium mb-1',
              hasError ? 'text-red-600' : 'text-gray-700',
              disabled && 'text-gray-400'
            )}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <textarea
          className={cn(
            // Base styles
            'block w-full rounded-lg transition-colors duration-200',
            'focus:outline-none focus:ring-2 focus:ring-offset-0',
            'disabled:opacity-50 disabled:cursor-not-allowed',
            'placeholder:text-gray-400',
            'px-3 py-2 text-sm',
            
            // Variant styles
            inputVariants[variant],
            
            // Resize styles
            resizeClasses[resize],
            
            // State styles
            hasError && 'border-red-300 focus:border-red-500 focus:ring-red-500',
            hasSuccess && 'border-green-300 focus:border-green-500 focus:ring-green-500',
            
            // Custom className
            className
          )}
          disabled={disabled}
          ref={ref}
          {...props}
        />

        {(error || success || helperText) && (
          <div className="mt-1">
            {error && (
              <p className="text-sm text-red-600 flex items-center">
                <AlertCircle className="w-3 h-3 mr-1 flex-shrink-0" />
                {error}
              </p>
            )}
            {success && !error && (
              <p className="text-sm text-green-600 flex items-center">
                <Check className="w-3 h-3 mr-1 flex-shrink-0" />
                {success}
              </p>
            )}
            {helperText && !error && !success && (
              <p className="text-sm text-gray-500">{helperText}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = 'Textarea';

// Search input component
export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'type'> {
  onSearch?: (value: string) => void;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  onSearch,
  onClear,
  showClearButton = true,
  ...props
}) => {
  const [value, setValue] = useState(props.value || '');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setValue(newValue);
    props.onChange?.(e);
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      onSearch?.(value as string);
    }
    props.onKeyPress?.(e);
  };

  const handleClear = () => {
    setValue('');
    onClear?.();
  };

  return (
    <Input
      {...props}
      type="search"
      value={value}
      onChange={handleChange}
      onKeyPress={handleKeyPress}
      leftIcon={
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      }
      rightIcon={
        showClearButton && value ? (
          <button
            type="button"
            onClick={handleClear}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        ) : undefined
      }
    />
  );
};
