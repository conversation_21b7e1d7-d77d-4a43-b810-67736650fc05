import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Monitor, 
  Tablet, 
  Smartphone, 
  RotateCcw,
  Eye,
  EyeOff,
  Settings,
  X
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { useResponsive } from '@/utils/responsive';

// Only show in development mode
const isDev = import.meta.env.DEV;

interface DevicePreset {
  name: string;
  width: number;
  height: number;
  icon: React.ComponentType<any>;
  category: 'mobile' | 'tablet' | 'desktop';
}

const devicePresets: DevicePreset[] = [
  // Mobile devices
  { name: 'iPhone SE', width: 375, height: 667, icon: Smartphone, category: 'mobile' },
  { name: 'iPhone 12', width: 390, height: 844, icon: Smartphone, category: 'mobile' },
  { name: 'iPhone 12 Pro Max', width: 428, height: 926, icon: Smartphone, category: 'mobile' },
  { name: 'Samsung Galaxy S21', width: 384, height: 854, icon: Smartphone, category: 'mobile' },
  
  // Tablets
  { name: 'iPad', width: 768, height: 1024, icon: Tablet, category: 'tablet' },
  { name: 'iPad Pro 11"', width: 834, height: 1194, icon: Tablet, category: 'tablet' },
  { name: 'iPad Pro 12.9"', width: 1024, height: 1366, icon: Tablet, category: 'tablet' },
  
  // Desktop
  { name: 'Laptop', width: 1366, height: 768, icon: Monitor, category: 'desktop' },
  { name: 'Desktop', width: 1920, height: 1080, icon: Monitor, category: 'desktop' },
  { name: 'Large Desktop', width: 2560, height: 1440, icon: Monitor, category: 'desktop' },
];

export const ResponsiveTestPanel: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<DevicePreset | null>(null);
  const [isLandscape, setIsLandscape] = useState(false);
  const [customWidth, setCustomWidth] = useState('');
  const [customHeight, setCustomHeight] = useState('');
  
  const deviceInfo = useResponsive();

  // Don't render in production
  if (!isDev) return null;

  const handleDeviceSelect = (device: DevicePreset) => {
    setSelectedDevice(device);
    
    // Apply device dimensions to viewport
    const width = isLandscape ? device.height : device.width;
    const height = isLandscape ? device.width : device.height;
    
    // In a real implementation, you would resize the viewport or iframe
    console.log(`Simulating device: ${device.name} (${width}x${height})`);
  };

  const toggleOrientation = () => {
    setIsLandscape(!isLandscape);
    if (selectedDevice) {
      handleDeviceSelect(selectedDevice);
    }
  };

  const resetViewport = () => {
    setSelectedDevice(null);
    setIsLandscape(false);
    // Reset to natural viewport size
    console.log('Reset to natural viewport');
  };

  const applyCustomSize = () => {
    const width = parseInt(customWidth);
    const height = parseInt(customHeight);
    
    if (width && height) {
      const customDevice: DevicePreset = {
        name: 'Custom',
        width,
        height,
        icon: Monitor,
        category: 'desktop'
      };
      handleDeviceSelect(customDevice);
    }
  };

  const getDevicesByCategory = (category: string) => {
    return devicePresets.filter(device => device.category === category);
  };

  return (
    <>
      {/* Toggle Button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-6 left-6 w-12 h-12 bg-gray-800 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center z-40 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        title="响应式测试面板"
      >
        {isVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
      </motion.button>

      {/* Test Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, x: -300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -300 }}
            className="fixed top-4 left-4 w-80 max-h-[calc(100vh-2rem)] overflow-y-auto z-50"
          >
            <Card className="p-4 bg-gray-900 text-white border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Settings className="w-5 h-5" />
                  <h3 className="font-semibold">响应式测试</h3>
                </div>
                <button
                  onClick={() => setIsVisible(false)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Current Device Info */}
              <div className="mb-4 p-3 bg-gray-800 rounded-lg">
                <h4 className="text-sm font-medium mb-2">当前设备信息</h4>
                <div className="text-xs space-y-1 text-gray-300">
                  <div>尺寸: {deviceInfo.width} × {deviceInfo.height}</div>
                  <div>断点: {deviceInfo.breakpoint}</div>
                  <div>类型: {deviceInfo.isMobile ? '移动端' : deviceInfo.isTablet ? '平板' : '桌面端'}</div>
                  <div>方向: {deviceInfo.orientation === 'portrait' ? '竖屏' : '横屏'}</div>
                  <div>触摸支持: {deviceInfo.touchSupport ? '是' : '否'}</div>
                </div>
              </div>

              {/* Device Presets */}
              <div className="space-y-4">
                {/* Mobile */}
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Smartphone className="w-4 h-4 mr-2" />
                    移动设备
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {getDevicesByCategory('mobile').map((device) => {
                      const Icon = device.icon;
                      const isSelected = selectedDevice?.name === device.name;
                      return (
                        <button
                          key={device.name}
                          onClick={() => handleDeviceSelect(device)}
                          className={`p-2 rounded text-left text-xs transition-colors ${
                            isSelected 
                              ? 'bg-primary-600 text-white' 
                              : 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span>{device.name}</span>
                            <span>{device.width}×{device.height}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Tablet */}
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Tablet className="w-4 h-4 mr-2" />
                    平板设备
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {getDevicesByCategory('tablet').map((device) => {
                      const isSelected = selectedDevice?.name === device.name;
                      return (
                        <button
                          key={device.name}
                          onClick={() => handleDeviceSelect(device)}
                          className={`p-2 rounded text-left text-xs transition-colors ${
                            isSelected 
                              ? 'bg-primary-600 text-white' 
                              : 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span>{device.name}</span>
                            <span>{device.width}×{device.height}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Desktop */}
                <div>
                  <h4 className="text-sm font-medium mb-2 flex items-center">
                    <Monitor className="w-4 h-4 mr-2" />
                    桌面设备
                  </h4>
                  <div className="grid grid-cols-1 gap-2">
                    {getDevicesByCategory('desktop').map((device) => {
                      const isSelected = selectedDevice?.name === device.name;
                      return (
                        <button
                          key={device.name}
                          onClick={() => handleDeviceSelect(device)}
                          className={`p-2 rounded text-left text-xs transition-colors ${
                            isSelected 
                              ? 'bg-primary-600 text-white' 
                              : 'bg-gray-800 hover:bg-gray-700 text-gray-300'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span>{device.name}</span>
                            <span>{device.width}×{device.height}</span>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Custom Size */}
                <div>
                  <h4 className="text-sm font-medium mb-2">自定义尺寸</h4>
                  <div className="space-y-2">
                    <div className="flex space-x-2">
                      <input
                        type="number"
                        placeholder="宽度"
                        value={customWidth}
                        onChange={(e) => setCustomWidth(e.target.value)}
                        className="flex-1 px-2 py-1 bg-gray-800 border border-gray-600 rounded text-xs"
                      />
                      <input
                        type="number"
                        placeholder="高度"
                        value={customHeight}
                        onChange={(e) => setCustomHeight(e.target.value)}
                        className="flex-1 px-2 py-1 bg-gray-800 border border-gray-600 rounded text-xs"
                      />
                    </div>
                    <button
                      onClick={applyCustomSize}
                      className="w-full px-3 py-1 bg-primary-600 hover:bg-primary-700 rounded text-xs transition-colors"
                    >
                      应用
                    </button>
                  </div>
                </div>

                {/* Controls */}
                <div className="flex space-x-2 pt-2 border-t border-gray-700">
                  <button
                    onClick={toggleOrientation}
                    disabled={!selectedDevice}
                    className="flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed rounded text-xs transition-colors flex items-center justify-center"
                  >
                    <RotateCcw className="w-3 h-3 mr-1" />
                    旋转
                  </button>
                  <button
                    onClick={resetViewport}
                    className="flex-1 px-3 py-2 bg-gray-800 hover:bg-gray-700 rounded text-xs transition-colors"
                  >
                    重置
                  </button>
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
