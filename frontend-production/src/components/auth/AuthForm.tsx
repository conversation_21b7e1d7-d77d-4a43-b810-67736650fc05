import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { BookO<PERSON>, Shield, Heart } from 'lucide-react';
import { GoogleAuthButton } from './GoogleAuthButton';
import { Card } from '@/components/ui/Card';

interface AuthFormProps {
  onSuccess?: () => void;
  redirectTo?: string;
}

export const AuthForm: React.FC<AuthFormProps> = ({
  onSuccess,
  redirectTo: _redirectTo,
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleAuthSuccess = () => {
    setIsLoading(false);
    onSuccess?.();
  };

  const handleAuthError = (error: string) => {
    setIsLoading(false);
    console.error('Auth error:', error);
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <Card className="p-8">
        {/* Logo and Title */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
              <BookOpen className="w-7 h-7 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            欢迎来到 StoryWeaver
          </h1>
          <p className="text-gray-600">
            开始创作属于您孩子的专属故事
          </p>
        </div>

        {/* Google Auth Button */}
        <div className="mb-6">
          <GoogleAuthButton
            onSuccess={handleAuthSuccess}
            onError={handleAuthError}
            disabled={isLoading}
            className="w-full"
          />
        </div>

        {/* Features */}
        <div className="space-y-4 mb-6">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <BookOpen className="w-4 h-4 text-primary-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">AI 智能创作</h3>
              <p className="text-sm text-gray-600">
                基于先进的 AI 技术，为您的孩子创作独一无二的故事
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <Shield className="w-4 h-4 text-secondary-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">安全可靠</h3>
              <p className="text-sm text-gray-600">
                所有内容都经过安全检查，确保适合儿童阅读
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-accent-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-0.5">
              <Heart className="w-4 h-4 text-accent-600" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">个性化定制</h3>
              <p className="text-sm text-gray-600">
                根据孩子的年龄、兴趣和特点，定制专属故事内容
              </p>
            </div>
          </div>
        </div>

        {/* Terms and Privacy */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            登录即表示您同意我们的{' '}
            <Link 
              to="/terms" 
              className="text-primary-600 hover:text-primary-700 underline"
            >
              服务条款
            </Link>
            {' '}和{' '}
            <Link 
              to="/privacy" 
              className="text-primary-600 hover:text-primary-700 underline"
            >
              隐私政策
            </Link>
          </p>
        </div>
      </Card>

      {/* Additional Info */}
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-600">
          还没有账户？登录后将自动为您创建账户
        </p>
      </div>

      {/* Demo Notice */}
      {import.meta.env.DEV && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>开发模式提示：</strong> 当前使用占位符 Google OAuth 配置。
            部署后需要配置真实的 Google 客户端 ID。
          </p>
        </div>
      )}
    </div>
  );
};

// Loading state component
export const AuthFormSkeleton: React.FC = () => (
  <div className="w-full max-w-md mx-auto">
    <Card className="p-8">
      <div className="text-center mb-8">
        <div className="w-12 h-12 bg-gray-200 rounded-xl mx-auto mb-4 animate-pulse" />
        <div className="h-6 bg-gray-200 rounded mx-auto mb-2 animate-pulse" style={{ width: '60%' }} />
        <div className="h-4 bg-gray-200 rounded mx-auto animate-pulse" style={{ width: '80%' }} />
      </div>
      
      <div className="h-12 bg-gray-200 rounded-lg mb-6 animate-pulse" />
      
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-gray-200 rounded-lg animate-pulse" />
            <div className="flex-1">
              <div className="h-4 bg-gray-200 rounded mb-1 animate-pulse" style={{ width: '70%' }} />
              <div className="h-3 bg-gray-200 rounded animate-pulse" style={{ width: '90%' }} />
            </div>
          </div>
        ))}
      </div>
    </Card>
  </div>
);
