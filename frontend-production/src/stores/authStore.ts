import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, AuthTokens, LoginCredentials } from '@/types';
import { authService } from '@/services/auth';
import {
  isDebugMode,
  shouldSkipAuth,
  getDebugUser,
  debugActions,
  DEBUG_USER_TYPES,
  debugLog,
  isIllegalDebugUser,
  forceRemoveIllegalDebugUser,
  resetIllegalDebugUserCounter,
  type DebugUser
} from '@/utils/debug';

interface AuthState {
  // State
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  isDebugMode: boolean;

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  initializeAuth: () => Promise<void>;
  checkTokenExpiry: () => Promise<void>;
  refreshUser: () => Promise<void>;

  // Debug actions
  setDebugUser: (userType?: keyof typeof DEBUG_USER_TYPES) => void;
  addDebugCredits: (amount: number) => void;
  resetDebugState: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => {
      // Initialize debug mode if enabled
      const debugMode = isDebugMode();
      // 🔒 CRITICAL: 只有在明确没有真实用户且启用跳过认证时才使用调试用户
      // 🚨 PRODUCTION FIX: 在生产环境中绝对不调用getDebugUser()
      let initialDebugUser = null;
      if (debugMode && shouldSkipAuth() && import.meta.env.VITE_ENVIRONMENT !== 'production') {
        try {
          initialDebugUser = getDebugUser();
        } catch (error) {
          console.warn('Failed to get debug user:', error);
          initialDebugUser = null;
        }
      }

      // 🔒 CRITICAL SECURITY: 检测并清除非法调试用户
      if (typeof window !== 'undefined') {
        const storedData = localStorage.getItem('auth-storage');
        if (storedData) {
          try {
            const parsed = JSON.parse(storedData);
            const storedUser = parsed.state?.user;
            
            // 检查是否存在非法调试用户数据
            if (storedUser && isIllegalDebugUser(storedUser)) {
              console.error('ILLEGAL DEBUG USER DETECTED IN STORAGE!');
              console.warn('SECURITY: Force removing illegal debug user data');
              
              // 强制清除非法调试用户
              forceRemoveIllegalDebugUser();
              return; // 直接返回，不继续初始化
            }
          } catch (error) {
            console.warn('Failed to parse stored auth data:', error);
          }
        }
      }

      if (debugMode) {
        debugLog.info('AuthStore initialized in debug mode');
        if (initialDebugUser) {
          debugLog.info('Auto-setting debug user:', initialDebugUser);
        }
      }

      return {
        // Initial state
        user: initialDebugUser,
        tokens: initialDebugUser ? {
          accessToken: 'debug-token',
          refreshToken: 'debug-refresh-token',
          expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours from now
        } : null,
        isAuthenticated: !!initialDebugUser,
        isLoading: false,
        error: null,
        isDebugMode: debugMode,

        // Login action
      login: async (credentials: LoginCredentials) => {
        // Skip actual login in debug mode
        if (get().isDebugMode && shouldSkipAuth() && import.meta.env.VITE_ENVIRONMENT !== 'production') {
          debugLog.info('Skipping login in debug mode');
          try {
            const debugUser = getDebugUser();
            set({
              user: debugUser,
              tokens: {
                accessToken: 'debug-token',
                refreshToken: 'debug-refresh-token',
                expiresAt: Date.now() + 24 * 60 * 60 * 1000,
              },
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return;
          } catch (error) {
            console.warn('Failed to get debug user in login:', error);
            // Fall through to normal login
          }
        }

        set({ isLoading: true, error: null });

        try {
          const response = await authService.login(credentials);

          // 检查是否为真实用户，如果是则重置计数器
          if (response.user && !isIllegalDebugUser(response.user)) {
            resetIllegalDebugUserCounter();
          }

          set({
            user: response.user,
            tokens: {
              accessToken: response.tokens.accessToken,
              refreshToken: response.tokens.refreshToken,
              expiresAt: response.tokens.expiresAt || (Date.now() + 24 * 60 * 60 * 1000), // 使用后端返回的过期时间
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      // Logout action
      logout: () => {
        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          error: null,
        });
        
        // Clear any stored tokens
        localStorage.removeItem('auth-storage');
      },

      // Refresh token action
      refreshToken: async () => {
        const { tokens } = get();
        
        if (!tokens?.refreshToken) {
          get().logout();
          throw new Error('No refresh token available');
        }

        try {
          const response = await authService.refreshToken({
            refreshToken: tokens.refreshToken,
          });
          
          set({
            tokens: {
              accessToken: response.token,
              refreshToken: response.refreshToken,
              expiresAt: response.expiresAt || (Date.now() + 24 * 60 * 60 * 1000), // 使用后端返回的过期时间
            },
          });
        } catch (error) {
          get().logout();
          throw error;
        }
      },

      // Update user action
      updateUser: (userData: Partial<User>) => {
        const { user } = get();
        if (user) {
          set({
            user: { ...user, ...userData },
          });
        }
      },

      // Clear error action
      clearError: () => {
        set({ error: null });
      },

      // Set loading action
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Initialize auth on app start
      initializeAuth: async () => {
        const { tokens, user } = get();
        
        // 🔒 CRITICAL: 检测当前用户是否为非法调试用户
        if (user && isIllegalDebugUser(user)) {
          console.error('ILLEGAL DEBUG USER DETECTED IN AUTH STATE!');
          forceRemoveIllegalDebugUser();
          return;
        }
        
        if (!tokens?.accessToken) {
          return;
        }

        // Check if token is expired
        if (tokens.expiresAt && Date.now() > tokens.expiresAt) {
          try {
            await get().refreshToken();
          } catch (error) {
            get().logout();
            return;
          }
        }

        // Verify token and get user info
        try {
          set({ isLoading: true });
          const fetchedUser = await authService.getCurrentUser();
          
          // 🔒 CRITICAL: 检测从服务器获取的用户是否为非法调试用户
          if (fetchedUser && isIllegalDebugUser(fetchedUser)) {
            console.error('ILLEGAL DEBUG USER RECEIVED FROM SERVER!');
            forceRemoveIllegalDebugUser();
            return;
          }
          
          // 如果是真实用户，重置计数器
          if (fetchedUser && !isIllegalDebugUser(fetchedUser)) {
            resetIllegalDebugUserCounter();
          }
          
          set({
            user: fetchedUser,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          get().logout();
          set({ isLoading: false });
        }
      },

      // Check token expiry and refresh if needed
      checkTokenExpiry: async () => {
        const { tokens, isAuthenticated, isDebugMode } = get();

        // Skip check in debug mode
        if (isDebugMode) return;

        if (!isAuthenticated || !tokens?.expiresAt) return;

        const now = Date.now();
        const timeUntilExpiry = tokens.expiresAt - now;

        // If token expires in less than 5 minutes, refresh it
        if (timeUntilExpiry < 5 * 60 * 1000) {
          debugLog.info('Token expiring soon, refreshing...');
          try {
            await get().refreshToken();
            debugLog.info('Token refreshed successfully');
          } catch (error) {
            debugLog.error('Token refresh failed:', error);
            get().logout();
          }
        }
      },

      // Refresh user data from server
      refreshUser: async () => {
        const { isAuthenticated, isDebugMode } = get();

        // Skip in debug mode
        if (isDebugMode) {
          debugLog.info('Skipping user refresh in debug mode');
          return;
        }

        if (!isAuthenticated) return;

        try {
          const user = await authService.getCurrentUser();
          set({ user });
          debugLog.info('User data refreshed successfully');
        } catch (error) {
          debugLog.error('Failed to refresh user data:', error);
        }
      },

      // Debug actions
      setDebugUser: (userType?: keyof typeof DEBUG_USER_TYPES) => {
        if (!get().isDebugMode || import.meta.env.VITE_ENVIRONMENT === 'production') {
          debugLog.warn('Attempted to set debug user but debug mode is disabled or in production');
          return;
        }

        const currentState = get();

        // 🔒 CRITICAL: 防止覆盖真实用户
        if (currentState.user && !(currentState.user as any).isDebugUser) {
          debugLog.error('BLOCKED: Attempted to override real user with debug user!', {
            realUser: currentState.user.email,
            realUserId: currentState.user.id
          });
          return;
        }

        try {
          const debugUser = userType ? DEBUG_USER_TYPES[userType] : getDebugUser();
          debugLog.info('Setting debug user:', debugUser);

          set({
            user: debugUser,
            tokens: {
              accessToken: 'debug-token',
              refreshToken: 'debug-refresh-token',
              expiresAt: Date.now() + 24 * 60 * 60 * 1000,
            },
            isAuthenticated: true,
          });
        } catch (error) {
          console.warn('Failed to set debug user:', error);
        }
      },

      addDebugCredits: (amount: number) => {
        if (!get().isDebugMode || !get().user) return;

        const currentUser = get().user!;
        const updatedUser = {
          ...currentUser,
          credits: currentUser.credits + amount,
        };

        debugLog.info(`Adding ${amount} credits. New total: ${updatedUser.credits}`);
        set({ user: updatedUser });
      },

      resetDebugState: () => {
        if (!get().isDebugMode || import.meta.env.VITE_ENVIRONMENT === 'production') return;

        debugLog.info('Resetting debug state');
        try {
          const debugUser = getDebugUser();
          set({
            user: debugUser,
            tokens: {
              accessToken: 'debug-token',
              refreshToken: 'debug-refresh-token',
              expiresAt: Date.now() + 24 * 60 * 60 * 1000,
            },
            isAuthenticated: true,
            error: null,
          });
        } catch (error) {
          console.warn('Failed to reset debug state:', error);
        }
      },
      };
    },
    {
      name: 'auth-storage',
      partialize: (state) => {
        // 🔒 CRITICAL PRODUCTION SECURITY: 绝对不持久化调试用户数据
        const isProduction = import.meta.env.VITE_ENVIRONMENT === 'production';
        const isHTTPS = typeof window !== 'undefined' && window.location.protocol === 'https:';
        const isProductionDomain = typeof window !== 'undefined' && !window.location.hostname.includes('localhost');
        
        // 生产环境检查
        if (isProduction || (isHTTPS && isProductionDomain)) {
          if (state.user && (
            (state.user as any).isDebugUser ||
            state.user.email === '<EMAIL>' ||
            state.user.id === 'debug-user-001' ||
            state.user.email?.includes('debug') ||
            state.user.name?.includes('调试')
          )) {
            console.warn('🔒 SECURITY: Blocking debug user persistence in production');
            return {
              user: null,
              tokens: null,
              isAuthenticated: false,
            };
          }
        }

        // 开发环境也要检查调试模式
        if (!isDebugMode() && state.user && (
          (state.user as any).isDebugUser ||
          state.user.email === '<EMAIL>' ||
          state.user.id === 'debug-user-001'
        )) {
          console.warn('🔒 Debug mode disabled: Clearing debug user data');
          return {
            user: null,
            tokens: null,
            isAuthenticated: false,
          };
        }

        return {
          user: state.user,
          tokens: state.tokens,
          isAuthenticated: state.isAuthenticated,
        };
      },
    }
  )
);

// Selectors for easier access to specific state
export const useAuth = () => {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  return { user, isAuthenticated, isLoading };
};

export const useAuthActions = () => {
  const { login, logout, refreshToken, updateUser, clearError, setDebugUser, addDebugCredits, resetDebugState } = useAuthStore();
  return { login, logout, refreshToken, updateUser, clearError, setDebugUser, addDebugCredits, resetDebugState };
};

// Initialize debug event listeners
if (isDebugMode() && typeof window !== 'undefined') {
  debugLog.info('Setting up debug event listeners');

  window.addEventListener('debug:switchUser', (event: any) => {
    const { userType } = event.detail;
    useAuthStore.getState().setDebugUser(userType);
  });

  window.addEventListener('debug:addCredits', (event: any) => {
    const { amount } = event.detail;
    useAuthStore.getState().addDebugCredits(amount);
  });

  window.addEventListener('debug:resetState', () => {
    useAuthStore.getState().resetDebugState();
  });
}

// Auto-refresh token when it's about to expire
setInterval(() => {
  const { tokens, refreshToken, isAuthenticated } = useAuthStore.getState();
  
  if (isAuthenticated && tokens?.expiresAt) {
    const timeUntilExpiry = tokens.expiresAt - Date.now();
    const fiveMinutes = 5 * 60 * 1000;
    
    // Refresh token if it expires in less than 5 minutes
    if (timeUntilExpiry < fiveMinutes && timeUntilExpiry > 0) {
      refreshToken().catch(() => {
        // Token refresh failed, user will be logged out
      });
    }
  }
}, 60 * 1000); // Check every minute
