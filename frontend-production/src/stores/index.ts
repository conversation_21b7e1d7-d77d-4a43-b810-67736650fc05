// Re-export all stores and their hooks
export * from './authStore';
export * from './storyStore';
export * from './uiStore';

// Store initialization
import { useAuthStore } from './authStore';
import { useUIStore } from './uiStore';

/**
 * Initialize all stores
 */
export const initializeStores = async () => {
  try {
    // Initialize auth store
    const authStore = useAuthStore.getState();
    await authStore.initializeAuth();

    console.log('✅ All stores initialized');

    // Return cleanup function
    return () => {
      // Cleanup stores if needed
      console.log('🧹 Cleaning up stores');
    };
  } catch (error) {
    console.error('❌ Failed to initialize stores:', error);
    throw error;
  }
};

// Global error handler for stores
export const handleStoreError = (error: unknown, context: string) => {
  const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';

  console.error(`Store error in ${context}:`, error);

  // Show error notification if UI store has the method
  const uiStore = useUIStore.getState();
  if ('addNotification' in uiStore && typeof uiStore.addNotification === 'function') {
    uiStore.addNotification({
      type: 'error',
      title: 'Something went wrong',
      message: errorMessage,
      duration: 5000,
    });
  }
};

// Store reset function (useful for testing or logout)
export const resetAllStores = () => {
  const authStore = useAuthStore.getState();
  const uiStore = useUIStore.getState();

  authStore.logout();

  if ('clearNotifications' in uiStore && typeof uiStore.clearNotifications === 'function') {
    uiStore.clearNotifications();
  }
};
