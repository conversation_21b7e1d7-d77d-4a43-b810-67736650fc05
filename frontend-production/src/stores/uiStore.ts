import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useCallback } from 'react';
import { 
  Notification, 
  NotificationType, 
  ThemeConfig, 
  DeviceInfo, 
  ModalState 
} from '@/types';

interface UIState {
  // Theme and preferences
  theme: ThemeConfig;
  
  // Device and responsive state
  device: DeviceInfo;
  
  // Notifications
  notifications: Notification[];
  
  // Modals and dialogs
  modals: {
    [key: string]: ModalState;
  };
  
  // Loading states for global operations
  globalLoading: boolean;
  
  // Sidebar state (for mobile)
  sidebarOpen: boolean;
  
  // Search state
  searchOpen: boolean;
  searchQuery: string;
  
  // Actions
  setTheme: (theme: Partial<ThemeConfig>) => void;
  setDevice: (device: Partial<DeviceInfo>) => void;
  
  // Notification actions
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;
  
  // Modal actions
  openModal: (key: string, modal: Omit<ModalState, 'isOpen'>) => void;
  closeModal: (key: string) => void;
  closeAllModals: () => void;
  
  // UI actions
  setGlobalLoading: (loading: boolean) => void;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setSearchOpen: (open: boolean) => void;
  setSearchQuery: (query: string) => void;
}

const defaultTheme: ThemeConfig = {
  mode: 'light',
  primaryColor: '#0ea5e9',
  fontSize: 'medium',
  reducedMotion: false,
};

const defaultDevice: DeviceInfo = {
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  breakpoint: 'lg',
  orientation: 'landscape',
};

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: defaultTheme,
      device: defaultDevice,
      notifications: [],
      modals: {},
      globalLoading: false,
      sidebarOpen: false,
      searchOpen: false,
      searchQuery: '',

      // Theme actions
      setTheme: (themeUpdates: Partial<ThemeConfig>) => {
        set((state) => ({
          theme: { ...state.theme, ...themeUpdates },
        }));
      },

      // Device actions
      setDevice: (deviceUpdates: Partial<DeviceInfo>) => {
        set((state) => ({
          device: { ...state.device, ...deviceUpdates },
        }));
      },

      // Notification actions
      addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => {
        try {
          // 验证必需的字段
          if (!notification || !notification.type || !notification.title) {
            console.warn('Invalid notification data:', notification);
            return;
          }

          const id = Math.random().toString(36).substr(2, 9);
          const newNotification: Notification = {
            type: notification.type,
            title: notification.title,
            message: notification.message || '',
            duration: notification.duration,
            id,
            createdAt: Date.now(),
          };

          set((state) => ({
            notifications: [newNotification, ...(state.notifications || [])],
          }));

          // Auto-remove notification after duration
          if (notification.duration !== 0) {
            const duration = notification.duration || 5000;
            setTimeout(() => {
              try {
                get().removeNotification(id);
              } catch (error) {
                console.error('Failed to auto-remove notification:', error);
              }
            }, duration);
          }
        } catch (error) {
          console.error('Failed to add notification:', error);
        }
      },

      removeNotification: (id: string) => {
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id),
        }));
      },

      clearNotifications: () => {
        set({ notifications: [] });
      },

      // Modal actions
      openModal: (key: string, modal: Omit<ModalState, 'isOpen'>) => {
        set((state) => ({
          modals: {
            ...state.modals,
            [key]: { ...modal, isOpen: true },
          },
        }));
      },

      closeModal: (key: string) => {
        set((state) => ({
          modals: {
            ...state.modals,
            [key]: { ...state.modals[key], isOpen: false },
          },
        }));
      },

      closeAllModals: () => {
        set((state) => {
          const closedModals = Object.keys(state.modals).reduce((acc, key) => {
            acc[key] = { ...state.modals[key], isOpen: false };
            return acc;
          }, {} as typeof state.modals);
          
          return { modals: closedModals };
        });
      },

      // UI actions
      setGlobalLoading: (loading: boolean) => {
        set({ globalLoading: loading });
      },

      setSidebarOpen: (open: boolean) => {
        set({ sidebarOpen: open });
      },

      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },

      setSearchOpen: (open: boolean) => {
        set({ searchOpen: open });
      },

      setSearchQuery: (query: string) => {
        set({ searchQuery: query });
      },

      // Initialize store
      initialize: () => {
        console.log('UI store initialized');
      },
    }),
    {
      name: 'ui-storage',
      partialize: (state) => ({
        theme: state.theme,
        sidebarOpen: state.sidebarOpen,
      }),
    }
  )
);

// Notification helper functions
export const useNotifications = () => {
  const store = useUIStore();

  // 确保所有函数都存在，提供默认实现
  const notifications = store?.notifications || [];
  const addNotification = store?.addNotification || (() => {
    console.warn('addNotification not available');
  });
  const removeNotification = store?.removeNotification || (() => {
    console.warn('removeNotification not available');
  });
  const clearNotifications = store?.clearNotifications || (() => {
    console.warn('clearNotifications not available');
  });

  const showSuccess = useCallback((title: string, message?: string, duration?: number) => {
    try {
      addNotification({ type: 'success', title, message, duration });
    } catch (error) {
      console.error('Failed to show success notification:', error);
    }
  }, [addNotification]);

  const showError = useCallback((title: string, message?: string, duration?: number) => {
    try {
      addNotification({ type: 'error', title, message, duration });
    } catch (error) {
      console.error('Failed to show error notification:', error);
    }
  }, [addNotification]);

  const showWarning = useCallback((title: string, message?: string, duration?: number) => {
    try {
      addNotification({ type: 'warning', title, message, duration });
    } catch (error) {
      console.error('Failed to show warning notification:', error);
    }
  }, [addNotification]);

  const showInfo = useCallback((title: string, message?: string, duration?: number) => {
    try {
      addNotification({ type: 'info', title, message, duration });
    } catch (error) {
      console.error('Failed to show info notification:', error);
    }
  }, [addNotification]);

  return {
    notifications,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearNotifications,
  };
};

// Modal helper functions
export const useModal = (key: string) => {
  const { modals, openModal, closeModal } = useUIStore();
  const modal = modals[key];
  
  const open = (modalConfig: Omit<ModalState, 'isOpen'>) => {
    openModal(key, modalConfig);
  };
  
  const close = () => {
    closeModal(key);
  };
  
  return {
    isOpen: modal?.isOpen || false,
    modal,
    open,
    close,
  };
};

// Theme helper functions
export const useTheme = () => {
  const { theme, setTheme } = useUIStore();
  
  const toggleMode = () => {
    setTheme({ mode: theme.mode === 'light' ? 'dark' : 'light' });
  };
  
  const setMode = (mode: ThemeConfig['mode']) => {
    setTheme({ mode });
  };
  
  const setPrimaryColor = (color: string) => {
    setTheme({ primaryColor: color });
  };
  
  const setFontSize = (fontSize: ThemeConfig['fontSize']) => {
    setTheme({ fontSize });
  };
  
  const toggleReducedMotion = () => {
    setTheme({ reducedMotion: !theme.reducedMotion });
  };
  
  return {
    theme,
    toggleMode,
    setMode,
    setPrimaryColor,
    setFontSize,
    toggleReducedMotion,
  };
};

// Device helper functions
export const useDevice = () => {
  const { device, setDevice } = useUIStore();
  
  return {
    device,
    setDevice,
    isMobile: device.isMobile,
    isTablet: device.isTablet,
    isDesktop: device.isDesktop,
  };
};
