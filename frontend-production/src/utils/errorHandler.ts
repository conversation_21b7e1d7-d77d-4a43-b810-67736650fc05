import { AxiosError } from 'axios';
import type { ApiResponse } from '@/types';

export interface ErrorDetails {
  message: string;
  code?: string;
  status?: number;
  details?: any;
  timestamp: string;
  url?: string;
}

export class AppError extends Error {
  public readonly code?: string;
  public readonly status?: number;
  public readonly details?: any;
  public readonly timestamp: string;
  public readonly url?: string;

  constructor(
    message: string,
    code?: string,
    status?: number,
    details?: any,
    url?: string
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.status = status;
    this.details = details;
    this.timestamp = new Date().toISOString();
    this.url = url;
  }

  toJSON(): ErrorDetails {
    return {
      message: this.message,
      code: this.code,
      status: this.status,
      details: this.details,
      timestamp: this.timestamp,
      url: this.url,
    };
  }
}

// Error type definitions
export type ErrorType = 
  | 'NETWORK_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'AUTHORIZATION_ERROR'
  | 'VALIDATION_ERROR'
  | 'NOT_FOUND_ERROR'
  | 'SERVER_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'UNKNOWN_ERROR';

export interface ProcessedError {
  type: ErrorType;
  message: string;
  userMessage: string;
  code?: string;
  status?: number;
  details?: any;
  retryable: boolean;
  timestamp: string;
}

/**
 * Process and categorize errors for better user experience
 */
export const processError = (error: unknown, context?: string): ProcessedError => {
  const timestamp = new Date().toISOString();
  
  // Handle Axios errors
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    const data = error.response?.data as ApiResponse<any>;
    
    switch (status) {
      case 400:
        return {
          type: 'VALIDATION_ERROR',
          message: data?.error || error.message,
          userMessage: '请求参数有误，请检查输入内容',
          code: data?.code,
          status,
          details: (data as any)?.details,
          retryable: false,
          timestamp,
        };
        
      case 401:
        return {
          type: 'AUTHENTICATION_ERROR',
          message: data?.error || '身份验证失败',
          userMessage: '登录已过期，请重新登录',
          code: data?.code,
          status,
          retryable: false,
          timestamp,
        };
        
      case 403:
        return {
          type: 'AUTHORIZATION_ERROR',
          message: data?.error || '权限不足',
          userMessage: '您没有权限执行此操作',
          code: data?.code,
          status,
          retryable: false,
          timestamp,
        };
        
      case 404:
        return {
          type: 'NOT_FOUND_ERROR',
          message: data?.error || '资源未找到',
          userMessage: '请求的资源不存在',
          code: data?.code,
          status,
          retryable: false,
          timestamp,
        };
        
      case 429:
        return {
          type: 'RATE_LIMIT_ERROR',
          message: data?.error || '请求过于频繁',
          userMessage: '请求过于频繁，请稍后再试',
          code: data?.code,
          status,
          retryable: true,
          timestamp,
        };
        
      case 500:
      case 502:
      case 503:
      case 504:
        return {
          type: 'SERVER_ERROR',
          message: data?.error || '服务器错误',
          userMessage: '服务器暂时无法处理请求，请稍后重试',
          code: data?.code,
          status,
          retryable: true,
          timestamp,
        };
        
      default:
        if (error.code === 'NETWORK_ERROR' || error.code === 'ERR_NETWORK') {
          return {
            type: 'NETWORK_ERROR',
            message: '网络连接失败',
            userMessage: '网络连接异常，请检查网络设置',
            code: error.code,
            retryable: true,
            timestamp,
          };
        }
    }
  }
  
  // Handle AppError
  if (error instanceof AppError) {
    return {
      type: 'UNKNOWN_ERROR',
      message: error.message,
      userMessage: error.message,
      code: error.code,
      status: error.status,
      details: error.details,
      retryable: false,
      timestamp,
    };
  }
  
  // Handle generic Error
  if (error instanceof Error) {
    return {
      type: 'UNKNOWN_ERROR',
      message: error.message,
      userMessage: '发生了未知错误，请稍后重试',
      retryable: false,
      timestamp,
    };
  }
  
  // Handle unknown error types
  return {
    type: 'UNKNOWN_ERROR',
    message: String(error),
    userMessage: '发生了未知错误，请稍后重试',
    retryable: false,
    timestamp,
  };
};

/**
 * Get user-friendly error message based on error type
 */
export const getErrorMessage = (error: ProcessedError, context?: string): string => {
  const contextPrefix = context ? `${context}: ` : '';
  
  switch (error.type) {
    case 'NETWORK_ERROR':
      return `${contextPrefix}网络连接失败，请检查网络设置后重试`;
    case 'AUTHENTICATION_ERROR':
      return `${contextPrefix}登录已过期，请重新登录`;
    case 'AUTHORIZATION_ERROR':
      return `${contextPrefix}您没有权限执行此操作`;
    case 'VALIDATION_ERROR':
      return `${contextPrefix}输入信息有误，请检查后重试`;
    case 'NOT_FOUND_ERROR':
      return `${contextPrefix}请求的资源不存在`;
    case 'RATE_LIMIT_ERROR':
      return `${contextPrefix}操作过于频繁，请稍后再试`;
    case 'SERVER_ERROR':
      return `${contextPrefix}服务器暂时无法处理请求，请稍后重试`;
    default:
      return `${contextPrefix}${error.userMessage}`;
  }
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: ProcessedError): boolean => {
  return error.retryable;
};

/**
 * Get retry delay based on error type (in milliseconds)
 */
export const getRetryDelay = (error: ProcessedError, attempt: number): number => {
  const baseDelay = 1000; // 1 second
  const maxDelay = 30000; // 30 seconds
  
  switch (error.type) {
    case 'RATE_LIMIT_ERROR':
      return Math.min(baseDelay * Math.pow(2, attempt), maxDelay);
    case 'NETWORK_ERROR':
    case 'SERVER_ERROR':
      return Math.min(baseDelay * Math.pow(1.5, attempt), maxDelay);
    default:
      return baseDelay;
  }
};

/**
 * Log error for debugging and monitoring
 */
export const logError = (error: ProcessedError, context?: string): void => {
  const logData = {
    ...error,
    context,
    userAgent: navigator.userAgent,
    url: window.location.href,
    userId: localStorage.getItem('userId'), // If available
  };
  
  if (import.meta.env.DEV) {
    console.error('Error logged:', logData);
  }
  
  // In production, send to error monitoring service
  if (import.meta.env.PROD) {
    // Example: Send to Sentry, LogRocket, etc.
    // errorMonitoringService.captureError(logData);
  }
};

/**
 * Create error handler for async operations
 */
export const createErrorHandler = (
  context: string,
  onError?: (error: ProcessedError) => void
) => {
  return (error: unknown) => {
    const processedError = processError(error, context);
    logError(processedError, context);
    onError?.(processedError);
    return processedError;
  };
};

/**
 * Retry mechanism for failed operations
 */
export const withRetry = async <T>(
  operation: () => Promise<T>,
  maxAttempts: number = 3,
  context?: string
): Promise<T> => {
  let lastError: ProcessedError | null = null;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await operation();
    } catch (error) {
      const processedError = processError(error, context);
      lastError = processedError;
      
      if (attempt === maxAttempts || !isRetryableError(processedError)) {
        logError(processedError, `${context} (final attempt: ${attempt})`);
        throw error;
      }
      
      const delay = getRetryDelay(processedError, attempt);
      logError(processedError, `${context} (attempt: ${attempt}, retrying in ${delay}ms)`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

/**
 * Error boundary helper for React components
 */
export const handleComponentError = (
  error: Error,
  errorInfo: any,
  componentName: string
): void => {
  const processedError = processError(error, `Component: ${componentName}`);
  logError(processedError, componentName);
};

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandlers = (): void => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = processError(event.reason, 'Unhandled Promise Rejection');
    logError(error);
    
    // Prevent the default browser behavior
    event.preventDefault();
  });
  
  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    const error = processError(event.error, 'Uncaught Error');
    logError(error);
  });
};
