import { useEffect, useState } from 'react';

// Breakpoint definitions (matching Tailwind CSS)
export const breakpoints = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

export type Breakpoint = keyof typeof breakpoints;

export interface DeviceInfo {
  width: number;
  height: number;
  breakpoint: Breakpoint;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  orientation: 'portrait' | 'landscape';
  pixelRatio: number;
  touchSupport: boolean;
}

/**
 * Get current breakpoint based on window width
 */
export const getCurrentBreakpoint = (width: number): Breakpoint => {
  if (width >= breakpoints['2xl']) return '2xl';
  if (width >= breakpoints.xl) return 'xl';
  if (width >= breakpoints.lg) return 'lg';
  if (width >= breakpoints.md) return 'md';
  if (width >= breakpoints.sm) return 'sm';
  return 'sm'; // Default to smallest breakpoint
};

/**
 * Check if current width matches a breakpoint
 */
export const matchesBreakpoint = (width: number, breakpoint: Breakpoint): boolean => {
  return width >= breakpoints[breakpoint];
};

/**
 * Get device type based on width
 */
export const getDeviceType = (width: number) => {
  const isMobile = width < breakpoints.md;
  const isTablet = width >= breakpoints.md && width < breakpoints.lg;
  const isDesktop = width >= breakpoints.lg;
  
  return { isMobile, isTablet, isDesktop };
};

/**
 * Hook for responsive design
 */
export const useResponsive = () => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    if (typeof window === 'undefined') {
      return {
        width: 1024,
        height: 768,
        breakpoint: 'lg',
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        orientation: 'landscape',
        pixelRatio: 1,
        touchSupport: false,
      };
    }

    const width = window.innerWidth;
    const height = window.innerHeight;
    const { isMobile, isTablet, isDesktop } = getDeviceType(width);

    return {
      width,
      height,
      breakpoint: getCurrentBreakpoint(width),
      isMobile,
      isTablet,
      isDesktop,
      orientation: width > height ? 'landscape' : 'portrait',
      pixelRatio: window.devicePixelRatio || 1,
      touchSupport: 'ontouchstart' in window,
    };
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const { isMobile, isTablet, isDesktop } = getDeviceType(width);

      setDeviceInfo({
        width,
        height,
        breakpoint: getCurrentBreakpoint(width),
        isMobile,
        isTablet,
        isDesktop,
        orientation: width > height ? 'landscape' : 'portrait',
        pixelRatio: window.devicePixelRatio || 1,
        touchSupport: 'ontouchstart' in window,
      });
    };

    window.addEventListener('resize', updateDeviceInfo);
    window.addEventListener('orientationchange', updateDeviceInfo);

    return () => {
      window.removeEventListener('resize', updateDeviceInfo);
      window.removeEventListener('orientationchange', updateDeviceInfo);
    };
  }, []);

  return deviceInfo;
};

/**
 * Hook for specific breakpoint matching
 */
export const useBreakpoint = (breakpoint: Breakpoint) => {
  const { width } = useResponsive();
  return matchesBreakpoint(width, breakpoint);
};

/**
 * Hook for media queries
 */
export const useMediaQuery = (query: string) => {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(query);
    setMatches(mediaQuery.matches);

    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    mediaQuery.addEventListener('change', handler);
    return () => mediaQuery.removeEventListener('change', handler);
  }, [query]);

  return matches;
};

/**
 * Responsive value hook - returns different values based on breakpoint
 */
export const useResponsiveValue = <T>(values: {
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
  default: T;
}) => {
  const { breakpoint } = useResponsive();
  
  // Return the value for current breakpoint or fall back to smaller ones
  if (breakpoint === '2xl' && values['2xl'] !== undefined) return values['2xl'];
  if (breakpoint === 'xl' && values.xl !== undefined) return values.xl;
  if (breakpoint === 'lg' && values.lg !== undefined) return values.lg;
  if (breakpoint === 'md' && values.md !== undefined) return values.md;
  if (breakpoint === 'sm' && values.sm !== undefined) return values.sm;
  
  return values.default;
};

/**
 * Responsive grid columns hook
 */
export const useResponsiveColumns = (config: {
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  '2xl'?: number;
  default: number;
}) => {
  return useResponsiveValue(config);
};

/**
 * Safe area insets for mobile devices
 */
export const useSafeAreaInsets = () => {
  const [insets, setInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const updateInsets = () => {
      const style = getComputedStyle(document.documentElement);
      setInsets({
        top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
        right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
        bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
        left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
      });
    };

    updateInsets();
    window.addEventListener('resize', updateInsets);
    window.addEventListener('orientationchange', updateInsets);

    return () => {
      window.removeEventListener('resize', updateInsets);
      window.removeEventListener('orientationchange', updateInsets);
    };
  }, []);

  return insets;
};

/**
 * Responsive container hook
 */
export const useResponsiveContainer = () => {
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  const getContainerClass = () => {
    if (isMobile) return 'px-4 max-w-full';
    if (isTablet) return 'px-6 max-w-4xl mx-auto';
    return 'px-8 max-w-7xl mx-auto';
  };

  const getPadding = () => {
    if (isMobile) return { x: 16, y: 16 };
    if (isTablet) return { x: 24, y: 24 };
    return { x: 32, y: 32 };
  };

  return {
    containerClass: getContainerClass(),
    padding: getPadding(),
    isMobile,
    isTablet,
    isDesktop,
  };
};

/**
 * Responsive font size hook
 */
export const useResponsiveFontSize = (config: {
  sm?: string;
  md?: string;
  lg?: string;
  xl?: string;
  '2xl'?: string;
  default: string;
}) => {
  return useResponsiveValue(config);
};

/**
 * Touch gesture detection
 */
export const useTouchGestures = () => {
  const { touchSupport } = useResponsive();
  const [isTouch, setIsTouch] = useState(false);

  useEffect(() => {
    const handleTouchStart = () => setIsTouch(true);
    const handleMouseDown = () => setIsTouch(false);

    if (touchSupport) {
      document.addEventListener('touchstart', handleTouchStart);
      document.addEventListener('mousedown', handleMouseDown);

      return () => {
        document.removeEventListener('touchstart', handleTouchStart);
        document.removeEventListener('mousedown', handleMouseDown);
      };
    }
  }, [touchSupport]);

  return {
    touchSupport,
    isTouch,
    preferTouch: touchSupport && isTouch,
  };
};

/**
 * Responsive spacing utility
 */
export const getResponsiveSpacing = (
  size: 'xs' | 'sm' | 'md' | 'lg' | 'xl',
  breakpoint: Breakpoint
) => {
  const spacingMap = {
    xs: { sm: 2, md: 3, lg: 4, xl: 5, '2xl': 6 },
    sm: { sm: 3, md: 4, lg: 6, xl: 8, '2xl': 10 },
    md: { sm: 4, md: 6, lg: 8, xl: 12, '2xl': 16 },
    lg: { sm: 6, md: 8, lg: 12, xl: 16, '2xl': 20 },
    xl: { sm: 8, md: 12, lg: 16, xl: 24, '2xl': 32 },
  };

  return spacingMap[size][breakpoint] || spacingMap[size].md;
};

/**
 * Responsive image sizing
 */
export const getResponsiveImageSizes = (breakpoint: Breakpoint) => {
  const sizeMap = {
    sm: '(max-width: 640px) 100vw',
    md: '(max-width: 768px) 100vw, 50vw',
    lg: '(max-width: 1024px) 100vw, 33vw',
    xl: '(max-width: 1280px) 100vw, 25vw',
    '2xl': '(max-width: 1536px) 100vw, 20vw',
  };

  return sizeMap[breakpoint] || sizeMap.md;
};
