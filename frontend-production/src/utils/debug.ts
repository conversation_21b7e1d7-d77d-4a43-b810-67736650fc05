/**
 * Debug utilities for development environment
 * CRITICAL SECURITY: 调试用户在生产环境中被标记为非法用户
 */

import { User, UserSubscription } from '@/types/user';

export interface DebugUser extends User {
  subscriptionPlan?: 'free' | 'basic' | 'premium';
  isDebugUser: true;
}

/**
 * 检查调试用户是否为非法用户
 * 在生产环境中，任何调试用户都被视为非法
 */
export const isIllegalDebugUser = (user: any): boolean => {
  if (!user) return false;
  
  // 检查调试用户标识
  const debugUserIndicators = [
    user.isDebugUser === true,
    user.email === '<EMAIL>',
    user.id === 'debug-user-001',
    user.email?.includes('debug'),
    user.name?.includes('调试'),
    user.name?.includes('Debug'),
    user.id?.includes('debug')
  ];
  
  return debugUserIndicators.some(indicator => indicator);
};

/**
 * 强制清除非法调试用户
 */
export const forceRemoveIllegalDebugUser = (): void => {
  console.warn('SECURITY: Force removing illegal debug user');

  // 使用localStorage进行持久化计数，防止无限循环
  const clearingKey = 'storyweaver_clearing_debug_user';
  const lastClearTime = localStorage.getItem('storyweaver_last_clear_time');
  const clearingCount = parseInt(localStorage.getItem(clearingKey) || '0', 10);
  const now = Date.now();

  // 如果在5分钟内已经清除过3次，停止清除
  if (lastClearTime && (now - parseInt(lastClearTime)) < 5 * 60 * 1000 && clearingCount >= 3) {
    console.error('CRITICAL: Too many debug user clear attempts in 5 minutes, stopping to prevent infinite loop');
    // 直接跳转到登录页面，不再刷新
    if (typeof window !== 'undefined') {
      window.location.href = '/auth?reason=debug_user_limit';
    }
    return;
  }

  // 如果超过5分钟，重置计数器
  if (lastClearTime && (now - parseInt(lastClearTime)) >= 5 * 60 * 1000) {
    localStorage.removeItem(clearingKey);
    localStorage.removeItem('storyweaver_last_clear_time');
  }

  try {
    // 更新计数和时间
    localStorage.setItem(clearingKey, (clearingCount + 1).toString());
    localStorage.setItem('storyweaver_last_clear_time', now.toString());

    // 清除认证相关数据（但保留计数器）
    localStorage.removeItem('auth-storage');
    localStorage.removeItem('story-storage');
    localStorage.removeItem('user-preferences');

    // 清除sessionStorage
    sessionStorage.clear();

    console.log('SECURITY: Illegal debug user data cleared');

    // 直接跳转到登录页面，避免页面刷新循环
    if (typeof window !== 'undefined') {
      window.location.href = '/auth?reason=debug_user_cleared';
    }

  } catch (error) {
    console.error('Failed to clear illegal debug user:', error);
    // 如果清理失败，直接跳转到登录页面
    if (typeof window !== 'undefined') {
      window.location.href = '/auth?reason=clear_failed';
    }
  }
};

/**
 * Check if debug mode is enabled
 * PRODUCTION SECURITY: Multiple layers of protection
 */
export const isDebugMode = (): boolean => {
  // LAYER 1: Absolute production environment block
  if (import.meta.env.VITE_ENVIRONMENT === 'production') {
    return false;
  }

  // LAYER 2: Build mode check
  if (!import.meta.env.DEV || import.meta.env.PROD) {
    return false;
  }

  // LAYER 3: Explicit debug mode check
  if (import.meta.env.VITE_DEBUG_MODE !== 'true') {
    return false;
  }

  // LAYER 4: Hostname whitelist (localhost only)
  if (typeof window !== 'undefined') {
    const hostname = window.location.hostname;
    const isLocalhost = hostname === 'localhost' || 
                       hostname === '127.0.0.1' || 
                       hostname.startsWith('192.168.') ||
                       hostname.endsWith('.local');
    
    if (!isLocalhost) {
      console.warn('Debug mode blocked: Not on localhost');
      return false;
    }
  }

  // LAYER 5: Additional runtime check
  if (typeof window !== 'undefined' && window.location.protocol === 'https:' && 
      !window.location.hostname.includes('localhost')) {
    console.warn('Debug mode blocked: HTTPS production domain detected');
    return false;
  }

  return true;
};

/**
 * Get debug user configuration
 * CRITICAL: 在生产环境中，调试用户被标记为非法用户
 */
export const getDebugUser = (): DebugUser => {
  // PRODUCTION SECURITY: 在生产环境中抛出错误
  if (import.meta.env.VITE_ENVIRONMENT === 'production') {
    console.error('ILLEGAL: Debug user access attempted in production!');
    forceRemoveIllegalDebugUser();
    throw new Error('ILLEGAL_DEBUG_USER_ACCESS_IN_PRODUCTION');
  }

  const credits = parseInt(import.meta.env.VITE_DEBUG_USER_CREDITS || '1000', 10);
  const subscriptionPlan = import.meta.env.VITE_DEBUG_USER_SUBSCRIPTION || 'premium';

  return {
    id: 'debug-user-001',
    email: '<EMAIL>',
    name: '调试用户',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=debug',
    credits,
    subscriptionPlan: subscriptionPlan as 'free' | 'basic' | 'premium',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isDebugUser: true,
  };
};

/**
 * Check if authentication should be skipped
 */
export const shouldSkipAuth = (): boolean => {
  return isDebugMode() && import.meta.env.VITE_DEBUG_SKIP_AUTH === 'true';
};

/**
 * Check if payments should be mocked
 */
export const shouldMockPayments = (): boolean => {
  return isDebugMode() && import.meta.env.VITE_DEBUG_MOCK_PAYMENTS === 'true';
};

/**
 * Check if should use real Stripe
 */
export const shouldUseRealStripe = (): boolean => {
  if (import.meta.env.VITE_ENVIRONMENT === 'production') {
    return import.meta.env.VITE_PRODUCTION_STRIPE_ENABLED === 'true' ||
           import.meta.env.VITE_DEBUG_USE_REAL_STRIPE === 'true';
  }

  return isDebugMode() && import.meta.env.VITE_DEBUG_USE_REAL_STRIPE === 'true';
};

// 创建调试用户的订阅对象
const createDebugSubscription = (plan: string): UserSubscription => {
  const now = new Date();
  const nextMonth = new Date();
  nextMonth.setMonth(nextMonth.getMonth() + 1);
  
  return {
    id: 'debug-subscription-001',
    plan: plan,
    status: 'active',
    currentPeriodStart: now.toISOString(),
    currentPeriodEnd: nextMonth.toISOString(),
    cancelAtPeriodEnd: false,
    stripeSubscriptionId: 'debug-stripe-sub-001'
  };
};

/**
 * Debug user types for testing different scenarios
 * CRITICAL: 在生产环境中这些都被视为非法用户
 */
export const DEBUG_USER_TYPES = (() => {
  // 🚨 PRODUCTION FIX: 在生产环境中返回空对象，避免调用getDebugUser()
  if (import.meta.env.VITE_ENVIRONMENT === 'production') {
    return {} as const;
  }

  try {
    const baseUser = getDebugUser();
    return {
      FREE: {
        ...baseUser,
        subscription: createDebugSubscription('free'),
        credits: 50,
        name: '免费用户',
      },
      BASIC: {
        ...baseUser,
        subscription: createDebugSubscription('basic_monthly'),
        credits: 200,
        name: '基础用户',
      },
      PREMIUM: {
        ...baseUser,
        subscription: createDebugSubscription('pro_monthly'),
        credits: 1000,
        name: '高级用户',
      },
      LOW_CREDITS: {
        ...baseUser,
        subscription: createDebugSubscription('pro_monthly'),
        credits: 5,
        name: '积分不足用户',
      },
    } as const;
  } catch (error) {
    console.warn('Failed to create debug user types:', error);
    return {} as const;
  }
})();

/**
 * Debug actions for testing
 * CRITICAL: 在生产环境中这些操作被禁止
 */
export const debugActions = {
  addCredits: (amount: number) => {
    if (!isDebugMode()) return;
    
    const event = new CustomEvent('debug:addCredits', { 
      detail: { amount } 
    });
    window.dispatchEvent(event);
  },

  switchUserType: (type: keyof typeof DEBUG_USER_TYPES) => {
    if (!isDebugMode()) return;
    
    const event = new CustomEvent('debug:switchUser', { 
      detail: { userType: type } 
    });
    window.dispatchEvent(event);
  },

  resetState: () => {
    if (!isDebugMode()) return;
    
    const event = new CustomEvent('debug:resetState');
    window.dispatchEvent(event);
  },

  mockPaymentSuccess: (amount: number, credits: number) => {
    if (!isDebugMode()) return;
    
    const event = new CustomEvent('debug:mockPayment', { 
      detail: { success: true, amount, credits } 
    });
    window.dispatchEvent(event);
  },

  mockPaymentFailure: (error: string) => {
    if (!isDebugMode()) return;
    
    const event = new CustomEvent('debug:mockPayment', { 
      detail: { success: false, error } 
    });
    window.dispatchEvent(event);
  },
};

/**
 * Debug console logger with prefix
 */
export const debugLog = {
  info: (...args: any[]) => {
    if (isDebugMode()) {
      console.log('[DEBUG]', ...args);
    }
  },
  warn: (...args: any[]) => {
    if (isDebugMode()) {
      console.warn('[DEBUG-WARN]', ...args);
    }
  },
  error: (...args: any[]) => {
    if (isDebugMode()) {
      console.error('[DEBUG-ERROR]', ...args);
    }
  },
};

/**
 * Initialize debug mode
 */
export const initializeDebugMode = () => {
  if (!isDebugMode() || import.meta.env.VITE_ENVIRONMENT === 'production') return;

  debugLog.info('Debug mode initialized');
  try {
    debugLog.info('Debug user:', getDebugUser());
  } catch (error) {
    console.warn('Failed to get debug user in initialization:', error);
  }
  debugLog.info('Available debug actions:', Object.keys(debugActions));

  if (typeof window !== 'undefined') {
    (window as any).__STORYWEAVER_DEBUG__ = {
      isDebugMode,
      getDebugUser: () => {
        try {
          return getDebugUser();
        } catch (error) {
          console.warn('Failed to get debug user:', error);
          return null;
        }
      },
      debugActions,
      DEBUG_USER_TYPES,
    };
  }
};

/**
 * 重置非法调试用户清除计数器
 * 当用户成功登录真实用户时调用
 */
export const resetIllegalDebugUserCounter = (): void => {
  try {
    sessionStorage.removeItem('storyweaver_clearing_debug_user');
  } catch (e) {
    console.warn('Failed to reset debug user counter:', e);
  }
};

/**
 * Cleanup debug mode
 */
export const cleanupDebugMode = () => {
  if (!isDebugMode()) return;

  if (typeof window !== 'undefined') {
    delete (window as any).__STORYWEAVER_DEBUG__;
  }
};