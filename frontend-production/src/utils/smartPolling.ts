/**
 * 智能轮询工具类
 * 实现指数退避、网络状况检测和CDN友好的轮询策略
 */

export interface SmartPollingOptions {
  baseInterval?: number;      // 基础轮询间隔（毫秒）
  maxInterval?: number;       // 最大轮询间隔（毫秒）
  maxRetries?: number;        // 最大重试次数
  backoffMultiplier?: number; // 退避倍数
  jitterRange?: number;       // 抖动范围（0-1）
}

export interface PollingState {
  isRunning: boolean;
  currentInterval: number;
  retryCount: number;
  lastSuccessTime: number;
  lastErrorTime: number;
}

export class SmartPoller {
  private options: Required<SmartPollingOptions>;
  private state: PollingState;
  private timeoutId: NodeJS.Timeout | null = null;
  private pollFunction: () => Promise<boolean>;
  private onSuccess?: () => void;
  private onError?: (error: Error) => void;

  constructor(
    pollFunction: () => Promise<boolean>,
    options: SmartPollingOptions = {}
  ) {
    this.options = {
      baseInterval: 8000,        // 8秒基础间隔
      maxInterval: 30000,        // 30秒最大间隔
      maxRetries: 5,             // 最大5次重试
      backoffMultiplier: 1.5,    // 1.5倍退避
      jitterRange: 0.1,          // 10%抖动
      ...options
    };

    this.state = {
      isRunning: false,
      currentInterval: this.options.baseInterval,
      retryCount: 0,
      lastSuccessTime: 0,
      lastErrorTime: 0
    };

    this.pollFunction = pollFunction;
  }

  /**
   * 开始轮询
   */
  start(onSuccess?: () => void, onError?: (error: Error) => void): void {
    if (this.state.isRunning) {
      console.warn('SmartPoller: Already running');
      return;
    }

    this.onSuccess = onSuccess;
    this.onError = onError;
    this.state.isRunning = true;
    this.state.retryCount = 0;
    this.state.currentInterval = this.options.baseInterval;

    console.log(`🔄 SmartPoller: Starting with ${this.state.currentInterval}ms interval`);
    this.scheduleNext();
  }

  /**
   * 停止轮询
   */
  stop(): void {
    if (!this.state.isRunning) {
      return;
    }

    this.state.isRunning = false;
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    console.log('⏹️ SmartPoller: Stopped');
  }

  /**
   * 获取当前状态
   */
  getState(): Readonly<PollingState> {
    return { ...this.state };
  }

  /**
   * 调度下一次轮询
   */
  private scheduleNext(): void {
    if (!this.state.isRunning) {
      return;
    }

    const interval = this.calculateInterval();
    
    this.timeoutId = setTimeout(async () => {
      await this.executePoll();
    }, interval);
  }

  /**
   * 执行轮询
   */
  private async executePoll(): Promise<void> {
    if (!this.state.isRunning) {
      return;
    }

    try {
      console.log(`📡 SmartPoller: Polling (attempt ${this.state.retryCount + 1})`);
      
      const shouldContinue = await this.pollFunction();
      
      // 轮询成功
      this.handleSuccess();
      
      if (shouldContinue && this.state.isRunning) {
        this.scheduleNext();
      } else {
        console.log('✅ SmartPoller: Polling completed successfully');
        this.stop();
        this.onSuccess?.();
      }
      
    } catch (error) {
      this.handleError(error as Error);
    }
  }

  /**
   * 处理成功响应
   */
  private handleSuccess(): void {
    this.state.lastSuccessTime = Date.now();
    this.state.retryCount = 0;
    this.state.currentInterval = this.options.baseInterval;
  }

  /**
   * 处理错误响应
   */
  private handleError(error: Error): void {
    this.state.lastErrorTime = Date.now();
    this.state.retryCount++;

    console.warn(`❌ SmartPoller: Error (${this.state.retryCount}/${this.options.maxRetries}):`, error.message);

    if (this.state.retryCount >= this.options.maxRetries) {
      console.error('💥 SmartPoller: Max retries exceeded, stopping');
      this.stop();
      this.onError?.(error);
      return;
    }

    // 应用指数退避
    this.state.currentInterval = Math.min(
      this.state.currentInterval * this.options.backoffMultiplier,
      this.options.maxInterval
    );

    console.log(`⏳ SmartPoller: Backing off to ${this.state.currentInterval}ms`);

    if (this.state.isRunning) {
      this.scheduleNext();
    }
  }

  /**
   * 计算下一次轮询间隔（包含抖动）
   */
  private calculateInterval(): number {
    const baseInterval = this.state.currentInterval;
    const jitter = this.options.jitterRange;
    
    // 添加随机抖动以避免雷群效应
    const jitterAmount = baseInterval * jitter * (Math.random() - 0.5);
    const finalInterval = Math.max(1000, baseInterval + jitterAmount);
    
    return Math.round(finalInterval);
  }

  /**
   * 检查网络状况
   */
  private isNetworkHealthy(): boolean {
    const now = Date.now();
    const timeSinceLastSuccess = now - this.state.lastSuccessTime;
    const timeSinceLastError = now - this.state.lastErrorTime;
    
    // 如果最近有成功请求，认为网络健康
    if (timeSinceLastSuccess < 60000) { // 1分钟内
      return true;
    }
    
    // 如果最近有错误，认为网络不健康
    if (timeSinceLastError < 30000) { // 30秒内
      return false;
    }
    
    return true;
  }
}

/**
 * 创建故事轮询器的便捷函数 - 使用Durable Objects状态端点
 */
export function createStoryPoller(
  storyId: string,
  getStoryById: (id: string) => Promise<any>,
  options: SmartPollingOptions = {},
  onUpdate?: (story: any) => void
): SmartPoller {
  const pollFunction = async (): Promise<boolean> => {
    try {
      // 优先查询Durable Objects状态
      const doStatus = await getDurableObjectStatus(storyId);

      if (doStatus && doStatus.success) {
        // 使用DO状态数据
        const storyData = {
          id: storyId,
          status: mapDOStatusToStoryStatus(doStatus.story.status, doStatus.tasks),
          tasks: doStatus.tasks,
          doStatus: doStatus.story,
          lastUpdated: Date.now()
        };

        // 调用更新回调
        onUpdate?.(storyData);

        // 检查是否完成
        const isFinished = isStoryGenerationComplete(doStatus.tasks);
        return !isFinished;
      } else {
        // 降级到传统数据库查询
        console.warn('DO status query failed, falling back to database query');
        const story = await getStoryById(storyId);

        if (!story) {
          throw new Error('Story not found');
        }

        onUpdate?.(story);
        const isFinished = ['completed', 'failed'].includes(story.status);
        return !isFinished;
      }
    } catch (error) {
      console.error('Polling error:', error);
      // 降级到传统查询
      const story = await getStoryById(storyId);
      if (story) {
        onUpdate?.(story);
        const isFinished = ['completed', 'failed'].includes(story.status);
        return !isFinished;
      }
      throw error;
    }
  };

  return new SmartPoller(pollFunction, {
    baseInterval: 8000,    // 8秒基础间隔，CDN友好
    maxInterval: 30000,    // 30秒最大间隔
    maxRetries: 5,         // 5次重试
    backoffMultiplier: 1.5, // 1.5倍退避
    jitterRange: 0.1,      // 10%抖动
    ...options
  });
}

/**
 * 查询Durable Objects状态
 */
async function getDurableObjectStatus(storyId: string): Promise<any> {
  const response = await fetch(`/ai-queue/${storyId}/status?storyId=${storyId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`DO status query failed: ${response.status}`);
  }

  return await response.json();
}

/**
 * 将DO状态映射到故事状态
 */
function mapDOStatusToStoryStatus(doStatus: string, tasks: any[]): string {
  if (!tasks || tasks.length === 0) {
    return 'preparing';
  }

  const textTask = tasks.find(t => t.type === 'text');
  const imageTask = tasks.find(t => t.type === 'image');
  const audioTask = tasks.find(t => t.type === 'audio');

  // 检查所有任务是否完成
  if (tasks.every(t => t.status === 'completed')) {
    return 'completed';
  }

  // 检查是否有失败的任务
  if (tasks.some(t => t.status === 'failed')) {
    return 'failed';
  }

  // 根据当前运行的任务确定状态
  if (audioTask?.status === 'running') {
    return 'generating_audio';
  } else if (imageTask?.status === 'running') {
    return 'generating_images';
  } else if (textTask?.status === 'running') {
    return 'generating_text';
  } else if (audioTask?.status === 'completed' && imageTask?.status === 'completed' && textTask?.status === 'completed') {
    return 'composing';
  } else {
    return 'preparing';
  }
}

/**
 * 检查故事生成是否完成
 */
function isStoryGenerationComplete(tasks: any[]): boolean {
  if (!tasks || tasks.length === 0) {
    return false;
  }

  return tasks.every(task =>
    task.status === 'completed' || task.status === 'failed'
  );
}
