import { AuthTokens } from '@/types';

const TOKEN_STORAGE_KEY = 'auth-storage';
const REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry

export class TokenManager {
  private static instance: TokenManager;
  private refreshTimer: NodeJS.Timeout | null = null;
  private refreshPromise: Promise<void> | null = null;

  private constructor() {}

  static getInstance(): TokenManager {
    if (!TokenManager.instance) {
      TokenManager.instance = new TokenManager();
    }
    return TokenManager.instance;
  }

  /**
   * Get stored tokens
   */
  getTokens(): AuthTokens | null {
    try {
      const stored = localStorage.getItem(TOKEN_STORAGE_KEY);
      if (!stored) return null;

      const { state } = JSON.parse(stored);
      return state?.tokens || null;
    } catch (error) {
      console.error('Failed to parse stored tokens:', error);
      this.clearTokens();
      return null;
    }
  }

  /**
   * Store tokens
   */
  setTokens(tokens: AuthTokens): void {
    try {
      const stored = localStorage.getItem(TOKEN_STORAGE_KEY);
      let data = { state: { tokens } };

      if (stored) {
        const parsed = JSON.parse(stored);
        data = {
          ...parsed,
          state: {
            ...parsed.state,
            tokens,
          },
        };
      }

      localStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(data));
      this.scheduleTokenRefresh(tokens);
    } catch (error) {
      console.error('Failed to store tokens:', error);
    }
  }

  /**
   * Clear stored tokens
   */
  clearTokens(): void {
    localStorage.removeItem(TOKEN_STORAGE_KEY);
    this.clearRefreshTimer();
  }

  /**
   * Get access token
   */
  getAccessToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.accessToken || null;
  }

  /**
   * Get refresh token
   */
  getRefreshToken(): string | null {
    const tokens = this.getTokens();
    return tokens?.refreshToken || null;
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(): boolean {
    const tokens = this.getTokens();
    if (!tokens?.expiresAt) return true;

    return Date.now() >= tokens.expiresAt;
  }

  /**
   * Check if token needs refresh (within threshold)
   */
  needsRefresh(): boolean {
    const tokens = this.getTokens();
    if (!tokens?.expiresAt) return true;

    return Date.now() >= (tokens.expiresAt - REFRESH_THRESHOLD);
  }

  /**
   * Schedule automatic token refresh
   */
  private scheduleTokenRefresh(tokens: AuthTokens): void {
    this.clearRefreshTimer();

    if (!tokens.expiresAt) return;

    const refreshTime = tokens.expiresAt - Date.now() - REFRESH_THRESHOLD;
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(() => {
        this.refreshTokens();
      }, refreshTime);
    }
  }

  /**
   * Clear refresh timer
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Refresh tokens
   */
  async refreshTokens(): Promise<void> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    this.refreshPromise = this.performTokenRefresh(refreshToken);
    
    try {
      await this.refreshPromise;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(refreshToken: string): Promise<void> {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken }),
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Token refresh failed');
      }

      const newTokens: AuthTokens = {
        accessToken: data.data.token,
        refreshToken: data.data.refreshToken,
        expiresAt: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      };

      this.setTokens(newTokens);
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearTokens();
      
      // Redirect to login page
      window.location.href = '/auth';
      throw error;
    }
  }

  /**
   * Decode JWT token payload (without verification)
   */
  decodeToken(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid token format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      console.error('Failed to decode token:', error);
      return null;
    }
  }

  /**
   * Get token expiry time
   */
  getTokenExpiry(): Date | null {
    const tokens = this.getTokens();
    if (!tokens?.expiresAt) return null;

    return new Date(tokens.expiresAt);
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  getTimeUntilExpiry(): number {
    const tokens = this.getTokens();
    if (!tokens?.expiresAt) return 0;

    return Math.max(0, tokens.expiresAt - Date.now());
  }

  /**
   * Check if user has valid authentication
   */
  isAuthenticated(): boolean {
    const tokens = this.getTokens();
    return !!(tokens?.accessToken && !this.isTokenExpired());
  }

  /**
   * Initialize token manager
   */
  initialize(): void {
    const tokens = this.getTokens();
    if (tokens) {
      // Schedule refresh if token is still valid
      if (!this.isTokenExpired()) {
        this.scheduleTokenRefresh(tokens);
      } else {
        // Token is expired, try to refresh
        this.refreshTokens().catch(() => {
          // Refresh failed, clear tokens
          this.clearTokens();
        });
      }
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.clearRefreshTimer();
    this.refreshPromise = null;
  }
}

// Export singleton instance
export const tokenManager = TokenManager.getInstance();

// Auto-initialize when module is loaded
if (typeof window !== 'undefined') {
  tokenManager.initialize();
  
  // Cleanup on page unload
  window.addEventListener('beforeunload', () => {
    tokenManager.destroy();
  });
}

// Utility functions
export const getAuthHeader = (): Record<string, string> => {
  const token = tokenManager.getAccessToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
};

export const isTokenValid = (): boolean => {
  return tokenManager.isAuthenticated();
};

export const forceTokenRefresh = (): Promise<void> => {
  return tokenManager.refreshTokens();
};
