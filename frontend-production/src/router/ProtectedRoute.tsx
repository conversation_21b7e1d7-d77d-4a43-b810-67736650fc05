import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '@/stores/authStore';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { shouldSkipAuth, debugLog } from '@/utils/debug';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/auth',
}) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, user, initializeAuth } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    const initialize = async () => {
      if (!isInitialized) {
        await initializeAuth();
        setIsInitialized(true);
      }
    };

    initialize();
  }, [initializeAuth, isInitialized]);

  // Show loading while checking authentication
  if (isLoading || !isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mb-4" />
          <p className="text-gray-600">验证登录状态...</p>
        </div>
      </div>
    );
  }

  // In debug mode, skip auth requirements
  if (shouldSkipAuth()) {
    debugLog.info('ProtectedRoute: Skipping auth check in debug mode');
    return <>{children}</>;
  }

  // Redirect to auth if not authenticated and auth is required
  if (requireAuth && !isAuthenticated) {
    debugLog.info('ProtectedRoute: Redirecting to auth - not authenticated');
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // Redirect authenticated users away from auth pages
  if (!requireAuth && isAuthenticated && location.pathname === '/auth') {
    const from = location.state?.from || '/my-stories';
    return <Navigate to={from} replace />;
  }

  return <>{children}</>;
};

// Higher-order component for protecting routes
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireAuth?: boolean;
    redirectTo?: string;
  }
) => {
  return (props: P) => (
    <ProtectedRoute
      requireAuth={options?.requireAuth}
      redirectTo={options?.redirectTo}
    >
      <Component {...props} />
    </ProtectedRoute>
  );
};

// Hook for checking authentication in components
export const useRequireAuth = (redirectTo = '/auth') => {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useAuthStore();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to auth page with current location
      window.location.href = `${redirectTo}?from=${encodeURIComponent(location.pathname)}`;
    }
  }, [isAuthenticated, isLoading, redirectTo, location.pathname]);

  return { isAuthenticated, isLoading };
};
