import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Filter, 
  Grid, 
  List, 
  Calendar,
  Clock,
  Play,
  MoreHorizontal,
  Eye,
  Download,
  Trash2
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Input, SearchInput } from '@/components/ui/Input';
import { Card, ImageCard } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { StoryAudioBadge } from '@/components/features/StoryAudioIndicator';
import { useStoryStore } from '@/stores/storyStore';
import { useNotifications } from '@/stores/uiStore';
import { Story } from '@/types';
import { cn } from '@/utils/cn';

const MyStoriesPage: React.FC = () => {
  const navigate = useNavigate();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'title'>('newest');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [deletingStoryId, setDeletingStoryId] = useState<string | null>(null);

  const { 
    stories, 
    isLoading, 
    error, 
    pagination,
    getStories,
    deleteStory,
    setFilters 
  } = useStoryStore();
  
  const { showSuccess, showError } = useNotifications();

  useEffect(() => {
    // 加载故事列表
    getStories();
  }, [getStories]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setFilters({ query });
  };

  const handleDelete = async (storyId: string) => {
    if (window.confirm('确定要删除这个故事吗？此操作无法撤销。')) {
      try {
        // 设置删除状态
        setDeletingStoryId(storyId);

        await deleteStory(storyId);

        // 删除成功
        showSuccess('删除成功', '故事已从您的收藏中移除');
      } catch (error) {
        console.error('Delete story error:', error);
        showError('删除失败', error instanceof Error ? error.message : '请稍后重试');
      } finally {
        // 清除删除状态
        setDeletingStoryId(null);
      }
    }
  };

  const handleViewStory = (storyId: string) => {
    navigate(`/stories/${storyId}`);
  };

  const handleDownloadStory = async (story: Story) => {
    try {
      // 这里可以实现下载功能
      showSuccess('下载开始', '故事正在下载中...');
      // 实际实现可以调用下载API或生成PDF等
    } catch (error) {
      showError('下载失败', '请重试');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'generating':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'generating':
        return '生成中';
      case 'failed':
        return '生成失败';
      default:
        return '未知状态';
    }
  };

  const filteredStories = (stories || []).filter(story => {
    if (filterStatus !== 'all' && story.status !== filterStatus) {
      return false;
    }
    if (searchQuery && !story.title.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    return true;
  });

  const sortedStories = [...filteredStories].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case 'oldest':
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case 'title':
        return a.title.localeCompare(b.title);
      default:
        return 0;
    }
  });

  if (isLoading && stories.length === 0) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner size="lg" label="加载故事中..." />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">我的故事</h1>
            <p className="text-gray-600">
              您已创作了 {stories.length} 个故事
            </p>
          </div>
          <Link to="/create">
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              创作新故事
            </Button>
          </Link>
        </div>

        {/* Filters and Search */}
        <Card className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <SearchInput
                placeholder="搜索故事标题..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onSearch={handleSearch}
              />
            </div>
            
            <div className="flex gap-2">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">全部状态</option>
                <option value="completed">已完成</option>
                <option value="generating">生成中</option>
                <option value="failed">生成失败</option>
              </select>
              
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="newest">最新创建</option>
                <option value="oldest">最早创建</option>
                <option value="title">按标题排序</option>
              </select>
              
              <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={cn(
                    'p-2 text-sm',
                    viewMode === 'grid' ? 'bg-primary-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                  )}
                >
                  <Grid className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={cn(
                    'p-2 text-sm',
                    viewMode === 'list' ? 'bg-primary-500 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                  )}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </Card>

        {/* Stories Grid/List */}
        {sortedStories.length === 0 ? (
          <Card className="p-12 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Plus className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {searchQuery || filterStatus !== 'all' ? '没有找到匹配的故事' : '还没有故事'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery || filterStatus !== 'all' 
                ? '尝试调整搜索条件或筛选器'
                : '开始创作您的第一个专属故事吧！'
              }
            </p>
            {!searchQuery && filterStatus === 'all' && (
              <Link to="/create">
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  创作第一个故事
                </Button>
              </Link>
            )}
          </Card>
        ) : (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
              : 'space-y-4'
          )}>
            {sortedStories.map((story, index) => (
              <motion.div
                key={story.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {viewMode === 'grid' ? (
                  <ImageCard
                    src={story.coverImageUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjE1MCIgeT0iMTAwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiBmaWxsPSIjNmI3MjgwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTYiPuaVheS6i+WwgemdojwvdGV4dD4KPHN2Zz4='}
                    alt={story.title}
                    title={story.title}
                    description={`${story.characterName}的故事 · ${formatDate(story.createdAt)}`}
                    action={
                      <div className="flex items-center justify-between">
                        <span className={cn(
                          'px-2 py-1 rounded-full text-xs font-medium',
                          getStatusColor(story.status)
                        )}>
                          {getStatusText(story.status)}
                        </span>
                        <div className="flex space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-1"
                            onClick={() => handleViewStory(story.id)}
                            title="查看故事"
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-1"
                            onClick={() => handleDownloadStory(story)}
                            title="下载故事"
                          >
                            <Download className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="p-1 text-red-600 hover:text-red-700"
                            onClick={() => handleDelete(story.id)}
                            disabled={deletingStoryId === story.id}
                          >
                            {deletingStoryId === story.id ? (
                              <LoadingSpinner size="sm" />
                            ) : (
                              <Trash2 className="w-4 h-4" />
                            )}
                          </Button>
                        </div>
                      </div>
                    }
                  />
                ) : (
                  <Card className="p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center space-x-4">
                      <img
                        src={story.coverImageUrl || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjMyIiB5PSIzMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iIzZiNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0Ij7wn5OQPC90ZXh0Pgo8L3N2Zz4='}
                        alt={story.title}
                        className="w-16 h-16 rounded-lg object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjZjNmNGY2Ii8+Cjx0ZXh0IHg9IjMyIiB5PSIzMiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZG9taW5hbnQtYmFzZWxpbmU9Im1pZGRsZSIgZmlsbD0iIzZiNzI4MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0Ij7wn5OQPC90ZXh0Pgo8L3N2Zz4=';
                        }}
                      />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 truncate">
                          {story.title}
                        </h3>
                        <p className="text-sm text-gray-600">
                          {story.characterName}的故事
                        </p>
                        <div className="flex items-center space-x-4 mt-1">
                          <span className="text-xs text-gray-500 flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {formatDate(story.createdAt)}
                          </span>
                          <span className={cn(
                            'px-2 py-1 rounded-full text-xs font-medium',
                            getStatusColor(story.status)
                          )}>
                            {getStatusText(story.status)}
                          </span>
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2"
                          onClick={() => handleViewStory(story.id)}
                          title="查看故事"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2"
                          onClick={() => handleDownloadStory(story)}
                          title="下载故事"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-2 text-red-600 hover:text-red-700"
                          onClick={() => handleDelete(story.id)}
                          disabled={deletingStoryId === story.id}
                        >
                          {deletingStoryId === story.id ? (
                            <LoadingSpinner size="sm" />
                          ) : (
                            <Trash2 className="w-4 h-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </Card>
                )}
              </motion.div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.total > pagination.limit && (
          <div className="flex justify-center">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                disabled={!pagination.hasPrev}
                onClick={() => getStories(undefined, pagination.page - 1)}
              >
                上一页
              </Button>
              <span className="flex items-center px-4 text-sm text-gray-600">
                第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.limit)} 页
              </span>
              <Button
                variant="outline"
                disabled={!pagination.hasNext}
                onClick={() => getStories(undefined, pagination.page + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default MyStoriesPage;
