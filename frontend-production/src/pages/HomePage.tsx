import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import {
  Sparkles,
  BookOpen,
  Volume2,
  Image,
  Heart,
  Star,
  ArrowRight,
  Play,
  Users,
  Award,
  Zap
} from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Card, FeatureCard, StatsCard } from '@/components/ui/Card';
import { useAuthStore } from '@/stores/authStore';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuthStore();
  const { t } = useTranslation();

  const handleGetStarted = () => {
    if (isAuthenticated) {
      navigate('/create');
    } else {
      navigate('/auth');
    }
  };

  return (
    <Layout>
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
                {t('home.hero.title')}
                <span className="text-gradient block">{t('home.hero.titleHighlight')}</span>
              </h1>
              <p className="text-xl text-gray-800 mb-8 leading-relaxed">
                {t('home.hero.subtitle')}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  size="lg"
                  onClick={handleGetStarted}
                  className="text-lg px-8 py-4"
                >
                  <Sparkles className="w-5 h-5 mr-2" />
                  {t('home.hero.startCreating')}
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="text-lg px-8 py-4"
                >
                  <Play className="w-5 h-5 mr-2" />
                  {t('home.hero.watchDemo')}
                </Button>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <div className="relative z-10">
                <img
                  src="/images/hero-illustration.png"
                  alt="StoryWeaver 故事创作"
                  className="w-full h-auto rounded-2xl shadow-2xl"
                  onError={(e) => {
                    // 占位符图片
                    e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDYwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI2MDAiIGhlaWdodD0iNDAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNTAgMTUwSDM1MFYyNTBIMjUwVjE1MFoiIGZpbGw9IiNEMUQ1REIiLz4KPHN2ZyBpZD0iYm9vayIgd2lkdGg9IjEwMCIgaGVpZ2h0PSIxMDAiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4PSIyNTAiIHk9IjE1MCI+CjxwYXRoIGQ9Ik00IDEySDIwTTQgMTJWMTlDNCAyMC4xIDQuOSAyMSA2IDIxSDE4QzE5LjEgMjEgMjAgMjAuMSAyMCAxOVYxMk00IDEyVjVDNCAzLjkgNC45IDMgNiAzSDE4QzE5LjEgMyAyMCAzLjkgMjAgNVYxMiIgc3Ryb2tlPSIjNjM2NkYxIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4KPC9zdmc+';
                  }}
                />
              </div>
              {/* 装饰性元素 */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-primary-200 rounded-full opacity-20 animate-pulse" />
              <div className="absolute -bottom-4 -left-4 w-32 h-32 bg-secondary-200 rounded-full opacity-20 animate-pulse" />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <StatsCard
              title={t('home.stats.storiesCreated')}
              value="10,000+"
              change={{ value: 25, type: 'increase', period: t('home.stats.thisMonth') }}
              icon={<BookOpen className="w-6 h-6" />}
            />
            <StatsCard
              title={t('home.stats.activeUsers')}
              value="5,000+"
              change={{ value: 15, type: 'increase', period: t('home.stats.thisMonth') }}
              icon={<Users className="w-6 h-6" />}
            />
            <StatsCard
              title={t('home.stats.userRating')}
              value="4.9"
              icon={<Star className="w-6 h-6" />}
            />
            <StatsCard
              title={t('home.stats.awards')}
              value="12"
              icon={<Award className="w-6 h-6" />}
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('home.features.title')}
            </h2>
            <p className="text-xl text-gray-800 max-w-3xl mx-auto">
              {t('home.features.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <FeatureCard
                icon={<Sparkles className="w-6 h-6" />}
                title={t('home.features.aiCreation.title')}
                description={t('home.features.aiCreation.description')}
                action={
                  <Button variant="outline" size="sm">
                    {t('home.features.learnMore')}
                  </Button>
                }
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <FeatureCard
                icon={<Image className="w-6 h-6" />}
                title={t('home.features.illustrations.title')}
                description={t('home.features.illustrations.description')}
                action={
                  <Button variant="outline" size="sm">
                    {t('home.features.viewSamples')}
                  </Button>
                }
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 50 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <FeatureCard
                icon={<Volume2 className="w-6 h-6" />}
                title={t('home.features.narration.title')}
                description={t('home.features.narration.description')}
                action={
                  <Button variant="outline" size="sm">
                    {t('home.features.listenVoice')}
                  </Button>
                }
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* How it Works */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              {t('home.howItWorks.title')}
            </h2>
            <p className="text-xl text-gray-800">
              {t('home.howItWorks.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                step: '01',
                title: t('home.howItWorks.step1.title'),
                description: t('home.howItWorks.step1.description'),
                icon: <Heart className="w-8 h-8" />,
              },
              {
                step: '02',
                title: t('home.howItWorks.step2.title'),
                description: t('home.howItWorks.step2.description'),
                icon: <BookOpen className="w-8 h-8" />,
              },
              {
                step: '03',
                title: t('home.howItWorks.step3.title'),
                description: t('home.howItWorks.step3.description'),
                icon: <Zap className="w-8 h-8" />,
              },
            ].map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="relative mb-6">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <div className="text-primary-600">{item.icon}</div>
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-primary-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                    {item.step}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {item.title}
                </h3>
                <p className="text-gray-800">
                  {item.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary-500 to-secondary-500">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {t('home.cta.title')}
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              {t('home.cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                variant="secondary"
                onClick={handleGetStarted}
                className="text-lg px-8 py-4 bg-white text-primary-600 hover:bg-gray-50"
              >
                <Sparkles className="w-5 h-5 mr-2" />
                {t('home.cta.startNow')}
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="text-lg px-8 py-4 border-white text-white hover:bg-white hover:text-primary-600"
              >
                {t('home.cta.viewPricing')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </Layout>
  );
};

export default HomePage;
