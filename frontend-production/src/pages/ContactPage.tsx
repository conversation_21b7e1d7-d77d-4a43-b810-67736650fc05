import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useNotifications } from '@/stores/uiStore';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageCircle, 
  Send,
  CheckCircle,
  AlertCircle,
  HelpCircle
} from 'lucide-react';

const ContactPage: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { showSuccess, showError } = useNotifications();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // 这里应该调用API发送邮件
      // await contactService.sendMessage(formData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showSuccess('消息发送成功', '我们会在24小时内回复您');
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        type: 'general'
      });
    } catch (error) {
      showError('发送失败', '请稍后重试或通过其他方式联系我们');
    } finally {
      setIsSubmitting(false);
    }
  };

  const contactInfo = [
    {
      icon: Mail,
      title: '邮箱地址',
      content: '<EMAIL>',
      description: '我们会在24小时内回复您的邮件'
    },
    {
      icon: Phone,
      title: '客服热线',
      content: '************',
      description: '工作日 9:00-18:00'
    },
    {
      icon: MapPin,
      title: '公司地址',
      content: '北京市朝阳区创新大厦',
      description: '欢迎预约参观'
    },
    {
      icon: Clock,
      title: '服务时间',
      content: '周一至周五 9:00-18:00',
      description: '节假日可能延迟回复'
    }
  ];

  const faqItems = [
    {
      question: '如何创建我的第一个故事？',
      answer: '注册登录后，点击"创作故事"按钮，按照引导填写角色信息和故事主题即可。'
    },
    {
      question: '故事生成需要多长时间？',
      answer: '通常需要2-5分钟，包括文本生成、插图绘制和语音合成。'
    },
    {
      question: '可以修改已生成的故事吗？',
      answer: '目前暂不支持修改，但您可以重新生成或联系客服协助处理。'
    },
    {
      question: '如何订购实体书？',
      answer: '在故事详情页点击"定制实体书"，选择规格和数量即可下单。'
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12">
        <div className="container mx-auto px-4">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">联系我们</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              有任何问题或建议？我们很乐意为您提供帮助
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* 联系表单 */}
            <div className="lg:col-span-2">
              <Card className="p-8">
                <div className="flex items-center mb-6">
                  <MessageCircle className="w-6 h-6 text-primary-500 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-900">发送消息</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        姓名 *
                      </label>
                      <Input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        placeholder="请输入您的姓名"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱 *
                      </label>
                      <Input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="请输入您的邮箱"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      问题类型
                    </label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="general">一般咨询</option>
                      <option value="technical">技术问题</option>
                      <option value="billing">账单问题</option>
                      <option value="feature">功能建议</option>
                      <option value="bug">错误报告</option>
                      <option value="partnership">合作洽谈</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      主题 *
                    </label>
                    <Input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder="请简要描述您的问题"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      详细描述 *
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      rows={6}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="请详细描述您的问题或建议..."
                      required
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        发送中...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        发送消息
                      </>
                    )}
                  </Button>
                </form>
              </Card>
            </div>

            {/* 联系信息 */}
            <div className="space-y-6">
              {/* 联系方式 */}
              <Card className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">联系方式</h3>
                <div className="space-y-4">
                  {contactInfo.map((item, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0">
                        <item.icon className="w-5 h-5 text-primary-500 mt-1" />
                      </div>
                      <div className="ml-3">
                        <h4 className="font-medium text-gray-900">{item.title}</h4>
                        <p className="text-gray-700">{item.content}</p>
                        <p className="text-sm text-gray-500">{item.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 常见问题 */}
              <Card className="p-6">
                <div className="flex items-center mb-4">
                  <HelpCircle className="w-5 h-5 text-primary-500 mr-2" />
                  <h3 className="text-xl font-bold text-gray-900">常见问题</h3>
                </div>
                <div className="space-y-4">
                  {faqItems.map((item, index) => (
                    <div key={index} className="border-b border-gray-200 pb-3 last:border-b-0">
                      <h4 className="font-medium text-gray-900 mb-1">{item.question}</h4>
                      <p className="text-sm text-gray-600">{item.answer}</p>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 响应时间 */}
              <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50">
                <div className="flex items-center mb-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                  <h3 className="font-bold text-gray-900">响应承诺</h3>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 一般问题：24小时内回复</li>
                  <li>• 技术问题：12小时内回复</li>
                  <li>• 紧急问题：4小时内回复</li>
                  <li>• 工作日优先处理</li>
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ContactPage;