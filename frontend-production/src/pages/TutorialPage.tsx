import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { 
  Play, 
  User, 
  Palette, 
  BookOpen, 
  Download, 
  Share2,
  CheckCircle,
  ArrowRight,
  Video,
  FileText,
  Lightbulb,
  Star,
  Clock,
  Target
} from 'lucide-react';

const TutorialPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [activeTab, setActiveTab] = useState('getting-started');

  const tutorialSteps = [
    {
      id: 0,
      title: '注册登录',
      description: '创建您的StoryWeaver账户',
      icon: User,
      content: {
        overview: '首先需要创建一个StoryWeaver账户来开始您的故事创作之旅。',
        steps: [
          '点击页面右上角的"登录"按钮',
          '选择"Google登录"或"邮箱注册"',
          '填写必要的个人信息',
          '验证邮箱地址（如果使用邮箱注册）',
          '完成注册后自动登录到平台'
        ],
        tips: [
          '建议使用Google账户登录，更加便捷安全',
          '注册后会获得1个免费故事额度',
          '请使用真实邮箱以便接收重要通知'
        ],
        video: '/tutorials/step1-register.mp4'
      }
    },
    {
      id: 1,
      title: '角色设定',
      description: '创建故事的主角',
      icon: User,
      content: {
        overview: '设定故事主角的基本信息，这将决定故事的个性化程度。',
        steps: [
          '点击"创作故事"按钮进入创作页面',
          '输入主角的姓名（可以是孩子的真实姓名）',
          '选择主角的年龄（3-12岁）',
          '选择2-3个性格特征（勇敢、善良、聪明等）',
          '可选：上传主角的照片作为参考'
        ],
        tips: [
          '使用孩子的真实姓名会让故事更有代入感',
          '年龄设置会影响故事的复杂度和词汇难度',
          '性格特征会影响故事情节的发展方向'
        ],
        video: '/tutorials/step2-character.mp4'
      }
    },
    {
      id: 2,
      title: '主题选择',
      description: '选择故事的主题和场景',
      icon: BookOpen,
      content: {
        overview: '选择适合的故事主题和背景场景，打造独特的故事世界。',
        steps: [
          '从预设主题中选择（冒险、友谊、学习、家庭等）',
          '选择故事发生的场景（森林、海洋、城市、太空等）',
          '可选：添加自定义的故事元素',
          '预览主题和场景的组合效果',
          '确认选择并进入下一步'
        ],
        tips: [
          '不同主题适合不同年龄段的孩子',
          '可以根据孩子的兴趣爱好选择主题',
          '场景选择会影响插图的风格和内容'
        ],
        video: '/tutorials/step3-theme.mp4'
      }
    },
    {
      id: 3,
      title: '风格配置',
      description: '设置插图风格和语音类型',
      icon: Palette,
      content: {
        overview: '个性化设置故事的视觉和听觉效果，创造最佳的阅读体验。',
        steps: [
          '选择插图风格（卡通、水彩、素描、奇幻等）',
          '选择语音旁白类型（温柔女声、温暖男声、儿童友好等）',
          '预览不同风格的效果',
          '调整语音语速和音调（可选）',
          '确认所有设置'
        ],
        tips: [
          '卡通风格最受儿童喜爱',
          '可以根据故事主题选择匹配的插图风格',
          '语音类型会影响故事的情感表达'
        ],
        video: '/tutorials/step4-style.mp4'
      }
    },
    {
      id: 4,
      title: '生成故事',
      description: 'AI开始创作您的专属故事',
      icon: Star,
      content: {
        overview: 'AI将根据您的设置生成独一无二的故事，包括文字、插图和语音。',
        steps: [
          '点击"开始创作"按钮',
          '等待AI生成故事文本（约30-60秒）',
          '等待AI绘制插图（约1-2分钟）',
          '等待AI合成语音旁白（约1-2分钟）',
          '故事生成完成，可以开始阅读'
        ],
        tips: [
          '生成过程中请不要关闭页面',
          '可以观看生成进度和预览',
          '如果不满意可以重新生成'
        ],
        video: '/tutorials/step5-generate.mp4'
      }
    },
    {
      id: 5,
      title: '阅读分享',
      description: '享受故事并与他人分享',
      icon: Share2,
      content: {
        overview: '阅读完成的故事，并可以保存、分享或定制成实体书。',
        steps: [
          '在线阅读生成的故事',
          '播放AI生成的语音旁白',
          '下载故事的PDF版本',
          '分享故事链接给家人朋友',
          '可选：定制成精美的实体书'
        ],
        tips: [
          '可以收藏喜欢的故事',
          '支持离线下载阅读',
          '实体书是很好的纪念品和礼物'
        ],
        video: '/tutorials/step6-share.mp4'
      }
    }
  ];

  const quickTips = [
    {
      icon: Lightbulb,
      title: '创作技巧',
      tips: [
        '使用孩子熟悉的名字和环境',
        '选择符合年龄的主题和复杂度',
        '尝试不同的插图风格找到最喜欢的',
        '可以为不同场合创作不同类型的故事'
      ]
    },
    {
      icon: Target,
      title: '最佳实践',
      tips: [
        '在安静的环境中与孩子一起阅读',
        '鼓励孩子参与故事创作过程',
        '定期创作新故事保持新鲜感',
        '将故事作为睡前读物或教育工具'
      ]
    },
    {
      icon: Clock,
      title: '时间管理',
      tips: [
        '故事生成通常需要2-5分钟',
        '建议在网络稳定时进行创作',
        '可以同时创作多个故事',
        '实体书制作需要7-10个工作日'
      ]
    }
  ];

  const videoTutorials = [
    {
      title: '5分钟快速上手',
      duration: '5:23',
      description: '从注册到生成第一个故事的完整流程',
      thumbnail: '/thumbnails/quick-start.jpg',
      url: '/videos/quick-start.mp4'
    },
    {
      title: '高级功能详解',
      duration: '8:45',
      description: '深入了解各种自定义选项和高级功能',
      thumbnail: '/thumbnails/advanced-features.jpg',
      url: '/videos/advanced-features.mp4'
    },
    {
      title: '实体书定制指南',
      duration: '6:12',
      description: '如何将数字故事制作成精美的实体书',
      thumbnail: '/thumbnails/book-customization.jpg',
      url: '/videos/book-customization.mp4'
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 py-12">
        <div className="container mx-auto px-4">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">使用教程</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              跟随我们的详细教程，快速掌握StoryWeaver的使用方法
            </p>
          </div>

          {/* 标签页导航 */}
          <div className="flex justify-center mb-8">
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveTab('getting-started')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'getting-started'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                入门指南
              </button>
              <button
                onClick={() => setActiveTab('video-tutorials')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'video-tutorials'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                视频教程
              </button>
              <button
                onClick={() => setActiveTab('tips')}
                className={`px-6 py-2 rounded-md font-medium transition-colors ${
                  activeTab === 'tips'
                    ? 'bg-white text-primary-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                使用技巧
              </button>
            </div>
          </div>

          {/* 入门指南 */}
          {activeTab === 'getting-started' && (
            <div className="grid lg:grid-cols-4 gap-8">
              {/* 步骤导航 */}
              <div className="lg:col-span-1">
                <Card className="p-6 sticky top-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">创作步骤</h3>
                  <div className="space-y-2">
                    {tutorialSteps.map((step, index) => (
                      <button
                        key={step.id}
                        onClick={() => setActiveStep(index)}
                        className={`w-full flex items-center px-3 py-3 rounded-lg text-left transition-colors ${
                          activeStep === index
                            ? 'bg-primary-100 text-primary-700 border border-primary-200'
                            : 'hover:bg-gray-100 text-gray-700'
                        }`}
                      >
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 text-xs font-medium ${
                          activeStep === index
                            ? 'bg-primary-500 text-white'
                            : 'bg-gray-200 text-gray-600'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{step.title}</div>
                          <div className="text-xs text-gray-500">{step.description}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </Card>
              </div>

              {/* 步骤详情 */}
              <div className="lg:col-span-3">
                <Card className="p-8">
                  <div className="flex items-center mb-6">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                      {React.createElement(tutorialSteps[activeStep].icon, {
                        className: "w-6 h-6 text-primary-600"
                      })}
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">
                        步骤 {activeStep + 1}: {tutorialSteps[activeStep].title}
                      </h2>
                      <p className="text-gray-600">{tutorialSteps[activeStep].description}</p>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 mb-4">操作步骤</h3>
                      <p className="text-gray-600 mb-4">
                        {tutorialSteps[activeStep].content.overview}
                      </p>
                      <ol className="space-y-3">
                        {tutorialSteps[activeStep].content.steps.map((step, index) => (
                          <li key={index} className="flex items-start">
                            <div className="w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5 flex-shrink-0">
                              {index + 1}
                            </div>
                            <span className="text-gray-700">{step}</span>
                          </li>
                        ))}
                      </ol>
                    </div>

                    <div>
                      <h3 className="text-lg font-bold text-gray-900 mb-4">实用提示</h3>
                      <div className="space-y-3">
                        {tutorialSteps[activeStep].content.tips.map((tip, index) => (
                          <div key={index} className="flex items-start">
                            <Lightbulb className="w-5 h-5 text-yellow-500 mr-3 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{tip}</span>
                          </div>
                        ))}
                      </div>

                      {/* 视频预览 */}
                      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center mb-2">
                          <Video className="w-5 h-5 text-gray-600 mr-2" />
                          <span className="font-medium text-gray-900">视频演示</span>
                        </div>
                        <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                          <Play className="w-12 h-12 text-gray-400" />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 导航按钮 */}
                  <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
                    <Button
                      variant="outline"
                      onClick={() => setActiveStep(Math.max(0, activeStep - 1))}
                      disabled={activeStep === 0}
                    >
                      上一步
                    </Button>
                    <Button
                      onClick={() => setActiveStep(Math.min(tutorialSteps.length - 1, activeStep + 1))}
                      disabled={activeStep === tutorialSteps.length - 1}
                    >
                      下一步
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </Card>
              </div>
            </div>
          )}

          {/* 视频教程 */}
          {activeTab === 'video-tutorials' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {videoTutorials.map((video, index) => (
                <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video bg-gray-200 relative">
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Play className="w-12 h-12 text-white bg-black bg-opacity-50 rounded-full p-3" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                      {video.duration}
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-bold text-gray-900 mb-2">{video.title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{video.description}</p>
                    <Button size="sm" className="w-full">
                      <Play className="w-4 h-4 mr-2" />
                      观看视频
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* 使用技巧 */}
          {activeTab === 'tips' && (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {quickTips.map((tipGroup, index) => (
                <Card key={index} className="p-6">
                  <div className="flex items-center mb-4">
                    <tipGroup.icon className="w-6 h-6 text-primary-500 mr-3" />
                    <h3 className="text-lg font-bold text-gray-900">{tipGroup.title}</h3>
                  </div>
                  <ul className="space-y-3">
                    {tipGroup.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start">
                        <CheckCircle className="w-4 h-4 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700 text-sm">{tip}</span>
                      </li>
                    ))}
                  </ul>
                </Card>
              ))}
            </div>
          )}

          {/* 快速开始按钮 */}
          <div className="text-center mt-12">
            <Card className="p-8 bg-gradient-to-r from-primary-50 to-purple-50 inline-block">
              <h3 className="text-xl font-bold text-gray-900 mb-4">准备好开始创作了吗？</h3>
              <p className="text-gray-600 mb-6">
                现在就开始创作您的第一个专属故事吧！
              </p>
              <Button size="lg">
                <Star className="w-5 h-5 mr-2" />
                开始创作故事
              </Button>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TutorialPage;