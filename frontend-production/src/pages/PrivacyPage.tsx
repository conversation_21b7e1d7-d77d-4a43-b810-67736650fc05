import React from 'react';
import { motion } from 'framer-motion';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';

const PrivacyPage: React.FC = () => {
  return (
    <Layout>
      <div className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
              隐私政策
            </h1>
            
            <Card className="p-8">
              <div className="prose prose-lg max-w-none">
                <p className="text-gray-600 mb-6">
                  最后更新：2024年1月1日
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">1. 信息收集</h2>
                <p className="text-gray-700 mb-4">我们收集以下类型的信息：</p>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>账户信息：姓名、邮箱地址、登录凭据</li>
                  <li>故事创作信息：角色设定、主题选择、创作偏好</li>
                  <li>使用数据：访问日志、功能使用情况、设备信息</li>
                  <li>支付信息：通过第三方支付处理商处理，我们不存储完整的支付卡信息</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">2. 信息使用</h2>
                <p className="text-gray-700 mb-4">我们使用收集的信息用于：</p>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>提供和改进我们的服务</li>
                  <li>个性化用户体验</li>
                  <li>处理支付和账户管理</li>
                  <li>发送服务相关通知</li>
                  <li>分析服务使用情况以优化功能</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">3. 信息分享</h2>
                <p className="text-gray-700 mb-6">
                  我们不会出售、租赁或以其他方式向第三方披露您的个人信息，除非：
                  (1) 获得您的明确同意；(2) 法律要求；(3) 保护我们的权利和安全；(4) 与可信的服务提供商合作提供服务。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">4. 数据安全</h2>
                <p className="text-gray-700 mb-6">
                  我们采用行业标准的安全措施保护您的信息，包括加密传输、安全存储和访问控制。
                  但请注意，没有任何互联网传输或存储方法是100%安全的。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">5. 儿童隐私</h2>
                <p className="text-gray-700 mb-6">
                  我们的服务面向成年用户为儿童创作故事。我们不会故意收集13岁以下儿童的个人信息。
                  如果您发现我们无意中收集了儿童信息，请立即联系我们。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Cookie和追踪技术</h2>
                <p className="text-gray-700 mb-6">
                  我们使用Cookie和类似技术来改善用户体验、分析网站使用情况和提供个性化内容。
                  您可以通过浏览器设置控制Cookie的使用。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">7. 您的权利</h2>
                <p className="text-gray-700 mb-4">您有权：</p>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>访问和更新您的个人信息</li>
                  <li>删除您的账户和相关数据</li>
                  <li>导出您的数据</li>
                  <li>选择退出营销通讯</li>
                  <li>对数据处理提出异议</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">8. 政策更新</h2>
                <p className="text-gray-700 mb-6">
                  我们可能会不时更新此隐私政策。重大变更将通过邮件或平台通知您。
                  请定期查看此页面以了解最新的隐私实践。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">9. 联系我们</h2>
                <p className="text-gray-700">
                  如果您对此隐私政策有任何疑问或需要行使您的权利，请通过 <EMAIL> 联系我们，
                  或写信至：StoryWeaver 隐私团队，[公司地址]。
                </p>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default PrivacyPage;
