import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Search, ChevronDown, ChevronRight, MessageCircle, Mail, Phone } from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { cn } from '@/utils/cn';

const HelpPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

  const faqCategories = [
    {
      title: '开始使用',
      faqs: [
        {
          question: '如何创作我的第一个故事？',
          answer: '点击"创作故事"按钮，按照向导设置角色信息、选择主题和风格，然后等待AI为您生成专属故事。整个过程大约需要5-10分钟。',
        },
        {
          question: '什么是积分？如何获得积分？',
          answer: '积分是创作故事的消费单位。您可以通过购买积分包或订阅会员来获得积分。免费用户每月可获得50积分。',
        },
        {
          question: '创作一个故事需要多少积分？',
          answer: '创作一个完整故事（包含文本、插图和语音）通常需要30-50积分，具体取决于故事长度和复杂度。',
        },
      ],
    },
    {
      title: '故事创作',
      faqs: [
        {
          question: '可以自定义故事的哪些内容？',
          answer: '您可以设置主角的姓名、年龄、性格特点，选择故事主题、场景、叙述风格和朗读声音。AI会根据这些设置创作独特的故事。',
        },
        {
          question: '故事生成需要多长时间？',
          answer: '通常需要6-10分钟。文本生成需要1-2分钟，插图绘制需要3-5分钟，语音合成需要2-3分钟。',
        },
        {
          question: '如果对生成的故事不满意怎么办？',
          answer: '您可以重新生成故事的任何部分，或者调整设置后重新创作。我们致力于让每个故事都符合您的期望。',
        },
      ],
    },
    {
      title: '账户和订阅',
      faqs: [
        {
          question: '如何升级到付费计划？',
          answer: '访问定价页面，选择适合的计划，通过安全的支付系统完成购买。支持支付宝、微信支付等多种方式。',
        },
        {
          question: '可以随时取消订阅吗？',
          answer: '是的，您可以随时在设置页面取消订阅。取消后您仍可使用剩余积分，但不会自动续费。',
        },
        {
          question: '如何下载和分享故事？',
          answer: '完成的故事可以下载为PDF或音频文件，也可以生成分享链接发送给朋友和家人。',
        },
      ],
    },
    {
      title: '技术支持',
      faqs: [
        {
          question: '支持哪些设备和浏览器？',
          answer: '支持所有现代浏览器（Chrome、Safari、Firefox、Edge）和移动设备。推荐使用最新版本的浏览器以获得最佳体验。',
        },
        {
          question: '故事数据是否安全？',
          answer: '我们采用企业级安全措施保护您的数据，包括加密传输、安全存储和定期备份。您的故事只有您自己可以访问。',
        },
        {
          question: '遇到技术问题怎么办？',
          answer: '请通过客服聊天、邮件或电话联系我们。我们的技术支持团队会尽快为您解决问题。',
        },
      ],
    },
  ];

  const contactMethods = [
    {
      icon: MessageCircle,
      title: '在线客服',
      description: '实时聊天支持',
      action: '开始聊天',
      available: '周一至周日 9:00-21:00',
    },
    {
      icon: Mail,
      title: '邮件支持',
      description: '<EMAIL>',
      action: '发送邮件',
      available: '24小时内回复',
    },
    {
      icon: Phone,
      title: '电话支持',
      description: '************',
      action: '拨打电话',
      available: '工作日 9:00-18:00',
    },
  ];

  const filteredFaqs = faqCategories.map(category => ({
    ...category,
    faqs: category.faqs.filter(faq =>
      searchQuery === '' ||
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    ),
  })).filter(category => category.faqs.length > 0);

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <Layout>
      <div className="py-20 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              帮助中心
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              找到您需要的答案，或联系我们的支持团队
            </p>

            {/* Search */}
            <div className="max-w-md mx-auto">
              <Input
                leftIcon={<Search className="w-4 h-4" />}
                placeholder="搜索问题..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="text-center"
              />
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              常见问题
            </h2>

            <div className="space-y-6">
              {filteredFaqs.map((category, categoryIndex) => (
                <Card key={categoryIndex} className="overflow-hidden">
                  <div className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      {category.title}
                    </h3>
                    <div className="space-y-3">
                      {category.faqs.map((faq, faqIndex) => {
                        const globalIndex = categoryIndex * 100 + faqIndex;
                        const isExpanded = expandedFaq === globalIndex;
                        
                        return (
                          <div key={faqIndex} className="border border-gray-200 rounded-lg">
                            <button
                              onClick={() => toggleFaq(globalIndex)}
                              className="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 transition-colors"
                            >
                              <span className="font-medium text-gray-900">
                                {faq.question}
                              </span>
                              {isExpanded ? (
                                <ChevronDown className="w-5 h-5 text-gray-500" />
                              ) : (
                                <ChevronRight className="w-5 h-5 text-gray-500" />
                              )}
                            </button>
                            {isExpanded && (
                              <motion.div
                                initial={{ height: 0, opacity: 0 }}
                                animate={{ height: 'auto', opacity: 1 }}
                                exit={{ height: 0, opacity: 0 }}
                                transition={{ duration: 0.2 }}
                                className="px-4 pb-3"
                              >
                                <p className="text-gray-600 leading-relaxed">
                                  {faq.answer}
                                </p>
                              </motion.div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {filteredFaqs.length === 0 && searchQuery && (
              <Card className="p-8 text-center">
                <p className="text-gray-600">
                  没有找到匹配的问题。请尝试其他关键词或联系客服。
                </p>
              </Card>
            )}
          </motion.div>

          {/* Contact Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              联系我们
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {contactMethods.map((method, index) => {
                const Icon = method.icon;
                
                return (
                  <Card key={index} className="p-6 text-center hover:shadow-lg transition-shadow">
                    <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <Icon className="w-6 h-6 text-primary-600" />
                    </div>
                    <h3 className="font-semibold text-gray-900 mb-2">
                      {method.title}
                    </h3>
                    <p className="text-gray-600 mb-2">
                      {method.description}
                    </p>
                    <p className="text-sm text-gray-500 mb-4">
                      {method.available}
                    </p>
                    <Button variant="outline" className="w-full">
                      {method.action}
                    </Button>
                  </Card>
                );
              })}
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default HelpPage;
