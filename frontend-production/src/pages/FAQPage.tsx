import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { 
  ChevronDown, 
  ChevronUp, 
  Search, 
  HelpCircle, 
  Book, 
  CreditCard, 
  Settings, 
  Truck,
  MessageCircle,
  Star
} from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const FAQPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const categories = [
    { id: 'all', name: '全部问题', icon: HelpCircle },
    { id: 'getting-started', name: '新手入门', icon: Star },
    { id: 'story-creation', name: '故事创作', icon: Book },
    { id: 'payment', name: '支付相关', icon: CreditCard },
    { id: 'account', name: '账户设置', icon: Settings },
    { id: 'shipping', name: '配送服务', icon: Truck },
    { id: 'support', name: '技术支持', icon: MessageCircle }
  ];

  const faqData: FAQItem[] = [
    // 新手入门
    {
      id: '1',
      category: 'getting-started',
      question: '什么是StoryWeaver？',
      answer: 'StoryWeaver是一个AI驱动的个性化儿童故事创作平台。您只需提供主角信息和故事主题，我们的AI就能为您生成独一无二的故事，配上精美插图和生动旁白，还可以定制成实体书。'
    },
    {
      id: '2',
      category: 'getting-started',
      question: '如何开始使用StoryWeaver？',
      answer: '1. 注册并登录账户\n2. 点击"创作故事"按钮\n3. 填写角色信息（姓名、年龄、性格特征）\n4. 选择故事主题和场景\n5. 设置插图风格和语音类型\n6. 等待AI生成您的专属故事'
    },
    {
      id: '3',
      category: 'getting-started',
      question: '适合什么年龄段的孩子？',
      answer: 'StoryWeaver主要面向3-12岁的儿童。我们的AI会根据您设置的角色年龄，自动调整故事的复杂度、词汇难度和内容深度，确保故事适合对应年龄段的孩子理解和享受。'
    },

    // 故事创作
    {
      id: '4',
      category: 'story-creation',
      question: '故事生成需要多长时间？',
      answer: '通常需要2-5分钟完成整个故事的生成，包括：\n• 文本生成：30-60秒\n• 插图绘制：1-2分钟\n• 语音合成：1-2分钟\n具体时间可能因服务器负载而有所变化。'
    },
    {
      id: '5',
      category: 'story-creation',
      question: '可以自定义故事内容吗？',
      answer: '是的！您可以：\n• 设置主角的姓名、年龄和性格特征\n• 选择故事主题（冒险、友谊、学习等）\n• 选择故事场景（森林、海洋、城市等）\n• 选择插图风格（卡通、水彩、素描等）\n• 选择旁白声音类型'
    },
    {
      id: '6',
      category: 'story-creation',
      question: '生成的故事安全吗？',
      answer: '绝对安全！我们采用多重内容安全机制：\n• AI内容过滤确保积极正面\n• 专业审核团队人工检查\n• 严格遵循儿童内容安全标准\n• 避免暴力、恐怖等不当内容'
    },
    {
      id: '7',
      category: 'story-creation',
      question: '可以修改已生成的故事吗？',
      answer: '目前暂不支持直接编辑故事内容，但您可以：\n• 重新生成故事（调整参数）\n• 联系客服协助修改\n• 保存多个版本进行对比\n我们正在开发故事编辑功能，敬请期待。'
    },

    // 支付相关
    {
      id: '8',
      category: 'payment',
      question: '有哪些付费方案？',
      answer: '我们提供灵活的付费选项：\n• 免费体验：1个故事（仅数字版）\n• 积分包：¥68/5个故事\n• 月度订阅：¥98/月（无限故事）\n• 年度订阅：¥888/年（无限故事+优惠）\n• 实体书定制：¥199-399/本'
    },
    {
      id: '9',
      category: 'payment',
      question: '支持哪些支付方式？',
      answer: '我们支持多种安全的支付方式：\n• 微信支付\n• 支付宝\n• 银联卡支付\n• 信用卡支付\n• Apple Pay\n• Google Pay\n所有支付都通过SSL加密保护。'
    },
    {
      id: '10',
      category: 'payment',
      question: '可以退款吗？',
      answer: '我们提供灵活的退款政策：\n• 未使用的积分：7天内可申请退款\n• 订阅服务：购买后24小时内可取消\n• 实体书：收货后30天内可退换\n• 质量问题：无条件退款或重制\n具体请查看退款政策或联系客服。'
    },

    // 账户设置
    {
      id: '11',
      category: 'account',
      question: '如何修改个人信息？',
      answer: '登录后进入"个人中心"：\n1. 点击右上角头像\n2. 选择"个人资料"\n3. 修改姓名、邮箱等信息\n4. 点击"保存"确认修改\n注意：邮箱修改需要验证新邮箱。'
    },
    {
      id: '12',
      category: 'account',
      question: '忘记密码怎么办？',
      answer: '您可以通过以下方式重置密码：\n1. 在登录页点击"忘记密码"\n2. 输入注册邮箱\n3. 查收重置邮件\n4. 点击邮件中的链接\n5. 设置新密码\n如果没收到邮件，请检查垃圾邮件箱。'
    },

    // 配送服务
    {
      id: '13',
      category: 'shipping',
      question: '实体书多久能收到？',
      answer: '实体书制作和配送时间：\n• 制作时间：7-10个工作日\n• 配送时间：2-5个工作日\n• 总计：9-15个工作日\n• 加急服务：+¥50可缩短至7个工作日\n我们会及时更新订单状态。'
    },
    {
      id: '14',
      category: 'shipping',
      question: '配送范围和费用？',
      answer: '配送服务详情：\n• 覆盖全国（除港澳台）\n• 单本免运费（满¥199）\n• 偏远地区可能需要额外运费\n• 支持顺丰、圆通等快递\n• 提供物流跟踪服务'
    },

    // 技术支持
    {
      id: '15',
      category: 'support',
      question: '遇到技术问题怎么办？',
      answer: '如果遇到技术问题，请尝试：\n1. 刷新页面或重新登录\n2. 清除浏览器缓存\n3. 检查网络连接\n4. 更新浏览器版本\n5. 联系在线客服\n我们的技术团队会及时为您解决问题。'
    },
    {
      id: '16',
      category: 'support',
      question: '支持哪些设备和浏览器？',
      answer: '兼容性说明：\n• 设备：电脑、平板、手机\n• 浏览器：Chrome、Safari、Firefox、Edge\n• 系统：Windows、macOS、iOS、Android\n• 建议使用最新版本浏览器以获得最佳体验'
    }
  ];

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const filteredFAQs = faqData.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-12">
        <div className="container mx-auto px-4">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">常见问题</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              快速找到您需要的答案，让使用StoryWeaver更加轻松
            </p>
          </div>

          {/* 搜索框 */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <Input
                type="text"
                placeholder="搜索问题..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          <div className="grid lg:grid-cols-4 gap-8">
            {/* 分类导航 */}
            <div className="lg:col-span-1">
              <Card className="p-6 sticky top-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">问题分类</h3>
                <div className="space-y-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full flex items-center px-3 py-2 rounded-lg text-left transition-colors ${
                        selectedCategory === category.id
                          ? 'bg-primary-100 text-primary-700 border border-primary-200'
                          : 'hover:bg-gray-100 text-gray-700'
                      }`}
                    >
                      <category.icon className="w-4 h-4 mr-3" />
                      <span className="text-sm font-medium">{category.name}</span>
                    </button>
                  ))}
                </div>
              </Card>
            </div>

            {/* FAQ列表 */}
            <div className="lg:col-span-3">
              {filteredFAQs.length === 0 ? (
                <Card className="p-8 text-center">
                  <HelpCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">没有找到相关问题</h3>
                  <p className="text-gray-600 mb-4">
                    尝试调整搜索关键词或选择其他分类
                  </p>
                  <button className="text-primary-600 hover:text-primary-700 font-medium">
                    联系客服获取帮助
                  </button>
                </Card>
              ) : (
                <div className="space-y-4">
                  {filteredFAQs.map((item) => (
                    <Card key={item.id} className="overflow-hidden">
                      <button
                        onClick={() => toggleExpanded(item.id)}
                        className="w-full px-6 py-4 text-left hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-center justify-between">
                          <h3 className="text-lg font-medium text-gray-900 pr-4">
                            {item.question}
                          </h3>
                          {expandedItems.has(item.id) ? (
                            <ChevronUp className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          ) : (
                            <ChevronDown className="w-5 h-5 text-gray-500 flex-shrink-0" />
                          )}
                        </div>
                      </button>
                      
                      {expandedItems.has(item.id) && (
                        <div className="px-6 pb-4 border-t border-gray-100">
                          <div className="pt-4">
                            <div className="prose prose-sm max-w-none text-gray-700">
                              {item.answer.split('\n').map((line, index) => (
                                <p key={index} className={index > 0 ? 'mt-2' : ''}>
                                  {line}
                                </p>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}
                    </Card>
                  ))}
                </div>
              )}

              {/* 联系客服 */}
              <Card className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50">
                <div className="text-center">
                  <MessageCircle className="w-8 h-8 text-primary-500 mx-auto mb-3" />
                  <h3 className="text-lg font-bold text-gray-900 mb-2">还有其他问题？</h3>
                  <p className="text-gray-600 mb-4">
                    我们的客服团队随时为您提供帮助
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <button className="px-6 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors">
                      在线客服
                    </button>
                    <button className="px-6 py-2 border border-primary-500 text-primary-500 rounded-lg hover:bg-primary-50 transition-colors">
                      发送邮件
                    </button>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FAQPage;