import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { Check, Star, Zap, Crown, Gift } from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { PaymentModal } from '@/components/features/PaymentModal';
import { PlanDetailsModal } from '@/components/features/PlanDetailsModal';
import { PRICING_PLANS } from '@/config/pricing';
import { useAuthStore } from '@/stores/authStore';
import { cn } from '@/utils/cn';

const PricingPage: React.FC = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [paymentModalOpen, setPaymentModalOpen] = useState(false);
  const [planDetailsModalOpen, setPlanDetailsModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [selectedPlanData, setSelectedPlanData] = useState<any>(null);
  const { isAuthenticated } = useAuthStore();
  const { t } = useTranslation();

  const getPlans = () => PRICING_PLANS;

  const plans = getPlans();

  // 检查登录后是否需要自动打开支付模态框
  useEffect(() => {
    if (isAuthenticated) {
      const savedPlan = localStorage.getItem('selectedPlan');
      if (savedPlan) {
        localStorage.removeItem('selectedPlan');
        setSelectedPlan(savedPlan);
        setPaymentModalOpen(true);
      }
    }
  }, [isAuthenticated]);

  const handleSelectPlan = (planId: string) => {
    if (planId === 'free') {
      // 免费计划直接注册
      if (!isAuthenticated) {
        window.location.href = '/auth';
      }
      return;
    }

    // 付费计划需要先登录
    if (!isAuthenticated) {
      // 保存当前选择的计划，登录后自动打开支付模态框
      localStorage.setItem('selectedPlan', planId);
      window.location.href = '/auth?from=' + encodeURIComponent(window.location.pathname);
      return;
    }

    // 已登录用户先打开计划详情模态框
    const plan = plans.find(p => p.id === planId);
    setSelectedPlan(planId);
    setSelectedPlanData(plan);
    setPlanDetailsModalOpen(true);
  };

  const handleProceedToPayment = () => {
    setPlanDetailsModalOpen(false);
    setPaymentModalOpen(true);
  };

  const getYearlySavings = (plan: any) => {
    const monthlyTotal = plan.price.monthly * 12;
    const yearlyPrice = plan.price.yearly;
    const savings = monthlyTotal - yearlyPrice;
    const percentage = Math.round((savings / monthlyTotal) * 100);
    return { savings, percentage };
  };

  return (
    <Layout>
      <div className="py-20 bg-gradient-to-br from-primary-50 via-white to-secondary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              {t('pricing.title')}
              <span className="text-gradient block">{t('pricing.titleHighlight')}</span>
            </h1>
            <p className="text-xl text-gray-800 max-w-3xl mx-auto mb-8">
              {t('pricing.subtitle')}
            </p>

            {/* Billing Toggle */}
            <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setBillingCycle('monthly')}
                className={cn(
                  'px-4 py-2 rounded-md text-sm font-medium transition-colors',
                  billingCycle === 'monthly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-700 hover:text-gray-900'
                )}
              >
                {t('pricing.monthly')}
              </button>
              <button
                onClick={() => setBillingCycle('yearly')}
                className={cn(
                  'px-4 py-2 rounded-md text-sm font-medium transition-colors relative',
                  billingCycle === 'yearly'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-700 hover:text-gray-900'
                )}
              >
                {t('pricing.yearly')}
                <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                  {t('pricing.save')}
                </span>
              </button>
            </div>
          </motion.div>

          {/* Pricing Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {plans.map((plan, index) => {
              const Icon = plan.icon;
              const savings = getYearlySavings(plan);
              
              return (
                <motion.div
                  key={plan.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="relative"
                >
                  {plan.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                      <span className="bg-primary-500 text-white text-sm px-4 py-1 rounded-full">
                        {t('pricing.mostPopular')}
                      </span>
                    </div>
                  )}
                  
                  <Card className={cn(
                    'h-full p-6 relative overflow-hidden',
                    plan.popular && 'ring-2 ring-primary-500 shadow-xl scale-105'
                  )}>
                    {/* Background decoration */}
                    <div className={cn(
                      'absolute top-0 right-0 w-32 h-32 opacity-10 transform translate-x-8 -translate-y-8',
                      plan.color === 'gray' && 'bg-gray-400',
                      plan.color === 'blue' && 'bg-blue-400',
                      plan.color === 'primary' && 'bg-primary-400',
                      plan.color === 'purple' && 'bg-purple-400'
                    )} style={{ borderRadius: '50%' }} />
                    
                    <div className="relative">
                      {/* Icon */}
                      <div className={cn(
                        'w-12 h-12 rounded-lg flex items-center justify-center mb-4',
                        plan.color === 'gray' && 'bg-gray-100',
                        plan.color === 'blue' && 'bg-blue-100',
                        plan.color === 'primary' && 'bg-primary-100',
                        plan.color === 'purple' && 'bg-purple-100'
                      )}>
                        <Icon className={cn(
                          'w-6 h-6',
                          plan.color === 'gray' && 'text-gray-600',
                          plan.color === 'blue' && 'text-blue-600',
                          plan.color === 'primary' && 'text-primary-600',
                          plan.color === 'purple' && 'text-purple-600'
                        )} />
                      </div>

                      {/* Plan Info */}
                      <h3 className="text-xl font-bold text-gray-900 mb-2">
                        {plan.name}
                      </h3>
                      <p className="text-gray-800 text-sm mb-4">
                        {plan.description}
                      </p>

                      {/* Price */}
                      <div className="mb-6">
                        <div className="flex items-baseline">
                          <span className="text-3xl font-bold text-gray-900">
                            ${plan.price[billingCycle]}
                          </span>
                          {plan.price[billingCycle] > 0 && (
                            <span className="text-gray-700 ml-1">
                              {billingCycle === 'monthly' ? t('pricing.perMonth') : t('pricing.perYear')}
                            </span>
                          )}
                        </div>
                        {billingCycle === 'yearly' && plan.price.yearly > 0 && (
                          <p className="text-sm text-green-600 mt-1">
                            节省 ${savings.savings} ({savings.percentage}%)
                          </p>
                        )}
                        <p className="text-sm text-gray-700 mt-1">
                          {plan.credits[billingCycle]} {t('nav.credits')}
                          {billingCycle === 'monthly' ? t('pricing.perMonth') : t('pricing.perYear')}
                        </p>
                      </div>

                      {/* Features */}
                      <ul className="space-y-3 mb-6">
                        {plan.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <Check className="w-4 h-4 text-green-500 mt-0.5 mr-3 flex-shrink-0" />
                            <span className="text-sm text-gray-800">{feature}</span>
                          </li>
                        ))}
                      </ul>

                      {/* Limitations */}
                      {plan.limitations && (
                        <ul className="space-y-2 mb-6">
                          {plan.limitations.map((limitation, limitIndex) => (
                            <li key={limitIndex} className="flex items-start">
                              <span className="w-4 h-4 text-gray-400 mt-0.5 mr-3 flex-shrink-0">×</span>
                              <span className="text-sm text-gray-700">{limitation}</span>
                            </li>
                          ))}
                        </ul>
                      )}

                      {/* CTA Button */}
                      <Button
                        onClick={() => handleSelectPlan(plan.id)}
                        className={cn(
                          'w-full',
                          plan.popular && 'bg-primary-500 hover:bg-primary-600'
                        )}
                        variant={plan.popular ? 'primary' : 'outline'}
                      >
                        {plan.id === 'free' ? t('pricing.freeStart') : t('pricing.selectPlan')}
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* FAQ Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="mt-20"
          >
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
              {t('pricing.faq.title')}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              {(t('pricing.faq.questions', { returnObjects: true }) as Array<{question: string, answer: string}>).map((faq, index) => (
                <Card key={index} className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-2">
                    {faq.question}
                  </h3>
                  <p className="text-gray-800 text-sm">
                    {faq.answer}
                  </p>
                </Card>
              ))}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Plan Details Modal */}
      <PlanDetailsModal
        isOpen={planDetailsModalOpen}
        onClose={() => setPlanDetailsModalOpen(false)}
        plan={selectedPlanData}
        billingCycle={billingCycle}
        onProceedToPayment={handleProceedToPayment}
      />

      {/* Payment Modal */}
      <PaymentModal
        isOpen={paymentModalOpen}
        onClose={() => setPaymentModalOpen(false)}
        type="subscription"
        initialSelectedPlan={selectedPlan}
        onSuccess={() => {
          setPaymentModalOpen(false);
          // Handle success
        }}
      />
    </Layout>
  );
};

export default PricingPage;
