import React from 'react';
import { motion } from 'framer-motion';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';

const TermsPage: React.FC = () => {
  return (
    <Layout>
      <div className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-bold text-gray-900 mb-8 text-center">
              服务条款
            </h1>
            
            <Card className="p-8">
              <div className="prose prose-lg max-w-none">
                <p className="text-gray-600 mb-6">
                  最后更新：2024年1月1日
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">1. 服务说明</h2>
                <p className="text-gray-700 mb-6">
                  StoryWeaver 是一个AI驱动的个性化儿童故事创作平台。我们为用户提供基于人工智能技术的故事创作、插图生成和语音合成服务。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">2. 用户责任</h2>
                <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
                  <li>您必须年满18周岁或在监护人同意下使用本服务</li>
                  <li>您有责任保护账户安全，不得与他人共享登录信息</li>
                  <li>您承诺提供真实、准确的个人信息</li>
                  <li>您不得使用本服务进行任何违法或有害活动</li>
                </ul>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">3. 知识产权</h2>
                <p className="text-gray-700 mb-6">
                  您通过本平台创作的故事内容归您所有。我们保留平台技术、算法和服务的知识产权。未经授权，不得复制、修改或分发我们的技术内容。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">4. 付费服务</h2>
                <p className="text-gray-700 mb-6">
                  我们提供免费和付费服务。付费服务的具体条款、价格和退款政策请参考定价页面。所有费用均不可退还，除非法律另有规定。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">5. 服务限制</h2>
                <p className="text-gray-700 mb-6">
                  我们保留随时修改、暂停或终止服务的权利。我们不保证服务的持续可用性，也不对因服务中断造成的损失承担责任。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">6. 免责声明</h2>
                <p className="text-gray-700 mb-6">
                  本服务按"现状"提供，我们不对服务的准确性、完整性或适用性做出任何保证。用户使用本服务的风险由用户自行承担。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">7. 条款变更</h2>
                <p className="text-gray-700 mb-6">
                  我们可能会不时更新这些条款。重大变更将通过邮件或平台通知您。继续使用服务即表示您接受更新后的条款。
                </p>

                <h2 className="text-2xl font-bold text-gray-900 mb-4">8. 联系我们</h2>
                <p className="text-gray-700">
                  如果您对这些条款有任何疑问，请通过 <EMAIL> 联系我们。
                </p>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default TermsPage;
