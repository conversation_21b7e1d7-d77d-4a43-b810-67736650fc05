import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, ArrowRight, Home, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNotifications } from '@/stores/uiStore';
import { useAuthStore } from '@/stores/authStore';
import { paymentService } from '@/services/payments';

const PaymentSuccessPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showSuccess, showError } = useNotifications();
  const { user } = useAuthStore();
  const [isProcessing, setIsProcessing] = useState(true);
  const [paymentDetails, setPaymentDetails] = useState<{
    amount?: number;
    credits?: number;
    sessionId?: string;
    type?: string;
    subscription?: {
      plan?: string;
      status?: string;
    };
  }>({});

  useEffect(() => {
    const sessionId = searchParams.get('session_id');
    
    if (!sessionId) {
      showError('支付验证失败', '缺少支付会话ID');
      navigate('/pricing');
      return;
    }

    // 验证支付并获取详情
    const verifyPayment = async () => {
      try {
        setIsProcessing(true);

        // 调用后端API来验证支付状态并获取详情
        const result = await paymentService.verifyCheckoutSession(sessionId);

        setPaymentDetails({
          sessionId,
          amount: result.amount || 0,
          credits: result.credits || 0,
          type: result.type || 'credits',
          subscription: result.subscription,
        });

        // 刷新用户信息以获取最新的积分余额
        const { refreshUser } = useAuthStore.getState();
        if (refreshUser) {
          try {
            console.log('🔄 Refreshing user data to get updated credits...');
            await refreshUser();
            console.log('✅ User data refreshed successfully');
          } catch (error) {
            console.error('❌ Failed to refresh user data:', error);
            // 即使刷新失败，我们也继续处理，不要中断流程
          }
        }

        // 根据支付类型显示不同的成功消息
        if (result.type === 'subscription') {
          showSuccess('订阅成功', `您已成功订阅${result.subscription?.plan || '高级'}会员`);
        } else {
          showSuccess('支付成功', `您的${result.credits || 0}积分已成功添加到账户`);
        }
        
      } catch (error) {
        console.error('Payment verification failed:', error);
        showError('支付验证失败', '请联系客服处理');
        navigate('/pricing');
      } finally {
        setIsProcessing(false);
      }
    };

    verifyPayment();
  }, [searchParams, navigate, showSuccess, showError]);

  if (isProcessing) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <LoadingSpinner size="lg" className="mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              正在验证支付...
            </h2>
            <p className="text-gray-600">
              请稍候，我们正在确认您的支付状态
            </p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="p-8 max-w-md w-full mx-4">
        <div className="text-center">
          {/* 成功图标 */}
          <div className="mx-auto flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-6">
            <CheckCircle className="w-8 h-8 text-green-600" />
          </div>

          {/* 标题 */}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            支付成功！
          </h1>
          
          <p className="text-gray-600 mb-6">
            {paymentDetails.type === 'subscription' 
              ? '感谢您的订阅，您现在可以无限创作故事了'
              : '感谢您的购买，您的积分已成功添加到账户'
            }
          </p>

          {/* 支付详情 */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center mb-3">
              <CreditCard className="w-5 h-5 text-green-600 mr-2" />
              <span className="text-sm font-medium text-green-800">支付详情</span>
            </div>
            
            <div className="space-y-2 text-sm">
              {paymentDetails.amount && (
                <div className="flex justify-between">
                  <span className="text-green-700">支付金额:</span>
                  <span className="font-medium text-green-800">
                    ${paymentDetails.amount}
                  </span>
                </div>
              )}
              
              {paymentDetails.type === 'subscription' ? (
                <div className="flex justify-between">
                  <span className="text-green-700">订阅计划:</span>
                  <span className="font-medium text-green-800">
                    {paymentDetails.subscription?.plan === 'pro_monthly' ? '专业会员' : 
                     paymentDetails.subscription?.plan === 'unlimited_monthly' ? '无限会员' : 
                     paymentDetails.subscription?.plan === 'basic_monthly' ? '基础会员' : '高级会员'}
                  </span>
                </div>
              ) : paymentDetails.credits && (
                <div className="flex justify-between">
                  <span className="text-green-700">获得积分:</span>
                  <span className="font-medium text-green-800">
                    {paymentDetails.credits} 个故事
                  </span>
                </div>
              )}
              
              {paymentDetails.type === 'subscription' ? (
                <div className="flex justify-between pt-2 border-t border-green-300">
                  <span className="text-green-700">创作额度:</span>
                  <span className="font-medium text-green-800">
                    无限制
                  </span>
                </div>
              ) : user?.credits && (
                <div className="flex justify-between pt-2 border-t border-green-300">
                  <span className="text-green-700">当前余额:</span>
                  <span className="font-medium text-green-800">
                    {user.credits} 个故事
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button
              onClick={() => navigate('/create')}
              className="w-full"
              size="lg"
            >
              开始创作故事
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate('/')}
              className="w-full"
            >
              <Home className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </div>

          {/* 会话ID（用于客服） */}
          {paymentDetails.sessionId && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              <p className="text-xs text-gray-500">
                支付ID: {paymentDetails.sessionId.substring(0, 20)}...
              </p>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default PaymentSuccessPage;
