import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Home, ArrowLeft, Search, BookOpen } from 'lucide-react';
import { Layout } from '@/components/layout/Layout';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

const NotFoundPage: React.FC = () => {
  const navigate = useNavigate();

  const goBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate('/');
    }
  };

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 px-4">
        <div className="max-w-md w-full text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* 404 Illustration */}
            <div className="mb-8">
              <div className="relative">
                <div className="text-8xl font-bold text-primary-200 mb-4">404</div>
                <motion.div
                  animate={{ 
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  }}
                  transition={{ 
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                >
                  <BookOpen className="w-16 h-16 text-primary-400" />
                </motion.div>
              </div>
            </div>

            <Card className="p-8 shadow-lg">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">
                哎呀！页面走丢了
              </h1>
              <p className="text-gray-600 mb-8">
                您访问的页面可能已经移动、删除或者从未存在过。
                不过别担心，我们来帮您找到正确的方向！
              </p>

              {/* Action Buttons */}
              <div className="space-y-4">
                <Button
                  onClick={goBack}
                  className="w-full"
                  size="lg"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  返回上一页
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => navigate('/')}
                  className="w-full"
                  size="lg"
                >
                  <Home className="w-4 h-4 mr-2" />
                  回到首页
                </Button>
              </div>
            </Card>

            {/* Quick Links */}
            <div className="mt-8">
              <p className="text-sm text-gray-500 mb-4">或者您可以访问：</p>
              <div className="flex flex-wrap justify-center gap-4">
                <Link
                  to="/create"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium underline"
                >
                  创作故事
                </Link>
                <Link
                  to="/my-stories"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium underline"
                >
                  我的故事
                </Link>
                <Link
                  to="/pricing"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium underline"
                >
                  定价方案
                </Link>
                <Link
                  to="/help"
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium underline"
                >
                  帮助中心
                </Link>
              </div>
            </div>

            {/* Fun Message */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1, duration: 0.5 }}
              className="mt-8 p-4 bg-primary-50 rounded-lg border border-primary-200"
            >
              <p className="text-sm text-primary-700">
                💡 小贴士：也许这是开始创作新故事的好时机？
              </p>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default NotFoundPage;
