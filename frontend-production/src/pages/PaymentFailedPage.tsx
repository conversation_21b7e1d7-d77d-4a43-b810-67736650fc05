import React from 'react';
import { useNavigate } from 'react-router-dom';
import { XCircle, ArrowLeft, CreditCard, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

const PaymentFailedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="p-8 max-w-md w-full mx-4">
        <div className="text-center">
          {/* 失败图标 */}
          <div className="mx-auto flex items-center justify-center w-16 h-16 bg-red-100 rounded-full mb-6">
            <XCircle className="w-8 h-8 text-red-600" />
          </div>

          {/* 标题 */}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            支付未完成
          </h1>
          
          <p className="text-gray-600 mb-6">
            您的支付未能成功完成，请重试或联系客服
          </p>

          {/* 常见原因 */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-center mb-3">
              <HelpCircle className="w-5 h-5 text-yellow-600 mr-2" />
              <span className="text-sm font-medium text-yellow-800">可能的原因</span>
            </div>
            
            <div className="text-left space-y-2 text-sm text-yellow-700">
              <div className="flex items-start">
                <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>银行卡余额不足或被拒绝</span>
              </div>
              <div className="flex items-start">
                <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>网络连接中断</span>
              </div>
              <div className="flex items-start">
                <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>支付信息输入错误</span>
              </div>
              <div className="flex items-start">
                <span className="w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>主动取消了支付</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button
              onClick={() => navigate('/pricing')}
              className="w-full"
              size="lg"
            >
              <CreditCard className="w-4 h-4 mr-2" />
              重新购买
            </Button>
            
            <Button
              variant="outline"
              onClick={() => navigate(-1)}
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回上一页
            </Button>
          </div>

          {/* 客服信息 */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-600 mb-2">
              遇到问题？联系我们的客服团队
            </p>
            <div className="space-y-1 text-xs text-gray-500">
              <p>邮箱: <EMAIL></p>
              <p>工作时间: 周一至周五 9:00-18:00</p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default PaymentFailedPage;
