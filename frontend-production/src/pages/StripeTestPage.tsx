import React, { useState, useEffect } from 'react';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Layout } from '@/components/layout/Layout';
import { loadStripe } from '@/services/payments';
import { shouldUseRealStripe, isDebugMode, debugLog } from '@/utils/debug';
import { AlertCircle, CheckCircle, CreditCard } from 'lucide-react';

const StripeTestPage: React.FC = () => {
  const [stripe, setStripe] = useState<any>(null);
  const [elements, setElements] = useState<any>(null);
  const [cardElement, setCardElement] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [paymentResult, setPaymentResult] = useState<any>(null);

  useEffect(() => {
    if (shouldUseRealStripe()) {
      loadStripe().then((stripeInstance) => {
        setStripe(stripeInstance);
        if (stripeInstance) {
          const elementsInstance = stripeInstance.elements();
          setElements(elementsInstance);
          
          const card = elementsInstance.create('card', {
            style: {
              base: {
                fontSize: '16px',
                color: '#424770',
                '::placeholder': {
                  color: '#aab7c4',
                },
              },
              invalid: {
                color: '#9e2146',
              },
            },
          });
          
          card.on('change', ({ error }) => {
            setErrorMessage(error ? error.message : '');
          });
          
          setCardElement(card);
          
          // Mount the card element
          setTimeout(() => {
            const cardContainer = document.getElementById('stripe-card-element');
            if (cardContainer && !cardContainer.hasChildNodes()) {
              card.mount('#stripe-card-element');
            }
          }, 100);
        }
      }).catch((error) => {
        console.error('Failed to load Stripe:', error);
        setErrorMessage('Failed to load Stripe');
      });
    }
  }, []);

  const handleTestPayment = async () => {
    if (!stripe || !cardElement) {
      setErrorMessage('Stripe not ready');
      return;
    }

    setIsLoading(true);
    setPaymentStatus('processing');
    setErrorMessage('');

    try {
      // Create a test payment intent (mock)
      const testPaymentIntent = {
        id: 'pi_test_' + Math.random().toString(36).substr(2, 9),
        client_secret: 'pi_test_secret_' + Math.random().toString(36).substr(2, 9),
        amount: 1000, // $10.00 in cents
        currency: 'usd',
      };

      debugLog.info('Testing Stripe payment with:', testPaymentIntent);

      // Confirm the payment with Stripe
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        testPaymentIntent.client_secret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: 'Test User',
              email: '<EMAIL>',
            },
          },
        }
      );

      if (error) {
        throw new Error(error.message);
      }

      if (paymentIntent?.status === 'succeeded') {
        setPaymentStatus('success');
        setPaymentResult(paymentIntent);
        debugLog.info('Payment succeeded:', paymentIntent);
      } else {
        throw new Error('Payment not completed');
      }

    } catch (error) {
      setPaymentStatus('error');
      setErrorMessage(error instanceof Error ? error.message : 'Payment failed');
      debugLog.error('Payment failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const resetTest = () => {
    setPaymentStatus('idle');
    setErrorMessage('');
    setPaymentResult(null);
  };

  if (!isDebugMode()) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <Card className="max-w-md mx-auto p-6 text-center">
            <AlertCircle className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
            <h2 className="text-lg font-semibold mb-2">仅限调试模式</h2>
            <p className="text-gray-600">此页面仅在调试模式下可用</p>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              Stripe 支付测试
            </h1>
            <p className="text-gray-600">
              测试真实的 Stripe 支付集成
            </p>
          </div>

          <div className="grid gap-6">
            {/* 配置状态 */}
            <Card className="p-6">
              <h2 className="text-lg font-semibold mb-4">配置状态</h2>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span>调试模式</span>
                  <span className={`px-2 py-1 rounded text-xs ${isDebugMode() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {isDebugMode() ? '已启用' : '未启用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>真实 Stripe</span>
                  <span className={`px-2 py-1 rounded text-xs ${shouldUseRealStripe() ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {shouldUseRealStripe() ? '已启用' : '未启用'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Stripe 加载</span>
                  <span className={`px-2 py-1 rounded text-xs ${stripe ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                    {stripe ? '已加载' : '加载中...'}
                  </span>
                </div>
              </div>
            </Card>

            {/* 支付测试 */}
            {shouldUseRealStripe() && (
              <Card className="p-6">
                <h2 className="text-lg font-semibold mb-4 flex items-center">
                  <CreditCard className="w-5 h-5 mr-2" />
                  支付测试
                </h2>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      测试卡片信息
                    </label>
                    <div 
                      id="stripe-card-element"
                      className="p-3 border border-gray-300 rounded-lg bg-white"
                      style={{ minHeight: '40px' }}
                    />
                  </div>

                  {errorMessage && (
                    <div className="text-sm text-red-600 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {errorMessage}
                    </div>
                  )}

                  <div className="bg-blue-50 border border-blue-200 rounded p-3">
                    <div className="text-xs text-blue-700">
                      <p className="font-medium mb-1">测试卡号</p>
                      <p>成功: 4242 4242 4242 4242</p>
                      <p>失败: 4000 0000 0000 0002</p>
                      <p>有效期: 任意未来日期，CVC: 任意3位数字</p>
                    </div>
                  </div>

                  <div className="flex space-x-3">
                    <Button
                      onClick={handleTestPayment}
                      loading={isLoading}
                      disabled={!stripe || !cardElement || paymentStatus === 'processing'}
                      className="flex-1"
                    >
                      {paymentStatus === 'processing' ? '处理中...' : '测试支付 $10.00'}
                    </Button>
                    
                    {paymentStatus !== 'idle' && (
                      <Button
                        variant="outline"
                        onClick={resetTest}
                        disabled={isLoading}
                      >
                        重置
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            )}

            {/* 支付结果 */}
            {paymentStatus === 'success' && paymentResult && (
              <Card className="p-6 border-green-200 bg-green-50">
                <div className="flex items-center mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600 mr-2" />
                  <h3 className="text-lg font-semibold text-green-800">支付成功！</h3>
                </div>
                <div className="text-sm text-green-700 space-y-1">
                  <p><strong>Payment ID:</strong> {paymentResult.id}</p>
                  <p><strong>Amount:</strong> ${(paymentResult.amount / 100).toFixed(2)}</p>
                  <p><strong>Status:</strong> {paymentResult.status}</p>
                  <p><strong>Created:</strong> {new Date(paymentResult.created * 1000).toLocaleString()}</p>
                </div>
              </Card>
            )}

            {paymentStatus === 'error' && (
              <Card className="p-6 border-red-200 bg-red-50">
                <div className="flex items-center mb-2">
                  <AlertCircle className="w-6 h-6 text-red-600 mr-2" />
                  <h3 className="text-lg font-semibold text-red-800">支付失败</h3>
                </div>
                <p className="text-sm text-red-700">{errorMessage}</p>
              </Card>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default StripeTestPage;
