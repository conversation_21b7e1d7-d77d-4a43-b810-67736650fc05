import React from 'react';
import { motion } from 'framer-motion';
import { DashboardLayout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuthStore } from '@/stores/authStore';

const SettingsPage: React.FC = () => {
  const { logout } = useAuthStore();

  const handleLogout = () => {
    if (window.confirm('确定要退出登录吗？')) {
      logout();
    }
  };

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        <div>
          <h1 className="text-2xl font-bold text-gray-900">设置</h1>
          <p className="text-gray-600">管理您的账户设置和偏好</p>
        </div>

        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">通知设置</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">邮件通知</h3>
                <p className="text-sm text-gray-600">接收故事创作完成和重要更新通知</p>
              </div>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">推广信息</h3>
                <p className="text-sm text-gray-600">接收新功能和优惠活动信息</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">隐私设置</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">故事公开</h3>
                <p className="text-sm text-gray-600">允许其他用户查看您的公开故事</p>
              </div>
              <input type="checkbox" className="rounded" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium text-gray-900">数据分析</h3>
                <p className="text-sm text-gray-600">帮助我们改进服务质量</p>
              </div>
              <input type="checkbox" className="rounded" defaultChecked />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">账户操作</h2>
          <div className="space-y-4">
            <Button variant="outline" className="w-full">
              导出我的数据
            </Button>
            <Button 
              variant="destructive" 
              className="w-full"
              onClick={handleLogout}
            >
              退出登录
            </Button>
            <Button variant="outline" className="w-full text-red-600 border-red-300 hover:bg-red-50">
              删除账户
            </Button>
          </div>
        </Card>
      </motion.div>
    </DashboardLayout>
  );
};

export default SettingsPage;
