import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import { useAuthStore } from '@/stores/authStore';
import { useNotifications } from '@/stores/uiStore';
import { AuthLayout } from '@/components/layout/Layout';

interface CallbackState {
  status: 'loading' | 'success' | 'error';
  message: string;
  details?: string;
}

const AuthCallbackPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuthStore();
  const { showSuccess, showError } = useNotifications();
  const [callbackState, setCallbackState] = useState<CallbackState>({
    status: 'loading',
    message: '正在处理登录...'
  });

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        // 获取URL参数
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');

        // 检查是否有错误
        if (error) {
          setCallbackState({
            status: 'error',
            message: '登录失败',
            details: errorDescription || error
          });
          showError('登录失败', errorDescription || error);
          setTimeout(() => navigate('/auth'), 3000);
          return;
        }

        // 检查是否有授权码
        if (!code) {
          setCallbackState({
            status: 'error',
            message: '缺少授权码',
            details: '未收到Google OAuth授权码'
          });
          showError('登录失败', '未收到授权码，请重试');
          setTimeout(() => navigate('/auth'), 3000);
          return;
        }

        // 验证state参数（防止CSRF攻击）
        const storedState = sessionStorage.getItem('oauth_state');
        if (state && storedState && state !== storedState) {
          setCallbackState({
            status: 'error',
            message: '安全验证失败',
            details: 'State参数不匹配'
          });
          showError('登录失败', '安全验证失败，请重试');
          setTimeout(() => navigate('/auth'), 3000);
          return;
        }

        // 清除存储的state
        sessionStorage.removeItem('oauth_state');

        setCallbackState({
          status: 'loading',
          message: '正在验证授权码...'
        });

        // 使用授权码登录
        await login({
          code: code,
          redirectUri: window.location.origin + '/auth/callback'
        });

        setCallbackState({
          status: 'success',
          message: '登录成功！'
        });

        showSuccess('登录成功', '欢迎回到 StoryWeaver！');

        // 获取重定向目标
        const redirectTo = sessionStorage.getItem('auth_redirect') || '/my-stories';
        sessionStorage.removeItem('auth_redirect');

        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          navigate(redirectTo, { replace: true });
        }, 1500);

      } catch (error) {
        console.error('OAuth callback error:', error);
        
        setCallbackState({
          status: 'error',
          message: '登录处理失败',
          details: error instanceof Error ? error.message : '未知错误'
        });

        showError('登录失败', '处理登录信息时发生错误，请重试');
        setTimeout(() => navigate('/auth'), 3000);
      }
    };

    handleOAuthCallback();
  }, [searchParams, login, navigate, showSuccess, showError]);

  const getIcon = () => {
    switch (callbackState.status) {
      case 'loading':
        return <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="w-12 h-12 text-green-500" />;
      case 'error':
        return <XCircle className="w-12 h-12 text-red-500" />;
      default:
        return <AlertCircle className="w-12 h-12 text-yellow-500" />;
    }
  };

  const getStatusColor = () => {
    switch (callbackState.status) {
      case 'loading':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <AuthLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md mx-auto"
      >
        <div className="bg-white rounded-2xl shadow-xl p-8 text-center">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="flex justify-center mb-6"
          >
            {getIcon()}
          </motion.div>

          <motion.h1
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
            className={`text-2xl font-bold mb-4 ${getStatusColor()}`}
          >
            {callbackState.message}
          </motion.h1>

          {callbackState.details && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
              className="text-gray-600 mb-6"
            >
              {callbackState.details}
            </motion.p>
          )}

          {callbackState.status === 'loading' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-sm text-gray-500"
            >
              请稍候，正在处理您的登录信息...
            </motion.div>
          )}

          {callbackState.status === 'error' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="space-y-4"
            >
              <p className="text-sm text-gray-500">
                将在3秒后自动返回登录页面
              </p>
              <button
                onClick={() => navigate('/auth')}
                className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                立即返回登录
              </button>
            </motion.div>
          )}

          {callbackState.status === 'success' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="text-sm text-gray-500"
            >
              正在跳转到您的故事库...
            </motion.div>
          )}
        </div>
      </motion.div>
    </AuthLayout>
  );
};

export default AuthCallbackPage;
