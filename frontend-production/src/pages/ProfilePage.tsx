import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Mail,
  Calendar,
  CreditCard,
  Award,
  BookOpen,
  Clock,
  Edit3,
  Camera,
  Shield,
  Bell,
  Download,
  Trash2
} from 'lucide-react';
import { DashboardLayout } from '@/components/layout/Layout';
import { Card, StatsCard } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Modal } from '@/components/ui/Modal';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { QuickBuyCreditsButton } from '@/components/features/PaymentModal';
import { SubscriptionCard } from '@/components/features';
import { useAuthStore } from '@/stores/authStore';
import { useStoryStore } from '@/stores/storyStore';
import { useNotifications } from '@/stores/uiStore';
import { userService } from '@/services/users';
import type { UserProfile, UserStats } from '@/types';

const ProfilePage: React.FC = () => {
  const { user, updateUser } = useAuthStore();
  const { stories } = useStoryStore();
  const { showSuccess, showError } = useNotifications();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [userStats, setUserStats] = useState<any | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [editForm, setEditForm] = useState({
    name: user?.name || '',
    email: user?.email || '',
    avatar: user?.avatar || '',
  });

  // Load user stats
  useEffect(() => {
    const loadUserStats = async () => {
      if (user) {
        try {
          const stats = await userService.getStats();
          setUserStats(stats);
        } catch (error) {
          console.error('Failed to load user stats:', error);
        }
      }
    };

    loadUserStats();
  }, [user]);

  // Handle profile update
  const handleUpdateProfile = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const updatedUser = await userService.updateProfile({
        name: editForm.name,
        avatar: editForm.avatar,
      });

      updateUser(updatedUser);
      setIsEditing(false);
      showSuccess('资料更新成功', '您的个人信息已更新');
    } catch (error) {
      showError('更新失败', '请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle avatar upload
  const handleAvatarUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // TODO: 实现头像上传功能
      const reader = new FileReader();
      reader.onload = (e) => {
        setEditForm(prev => ({
          ...prev,
          avatar: e.target?.result as string
        }));
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle data export
  const handleExportData = async () => {
    setIsExporting(true);
    try {
      // 调用数据导出API
      const response = await fetch('/api/users/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth-storage') ? JSON.parse(localStorage.getItem('auth-storage')!).state?.tokens?.accessToken : ''}`
        }
      });

      if (!response.ok) {
        throw new Error('导出请求失败');
      }

      const result = await response.json();

      if (result.success) {
        // 创建下载链接
        const dataStr = JSON.stringify(result.data, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);

        // 创建下载链接并触发下载
        const link = document.createElement('a');
        link.href = url;
        link.download = `storyweaver-data-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showSuccess('数据导出成功', '您的数据已下载到本地');
      } else {
        throw new Error(result.error || '导出失败');
      }
    } catch (error: any) {
      console.error('数据导出失败:', error);
      showError('导出失败', error.message || '请稍后重试');
    } finally {
      setIsExporting(false);
    }
  };

  // Handle privacy settings
  const handlePrivacySettings = () => {
    showSuccess('功能开发中', '隐私设置功能即将上线');
    // TODO: 导航到隐私设置页面或打开模态框
  };

  // Handle notification settings
  const handleNotificationSettings = () => {
    showSuccess('功能开发中', '通知设置功能即将上线');
    // TODO: 导航到通知设置页面或打开模态框
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    setShowDeleteConfirm(true);
  };

  // Confirm account deletion
  const confirmDeleteAccount = async () => {
    try {
      showSuccess('删除请求已提交', '我们将在7天内处理您的账户删除请求');
      setShowDeleteConfirm(false);
      // TODO: 实现实际的账户删除API调用
    } catch (error) {
      showError('删除失败', '请稍后重试');
    }
  };

  // Calculate user level based on stories created
  const getUserLevel = () => {
    const storyCount = userStats?.totalStories || 0;
    if (storyCount >= 50) return { level: 5, title: '故事大师', color: 'text-purple-600' };
    if (storyCount >= 20) return { level: 4, title: '创作专家', color: 'text-blue-600' };
    if (storyCount >= 10) return { level: 3, title: '故事达人', color: 'text-green-600' };
    if (storyCount >= 5) return { level: 2, title: '创作新手', color: 'text-yellow-600' };
    return { level: 1, title: '初学者', color: 'text-gray-600' };
  };

  const userLevel = getUserLevel();

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner size="lg" label="加载用户信息..." />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="space-y-6"
      >
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">个人资料</h1>
            <p className="text-gray-600">管理您的账户信息和偏好设置</p>
          </div>
          <Button
            onClick={() => setIsEditing(true)}
            className="flex items-center space-x-2"
          >
            <Edit3 className="w-4 h-4" />
            <span>编辑资料</span>
          </Button>
        </div>

        {/* User Info Card */}
        <Card className="p-6">
          <div className="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
            {/* Avatar */}
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-400 to-secondary-400 flex items-center justify-center text-white text-2xl font-bold">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  user.name?.charAt(0).toUpperCase() || 'U'
                )}
              </div>
              <div className={`absolute -bottom-1 -right-1 w-6 h-6 rounded-full bg-white border-2 border-white flex items-center justify-center ${userLevel.color}`}>
                <Award className="w-3 h-3" />
              </div>
            </div>

            {/* User Details */}
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h2 className="text-xl font-semibold text-gray-900">{user.name}</h2>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${userLevel.color} bg-gray-100`}>
                  Lv.{userLevel.level} {userLevel.title}
                </span>
              </div>
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>{user.email}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4" />
                  <span>加入时间: {new Date(user.createdAt).toLocaleDateString('zh-CN')}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CreditCard className="w-4 h-4" />
                  <span>当前积分: {user.credits}</span>
                  <QuickBuyCreditsButton variant="link" className="text-xs" />
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Subscription Card */}
        <SubscriptionCard />
        
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <StatsCard
            title="创作故事"
            value={userStats?.totalStories?.toString() || '0'}
            icon={<BookOpen className="w-6 h-6" />}
            change={userStats?.storiesThisMonth ? {
              value: userStats.storiesThisMonth,
              type: 'increase' as const,
              period: '本月'
            } : undefined}
          />
          <StatsCard
            title="使用积分"
            value={userStats?.totalCreditsUsed?.toString() || '0'}
            icon={<CreditCard className="w-6 h-6" />}
          />
          <StatsCard
            title="使用天数"
            value={userStats?.activeDays?.toString() || '0'}
            icon={<Clock className="w-6 h-6" />}
          />
          <StatsCard
            title="获得成就"
            value={userStats?.achievements?.toString() || '0'}
            icon={<Award className="w-6 h-6" />}
          />
        </div>

        {/* Recent Activity */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">最近活动</h3>
          {userStats?.recentActivity && userStats.recentActivity.length > 0 ? (
            <div className="space-y-3">
              {userStats.recentActivity.slice(0, 5).map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <BookOpen className="w-4 h-4 text-primary-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{new Date(activity.createdAt).toLocaleDateString('zh-CN')}</p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-8">暂无活动记录</p>
          )}
        </Card>

        {/* Account Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">账户操作</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="flex items-center justify-center space-x-2"
              onClick={handleExportData}
              loading={isExporting}
              loadingText="导出中..."
            >
              <Download className="w-4 h-4" />
              <span>导出数据</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center justify-center space-x-2"
              onClick={handlePrivacySettings}
            >
              <Shield className="w-4 h-4" />
              <span>隐私设置</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center justify-center space-x-2"
              onClick={handleNotificationSettings}
            >
              <Bell className="w-4 h-4" />
              <span>通知设置</span>
            </Button>
            <Button
              variant="outline"
              className="flex items-center justify-center space-x-2 text-red-600 border-red-300 hover:bg-red-50"
              onClick={handleDeleteAccount}
            >
              <Trash2 className="w-4 h-4" />
              <span>删除账户</span>
            </Button>
          </div>
        </Card>
      </motion.div>

      {/* Edit Profile Modal */}
      <Modal
        isOpen={isEditing}
        onClose={() => setIsEditing(false)}
        title="编辑个人资料"
        size="md"
      >
        <div className="p-6 space-y-6">
          {/* Avatar Upload */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              <div className="w-20 h-20 rounded-full bg-gradient-to-br from-primary-400 to-secondary-400 flex items-center justify-center text-white text-2xl font-bold overflow-hidden">
                {editForm.avatar ? (
                  <img
                    src={editForm.avatar}
                    alt="Avatar"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  editForm.name?.charAt(0).toUpperCase() || 'U'
                )}
              </div>
              <label className="absolute bottom-0 right-0 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center cursor-pointer hover:bg-primary-600 transition-colors">
                <Camera className="w-3 h-3 text-white" />
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  className="hidden"
                />
              </label>
            </div>
            <p className="text-sm text-gray-500">点击相机图标更换头像</p>
          </div>

          {/* Form Fields */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                姓名
              </label>
              <Input
                value={editForm.name}
                onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                placeholder="请输入您的姓名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                邮箱
              </label>
              <Input
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                placeholder="请输入您的邮箱"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setIsEditing(false)}
              disabled={isLoading}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleUpdateProfile}
              loading={isLoading}
              loadingText="保存中..."
              className="flex-1"
            >
              保存更改
            </Button>
          </div>
        </div>
      </Modal>

      {/* Delete Account Confirmation Modal */}
      <Modal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        title="删除账户"
        size="md"
      >
        <div className="p-6 space-y-4">
          <div className="flex items-center space-x-3 text-red-600">
            <Trash2 className="w-6 h-6" />
            <h3 className="text-lg font-semibold">确认删除账户</h3>
          </div>

          <div className="text-gray-700 space-y-2">
            <p>删除账户将会：</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>永久删除您的所有故事和数据</li>
              <li>取消您的订阅（如有）</li>
              <li>无法恢复任何信息</li>
            </ul>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-yellow-800 text-sm">
              ⚠️ 此操作不可逆转，请谨慎考虑
            </p>
          </div>

          <div className="flex space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setShowDeleteConfirm(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={confirmDeleteAccount}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              确认删除
            </Button>
          </div>
        </div>
      </Modal>
    </DashboardLayout>
  );
};

export default ProfilePage;
