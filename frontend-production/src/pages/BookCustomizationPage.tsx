import React, { useState } from 'react';
import { Layout } from '@/components/layout/Layout';
import { Card } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useNotifications } from '@/stores/uiStore';
import { 
  Book, 
  Star, 
  Truck, 
  Shield, 
  Heart, 
  Gift,
  CheckCircle,
  ArrowRight,
  Package,
  Palette,
  Ruler,
  Clock
} from 'lucide-react';

const BookCustomizationPage: React.FC = () => {
  const [selectedSize, setSelectedSize] = useState('standard');
  const [selectedCover, setSelectedCover] = useState('hardcover');
  const [selectedPaper, setSelectedPaper] = useState('premium');
  const [quantity, setQuantity] = useState(1);
  const { showSuccess } = useNotifications();

  const bookSizes = [
    {
      id: 'small',
      name: '小开本',
      size: '15×21cm',
      pages: '24页',
      price: 199,
      description: '便携精巧，适合随身携带'
    },
    {
      id: 'standard',
      name: '标准版',
      size: '21×28cm',
      pages: '32页',
      price: 299,
      description: '经典尺寸，阅读体验佳',
      popular: true
    },
    {
      id: 'large',
      name: '大开本',
      size: '24×32cm',
      pages: '40页',
      price: 399,
      description: '大幅插图，视觉震撼'
    }
  ];

  const coverOptions = [
    {
      id: 'hardcover',
      name: '精装硬壳',
      price: 0,
      description: '耐用美观，收藏首选'
    },
    {
      id: 'softcover',
      name: '平装软壳',
      price: -50,
      description: '轻便实惠，日常阅读'
    }
  ];

  const paperOptions = [
    {
      id: 'premium',
      name: '高级铜版纸',
      price: 0,
      description: '色彩饱满，质感优良'
    },
    {
      id: 'standard',
      name: '标准胶版纸',
      price: -30,
      description: '环保经济，清晰印刷'
    }
  ];

  const features = [
    {
      icon: Star,
      title: '专业印刷',
      description: '采用先进印刷工艺，色彩还原度高'
    },
    {
      icon: Shield,
      title: '质量保证',
      description: '严格质检，不满意无条件退换'
    },
    {
      icon: Truck,
      title: '快速配送',
      description: '7-10个工作日制作完成并发货'
    },
    {
      icon: Heart,
      title: '个性定制',
      description: '每本都是独一无二的专属故事'
    }
  ];

  const calculatePrice = () => {
    const basePrice = bookSizes.find(size => size.id === selectedSize)?.price || 0;
    const coverPrice = coverOptions.find(cover => cover.id === selectedCover)?.price || 0;
    const paperPrice = paperOptions.find(paper => paper.id === selectedPaper)?.price || 0;
    return (basePrice + coverPrice + paperPrice) * quantity;
  };

  const handleAddToCart = () => {
    showSuccess('已添加到购物车', '您可以继续选择其他商品或前往结算');
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-amber-50 via-white to-orange-50 py-12">
        <div className="container mx-auto px-4">
          {/* 页面标题 */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">实体书定制</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              将您的数字故事变成精美的实体书，留下珍贵的回忆
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-8">
            {/* 定制选项 */}
            <div className="lg:col-span-2 space-y-8">
              {/* 尺寸选择 */}
              <Card className="p-6">
                <div className="flex items-center mb-6">
                  <Ruler className="w-6 h-6 text-primary-500 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-900">选择尺寸</h2>
                </div>
                <div className="grid md:grid-cols-3 gap-4">
                  {bookSizes.map((size) => (
                    <div
                      key={size.id}
                      className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedSize === size.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedSize(size.id)}
                    >
                      {size.popular && (
                        <div className="absolute -top-2 left-4 bg-primary-500 text-white text-xs px-2 py-1 rounded">
                          推荐
                        </div>
                      )}
                      <div className="text-center">
                        <h3 className="font-bold text-gray-900 mb-1">{size.name}</h3>
                        <p className="text-sm text-gray-600 mb-2">{size.size}</p>
                        <p className="text-sm text-gray-500 mb-3">{size.pages}</p>
                        <p className="text-lg font-bold text-primary-600">¥{size.price}</p>
                        <p className="text-xs text-gray-500 mt-2">{size.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 封面选择 */}
              <Card className="p-6">
                <div className="flex items-center mb-6">
                  <Book className="w-6 h-6 text-primary-500 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-900">封面类型</h2>
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  {coverOptions.map((cover) => (
                    <div
                      key={cover.id}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedCover === cover.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedCover(cover.id)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-bold text-gray-900">{cover.name}</h3>
                        <span className="text-sm font-medium text-primary-600">
                          {cover.price > 0 ? `+¥${cover.price}` : cover.price < 0 ? `¥${cover.price}` : '标准'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{cover.description}</p>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 纸张选择 */}
              <Card className="p-6">
                <div className="flex items-center mb-6">
                  <Palette className="w-6 h-6 text-primary-500 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-900">纸张材质</h2>
                </div>
                <div className="grid md:grid-cols-2 gap-4">
                  {paperOptions.map((paper) => (
                    <div
                      key={paper.id}
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        selectedPaper === paper.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedPaper(paper.id)}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-bold text-gray-900">{paper.name}</h3>
                        <span className="text-sm font-medium text-primary-600">
                          {paper.price > 0 ? `+¥${paper.price}` : paper.price < 0 ? `¥${paper.price}` : '标准'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600">{paper.description}</p>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 数量选择 */}
              <Card className="p-6">
                <div className="flex items-center mb-6">
                  <Package className="w-6 h-6 text-primary-500 mr-3" />
                  <h2 className="text-2xl font-bold text-gray-900">购买数量</h2>
                </div>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="text-xl font-bold text-gray-900 min-w-[3rem] text-center">
                    {quantity}
                  </span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
                  >
                    +
                  </button>
                  <div className="ml-4 text-sm text-gray-600">
                    <p>• 单本起订，批量更优惠</p>
                    <p>• 5本以上享受9折优惠</p>
                  </div>
                </div>
              </Card>
            </div>

            {/* 订单摘要和特色 */}
            <div className="space-y-6">
              {/* 价格摘要 */}
              <Card className="p-6 sticky top-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">订单摘要</h3>
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">基础价格</span>
                    <span className="font-medium">¥{bookSizes.find(s => s.id === selectedSize)?.price}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">封面类型</span>
                    <span className="font-medium">
                      {coverOptions.find(c => c.id === selectedCover)?.price === 0 
                        ? '标准' 
                        : `¥${coverOptions.find(c => c.id === selectedCover)?.price}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">纸张材质</span>
                    <span className="font-medium">
                      {paperOptions.find(p => p.id === selectedPaper)?.price === 0 
                        ? '标准' 
                        : `¥${paperOptions.find(p => p.id === selectedPaper)?.price}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">数量</span>
                    <span className="font-medium">×{quantity}</span>
                  </div>
                  {quantity >= 5 && (
                    <div className="flex justify-between text-green-600">
                      <span>批量折扣 (9折)</span>
                      <span>-¥{Math.round(calculatePrice() * 0.1)}</span>
                    </div>
                  )}
                  <div className="border-t pt-3">
                    <div className="flex justify-between text-lg font-bold">
                      <span>总计</span>
                      <span className="text-primary-600">
                        ¥{quantity >= 5 ? Math.round(calculatePrice() * 0.9) : calculatePrice()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <Button onClick={handleAddToCart} className="w-full">
                    <Gift className="w-4 h-4 mr-2" />
                    加入购物车
                  </Button>
                  <Button variant="outline" className="w-full">
                    立即购买
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <div className="flex items-center mb-2">
                    <Clock className="w-4 h-4 text-blue-500 mr-2" />
                    <span className="text-sm font-medium text-blue-900">制作周期</span>
                  </div>
                  <p className="text-sm text-blue-700">
                    7-10个工作日完成制作并发货
                  </p>
                </div>
              </Card>

              {/* 服务特色 */}
              <Card className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-4">服务特色</h3>
                <div className="space-y-4">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-start">
                      <div className="flex-shrink-0">
                        <feature.icon className="w-5 h-5 text-primary-500 mt-1" />
                      </div>
                      <div className="ml-3">
                        <h4 className="font-medium text-gray-900">{feature.title}</h4>
                        <p className="text-sm text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </Card>

              {/* 质量保证 */}
              <Card className="p-6 bg-gradient-to-r from-green-50 to-blue-50">
                <div className="flex items-center mb-3">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
                  <h3 className="font-bold text-gray-900">质量承诺</h3>
                </div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• 30天无理由退换货</li>
                  <li>• 印刷质量问题免费重印</li>
                  <li>• 包装破损免费补发</li>
                  <li>• 全程物流跟踪</li>
                </ul>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BookCustomizationPage;