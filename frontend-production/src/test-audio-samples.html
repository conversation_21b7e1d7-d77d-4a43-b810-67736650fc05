<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音频样本测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .voice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .voice-card {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
            transition: all 0.3s ease;
        }
        .voice-card:hover {
            border-color: #4CAF50;
            transform: translateY(-2px);
        }
        .voice-card.playing {
            border-color: #2196F3;
            background: #e3f2fd;
        }
        .voice-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .voice-description {
            color: #666;
            margin-bottom: 12px;
            font-size: 14px;
        }
        .voice-characteristics {
            color: #888;
            font-size: 12px;
            margin-bottom: 15px;
            font-style: italic;
        }
        .button-group {
            display: flex;
            gap: 10px;
        }
        .play-button {
            flex: 1;
            padding: 10px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .play-button.zh {
            background: #4CAF50;
            color: white;
        }
        .play-button.zh:hover {
            background: #45a049;
        }
        .play-button.en {
            background: #2196F3;
            color: white;
        }
        .play-button.en:hover {
            background: #1976D2;
        }
        .play-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            text-align: center;
            padding: 15px;
            margin: 20px 0;
            border-radius: 6px;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .controls {
            text-align: center;
            margin-bottom: 20px;
        }
        .control-button {
            padding: 12px 24px;
            margin: 0 10px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        .stop-all {
            background: #f44336;
            color: white;
        }
        .stop-all:hover {
            background: #d32f2f;
        }
        .preload {
            background: #FF9800;
            color: white;
        }
        .preload:hover {
            background: #F57C00;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 StoryWeaver 语音样本测试</h1>
        
        <div class="controls">
            <button class="control-button stop-all" onclick="stopAllAudio()">停止所有音频</button>
            <button class="control-button preload" onclick="preloadAllSamples()">预加载所有样本</button>
        </div>

        <div id="status" class="status info">
            点击下方按钮测试语音样本播放功能
        </div>

        <div class="voice-grid" id="voiceGrid">
            <!-- 语音卡片将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 语音样本数据
        const voiceSamples = [
            {
                id: 'leda',
                name: 'Leda - 温柔女声',
                description: '年轻活泼的女性声音，适合儿童故事',
                characteristics: 'Youthful, Higher pitch',
                icon: '👩'
            },
            {
                id: 'puck',
                name: 'Puck - 活力男声',
                description: '乐观向上的男性声音，充满活力',
                characteristics: 'Upbeat, Middle pitch',
                icon: '👨'
            },
            {
                id: 'charon',
                name: 'Charon - 沉稳男声',
                description: '深沉稳重的男性声音，富有智慧',
                characteristics: 'Informative, Lower pitch',
                icon: '👨‍🏫'
            },
            {
                id: 'achernar',
                name: 'Achernar - 柔和女声',
                description: '轻柔温和的女性声音，如春风般温暖',
                characteristics: 'Soft, Higher pitch',
                icon: '👩‍💼'
            }
        ];

        let currentlyPlaying = null;
        let audioElements = new Map();

        // 生成语音卡片
        function generateVoiceCards() {
            const grid = document.getElementById('voiceGrid');
            grid.innerHTML = '';

            voiceSamples.forEach(voice => {
                const card = document.createElement('div');
                card.className = 'voice-card';
                card.id = `voice-${voice.id}`;
                
                card.innerHTML = `
                    <div class="voice-name">${voice.icon} ${voice.name}</div>
                    <div class="voice-description">${voice.description}</div>
                    <div class="voice-characteristics">${voice.characteristics}</div>
                    <div class="button-group">
                        <button class="play-button zh" onclick="playVoiceSample('${voice.id}', 'zh')">
                            播放中文样本
                        </button>
                        <button class="play-button en" onclick="playVoiceSample('${voice.id}', 'en')">
                            Play English
                        </button>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 播放语音样本
        async function playVoiceSample(voiceId, language) {
            try {
                // 停止当前播放的音频
                if (currentlyPlaying) {
                    stopAllAudio();
                }

                // 更新状态
                updateStatus(`正在播放 ${voiceId} (${language}) 语音样本...`, 'info');
                
                // 高亮当前播放的卡片
                document.querySelectorAll('.voice-card').forEach(card => {
                    card.classList.remove('playing');
                });
                document.getElementById(`voice-${voiceId}`).classList.add('playing');

                // 构建音频URL
                const audioUrl = `/audio-samples/${voiceId}-${language}.wav`;
                console.log(`Playing audio: ${audioUrl}`);

                // 创建或获取音频元素
                const key = `${voiceId}-${language}`;
                let audio = audioElements.get(key);
                
                if (!audio) {
                    audio = new Audio();
                    audio.src = audioUrl;
                    audio.preload = 'auto';
                    audioElements.set(key, audio);
                }

                // 设置事件监听器
                audio.onended = () => {
                    currentlyPlaying = null;
                    document.querySelectorAll('.voice-card').forEach(card => {
                        card.classList.remove('playing');
                    });
                    updateStatus('播放完成', 'success');
                };

                audio.onerror = (error) => {
                    console.error('Audio playback error:', error);
                    updateStatus(`播放失败: ${audioUrl}`, 'error');
                    currentlyPlaying = null;
                    document.querySelectorAll('.voice-card').forEach(card => {
                        card.classList.remove('playing');
                    });
                };

                // 播放音频
                currentlyPlaying = audio;
                await audio.play();
                updateStatus(`正在播放 ${voiceId} (${language}) - 时长约 10-13 秒`, 'info');

            } catch (error) {
                console.error('Error playing voice sample:', error);
                updateStatus(`播放错误: ${error.message}`, 'error');
                currentlyPlaying = null;
            }
        }

        // 停止所有音频
        function stopAllAudio() {
            if (currentlyPlaying) {
                currentlyPlaying.pause();
                currentlyPlaying.currentTime = 0;
                currentlyPlaying = null;
            }
            
            audioElements.forEach(audio => {
                if (!audio.paused) {
                    audio.pause();
                    audio.currentTime = 0;
                }
            });

            document.querySelectorAll('.voice-card').forEach(card => {
                card.classList.remove('playing');
            });

            updateStatus('已停止所有音频播放', 'success');
        }

        // 预加载所有样本
        async function preloadAllSamples() {
            updateStatus('正在预加载所有语音样本...', 'info');
            
            const promises = [];
            voiceSamples.forEach(voice => {
                ['zh', 'en'].forEach(lang => {
                    const promise = new Promise((resolve) => {
                        const audio = new Audio();
                        audio.src = `/audio-samples/${voice.id}-${lang}.wav`;
                        audio.preload = 'auto';
                        
                        const onLoad = () => {
                            audio.removeEventListener('canplaythrough', onLoad);
                            audio.removeEventListener('error', onError);
                            audioElements.set(`${voice.id}-${lang}`, audio);
                            resolve();
                        };

                        const onError = () => {
                            audio.removeEventListener('canplaythrough', onLoad);
                            audio.removeEventListener('error', onError);
                            console.warn(`Failed to preload: ${audio.src}`);
                            resolve();
                        };

                        audio.addEventListener('canplaythrough', onLoad);
                        audio.addEventListener('error', onError);
                    });
                    
                    promises.push(promise);
                });
            });

            await Promise.allSettled(promises);
            updateStatus(`预加载完成！已加载 ${audioElements.size} 个语音样本`, 'success');
        }

        // 更新状态显示
        function updateStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            generateVoiceCards();
            updateStatus('语音样本测试页面已加载，点击按钮测试播放功能', 'info');
        });
    </script>
</body>
</html>
