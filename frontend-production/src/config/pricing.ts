import { Gift, Star, Zap, Crown } from 'lucide-react';

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: {
    monthly: number;
    yearly: number;
  };
  credits: {
    monthly: number;
    yearly: number;
  };
  features: string[];
  limitations?: string[];
  popular: boolean;
  color: string;
  icon: any;
  currency: string;
  interval: 'month' | 'year';
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  popular: boolean;
  savings?: string;
}

// 统一的价格配置 - 所有价格数据的唯一来源
export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'free',
    name: '免费套餐',
    description: '开始您的创作之旅',
    price: { monthly: 0, yearly: 0 },
    credits: { monthly: 100, yearly: 100 }, // 用户偏好：免费用户获得100初始积分
    features: [
      '每月100个故事生成额度',
      '基础AI模型',
      '标准图片质量',
      '社区支持'
    ],
    limitations: [
      '功能有限',
      '无优先支持',
      '无高级功能'
    ],
    popular: false,
    color: 'gray',
    icon: Gift,
    currency: 'usd',
    interval: 'month',
  },
  {
    id: 'basic_monthly',
    name: '基础会员',
    description: '适合轻度使用者',
    price: { monthly: 9.99, yearly: 99.99 },
    credits: { monthly: 50, yearly: 600 },
    features: [
      '每月50个故事生成额度',
      '基础AI模型',
      '标准图片质量',
      '邮件客服支持'
    ],
    popular: false,
    color: 'blue',
    icon: Star,
    currency: 'usd',
    interval: 'month',
  },
  {
    id: 'pro_monthly',
    name: '专业会员',
    description: '最受欢迎的选择',
    price: { monthly: 19.99, yearly: 199.99 },
    credits: { monthly: 200, yearly: 2400 },
    features: [
      '每月200个故事生成额度',
      '高级AI模型',
      '高清图片质量',
      '优先客服支持',
      '自定义角色设定',
      '故事导出功能'
    ],
    popular: true,
    color: 'primary',
    icon: Zap,
    currency: 'usd',
    interval: 'month',
  },
  {
    id: 'unlimited_monthly',
    name: '无限会员',
    description: '专业创作者首选',
    price: { monthly: 39.99, yearly: 399.99 },
    credits: { monthly: 999999, yearly: 999999 },
    features: [
      '无限故事生成',
      '最新AI模型',
      '超高清图片',
      '24/7专属客服',
      '高级自定义功能',
      '商业使用授权',
      'API访问权限'
    ],
    popular: false,
    color: 'purple',
    icon: Crown,
    currency: 'usd',
    interval: 'month',
  },
  {
    id: 'pro_yearly',
    name: '专业会员（年付）',
    description: '年付享受优惠',
    price: { monthly: 19.99, yearly: 199.99 },
    credits: { monthly: 200, yearly: 2400 },
    features: [
      '每年2400个故事生成额度',
      '高级AI模型',
      '高清图片质量',
      '优先客服支持',
      '自定义角色设定',
      '故事导出功能',
      '年付优惠价格'
    ],
    popular: false,
    color: 'primary',
    icon: Zap,
    currency: 'usd',
    interval: 'year',
  }
];

// 转换为PaymentModal需要的格式
export const getSubscriptionPlans = (): SubscriptionPlan[] => {
  return PRICING_PLANS
    .filter(plan => plan.id !== 'free') // 排除免费套餐
    .map(plan => {
      const subscriptionPlan: SubscriptionPlan = {
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price.monthly, // 默认使用月价格
        currency: plan.currency,
        interval: plan.interval,
        features: plan.features,
        popular: plan.popular,
      };

      // 为年付套餐添加savings信息
      if (plan.interval === 'year') {
        const monthlyTotal = plan.price.monthly * 12;
        const savings = monthlyTotal - plan.price.yearly;
        subscriptionPlan.savings = `节省 $${savings.toFixed(2)}`;
      }

      return subscriptionPlan;
    });
};

// 根据ID获取套餐信息
export const getPlanById = (id: string): PricingPlan | undefined => {
  return PRICING_PLANS.find(plan => plan.id === id);
};

// 获取套餐的显示价格
export const getPlanPrice = (planId: string, billingCycle: 'monthly' | 'yearly' = 'monthly'): number => {
  const plan = getPlanById(planId);
  if (!plan) return 0;
  return plan.price[billingCycle];
};

// 获取套餐的积分数量
export const getPlanCredits = (planId: string, billingCycle: 'monthly' | 'yearly' = 'monthly'): number => {
  const plan = getPlanById(planId);
  if (!plan) return 0;
  return plan.credits[billingCycle];
};
