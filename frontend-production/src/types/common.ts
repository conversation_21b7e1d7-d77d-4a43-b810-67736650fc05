// Common utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Loading states
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface AsyncState<T> extends LoadingState {
  data: T | null;
}

// Pagination
export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedData<T> {
  items: T[];
  meta: PaginationMeta;
}

// Form states
export interface FormState<T> {
  values: T;
  errors: Partial<Record<keyof T, string>>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
  isValid: boolean;
}

// File handling
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: number;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export interface FileUpload extends FileInfo {
  file: File;
  progress: UploadProgress;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  url?: string;
}

// Modal and dialog states
export interface ModalState {
  isOpen: boolean;
  title?: string;
  content?: React.ReactNode;
  onClose?: () => void;
  onConfirm?: () => void;
}

// Notification types
export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
  createdAt: number;
}

// Theme and UI preferences
export interface ThemeConfig {
  mode: 'light' | 'dark' | 'system';
  primaryColor: string;
  fontSize: 'small' | 'medium' | 'large';
  reducedMotion: boolean;
}

// Device and responsive breakpoints
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  breakpoint: Breakpoint;
  orientation: 'portrait' | 'landscape';
}

// API request states
export interface RequestState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastFetch?: number;
}

// Search and filter utilities
export interface SearchState {
  query: string;
  filters: Record<string, any>;
  sort: {
    field: string;
    order: 'asc' | 'desc';
  };
  results: any[];
  total: number;
  loading: boolean;
}

// Validation
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

// Event handling
export interface EventHandler<T = any> {
  (event: T): void;
}

export interface AsyncEventHandler<T = any> {
  (event: T): Promise<void>;
}

// Component props utilities
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
}

export interface ClickableProps {
  onClick?: EventHandler<React.MouseEvent>;
  onDoubleClick?: EventHandler<React.MouseEvent>;
  disabled?: boolean;
}

export interface FocusableProps {
  onFocus?: EventHandler<React.FocusEvent>;
  onBlur?: EventHandler<React.FocusEvent>;
  autoFocus?: boolean;
  tabIndex?: number;
}

// Animation and transition states
export interface AnimationState {
  isAnimating: boolean;
  direction: 'in' | 'out';
  duration: number;
}

// Error handling
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  stack?: string;
}

export interface ErrorBoundaryState {
  hasError: boolean;
  error: AppError | null;
  errorInfo: any;
}

// Feature flags
export interface FeatureFlags {
  enableAnalytics: boolean;
  enableDebugMode: boolean;
  enableBetaFeatures: boolean;
  enableOfflineMode: boolean;
  enablePushNotifications: boolean;
}

// Environment configuration
export interface AppConfig {
  apiBaseUrl: string;
  googleClientId: string;
  stripePublishableKey: string;
  environment: 'development' | 'staging' | 'production';
  version: string;
  features: FeatureFlags;
}

// Local storage keys
export enum StorageKeys {
  AUTH_TOKEN = 'auth_token',
  REFRESH_TOKEN = 'refresh_token',
  USER_PREFERENCES = 'user_preferences',
  THEME_CONFIG = 'theme_config',
  DRAFT_STORY = 'draft_story',
  RECENT_SEARCHES = 'recent_searches',
}

// Route parameters
export interface RouteParams {
  [key: string]: string | undefined;
}

// Query parameters
export interface QueryParams {
  [key: string]: string | string[] | undefined;
}
