// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Error Codes
export enum ErrorCodes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  INSUFFICIENT_CREDITS = 'INSUFFICIENT_CREDITS',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  AI_GENERATION_FAILED = 'AI_GENERATION_FAILED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
}

// Authentication Types
export interface LoginRequest {
  googleToken?: string;
  code?: string;
  redirectUri?: string;
}

export interface LoginResponse {
  user: any; // User type is defined in user.ts
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
    expiresAt?: number;
  };
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
  expiresAt?: number;
}

// Story API Types (main definitions are in story.ts)

// Story API Types (main definitions are in story.ts)
export interface StoryGenerationStatus {
  storyId: string;
  status: 'generating' | 'completed' | 'failed';
  progress: {
    text: boolean;
    images: boolean;
    audio: boolean;
  };
  estimatedTimeRemaining?: number;
  error?: string;
}

// User API Types
export interface UpdateUserRequest {
  name?: string;
  avatar?: string;
}

// Payment Types
export interface PaymentIntent {
  id: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  type: 'credits' | 'subscription' | 'physical_book';
  metadata?: Record<string, string>;
}

// Subscription types are defined in user.ts

// Physical Book Types
export interface PhysicalBook {
  id: string;
  userId: string;
  storyId: string;
  status: 'pending' | 'processing' | 'printed' | 'shipped' | 'delivered';
  shippingAddress: ShippingAddress;
  trackingNumber?: string;
  estimatedDelivery?: string;
  price: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
}

export interface ShippingAddress {
  name: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

export interface CreatePhysicalBookRequest {
  storyId: string;
  shippingAddress: ShippingAddress;
}

// File Upload Types
export interface FileUploadResponse {
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

// Search and Filter Types
export interface SearchParams {
  query?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// StoryFilters is defined in story.ts
