// Re-export all types for easy importing
export * from './api';
export * from './user';
export * from './story';
export * from './common';

// Additional type utilities
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type NonNullable<T> = T extends null | undefined ? never : T;

export type ValueOf<T> = T[keyof T];

export type KeysOfType<T, U> = {
  [K in keyof T]: T[K] extends U ? K : never;
}[keyof T];

// React component types
export type ComponentWithChildren<P = {}> = React.FC<P & { children?: React.ReactNode }>;

export type ComponentWithoutChildren<P = {}> = React.FC<P>;

// Event types
export type ChangeEvent<T = HTMLInputElement> = React.ChangeEvent<T>;
export type ClickEvent = React.MouseEvent<HTMLElement>;
export type SubmitEvent = React.FormEvent<HTMLFormElement>;
export type KeyboardEvent = React.KeyboardEvent<HTMLElement>;

// Ref types
export type InputRef = React.RefObject<HTMLInputElement>;
export type ButtonRef = React.RefObject<HTMLButtonElement>;
export type DivRef = React.RefObject<HTMLDivElement>;

// Hook return types
export type UseStateReturn<T> = [T, React.Dispatch<React.SetStateAction<T>>];
export type UseEffectDeps = React.DependencyList;

// Promise types
export type PromiseResolve<T> = (value: T | PromiseLike<T>) => void;
export type PromiseReject = (reason?: any) => void;

// Function types
export type VoidFunction = () => void;
export type AsyncVoidFunction = () => Promise<void>;
export type Callback<T = void> = (arg: T) => void;
export type AsyncCallback<T = void> = (arg: T) => Promise<void>;

// Utility types for forms
export type FormValues<T> = {
  [K in keyof T]: T[K];
};

export type FormErrors<T> = {
  [K in keyof T]?: string;
};

export type FormTouched<T> = {
  [K in keyof T]?: boolean;
};

// API response wrapper
export type ApiResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  code?: string;
};

// Store types for Zustand
export type StoreApi<T> = {
  getState: () => T;
  setState: (partial: T | Partial<T> | ((state: T) => T | Partial<T>), replace?: boolean) => void;
  subscribe: (listener: (state: T, prevState: T) => void) => () => void;
  destroy: () => void;
};

export type StateCreator<T> = (
  set: (partial: T | Partial<T> | ((state: T) => T | Partial<T>), replace?: boolean) => void,
  get: () => T,
  api: StoreApi<T>
) => T;
