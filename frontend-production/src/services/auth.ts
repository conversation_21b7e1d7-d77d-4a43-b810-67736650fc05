import { api } from './api';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenRequest, 
  RefreshTokenResponse,
  UpdateUserRequest 
} from '@/types';

class AuthService {
  /**
   * Login with Google OAuth token
   */
  async login(request: LoginRequest): Promise<LoginResponse> {
    return api.post<LoginResponse>('/auth/google', request);
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(request: RefreshTokenRequest): Promise<RefreshTokenResponse> {
    return api.post<RefreshTokenResponse>('/auth/refresh', request);
  }

  /**
   * Logout user (invalidate tokens)
   */
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      // Even if logout fails on server, we should clear local storage
      console.warn('Logout request failed:', error);
    }
  }

  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<User> {
    return api.get<User>('/auth/me');
  }

  /**
   * Update user profile
   */
  async updateProfile(request: UpdateUserRequest): Promise<User> {
    return api.put<User>('/auth/profile', request);
  }

  /**
   * Upload user avatar
   */
  async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<{ url: string }> {
    return api.upload<{ url: string }>('/auth/avatar', file, onProgress);
  }

  /**
   * Delete user account
   */
  async deleteAccount(password?: string): Promise<void> {
    return api.delete('/auth/account', {
      data: { password },
    });
  }

  /**
   * Request password reset (if we add email/password auth later)
   */
  async requestPasswordReset(email: string): Promise<void> {
    return api.post('/auth/password-reset', { email });
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    return api.post('/auth/password-reset/confirm', {
      token,
      password: newPassword,
    });
  }

  /**
   * Verify email address
   */
  async verifyEmail(token: string): Promise<void> {
    return api.post('/auth/verify-email', { token });
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(): Promise<void> {
    return api.post('/auth/verify-email/resend');
  }

  /**
   * Check if user is authenticated (validate token)
   */
  async validateToken(): Promise<boolean> {
    try {
      await this.getCurrentUser();
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get user's subscription status
   */
  async getSubscriptionStatus(): Promise<{
    hasActiveSubscription: boolean;
    plan?: string;
    expiresAt?: string;
  }> {
    return api.get('/auth/subscription');
  }

  /**
   * Get user's credit balance
   */
  async getCreditBalance(): Promise<{
    current: number;
    pending: number;
    lifetime: number;
  }> {
    return api.get('/auth/credits');
  }

  /**
   * Get user's activity history
   */
  async getActivityHistory(page = 1, limit = 20): Promise<{
    items: Array<{
      id: string;
      type: string;
      description: string;
      createdAt: string;
    }>;
    total: number;
    page: number;
    limit: number;
  }> {
    return api.get(`/auth/activity?page=${page}&limit=${limit}`);
  }

  /**
   * Export user data
   */
  async exportUserData(options: {
    includeStories: boolean;
    includeImages: boolean;
    includeAudio: boolean;
    format: 'json' | 'zip';
  }): Promise<{ downloadUrl: string; expiresAt: string }> {
    return api.post('/auth/export', options);
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: {
    language?: string;
    timezone?: string;
    emailNotifications?: {
      storyCompleted: boolean;
      bookShipped: boolean;
      promotions: boolean;
      newsletter: boolean;
    };
    defaultStorySettings?: {
      style: string;
      voice: string;
      theme: string;
    };
  }): Promise<void> {
    return api.put('/auth/preferences', preferences);
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<{
    language: string;
    timezone: string;
    emailNotifications: {
      storyCompleted: boolean;
      bookShipped: boolean;
      promotions: boolean;
      newsletter: boolean;
    };
    defaultStorySettings: {
      style: string;
      voice: string;
      theme: string;
    };
  }> {
    return api.get('/auth/preferences');
  }
}

// Create and export service instance
export const authService = new AuthService();

// Google OAuth helper functions
export const initializeGoogleAuth = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('Google Auth can only be initialized in browser'));
      return;
    }

    // Load Google Identity Services script
    const script = document.createElement('script');
    script.src = 'https://accounts.google.com/gsi/client';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      // Initialize Google Identity Services
      if (window.google?.accounts?.id) {
        window.google.accounts.id.initialize({
          client_id: import.meta.env.VITE_GOOGLE_CLIENT_ID,
          callback: handleGoogleCallback,
          auto_select: false,
          cancel_on_tap_outside: true,
        });
        resolve();
      } else {
        reject(new Error('Google Identity Services not available'));
      }
    };
    
    script.onerror = () => {
      reject(new Error('Failed to load Google Identity Services'));
    };
    
    document.head.appendChild(script);
  });
};

// Google OAuth callback handler
const handleGoogleCallback = async (response: any) => {
  try {
    const { credential } = response;
    
    // Send credential to our backend
    const loginResponse = await authService.login({
      googleToken: credential,
    });
    
    // Handle successful login
    console.log('Login successful:', loginResponse);
    
    // The auth store will handle the response
    window.dispatchEvent(new CustomEvent('google-auth-success', {
      detail: loginResponse,
    }));
  } catch (error) {
    console.error('Google auth failed:', error);
    
    window.dispatchEvent(new CustomEvent('google-auth-error', {
      detail: error,
    }));
  }
};

// Trigger Google sign-in
export const signInWithGoogle = () => {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.prompt();
  } else {
    throw new Error('Google Identity Services not initialized');
  }
};

// Render Google sign-in button
export const renderGoogleSignInButton = (elementId: string) => {
  if (window.google?.accounts?.id) {
    window.google.accounts.id.renderButton(
      document.getElementById(elementId),
      {
        theme: 'outline',
        size: 'large',
        text: 'signin_with',
        shape: 'rectangular',
        logo_alignment: 'left',
      }
    );
  }
};

// Type declarations for Google Identity Services
declare global {
  interface Window {
    google?: {
      accounts?: {
        id?: {
          initialize: (config: any) => void;
          prompt: () => void;
          renderButton: (element: HTMLElement | null, config: any) => void;
        };
      };
    };
  }
}
