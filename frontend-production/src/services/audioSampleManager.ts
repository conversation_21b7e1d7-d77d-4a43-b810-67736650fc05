/**
 * 音频样本管理器
 * 管理从Google AI Studio获取的真实语音样本
 */

export interface VoiceAudioSample {
  voiceId: string;
  voiceName: string;
  language: 'zh' | 'en';
  audioUrl: string;
  audioData?: string; // base64 encoded audio data
  duration?: number;
  sampleText: string;
  characteristics: string; // e.g., "Youthful, Higher pitch"
}

export class AudioSampleManager {
  private static instance: AudioSampleManager;
  private audioCache = new Map<string, VoiceAudioSample>();
  private audioElements = new Map<string, HTMLAudioElement>();

  static getInstance(): AudioSampleManager {
    if (!AudioSampleManager.instance) {
      AudioSampleManager.instance = new AudioSampleManager();
    }
    return AudioSampleManager.instance;
  }

  constructor() {
    this.initializeVoiceSamples();
  }

  /**
   * 初始化语音样本数据
   * 这些是从Google AI Studio获取的真实语音样本
   */
  private initializeVoiceSamples(): void {
    const voiceSamples: VoiceAudioSample[] = [
      // Leda - Youthful, Higher pitch (年轻，高音调)
      {
        voiceId: 'leda',
        voiceName: 'Leda',
        language: 'zh',
        audioUrl: '/audio-samples/leda-zh.wav',
        sampleText: '你好，我是温柔的女声。我的声音年轻活泼，非常适合为孩子们讲故事。让我为你朗读一个美妙的故事吧！',
        characteristics: 'Youthful, Higher pitch',
        duration: 13
      },
      {
        voiceId: 'leda',
        voiceName: 'Leda',
        language: 'en',
        audioUrl: '/audio-samples/leda-en.wav',
        sampleText: 'Hello, I am a gentle female voice. My voice is youthful and lively, perfect for telling stories to children.',
        characteristics: 'Youthful, Higher pitch',
        duration: 10
      },
      
      // Puck - Upbeat, Middle pitch (乐观，中音调)
      {
        voiceId: 'puck',
        voiceName: 'Puck',
        language: 'zh',
        audioUrl: '/audio-samples/puck-zh.wav',
        sampleText: '你好，我是活力男声。我的声音乐观向上，充满活力，适合讲述冒险和探索的故事！',
        characteristics: 'Upbeat, Middle pitch',
        duration: 12
      },
      {
        voiceId: 'puck',
        voiceName: 'Puck',
        language: 'en',
        audioUrl: '/audio-samples/puck-en.wav',
        sampleText: 'Hello, I am an energetic male voice. My voice is upbeat and full of energy, perfect for adventure stories!',
        characteristics: 'Upbeat, Middle pitch',
        duration: 9
      },

      // Charon - Informative, Lower pitch (信息性，低音调)
      {
        voiceId: 'charon',
        voiceName: 'Charon',
        language: 'zh',
        audioUrl: '/audio-samples/charon-zh.wav',
        sampleText: '你好，我是沉稳男声。我的声音深沉稳重，富有智慧，适合讲述教育性的故事。',
        characteristics: 'Informative, Lower pitch',
        duration: 11
      },
      {
        voiceId: 'charon',
        voiceName: 'Charon',
        language: 'en',
        audioUrl: '/audio-samples/charon-en.wav',
        sampleText: 'Hello, I am a calm male voice. My voice is deep and steady, full of wisdom, perfect for educational stories.',
        characteristics: 'Informative, Lower pitch',
        duration: 10
      },

      // Achernar - Soft, Higher pitch (柔和，高音调)
      {
        voiceId: 'achernar',
        voiceName: 'Achernar',
        language: 'zh',
        audioUrl: '/audio-samples/achernar-zh.wav',
        sampleText: '你好，我是柔和女声。我的声音轻柔温和，如春风般温暖，最适合睡前故事。',
        characteristics: 'Soft, Higher pitch',
        duration: 12
      },
      {
        voiceId: 'achernar',
        voiceName: 'Achernar',
        language: 'en',
        audioUrl: '/audio-samples/achernar-en.wav',
        sampleText: 'Hello, I am a soft female voice. My voice is gentle and warm like a spring breeze, perfect for bedtime stories.',
        characteristics: 'Soft, Higher pitch',
        duration: 11
      }
    ];

    // 缓存所有样本
    voiceSamples.forEach(sample => {
      const key = `${sample.voiceId}-${sample.language}`;
      this.audioCache.set(key, sample);
    });
  }

  /**
   * 获取语音样本
   */
  getVoiceSample(voiceId: string, language: 'zh' | 'en' = 'zh'): VoiceAudioSample | null {
    const key = `${voiceId}-${language}`;
    return this.audioCache.get(key) || null;
  }

  /**
   * 播放语音样本
   */
  async playVoiceSample(voiceId: string, language: 'zh' | 'en' = 'zh'): Promise<boolean> {
    try {
      const sample = this.getVoiceSample(voiceId, language);
      if (!sample) {
        console.warn(`Voice sample not found: ${voiceId}-${language}`);
        return false;
      }

      // 停止其他正在播放的音频
      this.stopAllAudio();

      const key = `${voiceId}-${language}`;
      let audio = this.audioElements.get(key);

      if (!audio) {
        audio = new Audio();
        audio.src = sample.audioUrl;
        audio.preload = 'auto';
        this.audioElements.set(key, audio);
      }

      // 设置音频事件监听器
      return new Promise((resolve) => {
        if (!audio) {
          resolve(false);
          return;
        }

        const onEnded = () => {
          audio!.removeEventListener('ended', onEnded);
          audio!.removeEventListener('error', onError);
          resolve(true);
        };

        const onError = () => {
          audio!.removeEventListener('ended', onEnded);
          audio!.removeEventListener('error', onError);
          console.warn(`Failed to play audio sample: ${sample.audioUrl}`);
          resolve(false);
        };

        audio.addEventListener('ended', onEnded);
        audio.addEventListener('error', onError);

        // 播放音频
        audio.currentTime = 0;
        audio.play().catch(() => {
          onError();
        });
      });

    } catch (error) {
      console.error('Error playing voice sample:', error);
      return false;
    }
  }

  /**
   * 停止所有音频播放
   */
  stopAllAudio(): void {
    this.audioElements.forEach(audio => {
      if (!audio.paused) {
        audio.pause();
        audio.currentTime = 0;
      }
    });
  }

  /**
   * 预加载音频样本
   */
  async preloadVoiceSamples(voiceIds: string[], languages: ('zh' | 'en')[] = ['zh', 'en']): Promise<void> {
    const promises: Promise<void>[] = [];

    voiceIds.forEach(voiceId => {
      languages.forEach(language => {
        const sample = this.getVoiceSample(voiceId, language);
        if (sample) {
          const promise = new Promise<void>((resolve) => {
            const audio = new Audio();
            audio.src = sample.audioUrl;
            audio.preload = 'auto';

            // 添加超时机制，5秒后自动resolve
            const timeout = setTimeout(() => {
              audio.removeEventListener('canplaythrough', onLoad);
              audio.removeEventListener('error', onError);
              console.warn(`Preload timeout for audio: ${sample.audioUrl}`);
              resolve();
            }, 5000);

            const onLoad = () => {
              clearTimeout(timeout);
              audio.removeEventListener('canplaythrough', onLoad);
              audio.removeEventListener('error', onError);
              this.audioElements.set(`${voiceId}-${language}`, audio);
              resolve();
            };

            const onError = () => {
              clearTimeout(timeout);
              audio.removeEventListener('canplaythrough', onLoad);
              audio.removeEventListener('error', onError);
              console.warn(`Failed to preload audio: ${sample.audioUrl}`);
              resolve();
            };

            audio.addEventListener('canplaythrough', onLoad);
            audio.addEventListener('error', onError);
          });

          promises.push(promise);
        }
      });
    });

    await Promise.allSettled(promises);
  }

  /**
   * 获取所有可用的语音ID
   */
  getAvailableVoiceIds(): string[] {
    const voiceIds = new Set<string>();
    this.audioCache.forEach((sample) => {
      voiceIds.add(sample.voiceId);
    });
    return Array.from(voiceIds);
  }

  /**
   * 获取语音的特征描述
   */
  getVoiceCharacteristics(voiceId: string): string {
    const sample = this.getVoiceSample(voiceId, 'zh');
    return sample?.characteristics || '';
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopAllAudio();
    this.audioElements.clear();
  }
}

export const audioSampleManager = AudioSampleManager.getInstance();
