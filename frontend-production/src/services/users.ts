import { api } from './api';
import { 
  User, 
  UserProfile, 
  UserStats, 
  UserPreferences,
  CreditTransaction,
  CreditBalance,
  UserActivity,
  PaginatedData
} from '@/types';

class UserService {
  /**
   * Get user profile with extended information
   */
  async getProfile(): Promise<UserProfile> {
    return api.get<UserProfile>('/users/profile');
  }

  /**
   * Update user profile
   */
  async updateProfile(updates: {
    name?: string;
    avatar?: string;
  }): Promise<User> {
    return api.put<User>('/users/profile', updates);
  }

  /**
   * Get user statistics
   */
  async getStats(): Promise<UserStats> {
    return api.get<UserStats>('/users/stats');
  }

  /**
   * Get user preferences
   */
  async getPreferences(): Promise<UserPreferences> {
    return api.get<UserPreferences>('/users/preferences');
  }

  /**
   * Update user preferences
   */
  async updatePreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    return api.put<UserPreferences>('/users/preferences', preferences);
  }

  /**
   * Get credit balance
   */
  async getCreditBalance(): Promise<CreditBalance> {
    return api.get<CreditBalance>('/users/credits');
  }

  /**
   * Get credit transaction history
   */
  async getCreditHistory(page = 1, limit = 20): Promise<PaginatedData<CreditTransaction>> {
    return api.get<PaginatedData<CreditTransaction>>(`/users/credits/history?page=${page}&limit=${limit}`);
  }

  /**
   * Get user activity history
   */
  async getActivityHistory(page = 1, limit = 20): Promise<PaginatedData<UserActivity>> {
    return api.get<PaginatedData<UserActivity>>(`/users/activity?page=${page}&limit=${limit}`);
  }

  /**
   * Upload user avatar
   */
  async uploadAvatar(file: File, onProgress?: (progress: number) => void): Promise<{ url: string }> {
    return api.upload<{ url: string }>('/users/avatar', file, onProgress);
  }

  /**
   * Delete user avatar
   */
  async deleteAvatar(): Promise<void> {
    return api.delete('/users/avatar');
  }

  /**
   * Get user's subscription information
   */
  async getSubscription(): Promise<{
    id?: string;
    plan: string;
    status: 'active' | 'canceled' | 'expired' | 'past_due';
    currentPeriodStart: string;
    currentPeriodEnd: string;
    cancelAtPeriodEnd: boolean;
    stripeSubscriptionId?: string;
  } | null> {
    return api.get('/users/subscription');
  }

  /**
   * Get available subscription plans
   */
  async getSubscriptionPlans(): Promise<Array<{
    id: string;
    name: string;
    displayName: string;
    price: number;
    currency: string;
    interval: 'month' | 'year';
    features: string[];
    maxStories: number | null;
    maxCredits: number | null;
    priority: number;
  }>> {
    return api.get('/users/subscription/plans');
  }

  /**
   * Update email notification preferences
   */
  async updateNotificationPreferences(preferences: {
    storyCompleted?: boolean;
    bookShipped?: boolean;
    promotions?: boolean;
    newsletter?: boolean;
  }): Promise<void> {
    return api.put('/users/notifications', preferences);
  }

  /**
   * Get notification preferences
   */
  async getNotificationPreferences(): Promise<{
    storyCompleted: boolean;
    bookShipped: boolean;
    promotions: boolean;
    newsletter: boolean;
  }> {
    return api.get('/users/notifications');
  }

  /**
   * Update default story settings
   */
  async updateDefaultStorySettings(settings: {
    style?: string;
    voice?: string;
    theme?: string;
  }): Promise<void> {
    return api.put('/users/story-defaults', settings);
  }

  /**
   * Get default story settings
   */
  async getDefaultStorySettings(): Promise<{
    style: string;
    voice: string;
    theme: string;
  }> {
    return api.get('/users/story-defaults');
  }

  /**
   * Request account deletion
   */
  async requestAccountDeletion(reason?: string, feedback?: string): Promise<{
    deletionScheduledAt: string;
    canCancel: boolean;
  }> {
    return api.post('/users/delete-account', { reason, feedback });
  }

  /**
   * Cancel account deletion
   */
  async cancelAccountDeletion(): Promise<void> {
    return api.post('/users/cancel-deletion');
  }

  /**
   * Export user data
   */
  async exportData(options: {
    includeStories: boolean;
    includeImages: boolean;
    includeAudio: boolean;
    format: 'json' | 'zip';
  }): Promise<{
    exportId: string;
    downloadUrl?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    expiresAt: string;
  }> {
    return api.post('/users/export', options);
  }

  /**
   * Get export status
   */
  async getExportStatus(exportId: string): Promise<{
    exportId: string;
    downloadUrl?: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    expiresAt: string;
    error?: string;
  }> {
    return api.get(`/users/exports/${exportId}`);
  }

  /**
   * Get user's referral information
   */
  async getReferralInfo(): Promise<{
    referralCode: string;
    referralUrl: string;
    totalReferrals: number;
    creditsEarned: number;
    referralHistory: Array<{
      id: string;
      referredUserName: string;
      creditsEarned: number;
      createdAt: string;
    }>;
  }> {
    return api.get('/users/referrals');
  }

  /**
   * Apply referral code
   */
  async applyReferralCode(code: string): Promise<{
    success: boolean;
    creditsAwarded: number;
  }> {
    return api.post('/users/referrals/apply', { code });
  }

  /**
   * Get user's favorite stories
   */
  async getFavoriteStories(page = 1, limit = 12): Promise<PaginatedData<{
    id: string;
    storyId: string;
    story: {
      id: string;
      title: string;
      coverImageUrl?: string;
      characterName: string;
      theme: string;
      createdAt: string;
    };
    createdAt: string;
  }>> {
    return api.get(`/users/favorites?page=${page}&limit=${limit}`);
  }

  /**
   * Add story to favorites
   */
  async addToFavorites(storyId: string): Promise<void> {
    return api.post('/users/favorites', { storyId });
  }

  /**
   * Remove story from favorites
   */
  async removeFromFavorites(storyId: string): Promise<void> {
    return api.delete(`/users/favorites/${storyId}`);
  }

  /**
   * Check if story is in favorites
   */
  async isFavorite(storyId: string): Promise<{ isFavorite: boolean }> {
    return api.get(`/users/favorites/${storyId}/check`);
  }

  /**
   * Get user's recent activity summary
   */
  async getRecentActivity(): Promise<{
    storiesCreatedThisWeek: number;
    storiesCreatedThisMonth: number;
    creditsUsedThisWeek: number;
    creditsUsedThisMonth: number;
    lastStoryCreated?: {
      id: string;
      title: string;
      createdAt: string;
    };
  }> {
    return api.get('/users/activity/summary');
  }

  /**
   * Update user timezone
   */
  async updateTimezone(timezone: string): Promise<void> {
    return api.put('/users/timezone', { timezone });
  }

  /**
   * Update user language preference
   */
  async updateLanguage(language: string): Promise<void> {
    return api.put('/users/language', { language });
  }

  /**
   * Get user's usage analytics
   */
  async getUsageAnalytics(period: 'week' | 'month' | 'year' = 'month'): Promise<{
    storiesCreated: Array<{ date: string; count: number }>;
    creditsUsed: Array<{ date: string; amount: number }>;
    mostUsedThemes: Array<{ theme: string; count: number }>;
    mostUsedStyles: Array<{ style: string; count: number }>;
    averageStoryLength: number;
    totalTimeSpent: number; // in minutes
  }> {
    return api.get(`/users/analytics?period=${period}`);
  }
}

// Create and export service instance
export const userService = new UserService();
