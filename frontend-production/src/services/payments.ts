import { api } from './api';
import { 
  PaymentIntent, 
  CreatePaymentIntentRequest,
  SubscriptionPlan,
  UserSubscription
} from '@/types';

class PaymentService {
  /**
   * Create Stripe Checkout Session for redirect payment
   */
  async createCheckoutSession(request: CreatePaymentIntentRequest): Promise<{
    sessionId: string;
    url: string;
  }> {
    return api.post('/payments/create-checkout-session', request);
  }

  /**
   * Create payment intent for embedded payment (legacy method)
   */
  async createPaymentIntent(request: CreatePaymentIntentRequest): Promise<PaymentIntent> {
    // 注意：前端传入的金额应该已经是美元单位，不需要除以100
    return api.post('/payments/create-payment-intent', {
      planId: request.metadata?.packageId || 'default',
      amount: request.amount, // 保持美元单位
      currency: request.currency,
      type: request.type,
      metadata: request.metadata
    });
  }

  /**
   * Confirm payment after successful Stripe payment
   */
  async confirmPayment(paymentIntentId: string): Promise<{
    success: boolean;
    creditsAdded?: number;
    subscriptionId?: string;
  }> {
    return api.post('/payments/confirm', { paymentIntentId });
  }

  /**
   * Verify Stripe Checkout Session and get payment details
   */
  async verifyCheckoutSession(sessionId: string): Promise<{
    success: boolean;
    amount: number;
    credits: number;
    type: string;
    subscription?: {
      plan: string;
      status: string;
    };
  }> {
    return api.post('/payments/verify-checkout-session', { sessionId });
  }

  /**
   * Get available credit packages
   */
  async getCreditPackages(): Promise<Array<{
    id: string;
    name: string;
    credits: number;
    price: number;
    currency: string;
    bonus: number;
    popular: boolean;
    savings?: string;
  }>> {
    return api.get('/payments/credit-packages');
  }

  /**
   * Purchase credits
   */
  async purchaseCredits(packageId: string): Promise<PaymentIntent> {
    return api.post<PaymentIntent>('/payments/purchase-credits', { packageId });
  }

  /**
   * Get subscription plans
   */
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    return api.get<SubscriptionPlan[]>('/payments/subscription-plans');
  }

  /**
   * Create subscription
   */
  async createSubscription(planId: string): Promise<{
    subscriptionId: string;
    clientSecret?: string;
    status: string;
  }> {
    return api.post('/payments/create-subscription', { planId });
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(subscriptionId: string, cancelAtPeriodEnd = true): Promise<{
    success: boolean;
    canceledAt?: string;
    endsAt?: string;
  }> {
    return api.post('/payments/cancel-subscription', { 
      subscriptionId, 
      cancelAtPeriodEnd 
    });
  }

  /**
   * Resume canceled subscription
   */
  async resumeSubscription(subscriptionId: string): Promise<{
    success: boolean;
    resumedAt: string;
  }> {
    return api.post('/payments/resume-subscription', { subscriptionId });
  }

  /**
   * Update subscription plan
   */
  async updateSubscription(subscriptionId: string, newPlanId: string): Promise<{
    success: boolean;
    newPlan: string;
    effectiveDate: string;
    prorationAmount?: number;
  }> {
    return api.put('/payments/update-subscription', { 
      subscriptionId, 
      newPlanId 
    });
  }

  /**
   * Get current subscription
   */
  async getCurrentSubscription(): Promise<UserSubscription | null> {
    return api.get<UserSubscription | null>('/payments/subscription');
  }

  /**
   * Get payment history
   */
  async getPaymentHistory(page = 1, limit = 20): Promise<{
    items: Array<{
      id: string;
      type: 'credits' | 'subscription' | 'physical_book';
      amount: number;
      currency: string;
      status: 'pending' | 'succeeded' | 'failed' | 'canceled';
      description: string;
      createdAt: string;
      receiptUrl?: string;
    }>;
    total: number;
    page: number;
    limit: number;
  }> {
    return api.get(`/payments/history?page=${page}&limit=${limit}`);
  }

  /**
   * Get invoice by ID
   */
  async getInvoice(invoiceId: string): Promise<{
    id: string;
    number: string;
    amount: number;
    currency: string;
    status: string;
    paidAt?: string;
    dueDate?: string;
    downloadUrl: string;
    items: Array<{
      description: string;
      amount: number;
      quantity: number;
    }>;
  }> {
    return api.get(`/payments/invoices/${invoiceId}`);
  }

  /**
   * Download invoice PDF
   */
  async downloadInvoice(invoiceId: string): Promise<Blob> {
    const response = await fetch(`${api}/payments/invoices/${invoiceId}/download`, {
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
      },
    });
    
    if (!response.ok) {
      throw new Error('Failed to download invoice');
    }
    
    return response.blob();
  }

  /**
   * Get upcoming invoice preview
   */
  async getUpcomingInvoice(): Promise<{
    amount: number;
    currency: string;
    periodStart: string;
    periodEnd: string;
    items: Array<{
      description: string;
      amount: number;
    }>;
  } | null> {
    return api.get('/payments/upcoming-invoice');
  }

  /**
   * Update payment method
   */
  async updatePaymentMethod(paymentMethodId: string): Promise<{
    success: boolean;
    last4: string;
    brand: string;
  }> {
    return api.put('/payments/payment-method', { paymentMethodId });
  }

  /**
   * Get current payment method
   */
  async getPaymentMethod(): Promise<{
    id: string;
    last4: string;
    brand: string;
    expiryMonth: number;
    expiryYear: number;
  } | null> {
    return api.get('/payments/payment-method');
  }

  /**
   * Create setup intent for saving payment method
   */
  async createSetupIntent(): Promise<{
    clientSecret: string;
    setupIntentId: string;
  }> {
    return api.post('/payments/setup-intent');
  }

  /**
   * Apply promo code
   */
  async applyPromoCode(code: string): Promise<{
    valid: boolean;
    discount?: {
      type: 'percentage' | 'fixed';
      value: number;
      description: string;
    };
    error?: string;
  }> {
    return api.post('/payments/promo-code', { code });
  }

  /**
   * Get billing address
   */
  async getBillingAddress(): Promise<{
    name: string;
    email: string;
    address: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      postalCode: string;
      country: string;
    };
  } | null> {
    return api.get('/payments/billing-address');
  }

  /**
   * Update billing address
   */
  async updateBillingAddress(address: {
    name: string;
    email: string;
    address: {
      line1: string;
      line2?: string;
      city: string;
      state: string;
      postalCode: string;
      country: string;
    };
  }): Promise<void> {
    return api.put('/payments/billing-address', address);
  }

  /**
   * Get tax information
   */
  async getTaxInfo(): Promise<{
    taxId?: string;
    taxExempt: boolean;
    taxRates: Array<{
      jurisdiction: string;
      percentage: number;
    }>;
  }> {
    return api.get('/payments/tax-info');
  }

  /**
   * Update tax information
   */
  async updateTaxInfo(taxInfo: {
    taxId?: string;
    taxExempt?: boolean;
  }): Promise<void> {
    return api.put('/payments/tax-info', taxInfo);
  }

  /**
   * Get refund information for a payment
   */
  async getRefundInfo(paymentId: string): Promise<{
    refundable: boolean;
    amount: number;
    currency: string;
    reason?: string;
    refunds: Array<{
      id: string;
      amount: number;
      status: string;
      createdAt: string;
      reason?: string;
    }>;
  }> {
    return api.get(`/payments/${paymentId}/refunds`);
  }

  /**
   * Request refund
   */
  async requestRefund(paymentId: string, reason?: string): Promise<{
    refundId: string;
    status: string;
    amount: number;
    estimatedArrival: string;
  }> {
    return api.post(`/payments/${paymentId}/refund`, { reason });
  }

  // Helper method to get auth token
  private getAuthToken(): string {
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
      try {
        const { state } = JSON.parse(authStorage);
        return state?.tokens?.accessToken || '';
      } catch (error) {
        return '';
      }
    }
    return '';
  }
}

// Create and export service instance
export const paymentService = new PaymentService();

// Stripe integration helpers
export const loadStripe = async () => {
  if (typeof window === 'undefined') {
    throw new Error('Stripe can only be loaded in browser');
  }

  // Load Stripe.js script
  if (!window.Stripe) {
    const script = document.createElement('script');
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;
    
    await new Promise((resolve, reject) => {
      script.onload = resolve;
      script.onerror = reject;
      document.head.appendChild(script);
    });
  }

  const publishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY;
  if (!publishableKey) {
    throw new Error('Stripe publishable key not configured');
  }

  return window.Stripe(publishableKey);
};

// Type declarations for Stripe
declare global {
  interface Window {
    Stripe?: (key: string) => any;
  }
}
