// Re-export all services
export * from './api';
export * from './auth';
export * from './stories';
export * from './users';
export * from './payments';

// Service initialization
import { apiClient } from './api';

/**
 * Initialize all external services
 */
export const initializeServices = async () => {
  try {
    // Initialize API client
    apiClient.defaults.baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8787/api';

    // Set default headers
    apiClient.defaults.headers.common['Content-Type'] = 'application/json';

    console.log('✅ All services initialized');

    return { success: true };
  } catch (error) {
    console.error('❌ Failed to initialize services:', error);
    throw error;
  }
};

// Service health check
export const checkServiceHealth = async () => {
  const health = {
    api: false,
    auth: false,
    timestamp: new Date().toISOString(),
  };

  try {
    // Check API health
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/health`);
    health.api = response.ok;
  } catch (error) {
    console.warn('API health check failed:', error);
  }

  try {
    // Check auth service
    const authStorage = localStorage.getItem('auth-storage');
    if (authStorage) {
      const { state } = JSON.parse(authStorage);
      health.auth = !!state?.tokens?.accessToken;
    }
  } catch (error) {
    console.warn('Auth health check failed:', error);
  }

  return health;
};
