/**
 * 语音样本服务
 * 用于生成和管理语音样本
 */

export interface VoiceSample {
  id: string;
  name: string;
  language: 'zh' | 'en';
  text: string;
  audioUrl?: string;
}

export class VoiceSampleService {
  private static instance: VoiceSampleService;
  private audioCache = new Map<string, string>();

  static getInstance(): VoiceSampleService {
    if (!VoiceSampleService.instance) {
      VoiceSampleService.instance = new VoiceSampleService();
    }
    return VoiceSampleService.instance;
  }

  /**
   * 生成语音样本
   */
  async generateVoiceSample(
    text: string,
    voiceId: string,
    language: 'zh' | 'en' = 'zh'
  ): Promise<string | null> {
    const cacheKey = `${voiceId}-${language}-${text}`;
    
    // 检查缓存
    if (this.audioCache.has(cacheKey)) {
      return this.audioCache.get(cacheKey)!;
    }

    try {
      // 方案1: 使用Google Cloud Text-to-Speech API
      const audioUrl = await this.generateWithGoogleTTS(text, voiceId, language);
      if (audioUrl) {
        this.audioCache.set(cacheKey, audioUrl);
        return audioUrl;
      }

      // 方案2: 使用浏览器内置Speech Synthesis API
      return await this.generateWithBrowserTTS(text, voiceId, language);

    } catch (error) {
      console.warn('Voice sample generation failed:', error);
      return null;
    }
  }

  /**
   * 使用Google Cloud TTS生成语音
   */
  private async generateWithGoogleTTS(
    text: string,
    voiceId: string,
    language: 'zh' | 'en'
  ): Promise<string | null> {
    try {
      // 这里可以调用后端API来生成语音，添加5秒超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);

      const response = await fetch('/api/tts/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          voiceId,
          language,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        const data = await response.json();
        return data.audioUrl;
      }
    } catch (error) {
      console.warn('Google TTS failed:', error);
    }
    return null;
  }

  /**
   * 使用浏览器内置TTS生成语音
   */
  private async generateWithBrowserTTS(
    text: string,
    voiceId: string,
    language: 'zh' | 'en'
  ): Promise<string | null> {
    return new Promise((resolve) => {
      if (!('speechSynthesis' in window)) {
        resolve(null);
        return;
      }

      try {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = language === 'zh' ? 'zh-CN' : 'en-US';
        utterance.rate = 0.9;
        utterance.pitch = 1.0;

        // 根据voiceId选择合适的声音
        const voices = speechSynthesis.getVoices();
        const targetVoice = this.selectVoiceByType(voices, voiceId, language);
        
        if (targetVoice) {
          utterance.voice = targetVoice;
        }

        utterance.onend = () => {
          resolve('browser-tts-played');
        };

        utterance.onerror = () => {
          resolve(null);
        };

        speechSynthesis.speak(utterance);
      } catch (error) {
        console.warn('Browser TTS failed:', error);
        resolve(null);
      }
    });
  }

  /**
   * 根据语音类型选择合适的浏览器声音
   */
  private selectVoiceByType(
    voices: SpeechSynthesisVoice[],
    voiceId: string,
    language: 'zh' | 'en'
  ): SpeechSynthesisVoice | null {
    const langCode = language === 'zh' ? 'zh' : 'en';
    const availableVoices = voices.filter(voice => 
      voice.lang.toLowerCase().includes(langCode)
    );

    if (availableVoices.length === 0) {
      return voices[0] || null;
    }

    // 根据voiceId选择特定类型的声音
    switch (voiceId) {
      case 'warm-female':
      case 'gentle-female':
        return availableVoices.find(v => 
          v.name.toLowerCase().includes('female') || 
          v.name.toLowerCase().includes('woman')
        ) || availableVoices[0];

      case 'gentle-male':
      case 'storyteller':
        return availableVoices.find(v => 
          v.name.toLowerCase().includes('male') || 
          v.name.toLowerCase().includes('man')
        ) || availableVoices[0];

      case 'child-like':
        return availableVoices.find(v => 
          v.name.toLowerCase().includes('child') ||
          v.name.toLowerCase().includes('young')
        ) || availableVoices[0];

      case 'energetic':
        return availableVoices.find(v => 
          v.name.toLowerCase().includes('energetic') ||
          v.name.toLowerCase().includes('dynamic')
        ) || availableVoices[0];

      default:
        return availableVoices[0];
    }
  }

  /**
   * 预加载语音样本
   */
  async preloadVoiceSamples(voiceOptions: any[]): Promise<void> {
    const promises = voiceOptions.map(async (voice) => {
      if (voice.sampleText) {
        // 预加载中文样本
        if (voice.sampleText.zh) {
          await this.generateVoiceSample(voice.sampleText.zh, voice.id, 'zh');
        }
        // 预加载英文样本
        if (voice.sampleText.en) {
          await this.generateVoiceSample(voice.sampleText.en, voice.id, 'en');
        }
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.audioCache.clear();
  }

  /**
   * 获取预设的语音样本URL
   */
  getPresetSampleUrl(voiceId: string, language: 'zh' | 'en'): string | null {
    const presetSamples: Record<string, Record<string, string>> = {
      'warm-female': {
        zh: 'https://storage.googleapis.com/gtts-api-bucket/zh-cn-warm-female-sample.mp3',
        en: 'https://storage.googleapis.com/gtts-api-bucket/en-us-warm-female-sample.mp3'
      },
      'gentle-male': {
        zh: 'https://storage.googleapis.com/gtts-api-bucket/zh-cn-gentle-male-sample.mp3',
        en: 'https://storage.googleapis.com/gtts-api-bucket/en-us-gentle-male-sample.mp3'
      },
      'child-like': {
        zh: 'https://storage.googleapis.com/gtts-api-bucket/zh-cn-child-voice-sample.mp3',
        en: 'https://storage.googleapis.com/gtts-api-bucket/en-us-child-voice-sample.mp3'
      },
      'storyteller': {
        zh: 'https://storage.googleapis.com/gtts-api-bucket/zh-cn-storyteller-sample.mp3',
        en: 'https://storage.googleapis.com/gtts-api-bucket/en-us-storyteller-sample.mp3'
      },
      'energetic': {
        zh: 'https://storage.googleapis.com/gtts-api-bucket/zh-cn-energetic-sample.mp3',
        en: 'https://storage.googleapis.com/gtts-api-bucket/en-us-energetic-sample.mp3'
      }
    };

    return presetSamples[voiceId]?.[language] || null;
  }
}

export const voiceSampleService = VoiceSampleService.getInstance();
