import React, { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { ErrorBoundary } from '@/components/common/ErrorBoundary';
import { NotificationContainer } from '@/components/ui/NotificationContainer';
import { ModalProvider } from '@/components/ui/Modal';
import { useAuthStore } from '@/stores/authStore';
import { initializeStores } from '@/stores';
import { initializeServices } from '@/services';
import { initializeDebugMode, isDebugMode, shouldSkipAuth, debugLog } from '@/utils/debug';
import router from '@/router';

const App: React.FC = () => {
  const { initializeAuth, setDebugUser, checkTokenExpiry } = useAuthStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize debug mode first
        if (isDebugMode()) {
          initializeDebugMode();
          debugLog.info('Initializing app in debug mode');
        }

        // Initialize stores
        const storeCleanup = await initializeStores();

        // Initialize external services
        await initializeServices();

        // Initialize authentication
        await initializeAuth();

        // Get current auth state after initialization
        const { isAuthenticated, user } = useAuthStore.getState();

        // 🔒 DEVELOPMENT ONLY: 简化的调试用户逻辑（仅在开发环境且需要时设置）
        if (shouldSkipAuth() && !isAuthenticated && !user) {
          debugLog.info('Development: Setting debug user (no real user found)');
          setDebugUser('PREMIUM');
        } else if (isAuthenticated && user) {
          debugLog.info('User authenticated, keeping real user:', user.email);
        }

        console.log('✅ StoryWeaver app initialized successfully');

        // Cleanup function
        return () => {
          if (storeCleanup) {
            storeCleanup();
          }
        };
      } catch (error) {
        console.error('❌ Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, [initializeAuth]);

  return (
    <ErrorBoundary>
      <ModalProvider>
        <div className="App">
          <RouterProvider router={router} />
          <NotificationContainer />
        </div>
      </ModalProvider>
    </ErrorBoundary>
  );
};

export default App;
