#!/bin/bash

echo "🧪 测试前端与后端API连接..."

API_URL="https://storyweaver-api.stawky.workers.dev/api"
FRONTEND_URL="https://1bb4bd61.storyweaver.pages.dev"

echo "📡 测试后端API健康检查..."
echo "API地址: $API_URL"

# 测试API健康检查
echo "1. 测试健康检查端点..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$API_URL/health" || echo "❌ 健康检查失败"

echo ""
echo "2. 测试根端点..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" "$API_URL/" || echo "❌ 根端点测试失败"

echo ""
echo "3. 测试CORS预检请求..."
curl -s -o /dev/null -w "状态码: %{http_code}\n" \
  -H "Origin: $FRONTEND_URL" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Content-Type" \
  -X OPTIONS "$API_URL/health" || echo "❌ CORS预检失败"

echo ""
echo "🌐 测试前端部署..."
echo "前端地址: $FRONTEND_URL"

curl -s -o /dev/null -w "前端状态码: %{http_code}\n" "$FRONTEND_URL" || echo "❌ 前端访问失败"

echo ""
echo "✅ 测试完成！"
echo ""
echo "📋 部署信息:"
echo "  前端URL: $FRONTEND_URL"
echo "  后端API: $API_URL"
echo "  项目名称: storyweaver"
echo ""
echo "💡 如果看到200状态码，说明服务正常运行"
echo "💡 如果看到404或其他错误，可能需要检查后端部署"