{"name": "storyweaver-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "StoryWeaver AI-powered children's storybook platform frontend", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,css,md}\""}, "dependencies": {"@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "framer-motion": "^12.19.2", "i18next": "^25.3.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.59.0", "react-i18next": "^15.5.3", "react-router-dom": "^7.6.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^7.0.0"}}