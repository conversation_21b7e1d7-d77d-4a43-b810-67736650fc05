#!/bin/bash

echo "🚀 开始简化构建..."

# 创建基本的dist目录结构
mkdir -p dist
mkdir -p dist/assets

# 复制静态文件
cp index.html dist/
cp -r public/* dist/ 2>/dev/null || true

# 创建一个基本的JavaScript bundle
echo "📦 创建基本bundle..."

# 创建简化的CSS文件
cat > dist/assets/style.css << 'EOF'
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8fafc;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.text-center {
  text-align: center;
}

.mt-4 { margin-top: 1rem; }
.mb-4 { margin-bottom: 1rem; }
.p-4 { padding: 1rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-bold { font-weight: 700; }
EOF

# 创建简化的JavaScript文件
cat > dist/assets/app.js << 'EOF'
// 简化的应用程序
console.log('StoryWeaver Frontend - 简化版本');

// API配置
window.API_BASE_URL = 'https://storyweaver-api.stawky.workers.dev/api';

// 基本的路由处理
function initApp() {
  console.log('应用初始化完成');
  
  // 显示加载完成消息
  const loadingEl = document.getElementById('loading');
  if (loadingEl) {
    loadingEl.style.display = 'none';
  }
  
  // 显示主要内容
  const appEl = document.getElementById('app');
  if (appEl && !appEl.innerHTML.trim()) {
    appEl.innerHTML = `
      <div class="container">
        <div class="card text-center mt-4">
          <h1 class="text-2xl font-bold mb-4">🎨 StoryWeaver</h1>
          <p class="mb-4">AI驱动的儿童故事创作平台</p>
          <p class="text-sm">前端构建完成 - 等待后端API连接</p>
          <div class="mt-4">
            <button class="btn btn-primary" onclick="alert('功能开发中...')">
              开始创作故事
            </button>
          </div>
        </div>
      </div>
    `;
  }
}

// 当DOM加载完成时初始化应用
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}
EOF

# 更新HTML文件以引用我们的资源
cat > dist/index.html << 'EOF'
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>StoryWeaver - AI儿童故事创作平台</title>
  <link rel="stylesheet" href="/assets/style.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
  <div id="loading" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center;">
    <div>加载中...</div>
  </div>
  <div id="app"></div>
  <script src="/assets/app.js"></script>
</body>
</html>
EOF

echo "✅ 简化构建完成！"
echo "📁 构建文件位于: dist/"
echo "🌐 可以使用任何静态文件服务器运行"