#!/bin/bash

echo "🌐 启动本地开发服务器..."

# 检查是否存在构建文件
if [ ! -d "dist" ]; then
  echo "❌ 构建文件不存在，请先运行 ./build-simple.sh"
  exit 1
fi

# 检查是否安装了Python（用于简单的HTTP服务器）
if command -v python3 &> /dev/null; then
  echo "🐍 使用Python3启动服务器..."
  cd dist && python3 -m http.server 8080
elif command -v python &> /dev/null; then
  echo "🐍 使用Python启动服务器..."
  cd dist && python -m SimpleHTTPServer 8080
elif command -v node &> /dev/null; then
  echo "📦 使用Node.js启动服务器..."
  npx serve dist -p 8080
else
  echo "❌ 未找到Python或Node.js，无法启动本地服务器"
  echo "💡 请手动使用任何静态文件服务器运行dist目录"
  exit 1
fi