#!/bin/bash

# Stripe 支付测试脚本
# 用于快速启动开发环境并测试 Stripe 集成

set -e

echo "🧪 StoryWeaver Stripe 支付测试"
echo "================================"

# 检查环境变量
echo "📋 检查环境配置..."

if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在，正在从 .env.example 复制..."
    cp .env.example .env
    echo "✅ 已创建 .env 文件，请配置 Stripe 测试密钥"
fi

# 检查 Stripe 密钥配置
STRIPE_KEY=$(grep "VITE_STRIPE_PUBLISHABLE_KEY" .env | cut -d '=' -f2)
if [[ $STRIPE_KEY == *"demo"* ]] || [[ $STRIPE_KEY == *"your_stripe"* ]]; then
    echo "⚠️  警告: 检测到演示 Stripe 密钥"
    echo "   请在 .env 文件中配置真实的 Stripe 测试密钥 (pk_test_...)"
    echo "   获取方式: https://dashboard.stripe.com/test/apikeys"
    echo ""
fi

# 检查调试模式配置
DEBUG_MODE=$(grep "VITE_DEBUG_MODE" .env | cut -d '=' -f2)
USE_REAL_STRIPE=$(grep "VITE_DEBUG_USE_REAL_STRIPE" .env | cut -d '=' -f2)
MOCK_PAYMENTS=$(grep "VITE_DEBUG_MOCK_PAYMENTS" .env | cut -d '=' -f2)

echo "🔧 当前配置:"
echo "   调试模式: $DEBUG_MODE"
echo "   真实 Stripe: $USE_REAL_STRIPE"
echo "   模拟支付: $MOCK_PAYMENTS"
echo ""

if [ "$USE_REAL_STRIPE" != "true" ]; then
    echo "❌ 真实 Stripe 未启用"
    echo "   请在 .env 文件中设置: VITE_DEBUG_USE_REAL_STRIPE=true"
    exit 1
fi

if [ "$MOCK_PAYMENTS" == "true" ]; then
    echo "⚠️  警告: 模拟支付已启用，将覆盖真实 Stripe 测试"
    echo "   如需测试真实 Stripe，请设置: VITE_DEBUG_MOCK_PAYMENTS=false"
fi

# 安装依赖
echo "📦 检查依赖..."
if [ ! -d "node_modules" ]; then
    echo "正在安装依赖..."
    npm install
fi

# 启动开发服务器
echo "🚀 启动开发服务器..."
echo ""
echo "测试页面将在以下地址可用:"
echo "   主页: http://localhost:5173"
echo "   Stripe 测试页面: http://localhost:5173/test/stripe"
echo ""
echo "测试步骤:"
echo "1. 访问 http://localhost:5173/test/stripe"
echo "2. 检查配置状态"
echo "3. 使用测试卡号: 4242 4242 4242 4242"
echo "4. 观察支付流程和结果"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "================================"

# 启动开发服务器
npm run dev
