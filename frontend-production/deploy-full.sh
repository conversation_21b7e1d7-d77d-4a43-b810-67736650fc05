#!/bin/bash

echo "🚀 开始部署完整版本的前端到Cloudflare Pages..."

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "📋 Node.js版本: $NODE_VERSION"

# 检查是否安装了依赖
if [ ! -d "node_modules" ]; then
  echo "📦 安装依赖..."
  npm install
fi

# 类型检查
echo "🔍 进行TypeScript类型检查..."
npm run type-check
if [ $? -ne 0 ]; then
  echo "❌ TypeScript类型检查失败"
  exit 1
fi

# 构建项目
echo "🏗️ 构建生产版本..."
NODE_ENV=production npm run build
if [ $? -ne 0 ]; then
  echo "❌ 构建失败"
  exit 1
fi

# 检查构建文件
if [ ! -d "dist" ]; then
  echo "❌ 构建文件不存在"
  exit 1
fi

echo "📊 构建文件大小:"
du -sh dist/*

# 部署到Cloudflare Pages
echo "🌐 部署到Cloudflare Pages..."
wrangler pages deploy dist --project-name=storyweaver
if [ $? -ne 0 ]; then
  echo "❌ 部署失败"
  exit 1
fi

echo "✅ 完整版本前端部署完成！"
echo "🔗 您的应用现在可以在Cloudflare Pages上访问"
echo "📋 环境变量配置:"
echo "   - API URL: https://storyweaver-api.stawky.workers.dev/api"
echo "   - Google OAuth: 已配置"
echo "   - Stripe: 已配置测试密钥"