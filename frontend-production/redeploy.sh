#!/bin/bash

echo "🔄 重新部署StoryWeaver前端到Cloudflare Pages..."

# 检查是否存在构建文件
if [ ! -d "dist" ]; then
  echo "❌ 构建文件不存在，请先运行 ./build-simple.sh"
  exit 1
fi

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "📦 安装 Cloudflare Wrangler..."
  npm install -g wrangler
fi

echo "🔧 更新环境变量..."

# 更新生产环境变量
wrangler pages secret put VITE_API_BASE_URL --project-name=storyweaver <<< "https://storyweaver-api.stawky.workers.dev/api"

echo "🌐 重新部署到Cloudflare Pages..."
wrangler pages deploy dist --project-name=storyweaver

echo "✅ 重新部署完成！"
echo "🔗 新的后端API地址: https://storyweaver-api.stawky.workers.dev/api"
echo "🌐 前端访问地址: https://storyweaver.pages.dev"