#!/bin/bash

# StoryWeaver Frontend Deployment Script

set -e  # Exit on any error

echo "🚀 Starting StoryWeaver Frontend Deployment..."

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the frontend directory."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node --version)
echo "📦 Node.js version: $NODE_VERSION"

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Run type checking
echo "🔍 Running type checks..."
npm run type-check

# Run linting
echo "🧹 Running linter..."
npm run lint

# Run tests (if available)
if npm run test --silent 2>/dev/null; then
    echo "🧪 Running tests..."
    npm run test
else
    echo "⚠️  No tests found, skipping..."
fi

# Build for production
echo "🏗️  Building for production..."
npm run build

# Check build output
if [ ! -d "dist" ]; then
    echo "❌ Error: Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully!"

# Optional: Deploy to Cloudflare Pages using Wrangler
if command -v wrangler &> /dev/null; then
    echo "🌐 Deploying to Cloudflare Pages..."
    wrangler pages publish dist --project-name=storyweaver-frontend
else
    echo "⚠️  Wrangler not found. Please deploy manually or install Wrangler CLI."
    echo "📁 Build output is in the 'dist' directory"
fi

echo "🎉 Deployment process completed!"
echo ""
echo "📋 Next steps:"
echo "   1. If not using Wrangler, upload the 'dist' directory to your hosting provider"
echo "   2. Configure environment variables in your hosting dashboard"
echo "   3. Set up custom domain and SSL certificate"
echo "   4. Configure analytics and monitoring"
