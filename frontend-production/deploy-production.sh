#!/bin/bash

echo "🚀 部署StoryWeaver前端到Cloudflare Pages生产环境..."

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "❌ Wrangler CLI未安装，请先安装: npm install -g wrangler"
  exit 1
fi

# 检查是否已登录
if ! wrangler whoami &> /dev/null; then
  echo "❌ 未登录Cloudflare账户，请先运行: wrangler login"
  exit 1
fi

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "📋 Node.js版本: $NODE_VERSION"

# 安装依赖
if [ ! -d "node_modules" ]; then
  echo "📦 安装依赖..."
  npm install
fi

# 使用生产环境配置
echo "🔧 使用生产环境配置..."
cp .env.production .env

# 构建生产版本
echo "🏗️ 构建生产版本..."
NODE_ENV=production npm run build

if [ $? -ne 0 ]; then
  echo "❌ 构建失败"
  exit 1
fi

# 检查构建文件
if [ ! -d "dist" ]; then
  echo "❌ 构建文件不存在"
  exit 1
fi

echo "📊 构建文件大小:"
du -sh dist/*

# 部署到Cloudflare Pages
echo "🌐 部署到Cloudflare Pages..."
wrangler pages deploy dist --project-name=storyweaver

if [ $? -eq 0 ]; then
  echo "✅ 前端部署成功！"
  echo "🔗 网站地址: https://storyweaver.pages.dev"
  
  echo "🧪 测试网站连接..."
  curl -s -I https://storyweaver.pages.dev | head -1
  
else
  echo "❌ 部署失败，请检查错误信息"
  exit 1
fi

echo "🎉 前端部署完成！"
echo "📋 环境变量配置:"
echo "   - API URL: https://storyweaver-api.stawky.workers.dev/api"
echo "   - 调试模式: 已禁用"
echo "   - 环境: 生产环境"
