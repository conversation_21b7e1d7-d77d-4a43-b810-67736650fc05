# 语音样本文件

这个目录包含从Google AI Studio生成的真实语音样本文件。

## 文件结构

```
audio-samples/
├── leda-zh.wav          # Leda语音 - 中文样本 (年轻，高音调)
├── leda-en.wav          # Leda语音 - 英文样本 (Youthful, Higher pitch)
├── puck-zh.wav          # Puck语音 - 中文样本 (乐观，中音调)
├── puck-en.wav          # Puck语音 - 英文样本 (Upbeat, Middle pitch)
├── charon-zh.wav        # Charon语音 - 中文样本 (信息性，低音调)
├── charon-en.wav        # Charon语音 - 英文样本 (Informative, Lower pitch)
├── achernar-zh.wav      # Achernar语音 - 中文样本 (柔和，高音调)
└── achernar-en.wav      # Achernar语音 - 英文样本 (Soft, Higher pitch)
```

## 语音特征

### Leda (温柔女声)
- **特征**: Youthful, Higher pitch (年轻，高音调)
- **适用场景**: 儿童故事，活泼内容
- **中文样本**: "你好，我是温柔的女声。我的声音年轻活泼，非常适合为孩子们讲故事。让我为你朗读一个美妙的故事吧！"
- **英文样本**: "Hello, I am a gentle female voice. My voice is youthful and lively, perfect for telling stories to children."

### Puck (活力男声)
- **特征**: Upbeat, Middle pitch (乐观，中音调)
- **适用场景**: 冒险故事，探索内容
- **中文样本**: "你好，我是活力男声。我的声音乐观向上，充满活力，适合讲述冒险和探索的故事！"
- **英文样本**: "Hello, I am an energetic male voice. My voice is upbeat and full of energy, perfect for adventure stories!"

### Charon (沉稳男声)
- **特征**: Informative, Lower pitch (信息性，低音调)
- **适用场景**: 教育故事，知识内容
- **中文样本**: "你好，我是沉稳男声。我的声音深沉稳重，富有智慧，适合讲述教育性的故事。"
- **英文样本**: "Hello, I am a calm male voice. My voice is deep and steady, full of wisdom, perfect for educational stories."

### Achernar (柔和女声)
- **特征**: Soft, Higher pitch (柔和，高音调)
- **适用场景**: 睡前故事，温馨内容
- **中文样本**: "你好，我是柔和女声。我的声音轻柔温和，如春风般温暖，最适合睡前故事。"
- **英文样本**: "Hello, I am a soft female voice. My voice is gentle and warm like a spring breeze, perfect for bedtime stories."

## 技术规格

- **格式**: WAV (未压缩)
- **采样率**: 24kHz
- **位深度**: 16-bit
- **声道**: 单声道 (Mono)
- **平均时长**: 10-13秒

## 使用方法

这些音频文件通过 `AudioSampleManager` 类进行管理和播放：

```typescript
import { audioSampleManager } from '@/services/audioSampleManager';

// 播放中文语音样本
await audioSampleManager.playVoiceSample('leda', 'zh');

// 播放英文语音样本
await audioSampleManager.playVoiceSample('leda', 'en');
```

## 生成信息

这些语音样本是使用Google AI Studio的Text-to-Speech功能生成的：
- **服务**: Google AI Studio (https://aistudio.google.com/generate-speech)
- **模型**: Gemini 2.5 Pro Preview TTS
- **生成时间**: 2025年1月
- **语音引擎**: Google Cloud Text-to-Speech

## 版权说明

这些语音样本仅用于StoryWeaver项目的开发和演示目的。请遵守Google AI Studio的使用条款和版权规定。
