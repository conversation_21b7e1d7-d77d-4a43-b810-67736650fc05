#!/bin/bash

echo "🚀 开始部署到Cloudflare Pages..."

# 检查是否存在构建文件
if [ ! -d "dist" ]; then
  echo "❌ 构建文件不存在，请先运行 ./build-simple.sh"
  exit 1
fi

# 检查是否安装了wrangler
if ! command -v wrangler &> /dev/null; then
  echo "📦 安装 Cloudflare Wrangler..."
  npm install -g wrangler
fi

# 部署到Cloudflare Pages
echo "🌐 部署到Cloudflare Pages..."
wrangler pages deploy dist --project-name=storyweaver

echo "✅ 部署完成！"
echo "🔗 您的应用现在可以在Cloudflare Pages上访问"