#!/usr/bin/env node

/**
 * StoryWeaver 后端API连通性测试脚本
 */

import https from 'https';

const API_BASE_URL = 'https://storyweaver-api.stawky.workers.dev/api';

// 测试API端点
const testEndpoints = [
  { path: '/health', method: 'GET', description: '健康检查' },
  { path: '/auth/me', method: 'GET', description: '用户认证状态' },
  { path: '/payments/test', method: 'GET', description: '支付服务状态' },
];

function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'StoryWeaver-Test/1.0',
      },
    };

    const req = https.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testAPI() {
  console.log('🧪 StoryWeaver 后端API连通性测试');
  console.log('================================');
  console.log(`API Base URL: ${API_BASE_URL}`);
  console.log('');

  for (const endpoint of testEndpoints) {
    const url = `${API_BASE_URL}${endpoint.path}`;
    console.log(`📡 测试: ${endpoint.description}`);
    console.log(`   URL: ${url}`);
    console.log(`   方法: ${endpoint.method}`);

    try {
      const result = await makeRequest(url, endpoint.method);
      console.log(`   ✅ 状态: ${result.status}`);
      
      if (result.data) {
        try {
          const jsonData = JSON.parse(result.data);
          console.log(`   📄 响应: ${JSON.stringify(jsonData, null, 2).substring(0, 200)}...`);
        } catch (e) {
          console.log(`   📄 响应: ${result.data.substring(0, 100)}...`);
        }
      }
      
      // 检查CORS头
      if (result.headers['access-control-allow-origin']) {
        console.log(`   🌐 CORS: ${result.headers['access-control-allow-origin']}`);
      }
      
    } catch (error) {
      console.log(`   ❌ 错误: ${error.message}`);
    }
    
    console.log('');
  }

  // 测试基本连通性
  console.log('🔗 基本连通性测试');
  try {
    const result = await makeRequest(API_BASE_URL);
    console.log(`✅ 服务器响应: ${result.status}`);
    console.log(`📡 服务器在线: 是`);
  } catch (error) {
    console.log(`❌ 服务器连接失败: ${error.message}`);
    console.log(`📡 服务器在线: 否`);
  }

  console.log('');
  console.log('================================');
  console.log('测试完成');
}

// 运行测试
testAPI().catch(console.error);
