#!/bin/bash

# StoryWeaver DOCS_INDEX.md 更新脚本
# 用法: ./update-docs-index.sh [变更类型] [变更标题] [变更报告路径]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查参数
if [ $# -lt 3 ]; then
    print_message $RED "❌ 错误: 缺少必需参数"
    echo ""
    echo "用法: $0 [变更类型] [变更标题] [变更报告路径]"
    echo ""
    echo "变更类型: 变更的类型"
    echo "变更标题: 变更的简短描述"
    echo "变更报告路径: 变更报告文件的相对路径"
    echo ""
    echo "示例:"
    echo "  $0 '目录结构重构' 'frontend文件夹删除' 'docs/reports/progress/PROJECT_ANALYSIS_PHASE2_2025-07-12.md'"
    echo "  $0 '功能更新' 'API接口优化' 'docs/reports/implementation/API_OPTIMIZATION_2025-07-12.md'"
    exit 1
fi

# 参数处理
CHANGE_TYPE=$1
CHANGE_TITLE=$2
CHANGE_REPORT=$3
DATE=$(date +%Y-%m-%d)
DOCS_INDEX_PATH="DOCS_INDEX.md"

# 检查DOCS_INDEX.md是否存在
if [ ! -f "$DOCS_INDEX_PATH" ]; then
    print_message $RED "❌ 错误: DOCS_INDEX.md 文件不存在"
    exit 1
fi

# 检查变更报告文件是否存在
if [ ! -f "$CHANGE_REPORT" ]; then
    print_message $YELLOW "⚠️  警告: 变更报告文件不存在: $CHANGE_REPORT"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message $BLUE "ℹ️  操作已取消"
        exit 0
    fi
fi

print_message $BLUE "🚀 开始更新 DOCS_INDEX.md..."
print_message $BLUE "   变更类型: $CHANGE_TYPE"
print_message $BLUE "   变更标题: $CHANGE_TITLE"
print_message $BLUE "   变更报告: $CHANGE_REPORT"
print_message $BLUE "   变更日期: $DATE"

# 备份原文件
BACKUP_PATH="${DOCS_INDEX_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
cp "$DOCS_INDEX_PATH" "$BACKUP_PATH"
print_message $BLUE "📋 已创建备份文件: $BACKUP_PATH"

# 创建临时文件
TEMP_FILE=$(mktemp)

# 查找插入位置（在修改历史追踪部分的开头）
MARKER="## 📈 修改历史追踪"
FOUND_MARKER=false

# 读取文件并插入新记录
while IFS= read -r line; do
    echo "$line" >> "$TEMP_FILE"
    
    # 如果找到标记行，插入新记录
    if [[ "$line" == "$MARKER" ]]; then
        FOUND_MARKER=true
        echo "" >> "$TEMP_FILE"
        echo "### $DATE - $CHANGE_TITLE" >> "$TEMP_FILE"
        echo "- **变更类型**: $CHANGE_TYPE" >> "$TEMP_FILE"
        echo "- **主要变更**: $CHANGE_TITLE" >> "$TEMP_FILE"
        echo "- **执行者**: Augment Agent" >> "$TEMP_FILE"
        echo "- **变更报告**: \`$CHANGE_REPORT\`" >> "$TEMP_FILE"
    fi
done < "$DOCS_INDEX_PATH"

# 检查是否找到插入位置
if [ "$FOUND_MARKER" = false ]; then
    print_message $RED "❌ 错误: 未找到修改历史追踪部分"
    print_message $YELLOW "请确保 DOCS_INDEX.md 包含 '## 📈 修改历史追踪' 部分"
    rm "$TEMP_FILE"
    exit 1
fi

# 替换原文件
mv "$TEMP_FILE" "$DOCS_INDEX_PATH"

# 更新版本信息
CURRENT_VERSION=$(grep "文档整理版本:" "$DOCS_INDEX_PATH" | sed 's/.*v\([0-9.]*\).*/\1/')
if [ -n "$CURRENT_VERSION" ]; then
    # 简单的版本号递增（假设格式为 x.y）
    MAJOR=$(echo "$CURRENT_VERSION" | cut -d. -f1)
    MINOR=$(echo "$CURRENT_VERSION" | cut -d. -f2)
    NEW_MINOR=$((MINOR + 1))
    NEW_VERSION="$MAJOR.$NEW_MINOR"
    
    # 更新版本号
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s/文档整理版本: v${CURRENT_VERSION}/文档整理版本: v${NEW_VERSION}/g" "$DOCS_INDEX_PATH"
    else
        # Linux
        sed -i "s/文档整理版本: v${CURRENT_VERSION}/文档整理版本: v${NEW_VERSION}/g" "$DOCS_INDEX_PATH"
    fi
    
    print_message $GREEN "📈 版本号已更新: v${CURRENT_VERSION} → v${NEW_VERSION}"
fi

# 更新最后更新时间
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/\*索引最后更新: [0-9-]*\*/\*索引最后更新: $DATE\*/g" "$DOCS_INDEX_PATH"
else
    # Linux
    sed -i "s/\*索引最后更新: [0-9-]*\*/\*索引最后更新: $DATE\*/g" "$DOCS_INDEX_PATH"
fi

print_message $GREEN "✅ DOCS_INDEX.md 已成功更新"
print_message $BLUE "📋 更新内容:"
echo "   - 添加了新的变更记录"
echo "   - 更新了版本信息"
echo "   - 更新了最后修改时间"

# 显示新添加的记录
print_message $YELLOW "📝 新添加的记录:"
echo "### $DATE - $CHANGE_TITLE"
echo "- **变更类型**: $CHANGE_TYPE"
echo "- **主要变更**: $CHANGE_TITLE"
echo "- **执行者**: Augment Agent"
echo "- **变更报告**: \`$CHANGE_REPORT\`"

# 提供查看选项
read -p "是否查看更新后的文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v code &> /dev/null; then
        code "$DOCS_INDEX_PATH"
    elif command -v less &> /dev/null; then
        less "$DOCS_INDEX_PATH"
    else
        cat "$DOCS_INDEX_PATH"
    fi
fi

print_message $BLUE "📋 后续操作建议:"
echo "1. 检查更新后的 DOCS_INDEX.md 内容"
echo "2. 提交变更到版本控制系统"
echo "3. 通知相关团队成员"

print_message $GREEN "🎉 脚本执行完成！"
