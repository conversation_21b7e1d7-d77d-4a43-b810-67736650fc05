#!/bin/bash

# StoryWeaver 变更文档创建脚本
# 用法: ./create-change-report.sh [功能名] [变更类型]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查参数
if [ $# -lt 1 ]; then
    print_message $RED "❌ 错误: 缺少必需参数"
    echo ""
    echo "用法: $0 [功能名] [变更类型]"
    echo ""
    echo "功能名: 变更的功能或模块名称 (必需)"
    echo "变更类型: 变更的类型 (可选，默认为 'other')"
    echo ""
    echo "变更类型选项:"
    echo "  - structure: 结构变更"
    echo "  - feature: 功能更新"
    echo "  - docs: 文档整理"
    echo "  - config: 配置修改"
    echo "  - other: 其他"
    echo ""
    echo "示例:"
    echo "  $0 FRONTEND_RESTRUCTURE structure"
    echo "  $0 API_UPDATE feature"
    echo "  $0 DOCS_REORGANIZATION docs"
    exit 1
fi

# 参数处理
FEATURE_NAME=$1
CHANGE_TYPE=${2:-"other"}
DATE=$(date +%Y-%m-%d)
DATETIME=$(date '+%Y年%m月%d日')
FILENAME="CHANGE_REPORT_${DATE}_${FEATURE_NAME}.md"
FILEPATH="docs/reports/progress/${FILENAME}"

# 检查模板文件是否存在
TEMPLATE_PATH="docs/templates/CHANGE_REPORT_TEMPLATE.md"
if [ ! -f "$TEMPLATE_PATH" ]; then
    print_message $RED "❌ 错误: 模板文件不存在: $TEMPLATE_PATH"
    exit 1
fi

# 检查目标目录是否存在
TARGET_DIR="docs/reports/progress"
if [ ! -d "$TARGET_DIR" ]; then
    print_message $YELLOW "⚠️  目标目录不存在，正在创建: $TARGET_DIR"
    mkdir -p "$TARGET_DIR"
fi

# 检查文件是否已存在
if [ -f "$FILEPATH" ]; then
    print_message $YELLOW "⚠️  文件已存在: $FILEPATH"
    read -p "是否覆盖? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_message $BLUE "ℹ️  操作已取消"
        exit 0
    fi
fi

# 变更类型映射
case $CHANGE_TYPE in
    "structure")
        CHANGE_TYPE_CN="结构变更"
        ;;
    "feature")
        CHANGE_TYPE_CN="功能更新"
        ;;
    "docs")
        CHANGE_TYPE_CN="文档整理"
        ;;
    "config")
        CHANGE_TYPE_CN="配置修改"
        ;;
    "other")
        CHANGE_TYPE_CN="其他"
        ;;
    *)
        CHANGE_TYPE_CN="其他"
        ;;
esac

print_message $BLUE "🚀 开始创建变更文档..."
print_message $BLUE "   功能名称: $FEATURE_NAME"
print_message $BLUE "   变更类型: $CHANGE_TYPE_CN"
print_message $BLUE "   文件路径: $FILEPATH"

# 复制模板文件
cp "$TEMPLATE_PATH" "$FILEPATH"

# 替换模板变量
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    sed -i '' "s/\[变更标题\]/${FEATURE_NAME}/g" "$FILEPATH"
    sed -i '' "s/YYYY年MM月DD日/${DATETIME}/g" "$FILEPATH"
    sed -i '' "s/YYYY-MM-DD/${DATE}/g" "$FILEPATH"
    sed -i '' "s/\[结构变更\/功能更新\/文档整理\/配置修改\/其他\]/${CHANGE_TYPE_CN}/g" "$FILEPATH"
else
    # Linux
    sed -i "s/\[变更标题\]/${FEATURE_NAME}/g" "$FILEPATH"
    sed -i "s/YYYY年MM月DD日/${DATETIME}/g" "$FILEPATH"
    sed -i "s/YYYY-MM-DD/${DATE}/g" "$FILEPATH"
    sed -i "s/\[结构变更\/功能更新\/文档整理\/配置修改\/其他\]/${CHANGE_TYPE_CN}/g" "$FILEPATH"
fi

print_message $GREEN "✅ 变更文档已创建: $FILEPATH"
print_message $YELLOW "📝 请编辑文档并填写详细信息"

# 提供编辑选项
read -p "是否现在打开编辑器? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # 尝试使用不同的编辑器
    if command -v code &> /dev/null; then
        code "$FILEPATH"
    elif command -v vim &> /dev/null; then
        vim "$FILEPATH"
    elif command -v nano &> /dev/null; then
        nano "$FILEPATH"
    else
        print_message $YELLOW "⚠️  未找到可用的编辑器，请手动打开文件进行编辑"
    fi
fi

print_message $BLUE "📋 下一步操作:"
echo "1. 编辑变更文档，填写详细信息"
echo "2. 执行变更操作"
echo "3. 更新文档状态"
echo "4. 运行 ./update-docs-index.sh 更新索引"

print_message $GREEN "🎉 脚本执行完成！"
