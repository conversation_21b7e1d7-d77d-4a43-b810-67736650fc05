// 测试图片生成API的脚本
const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

async function testImageGeneration() {
  console.log('🧪 开始测试图片生成功能...');

  // 首先测试健康检查
  console.log('🔍 检查API健康状态...');
  try {
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    const healthData = await healthResponse.json();
    console.log('✅ API健康状态:', healthData);
  } catch (error) {
    console.error('❌ API健康检查失败:', error);
    return;
  }

  // 测试数据
  const testStory = {
    character: {
      name: "测试小熊",
      age: 5,
      traits: "勇敢、好奇"
    },
    theme: "冒险探索",
    setting: "魔法森林",
    style: "cartoon",
    voice: "gentle_female"
  };

  try {
    console.log('📝 尝试创建测试故事...');

    // 创建故事（不使用认证令牌，看看会发生什么）
    const createResponse = await fetch(`${API_BASE}/api/stories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testStory)
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.text();
      console.error('❌ 创建故事失败:', errorData);
      return;
    }
    
    const storyData = await createResponse.json();
    console.log('✅ 故事创建成功:', storyData.storyId);
    
    // 监控故事生成进度
    const storyId = storyData.storyId;
    let attempts = 0;
    const maxAttempts = 30; // 最多等待5分钟
    
    while (attempts < maxAttempts) {
      console.log(`🔍 检查故事状态 (${attempts + 1}/${maxAttempts})...`);
      
      const statusResponse = await fetch(`${API_BASE}/api/stories/${storyId}/status`, {
        headers: {
          'Authorization': 'Bearer test-token'
        }
      });
      
      if (statusResponse.ok) {
        const status = await statusResponse.json();
        console.log('📊 当前状态:', status);
        
        if (status.status === 'completed') {
          console.log('🎉 故事生成完成！');
          
          // 获取完整故事数据
          const storyResponse = await fetch(`${API_BASE}/api/stories/${storyId}`, {
            headers: {
              'Authorization': 'Bearer test-token'
            }
          });
          
          if (storyResponse.ok) {
            const fullStory = await storyResponse.json();
            console.log('📖 故事详情:', {
              title: fullStory.title,
              pages: fullStory.pages?.length || 0,
              hasImages: fullStory.pages?.some(p => p.imageUrl) || false,
              hasAudio: fullStory.pages?.some(p => p.audioUrl) || false
            });
          }
          
          break;
        } else if (status.status === 'failed') {
          console.error('❌ 故事生成失败:', status.error);
          break;
        }
      } else {
        console.warn('⚠️ 无法获取故事状态');
      }
      
      // 等待10秒后重试
      await new Promise(resolve => setTimeout(resolve, 10000));
      attempts++;
    }
    
    if (attempts >= maxAttempts) {
      console.warn('⏰ 等待超时，故事可能仍在生成中');
    }
    
  } catch (error) {
    console.error('💥 测试过程中发生错误:', error);
  }
}

// 运行测试
testImageGeneration();
