#!/bin/bash

# 进度显示界面修复部署脚本
# 解决故事详情页面显示"正在加载故事内容..."的问题

echo "🚀 开始部署进度显示界面修复..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ] && [ ! -d "backend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 修复内容:"
echo "1. ✅ 改进条件判断逻辑（检查多种生成状态）"
echo "2. ✅ 优化进度界面设计（蓝色图标+旋转动画）"
echo "3. ✅ 添加故事信息显示（标题+角色名）"
echo "4. ✅ 添加时间显示功能（分:秒格式）"
echo "5. ✅ 添加调试日志（便于问题诊断）"
echo "6. ✅ 同步修复开发和生产版本"
echo ""

# 1. 构建前端
echo "🔨 构建前端..."
cd frontend
if npm run build; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

# 2. 构建生产版本
echo "🔨 构建生产版本..."
cd frontend-production
if npm run build; then
    echo "✅ 生产版本构建成功"
else
    echo "❌ 生产版本构建失败"
    exit 1
fi
cd ..

# 3. 部署前端
echo "🚀 部署前端..."
cd frontend-production
if npx wrangler pages deploy dist --project-name storyweaver; then
    echo "✅ 前端部署成功"
else
    echo "❌ 前端部署失败"
    exit 1
fi
cd ..

echo ""
echo "🎉 进度显示界面修复部署完成!"
echo "=================================="
echo "📊 修复验证清单:"
echo "□ 访问 https://storyweaver.pages.dev"
echo "□ 创建新故事"
echo "□ 立即点击进入故事详情页面"
echo "□ 验证显示的是进度界面而不是'正在加载故事内容...'"
echo "□ 检查界面是否符合期望设计"
echo "□ 观察时间是否正常更新"
echo "□ 检查浏览器控制台的调试日志"
echo ""
echo "🎯 预期界面效果:"
echo "┌─────────────────────────────────────┐"
echo "│              🔵 (旋转)              │"
echo "│        正在为您创作专属故事         │"
echo "│         《小张的动物故事》          │"
echo "│                                     │"
echo "│ 总体进度              25%          │"
echo "│ ████████░░░░░░░░░░░░░░░░░░░░░░░░     │"
echo "│                                     │"
echo "│ 🟢 实时连接已建立    ⏰ 已用时 1:42 │"
echo "│                                     │"
echo "│ 📝 创作故事文本                     │"
echo "│    AI正在根据您的设定创作精彩的...  │"
echo "│    ████████████████████████████ 100%│"
echo "│                                     │"
echo "│ 🎨 绘制精美插图                     │"
echo "│    AI正在为故事绘制生动的插图...    │"
echo "│    ████████░░░░░░░░░░░░░░░░░░░░░  25%│"
echo "│                                     │"
echo "│ 🔊 合成语音朗读                     │"
echo "│    AI正在生成温暖的语音朗读...      │"
echo "│    ░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░   0%│"
echo "└─────────────────────────────────────┘"
echo ""
echo "📈 关键改进:"
echo "- ❌ 不再显示: '正在加载故事内容...'"
echo "- ✅ 正确显示: 美观的进度界面"
echo "- ✅ 故事标题: 动态显示"
echo "- ✅ 时间显示: 实时更新"
echo "- ✅ 进度条: 三个阶段"
echo "- ✅ 连接状态: 实时/轮询指示"
echo ""
echo "🔍 调试信息:"
echo "- 浏览器控制台会显示故事状态调试日志"
echo "- 格式: 🔍 故事状态调试: {id, title, status, pagesLength, hasPages, firstPageText}"
echo "- 便于诊断为什么显示进度界面"
echo ""
echo "如果仍有问题，请检查："
echo "1. 浏览器控制台的故事状态调试日志"
echo "2. 故事的实际状态和页面内容"
echo "3. WebSocket连接状态"
echo "4. 网络请求是否正常"
