#!/usr/bin/env node

/**
 * StoryWeaver故事创建API测试脚本
 * 用于验证修复后的API是否正常工作
 */

const API_BASE_URL = 'https://storyweaver-api.stawky.workers.dev/api';

// 测试数据 - 测试skipAudio场景
const testStoryData = {
  characterName: "小明",
  characterAge: 6,
  characterTraits: ["勇敢", "善良", "好奇"],
  theme: "adventure",
  setting: "forest",
  style: "fairy-tale",
  skipAudio: true, // 测试跳过音频的场景
  customPrompt: "测试数据库连接修复"
};

// 模拟认证token（用于测试）
const TEST_TOKEN = "test-token-for-api-debugging";

async function testStoryCreationAPI() {
  console.log('🧪 开始测试StoryWeaver故事创建API...');
  console.log('🎯 API地址:', API_BASE_URL);
  console.log('📝 测试数据:', JSON.stringify(testStoryData, null, 2));

  try {
    console.log('\n📡 发送POST请求到 /stories...');
    
    const response = await fetch(`${API_BASE_URL}/stories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'User-Agent': 'StoryWeaver-API-Test/1.0'
      },
      body: JSON.stringify(testStoryData)
    });

    console.log('📊 响应状态:', response.status, response.statusText);
    console.log('📊 响应头:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('📊 原始响应内容:', responseText);

    let responseData;
    try {
      responseData = JSON.parse(responseText);
      console.log('📊 解析后的响应数据:', JSON.stringify(responseData, null, 2));
    } catch (parseError) {
      console.error('❌ JSON解析失败:', parseError.message);
      console.log('📊 响应内容（文本）:', responseText);
      return;
    }

    // 分析响应结果
    if (response.ok) {
      console.log('✅ API调用成功！');
      if (responseData.success) {
        console.log('✅ 故事创建成功！');
        console.log('📖 故事ID:', responseData.data?.storyId);
        console.log('📊 故事状态:', responseData.data?.status);
      } else {
        console.log('⚠️ API返回成功但业务逻辑失败');
        console.log('❌ 错误信息:', responseData.error);
        console.log('🔍 错误代码:', responseData.code);
      }
    } else {
      console.log('❌ API调用失败');
      console.log('❌ HTTP状态:', response.status);
      console.log('❌ 错误信息:', responseData?.error || '未知错误');
      console.log('🔍 错误代码:', responseData?.code || '无错误代码');
      
      if (responseData?.debug) {
        console.log('🐛 调试信息:', responseData.debug);
      }
    }

  } catch (error) {
    console.error('🚨 网络请求失败:', error.message);
    console.error('🚨 错误堆栈:', error.stack);
  }
}

async function testHealthCheck() {
  console.log('\n🏥 测试健康检查端点...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/health`);
    const data = await response.text();
    
    console.log('🏥 健康检查状态:', response.status);
    console.log('🏥 健康检查响应:', data);
  } catch (error) {
    console.error('🚨 健康检查失败:', error.message);
  }
}

async function testAuthEndpoint() {
  console.log('\n🔐 测试认证端点...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    const data = await response.text();
    console.log('🔐 认证检查状态:', response.status);
    console.log('🔐 认证检查响应:', data);
  } catch (error) {
    console.error('🚨 认证检查失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 StoryWeaver API修复验证测试');
  console.log('=' .repeat(50));
  
  await testHealthCheck();
  await testAuthEndpoint();
  await testStoryCreationAPI();
  
  console.log('\n' + '=' .repeat(50));
  console.log('✅ 测试完成！');
  console.log('💡 请检查上述输出以验证API修复效果');
  console.log('💡 如果看到详细的错误信息而不是通用的"故事创建失败"，说明修复成功！');
}

// 运行测试
runTests().catch(console.error);
