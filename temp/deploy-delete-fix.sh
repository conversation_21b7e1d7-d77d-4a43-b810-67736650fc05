#!/bin/bash

# 故事删除功能修复部署脚本
# 解决删除成功但前端显示失败的问题

echo "🚀 开始部署故事删除功能修复..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ] && [ ! -d "backend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 修复内容:"
echo "1. ✅ 修复API客户端响应处理逻辑"
echo "2. ✅ 改进删除确认和状态管理"
echo "3. ✅ 添加删除按钮加载状态"
echo "4. ✅ 优化错误处理和用户反馈"
echo "5. ✅ 同步修复开发和生产版本"
echo ""

# 1. 构建前端
echo "🔨 构建前端..."
cd frontend
if npm run build; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

# 2. 构建生产版本
echo "🔨 构建生产版本..."
cd frontend-production
if npm run build; then
    echo "✅ 生产版本构建成功"
else
    echo "❌ 生产版本构建失败"
    exit 1
fi
cd ..

# 3. 部署前端
echo "🚀 部署前端..."
cd frontend-production
if npx wrangler pages deploy dist --project-name storyweaver; then
    echo "✅ 前端部署成功"
else
    echo "❌ 前端部署失败"
    exit 1
fi
cd ..

echo ""
echo "🎉 删除功能修复部署完成!"
echo "=================================="
echo "📊 修复验证清单:"
echo "□ 访问 https://storyweaver.pages.dev"
echo "□ 登录并进入我的故事页面"
echo "□ 测试删除故事功能"
echo "□ 验证删除成功后立即更新列表"
echo "□ 检查删除按钮的加载状态"
echo "□ 测试错误情况的处理"
echo ""
echo "🔍 修复详情:"
echo "- API响应处理: 支持无data字段的成功响应"
echo "- 状态管理: 删除成功后立即更新故事列表"
echo "- 用户体验: 加载状态和操作反馈"
echo "- 错误处理: 显示具体错误信息"
echo ""
echo "如果问题仍然存在，请检查浏览器控制台和网络面板。"
