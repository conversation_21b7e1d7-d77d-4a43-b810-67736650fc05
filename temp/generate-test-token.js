const crypto = require('crypto');

// JWT Secret from wrangler.toml
const JWT_SECRET = 'storyweaver-jwt-secret-key-2024';

// 简单的JWT生成函数
function base64UrlEncode(str) {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

function generateJWT(payload, secret) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(`${encodedHeader}.${encodedPayload}`)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');

  return `${encodedHeader}.${encodedPayload}.${signature}`;
}

// 生成真实用户的JWT token (<EMAIL>)
const now = Math.floor(Date.now() / 1000);
const payload = {
  userId: '32d476ae-ecf6-4ecf-a4e0-2532626d7f2b',
  email: '<EMAIL>',
  type: 'access',
  iat: now,
  exp: now + (60 * 60 * 24) // 24小时后过期
};

const token = generateJWT(payload, JWT_SECRET);

console.log('Generated JWT Token:');
console.log(token);
console.log('\nPayload:');
console.log(JSON.stringify(payload, null, 2));
console.log('\nUse this token in Authorization header:');
console.log(`Bearer ${token}`);