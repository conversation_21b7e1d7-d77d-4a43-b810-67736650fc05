#!/usr/bin/env node

/**
 * 简单的POST请求测试
 * 验证后端是否接收POST请求
 */

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

// 使用真实的JWT令牌
function createRealJWT() {
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.zJbL-GqCWdWm1k22bY_j8x0ZE1BSMTevYPBdHcbmKuY';
}

async function testSimplePost() {
  console.log('🧪 简单POST请求测试');
  console.log('============================================================');
  console.log(`📍 测试目标: ${API_BASE}`);
  console.log(`⏰ 开始时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('============================================================\n');

  const token = createRealJWT();
  
  const testData = {
    characterName: "测试角色",
    characterAge: 6,
    characterTraits: ["勇敢", "善良"],
    theme: "adventure",
    setting: "forest",
    style: "cartoon",
    voice: "gentle_female"
  };

  try {
    console.log('📤 发送POST请求到 /api/stories');
    console.log('请求数据:', JSON.stringify(testData, null, 2));
    
    const response = await fetch(`${API_BASE}/api/stories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(testData)
    });

    console.log(`📥 响应状态: ${response.status} ${response.statusText}`);
    
    const responseData = await response.text();
    console.log('📋 响应内容:', responseData);

    if (response.ok) {
      console.log('✅ POST请求成功！');
      try {
        const jsonData = JSON.parse(responseData);
        if (jsonData.success && jsonData.data && jsonData.data.storyId) {
          console.log(`🎯 故事ID: ${jsonData.data.storyId}`);
          return jsonData.data.storyId;
        }
      } catch (e) {
        console.log('⚠️ 响应不是有效的JSON');
      }
    } else {
      console.log('❌ POST请求失败');
    }

  } catch (error) {
    console.error('💥 请求异常:', error);
  }

  console.log('\n🏁 测试完成');
  console.log('============================================================');
}

// 运行测试
testSimplePost().catch(console.error);
