<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth 测试</title>
    <script src="https://accounts.google.com/gsi/client" async defer></script>
</head>
<body>
    <h1>Google OAuth 配置测试</h1>
    
    <div id="g_id_onload"
         data-client_id="************-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com"
         data-callback="handleCredentialResponse"
         data-auto_prompt="false">
    </div>
    
    <div class="g_id_signin" data-type="standard"></div>
    
    <div style="margin-top: 20px;">
        <button onclick="testOAuth()">测试 OAuth 配置</button>
        <div id="result" style="margin-top: 10px; padding: 10px; border: 1px solid #ccc;"></div>
    </div>

    <script>
        function handleCredentialResponse(response) {
            console.log("Encoded JWT ID token: " + response.credential);
            document.getElementById('result').innerHTML = 
                '<div style="color: green;">✅ Google OAuth 配置正常！收到凭据：' + 
                response.credential.substring(0, 50) + '...</div>';
        }

        function testOAuth() {
            const clientId = "************-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com";
            const redirectUri = "https://storyweaver.pages.dev/auth/callback";
            
            const result = document.getElementById('result');
            result.innerHTML = `
                <h3>OAuth 配置信息：</h3>
                <p><strong>Client ID:</strong> ${clientId}</p>
                <p><strong>重定向URI:</strong> ${redirectUri}</p>
                <p><strong>当前域名:</strong> ${window.location.origin}</p>
                <p><strong>状态:</strong> 正在测试...</p>
            `;

            // 测试Google OAuth URL
            const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?` +
                `client_id=${clientId}&` +
                `redirect_uri=${encodeURIComponent(redirectUri)}&` +
                `response_type=code&` +
                `scope=openid email profile&` +
                `state=test`;

            result.innerHTML += `
                <p><strong>OAuth URL:</strong> <a href="${authUrl}" target="_blank">点击测试登录</a></p>
                <p style="color: orange;">⚠️ 如果点击后出现错误，说明重定向URI配置有问题</p>
            `;
        }

        // 页面加载时自动测试
        window.onload = function() {
            testOAuth();
        };
    </script>
</body>
</html>