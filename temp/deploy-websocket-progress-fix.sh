#!/bin/bash

# WebSocket连接和进度显示修复部署脚本
# 解决WebSocket连接失败和进度显示问题

echo "🚀 开始部署WebSocket连接和进度显示修复..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ] && [ ! -d "backend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 修复内容:"
echo "1. ✅ 修复WebSocket URL构建逻辑"
echo "2. ✅ 创建通用进度显示组件"
echo "3. ✅ 改进故事详情页面显示"
echo "4. ✅ 优化创建故事流程"
echo "5. ✅ 添加详细调试日志"
echo "6. ✅ 同步修复开发和生产版本"
echo ""

# 1. 构建前端
echo "🔨 构建前端..."
cd frontend
if npm run build; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

# 2. 构建生产版本
echo "🔨 构建生产版本..."
cd frontend-production
if npm run build; then
    echo "✅ 生产版本构建成功"
else
    echo "❌ 生产版本构建失败"
    exit 1
fi
cd ..

# 3. 部署前端
echo "🚀 部署前端..."
cd frontend-production
if npx wrangler pages deploy dist --project-name storyweaver; then
    echo "✅ 前端部署成功"
else
    echo "❌ 前端部署失败"
    exit 1
fi
cd ..

echo ""
echo "🎉 WebSocket连接和进度显示修复部署完成!"
echo "=================================="
echo "📊 修复验证清单:"
echo "□ 访问 https://storyweaver.pages.dev"
echo "□ 创建新故事并观察WebSocket连接"
echo "□ 检查浏览器控制台的连接日志"
echo "□ 验证进度条是否实时更新"
echo "□ 测试故事详情页面的进度显示"
echo "□ 确认生成完成后的跳转"
echo ""
echo "🔍 调试信息:"
echo "- WebSocket URL: wss://storyweaver-api.stawky.workers.dev/ai-queue/{storyId}/websocket"
echo "- 进度组件: StoryGenerationProgress"
echo "- 连接状态: 实时显示在进度页面"
echo "- 备选机制: 轮询模式（3秒间隔）"
echo ""
echo "📈 预期效果:"
echo "- WebSocket连接成功建立"
echo "- 实时显示生成进度（文本→图片→音频）"
echo "- 故事详情页面显示进度条"
echo "- 生成完成后自动跳转"
echo "- 连接失败时自动降级到轮询"
echo ""
echo "如果WebSocket连接仍有问题，请检查："
echo "1. 浏览器控制台的连接日志"
echo "2. 网络面板的WebSocket连接状态"
echo "3. 后端Durable Objects的运行状态"
echo "4. 防火墙或代理设置"
