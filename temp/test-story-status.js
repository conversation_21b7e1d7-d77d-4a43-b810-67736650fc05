#!/usr/bin/env node

/**
 * 检查特定故事的状态
 */

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

// 使用真实的JWT令牌
function createRealJWT() {
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.zJbL-GqCWdWm1k22bY_j8x0ZE1BSMTevYPBdHcbmKuY';
}

async function checkStoryStatus(storyId) {
  console.log(`🔍 检查故事状态: ${storyId}`);
  console.log('============================================================');
  
  const token = createRealJWT();
  
  try {
    const response = await fetch(`${API_BASE}/api/stories/${storyId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log(`📥 响应状态: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('📋 故事状态:', JSON.stringify(data, null, 2));
      
      if (data.success && data.data) {
        const story = data.data;
        console.log('\n📊 状态摘要:');
        console.log(`   状态: ${story.status}`);
        console.log(`   页面数: ${story.pages ? story.pages.length : 0}`);
        console.log(`   音频URL: ${story.audioUrl ? '✅ 有' : '❌ 无'}`);
        
        if (story.pages && story.pages.length > 0) {
          const hasImages = story.pages.some(p => p.imageUrl);
          console.log(`   图片: ${hasImages ? '✅ 有' : '❌ 无'}`);
        }
      }
    } else {
      const errorData = await response.text();
      console.log('❌ 请求失败:', errorData);
    }

  } catch (error) {
    console.error('💥 请求异常:', error);
  }
}

// 从命令行参数获取故事ID，或使用最新的
const storyId = process.argv[2] || '0ee4d8b6-e748-4b16-a2ba-85cc17022e76';
checkStoryStatus(storyId).catch(console.error);
