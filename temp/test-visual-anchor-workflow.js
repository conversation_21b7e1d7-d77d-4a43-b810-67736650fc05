/**
 * 测试视觉锚定工作流 (Visual Anchor Workflow)
 * 
 * 这个脚本用于验证新的三阶段图片生成流程：
 * 1. 生成角色基准图 (Character Anchor)
 * 2. 生成故事脚本 (Story Script with editPrompts)
 * 3. 基于基准图逐页生成插图 (Iterative Illustration Generation)
 */

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

// 测试用的故事请求数据
const testStoryRequest = {
  characterName: "小艾",
  characterAge: 6,
  characterTraits: ["勇敢", "好奇", "善良"],
  theme: "friendship", // 友谊主题
  setting: "magical_garden", // 魔法花园
  style: "cartoon", // 卡通风格
  voice: "gentle_female",
  customPrompt: ""
};

console.log("🔗 测试视觉锚定工作流");
console.log("=".repeat(60));

async function testVisualAnchorWorkflow() {
  try {
    console.log("📋 测试配置:");
    console.log(`   角色: ${testStoryRequest.characterName} (${testStoryRequest.characterAge}岁)`);
    console.log(`   性格: ${testStoryRequest.characterTraits.join(', ')}`);
    console.log(`   主题: ${testStoryRequest.theme}`);
    console.log(`   场景: ${testStoryRequest.setting}`);
    console.log(`   风格: ${testStoryRequest.style}`);
    console.log("");

    // 步骤1: 检查API健康状态
    console.log("🔍 [步骤1] 检查API健康状态...");
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    if (!healthResponse.ok) {
      throw new Error(`API健康检查失败: ${healthResponse.status}`);
    }
    const healthData = await healthResponse.json();
    console.log("✅ API状态正常:", healthData.status);
    console.log("");

    // 步骤2: 创建故事
    console.log("📝 [步骤2] 创建故事...");
    const createResponse = await fetch(`${API_BASE}/api/stories`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.xOx12z6DeKWzg5O0HmN50rAKGc5KPVCNo94Ki-k7LnU'
      },
      body: JSON.stringify(testStoryRequest)
    });

    if (!createResponse.ok) {
      const errorText = await createResponse.text();
      throw new Error(`故事创建失败 (${createResponse.status}): ${errorText}`);
    }

    const storyData = await createResponse.json();
    const storyId = storyData.storyId;
    console.log("✅ 故事创建成功");
    console.log(`   故事ID: ${storyId}`);
    console.log("");

    // 步骤3: 监控生成进度
    console.log("📊 [步骤3] 监控视觉锚定工作流进度...");
    console.log("预期阶段:");
    console.log("   📍 阶段1: 生成角色基准图");
    console.log("   📝 阶段2: 生成故事脚本 (含editPrompts)");
    console.log("   🎨 阶段3: 基于基准图逐页生成插图");
    console.log("");

    let attempts = 0;
    const maxAttempts = 40; // 最多等待10分钟 (15秒间隔)
    let lastStatus = '';
    let stageProgress = {
      text: false,
      image: false,
      audio: false
    };

    while (attempts < maxAttempts) {
      console.log(`🔍 检查进度 (${attempts + 1}/${maxAttempts})...`);
      
      try {
        const statusResponse = await fetch(`${API_BASE}/api/stories/${storyId}/status`, {
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.xOx12z6DeKWzg5O0HmN50rAKGc5KPVCNo94Ki-k7LnU'
          }
        });
        
        if (statusResponse.ok) {
          const status = await statusResponse.json();
          
          // 检测状态变化
          if (status.status !== lastStatus) {
            console.log(`📈 状态变更: ${lastStatus} → ${status.status}`);
            lastStatus = status.status;
            
            // 记录各阶段完成情况
            switch (status.status) {
              case 'generating_text':
                console.log("📝 [阶段2] 正在生成故事脚本和editPrompts...");
                break;
              case 'generating_images':
                if (!stageProgress.text) {
                  console.log("✅ [阶段2] 故事脚本生成完成");
                  stageProgress.text = true;
                }
                console.log("🎨 [阶段3] 正在执行视觉锚定工作流...");
                console.log("   📍 子阶段: 生成角色基准图");
                console.log("   🖼️ 子阶段: 基于基准图生成插图");
                break;
              case 'generating_audio':
                if (!stageProgress.image) {
                  console.log("✅ [阶段3] 视觉锚定工作流完成");
                  stageProgress.image = true;
                }
                console.log("🔊 [阶段4] 正在生成音频...");
                break;
              case 'completed':
                console.log("🎉 所有阶段完成！");
                break;
              case 'failed':
                console.error("❌ 生成失败:", status.error);
                break;
            }
          }
          
          // 显示详细进度信息
          if (status.progress) {
            console.log(`   进度: ${status.progress}%`);
          }
          
          if (status.status === 'completed') {
            console.log("");
            console.log("🎯 [步骤4] 获取完整故事数据...");
            
            // 获取完整故事
            const storyResponse = await fetch(`${API_BASE}/api/stories/${storyId}`, {
              headers: {
                'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.xOx12z6DeKWzg5O0HmN50rAKGc5KPVCNo94Ki-k7LnU'
              }
            });
            
            if (storyResponse.ok) {
              const fullStory = await storyResponse.json();
              
              console.log("📖 故事详情:");
              console.log(`   标题: ${fullStory.title}`);
              console.log(`   页数: ${fullStory.pages?.length || 0}`);
              console.log(`   角色基准图: ${fullStory.characterAnchor ? '✅ 已生成' : '❌ 未找到'}`);
              
              // 检查每页是否都有图片
              let pagesWithImages = 0;
              let pagesWithEditPrompts = 0;
              
              if (fullStory.pages) {
                fullStory.pages.forEach((page, index) => {
                  if (page.imageUrl) pagesWithImages++;
                  if (page.editPrompt || page.imagePrompt) pagesWithEditPrompts++;
                  
                  console.log(`   第${index + 1}页:`);
                  console.log(`     文本: ${page.text?.substring(0, 30)}...`);
                  console.log(`     编辑指令: ${(page.editPrompt || page.imagePrompt || '无')?.substring(0, 50)}...`);
                  console.log(`     图片: ${page.imageUrl ? '✅ 已生成' : '❌ 缺失'}`);
                });
              }
              
              console.log("");
              console.log("📊 视觉锚定工作流验证结果:");
              console.log(`   ✅ 故事脚本: ${fullStory.title ? '成功' : '失败'}`);
              console.log(`   ✅ editPrompts: ${pagesWithEditPrompts}/${fullStory.pages?.length || 0} 页`);
              console.log(`   ✅ 插图生成: ${pagesWithImages}/${fullStory.pages?.length || 0} 页`);
              console.log(`   ✅ 音频生成: ${fullStory.audioUrl ? '成功' : '跳过/失败'}`);
              
              // 评估工作流成功率
              const successRate = (pagesWithImages / (fullStory.pages?.length || 1)) * 100;
              console.log(`   🎯 整体成功率: ${successRate.toFixed(1)}%`);
              
              if (successRate >= 80) {
                console.log("🌟 视觉锚定工作流测试 - 成功！");
              } else if (successRate >= 50) {
                console.log("⚠️ 视觉锚定工作流测试 - 部分成功");
              } else {
                console.log("❌ 视觉锚定工作流测试 - 失败");
              }
              
            } else {
              console.warn("⚠️ 无法获取完整故事数据");
            }
            
            break;
            
          } else if (status.status === 'failed') {
            console.error("❌ 故事生成失败:", status.error);
            break;
          }
          
        } else {
          console.warn(`⚠️ 无法获取故事状态 (${statusResponse.status})`);
        }
        
      } catch (error) {
        console.warn(`⚠️ 状态检查出错: ${error.message}`);
      }
      
      // 等待15秒后重试
      await new Promise(resolve => setTimeout(resolve, 15000));
      attempts++;
    }
    
    if (attempts >= maxAttempts) {
      console.warn("⏰ 等待超时，测试可能仍在进行中");
    }
    
  } catch (error) {
    console.error("💥 测试过程中发生错误:", error.message);
    console.error("错误详情:", error);
  }
}

// 显示测试说明
console.log("🎯 视觉锚定工作流测试说明:");
console.log("");
console.log("本测试将验证以下新功能:");
console.log("1. 📍 角色基准图生成 - 创建一致性视觉锚点");
console.log("2. 📝 editPrompt生成 - 替代传统imagePrompt");
console.log("3. 🎨 基于基准图的图像编辑 - 确保角色一致性");
console.log("4. 🔄 多层降级机制 - Imagen 4.0 → Gemini 2.0 → 传统模式");
console.log("5. 📊 实时进度监控 - 三阶段进度反馈");
console.log("");
console.log("预期优势:");
console.log("✨ 角色外观高度一致");
console.log("✨ 艺术风格统一");
console.log("✨ 更好的故事连贯性");
console.log("✨ 更稳定的生成质量");
console.log("");

// 运行测试
console.log("🚀 开始测试...");
console.log("");
testVisualAnchorWorkflow();