#!/usr/bin/env node

/**
 * StoryWeaver 生产环境前后端集成测试
 * 使用Playwright自动化测试故事生成完整流程
 */

const { chromium } = require('playwright');

// 生产环境配置
const PRODUCTION_CONFIG = {
  frontendUrl: 'https://storyweaver.pages.dev',
  backendUrl: 'https://storyweaver-api.stawky.workers.dev',
  testTimeout: 300000, // 5分钟超时
  pollInterval: 3000,  // 3秒轮询间隔
  maxPollAttempts: 100 // 最大轮询次数
};

// 测试用户凭据
const TEST_CREDENTIALS = {
  email: '<EMAIL>',
  // 使用真实的JWT令牌进行测试
  jwt: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.zJbL-GqCWdWm1k22bY_j8x0ZE1BSMTevYPBdHcbmKuY'
};

// 故事创建测试数据
const STORY_TEST_DATA = {
  characterName: 'Playwright测试角色',
  characterAge: 7,
  characterTraits: ['勇敢', '聪明'],
  theme: 'adventure',
  setting: 'forest',
  style: 'cartoon',
  voice: 'gentle_female'
};

class ProductionStoryTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      startTime: new Date(),
      tests: [],
      summary: {
        total: 0,
        passed: 0,
        failed: 0
      }
    };
  }

  async initialize() {
    console.log('🚀 启动生产环境测试');
    console.log('============================================================');
    console.log(`📍 前端URL: ${PRODUCTION_CONFIG.frontendUrl}`);
    console.log(`📍 后端URL: ${PRODUCTION_CONFIG.backendUrl}`);
    console.log(`⏰ 开始时间: ${this.testResults.startTime.toLocaleString('zh-CN')}`);
    console.log('============================================================\n');

    // 启动浏览器
    this.browser = await chromium.launch({
      headless: false, // 显示浏览器以便观察
      slowMo: 1000     // 减慢操作速度以便观察
    });

    this.page = await this.browser.newPage();
    
    // 设置视口大小
    await this.page.setViewportSize({ width: 1280, height: 720 });
    
    // 监听控制台消息
    this.page.on('console', msg => {
      console.log(`🖥️ 浏览器控制台: ${msg.text()}`);
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error(`❌ 页面错误: ${error.message}`);
    });
  }

  async runTest(testName, testFunction) {
    console.log(`\n🧪 开始测试: ${testName}`);
    console.log('--------------------------------------------------');
    
    const testStart = Date.now();
    let testResult = {
      name: testName,
      startTime: new Date(),
      status: 'running',
      duration: 0,
      error: null
    };

    try {
      await testFunction();
      testResult.status = 'passed';
      testResult.duration = Date.now() - testStart;
      this.testResults.summary.passed++;
      console.log(`✅ 测试通过: ${testName} (${testResult.duration}ms)`);
    } catch (error) {
      testResult.status = 'failed';
      testResult.duration = Date.now() - testStart;
      testResult.error = error.message;
      this.testResults.summary.failed++;
      console.error(`❌ 测试失败: ${testName} - ${error.message}`);
    }

    this.testResults.tests.push(testResult);
    this.testResults.summary.total++;
  }

  async testPageLoad() {
    await this.page.goto(PRODUCTION_CONFIG.frontendUrl);
    await this.page.waitForLoadState('networkidle');
    
    // 验证页面标题
    const title = await this.page.title();
    if (!title.includes('StoryWeaver')) {
      throw new Error(`页面标题不正确: ${title}`);
    }

    // 验证关键元素存在
    await this.page.waitForSelector('header', { timeout: 10000 });
    console.log('📄 页面加载成功');
  }

  async testUserAuthentication() {
    // 设置JWT令牌到localStorage
    await this.page.evaluate((jwt) => {
      localStorage.setItem('auth_token', jwt);
    }, TEST_CREDENTIALS.jwt);

    // 刷新页面以应用认证状态
    await this.page.reload();
    await this.page.waitForLoadState('networkidle');

    // 验证用户已登录
    await this.page.waitForSelector('[data-testid="user-menu"], .user-avatar, .profile-button', { timeout: 10000 });
    console.log('🔐 用户认证成功');
  }

  async testStoryCreationForm() {
    // 导航到故事创建页面
    await this.page.goto(`${PRODUCTION_CONFIG.frontendUrl}/create-story`);
    await this.page.waitForLoadState('networkidle');

    // 填写故事创建表单
    await this.page.fill('input[name="characterName"], [data-testid="character-name"]', STORY_TEST_DATA.characterName);
    await this.page.fill('input[name="characterAge"], [data-testid="character-age"]', STORY_TEST_DATA.characterAge.toString());

    // 选择主题
    await this.page.selectOption('select[name="theme"], [data-testid="theme-select"]', STORY_TEST_DATA.theme);

    // 选择设定
    await this.page.selectOption('select[name="setting"], [data-testid="setting-select"]', STORY_TEST_DATA.setting);

    // 选择风格
    await this.page.selectOption('select[name="style"], [data-testid="style-select"]', STORY_TEST_DATA.style);

    // 选择声音
    await this.page.selectOption('select[name="voice"], [data-testid="voice-select"]', STORY_TEST_DATA.voice);

    console.log('📝 故事创建表单填写完成');
  }

  async testStorySubmission() {
    // 提交故事创建表单
    await this.page.click('button[type="submit"], [data-testid="create-story-button"]');
    
    // 等待提交响应
    await this.page.waitForTimeout(2000);

    // 验证是否跳转到生成进度页面或显示进度指示器
    try {
      await this.page.waitForSelector('[data-testid="generation-progress"], .progress-indicator, .generating-status', { timeout: 10000 });
      console.log('📤 故事提交成功，开始生成');
    } catch (error) {
      // 如果没有找到进度指示器，检查是否有错误消息
      const errorElement = await this.page.$('.error-message, .alert-error');
      if (errorElement) {
        const errorText = await errorElement.textContent();
        throw new Error(`故事提交失败: ${errorText}`);
      }
      throw new Error('未找到生成进度指示器');
    }
  }

  async testGenerationProgress() {
    console.log('⏳ 开始监控故事生成进度...');
    
    let attempts = 0;
    let lastStatus = '';
    let storyCompleted = false;

    while (attempts < PRODUCTION_CONFIG.maxPollAttempts && !storyCompleted) {
      attempts++;
      
      try {
        // 检查页面状态
        const statusElement = await this.page.$('.story-status, [data-testid="story-status"]');
        if (statusElement) {
          const currentStatus = await statusElement.textContent();
          if (currentStatus !== lastStatus) {
            console.log(`📈 状态更新: ${lastStatus} → ${currentStatus}`);
            lastStatus = currentStatus;
          }

          // 检查是否完成
          if (currentStatus.includes('completed') || currentStatus.includes('完成')) {
            storyCompleted = true;
            console.log('🎉 故事生成完成！');
            break;
          }
        }

        // 检查是否有错误
        const errorElement = await this.page.$('.error-message, .generation-error');
        if (errorElement) {
          const errorText = await errorElement.textContent();
          throw new Error(`生成过程中出现错误: ${errorText}`);
        }

        // 等待下次检查
        await this.page.waitForTimeout(PRODUCTION_CONFIG.pollInterval);
        
      } catch (error) {
        console.error(`⚠️ 进度检查出错 (尝试 ${attempts}): ${error.message}`);
      }
    }

    if (!storyCompleted) {
      throw new Error(`故事生成超时 (${attempts} 次尝试)`);
    }
  }

  async testFinalResult() {
    // 验证故事内容
    await this.page.waitForSelector('.story-content, [data-testid="story-pages"]', { timeout: 10000 });
    
    // 检查页面数量
    const pages = await this.page.$$('.story-page, [data-testid="story-page"]');
    if (pages.length === 0) {
      throw new Error('未找到故事页面');
    }
    console.log(`📖 故事包含 ${pages.length} 页`);

    // 检查图片
    const images = await this.page.$$('.story-image, [data-testid="story-image"]');
    console.log(`🖼️ 故事包含 ${images.length} 张图片`);

    // 检查音频
    const audioElement = await this.page.$('.story-audio, [data-testid="story-audio"]');
    if (audioElement) {
      console.log('🔊 故事包含音频文件');
    }

    console.log('✅ 故事内容验证完成');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async generateReport() {
    const endTime = new Date();
    const totalDuration = endTime - this.testResults.startTime;

    console.log('\n🏁 测试完成');
    console.log('============================================================');
    console.log(`⏰ 总耗时: ${Math.round(totalDuration / 1000)}秒`);
    console.log(`📊 测试总数: ${this.testResults.summary.total}`);
    console.log(`✅ 通过: ${this.testResults.summary.passed}`);
    console.log(`❌ 失败: ${this.testResults.summary.failed}`);
    console.log(`📈 成功率: ${Math.round((this.testResults.summary.passed / this.testResults.summary.total) * 100)}%`);
    console.log('============================================================');

    // 详细测试结果
    console.log('\n📋 详细测试结果:');
    this.testResults.tests.forEach(test => {
      const status = test.status === 'passed' ? '✅' : '❌';
      console.log(`${status} ${test.name} (${test.duration}ms)`);
      if (test.error) {
        console.log(`   错误: ${test.error}`);
      }
    });
  }

  async run() {
    try {
      await this.initialize();

      // 执行测试套件
      await this.runTest('页面加载测试', () => this.testPageLoad());
      await this.runTest('用户认证测试', () => this.testUserAuthentication());
      await this.runTest('故事创建表单测试', () => this.testStoryCreationForm());
      await this.runTest('故事提交测试', () => this.testStorySubmission());
      await this.runTest('生成进度监控测试', () => this.testGenerationProgress());
      await this.runTest('最终结果验证测试', () => this.testFinalResult());

    } catch (error) {
      console.error(`💥 测试执行失败: ${error.message}`);
    } finally {
      await this.cleanup();
      await this.generateReport();
    }
  }
}

// 运行测试
const tester = new ProductionStoryTester();
tester.run().catch(console.error);
