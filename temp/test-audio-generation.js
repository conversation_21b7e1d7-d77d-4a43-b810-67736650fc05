#!/usr/bin/env node

/**
 * StoryWeaver 音频生成功能专项测试
 * 专门测试TTS（文本转语音）生成流程
 */

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

// 使用真实的JWT令牌
function createRealJWT() {
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.zJbL-GqCWdWm1k22bY_j8x0ZE1BSMTevYPBdHcbmKuY';
}

// HTTP请求封装
async function makeRequest(method, endpoint, data = null, headers = {}) {
  const url = `${API_BASE}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const responseData = await response.json();
    
    return {
      ok: response.ok,
      status: response.status,
      data: responseData
    };
  } catch (error) {
    console.error(`请求失败 ${method} ${url}:`, error);
    throw error;
  }
}

// 音频生成测试配置
const AUDIO_TEST_CONFIG = {
  storyRequest: {
    characterName: "音频测试",
    characterAge: 6,
    characterTraits: ["勇敢", "善良"],
    theme: "adventure",
    setting: "魔法森林",
    style: "cartoon",
    voice: "gentle_female",
    customPrompt: "专门测试音频生成功能的故事"
  },
  maxPollingAttempts: 120, // 增加到120次，等待更长时间
  pollingInterval: 5000    // 5秒间隔
};

// 主测试函数
async function testAudioGeneration() {
  console.log('🎵 StoryWeaver 音频生成功能专项测试');
  console.log('============================================================');
  console.log(`📍 测试目标: ${API_BASE}`);
  console.log(`⏰ 开始时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('============================================================\n');

  try {
    // 步骤1: 创建专门用于音频测试的故事
    console.log('🔍 步骤 1: 创建音频测试故事');
    console.log('==================================================');
    
    const token = createRealJWT();
    const createResponse = await makeRequest('POST', '/api/stories', AUDIO_TEST_CONFIG.storyRequest, {
      'Authorization': `Bearer ${token}`
    });

    if (!createResponse.ok || !createResponse.data.success) {
      throw new Error(`故事创建失败: ${JSON.stringify(createResponse.data)}`);
    }

    const storyId = createResponse.data.data.storyId;
    console.log(`✅ 音频测试故事创建成功! ID: ${storyId}\n`);

    // 步骤2: 监控完整的生成流程，特别关注音频生成
    console.log('🔍 步骤 2: 监控完整生成流程（重点关注音频）');
    console.log('==================================================');
    
    let attempts = 0;
    let lastStatus = '';
    let textCompleted = false;
    let imagesCompleted = false;
    let audioCompleted = false;
    
    while (attempts < AUDIO_TEST_CONFIG.maxPollingAttempts) {
      attempts++;
      
      try {
        const response = await makeRequest('GET', `/api/stories/${storyId}`, null, {
          'Authorization': `Bearer ${token}`
        });

        if (response.ok && response.data.success) {
          const story = response.data.data;
          const currentStatus = story.status;
          
          // 检测状态变化
          if (currentStatus !== lastStatus) {
            console.log(`📈 状态变更: ${lastStatus} → ${currentStatus}`);
            lastStatus = currentStatus;
          }

          // 检查各个生成阶段的完成情况
          const newTextCompleted = story.pages && story.pages.length > 0;
          const newImagesCompleted = story.pages && story.pages.some(p => p.imageUrl);
          const newAudioCompleted = !!story.audioUrl;

          // 报告新完成的阶段
          if (newTextCompleted && !textCompleted) {
            console.log(`✅ 文本生成完成 - 共 ${story.pages.length} 页`);
            textCompleted = true;
          }
          
          if (newImagesCompleted && !imagesCompleted) {
            const imageCount = story.pages.filter(p => p.imageUrl).length;
            console.log(`✅ 图片生成完成 - 共 ${imageCount} 张图片`);
            imagesCompleted = true;
          }
          
          if (newAudioCompleted && !audioCompleted) {
            console.log(`🎵 音频生成完成! URL: ${story.audioUrl}`);
            audioCompleted = true;
          }

          // 如果故事完成，进行详细分析
          if (currentStatus === 'completed') {
            console.log('\n🎉 故事生成完成! 进行音频功能分析...');
            console.log('==================================================');
            
            // 音频生成结果分析
            if (story.audioUrl) {
              console.log(`✅ 音频生成成功:`);
              console.log(`   📁 音频URL: ${story.audioUrl}`);
              console.log(`   🔊 声音类型: ${story.voice}`);
              console.log(`   📝 文本长度: ${story.pages.reduce((total, page) => total + page.text.length, 0)} 字符`);
              
              // 验证音频URL的有效性
              try {
                const audioResponse = await fetch(story.audioUrl, { method: 'HEAD' });
                if (audioResponse.ok) {
                  const contentType = audioResponse.headers.get('content-type');
                  const contentLength = audioResponse.headers.get('content-length');
                  console.log(`   ✅ 音频文件验证成功:`);
                  console.log(`      - Content-Type: ${contentType}`);
                  console.log(`      - Content-Length: ${contentLength} bytes`);
                } else {
                  console.log(`   ⚠️ 音频URL无法访问: ${audioResponse.status}`);
                }
              } catch (error) {
                console.log(`   ⚠️ 音频URL验证失败: ${error.message}`);
              }
            } else {
              console.log(`❌ 音频生成失败 - audioUrl为空`);
            }

            // 完整流程总结
            console.log('\n📊 完整流程总结:');
            console.log(`   📝 文本生成: ${textCompleted ? '✅ 成功' : '❌ 失败'}`);
            console.log(`   🖼️ 图片生成: ${imagesCompleted ? '✅ 成功' : '❌ 失败'}`);
            console.log(`   🎵 音频生成: ${audioCompleted ? '✅ 成功' : '❌ 失败'}`);
            
            return {
              success: true,
              storyId,
              textCompleted,
              imagesCompleted,
              audioCompleted,
              audioUrl: story.audioUrl
            };
          }

          console.log(`⏳ 第 ${attempts}/${AUDIO_TEST_CONFIG.maxPollingAttempts} 次检查 - 状态: ${currentStatus}`);
          
          // 显示当前进度
          const progress = [];
          if (textCompleted) progress.push('文本✅');
          if (imagesCompleted) progress.push('图片✅');
          if (audioCompleted) progress.push('音频✅');
          if (progress.length > 0) {
            console.log(`   📋 已完成: ${progress.join(', ')}`);
          }

        } else {
          console.log(`❌ 获取故事状态失败: ${response.status}`);
        }

      } catch (error) {
        console.log(`⚠️ 第 ${attempts} 次检查出错:`, error.message);
      }

      // 等待下次检查
      if (attempts < AUDIO_TEST_CONFIG.maxPollingAttempts) {
        await new Promise(resolve => setTimeout(resolve, AUDIO_TEST_CONFIG.pollingInterval));
      }
    }

    // 如果超时仍未完成
    console.log(`\n⏰ 测试超时 (${AUDIO_TEST_CONFIG.maxPollingAttempts} 次检查)`);
    console.log('最终状态分析:');
    console.log(`   📝 文本生成: ${textCompleted ? '✅ 完成' : '❌ 未完成'}`);
    console.log(`   🖼️ 图片生成: ${imagesCompleted ? '✅ 完成' : '❌ 未完成'}`);
    console.log(`   🎵 音频生成: ${audioCompleted ? '✅ 完成' : '❌ 未完成'}`);

    return {
      success: false,
      reason: 'timeout',
      storyId,
      textCompleted,
      imagesCompleted,
      audioCompleted
    };

  } catch (error) {
    console.error('\n❌ 音频生成测试失败:', error);
    return {
      success: false,
      reason: 'error',
      error: error.message
    };
  }
}

// 运行测试
if (require.main === module) {
  testAudioGeneration()
    .then(result => {
      console.log('\n🏁 测试完成');
      console.log('============================================================');
      console.log(`⏰ 结束时间: ${new Date().toLocaleString('zh-CN')}`);
      console.log(`📊 测试结果: ${result.success ? '✅ 成功' : '❌ 失败'}`);
      
      if (result.success && result.audioCompleted) {
        console.log('🎵 音频生成功能验证: ✅ 正常工作');
      } else if (!result.audioCompleted) {
        console.log('🎵 音频生成功能验证: ❌ 需要进一步检查');
      }
      
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('💥 测试执行失败:', error);
      process.exit(1);
    });
}

module.exports = { testAudioGeneration };
