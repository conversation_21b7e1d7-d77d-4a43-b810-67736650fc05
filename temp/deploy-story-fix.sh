#!/bin/bash

# 故事生成进度修复部署脚本
# 解决进度卡在0%的问题

echo "🚀 开始部署故事生成进度修复..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ] && [ ! -d "backend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 修复内容:"
echo "1. ✅ 统一前后端字段名: voiceId → voice"
echo "2. ✅ 修复字段映射不一致问题"
echo "3. ✅ 添加传统模式WebSocket进度广播"
echo "4. ✅ 改进Durable Objects错误处理"
echo "5. ✅ 添加详细的进度更新机制"
echo ""

# 1. 构建前端
echo "🔨 构建前端..."
cd frontend
if npm run build; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

# 2. 同步到生产版本
echo "📦 同步修改到生产版本..."
if [ -d "frontend-production" ]; then
    # 同步关键修改的文件
    cp frontend/src/types/story.ts frontend-production/src/types/
    cp frontend/src/components/features/StoryCreator.tsx frontend-production/src/components/features/
    cp frontend/src/components/features/story-creator/StyleConfiguration.tsx frontend-production/src/components/features/story-creator/
    cp frontend/src/components/features/story-creator/StoryPreview.tsx frontend-production/src/components/features/story-creator/
    cp frontend/src/services/stories.ts frontend-production/src/services/
    echo "✅ 文件同步完成"
else
    echo "⚠️  警告: frontend-production 目录不存在"
fi

# 3. 构建生产版本
echo "🔨 构建生产版本..."
cd frontend-production
if npm run build; then
    echo "✅ 生产版本构建成功"
else
    echo "❌ 生产版本构建失败"
    exit 1
fi
cd ..

# 4. 部署后端
echo "🚀 部署后端..."
cd backend
if npx wrangler deploy --env production; then
    echo "✅ 后端部署成功"
else
    echo "❌ 后端部署失败"
    exit 1
fi
cd ..

# 5. 部署前端
echo "🚀 部署前端..."
cd frontend-production
if npx wrangler pages deploy dist --project-name storyweaver; then
    echo "✅ 前端部署成功"
else
    echo "❌ 前端部署失败"
    exit 1
fi
cd ..

echo ""
echo "🎉 部署完成!"
echo "=================================="
echo "📊 修复验证清单:"
echo "□ 访问 https://storyweaver.pages.dev"
echo "□ 创建新故事测试"
echo "□ 检查WebSocket连接状态"
echo "□ 验证进度更新是否正常"
echo "□ 测试传统模式降级机制"
echo ""
echo "🔍 调试信息:"
echo "- 前端: https://storyweaver.pages.dev"
echo "- 后端: https://storyweaver-api.stawky.workers.dev"
echo "- WebSocket: wss://storyweaver-api.stawky.workers.dev/ai-queue/{storyId}/websocket"
echo ""
echo "如果问题仍然存在，请检查浏览器控制台和网络面板。"
