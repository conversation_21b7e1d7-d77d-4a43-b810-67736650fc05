/**
 * StoryWeaver 真实API测试脚本
 * 用于测试实际部署的API服务
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 测试配置
const CONFIG = {
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8787',
  TIMEOUT: 30000,
  MAX_RETRIES: 3,
  POLL_INTERVAL: 5000,
  MAX_POLL_TIME: 300000 // 5分钟
};

// HTTP请求工具
class HttpClient {
  static async request(method, url, data = null, headers = {}) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'StoryWeaver-Test/1.0',
          ...headers
        },
        timeout: CONFIG.TIMEOUT
      };
      
      if (data) {
        const jsonData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(jsonData);
      }
      
      const req = client.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          try {
            const parsedData = responseData ? JSON.parse(responseData) : {};
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: parsedData,
              ok: res.statusCode >= 200 && res.statusCode < 300
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              data: responseData,
              ok: res.statusCode >= 200 && res.statusCode < 300
            });
          }
        });
      });
      
      req.on('error', (error) => {
        reject(new Error(`Request failed: ${error.message}`));
      });
      
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
      
      if (data) {
        req.write(JSON.stringify(data));
      }
      
      req.end();
    });
  }
  
  static async get(url, headers = {}) {
    return this.request('GET', url, null, headers);
  }
  
  static async post(url, data, headers = {}) {
    return this.request('POST', url, data, headers);
  }
}

// 测试工具
class TestRunner {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }
  
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': '📝',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️',
      'progress': '🔄'
    }[type] || '📝';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }
  
  async test(name, testFn) {
    this.log(`开始测试: ${name}`, 'progress');
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'PASS',
        duration,
        result
      });
      
      this.log(`测试通过: ${name} (${duration}ms)`, 'success');
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      this.results.push({
        name,
        status: 'FAIL',
        duration,
        error: error.message
      });
      
      this.log(`测试失败: ${name} - ${error.message} (${duration}ms)`, 'error');
      throw error;
    }
  }
  
  generateReport() {
    const totalTime = Date.now() - this.startTime;
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 测试报告');
    console.log('='.repeat(80));
    console.log(`总测试数: ${this.results.length}`);
    console.log(`通过: ${passed}`);
    console.log(`失败: ${failed}`);
    console.log(`总耗时: ${Math.round(totalTime / 1000)}秒`);
    console.log('='.repeat(80));
    
    this.results.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.name} (${result.duration}ms)`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    return failed === 0;
  }
}

// 具体测试用例
class StoryAPITests {
  constructor(baseUrl) {
    this.baseUrl = baseUrl;
    this.runner = new TestRunner();
  }
  
  async runAll() {
    this.runner.log(`开始测试 API: ${this.baseUrl}`, 'info');
    
    try {
      // 1. 健康检查
      await this.runner.test('API健康检查', () => this.testHealth());
      
      // 2. 配置数据测试
      await this.runner.test('获取故事主题', () => this.testThemes());
      await this.runner.test('获取绘画风格', () => this.testStyles());
      await this.runner.test('获取声音类型', () => this.testVoices());
      
      // 3. 故事创建和生成测试
      const storyId = await this.runner.test('创建故事', () => this.testCreateStory());
      
      if (storyId) {
        await this.runner.test('监控故事生成', () => this.testStoryGeneration(storyId));
        await this.runner.test('获取完整故事', () => this.testGetStory(storyId));
      }
      
      // 4. 错误处理测试
      await this.runner.test('无效请求处理', () => this.testInvalidRequest());
      
    } catch (error) {
      this.runner.log(`测试过程中断: ${error.message}`, 'error');
    }
    
    return this.runner.generateReport();
  }
  
  async testHealth() {
    const response = await HttpClient.get(`${this.baseUrl}/health`);
    
    if (!response.ok) {
      throw new Error(`健康检查失败: HTTP ${response.status}`);
    }
    
    if (!response.data.success) {
      throw new Error('健康检查返回失败状态');
    }
    
    return response.data;
  }
  
  async testThemes() {
    const response = await HttpClient.get(`${this.baseUrl}/api/stories/themes`);
    
    if (!response.ok) {
      throw new Error(`获取主题失败: HTTP ${response.status}`);
    }
    
    if (!response.data.success || !Array.isArray(response.data.data)) {
      throw new Error('主题数据格式不正确');
    }
    
    if (response.data.data.length === 0) {
      throw new Error('没有可用的主题');
    }
    
    return response.data.data;
  }
  
  async testStyles() {
    const response = await HttpClient.get(`${this.baseUrl}/api/stories/styles`);
    
    if (!response.ok) {
      throw new Error(`获取风格失败: HTTP ${response.status}`);
    }
    
    if (!response.data.success || !Array.isArray(response.data.data)) {
      throw new Error('风格数据格式不正确');
    }
    
    return response.data.data;
  }
  
  async testVoices() {
    const response = await HttpClient.get(`${this.baseUrl}/api/stories/voices`);
    
    if (!response.ok) {
      throw new Error(`获取声音失败: HTTP ${response.status}`);
    }
    
    if (!response.data.success || !Array.isArray(response.data.data)) {
      throw new Error('声音数据格式不正确');
    }
    
    return response.data.data;
  }
  
  async testCreateStory() {
    const storyRequest = {
      characterName: '测试小明',
      characterAge: 6,
      characterTraits: ['勇敢', '善良', '聪明'],
      theme: 'adventure',
      setting: '神秘森林',
      style: 'cartoon',
      voice: 'gentle_female',
      customPrompt: '这是一个API测试故事'
    };
    
    const response = await HttpClient.post(`${this.baseUrl}/api/stories`, storyRequest);
    
    if (!response.ok) {
      throw new Error(`创建故事失败: HTTP ${response.status} - ${JSON.stringify(response.data)}`);
    }
    
    if (!response.data.success || !response.data.data.storyId) {
      throw new Error('故事创建响应格式不正确');
    }
    
    return response.data.data.storyId;
  }
  
  async testStoryGeneration(storyId) {
    const maxAttempts = Math.ceil(CONFIG.MAX_POLL_TIME / CONFIG.POLL_INTERVAL);
    let attempts = 0;
    
    while (attempts < maxAttempts) {
      const response = await HttpClient.get(`${this.baseUrl}/api/stories/${storyId}/status`);
      
      if (!response.ok) {
        throw new Error(`获取生成状态失败: HTTP ${response.status}`);
      }
      
      if (!response.data.success) {
        throw new Error('状态查询返回失败');
      }
      
      const status = response.data.data;
      this.runner.log(`生成进度: ${JSON.stringify(status.progress)} (剩余: ${status.estimatedTimeRemaining}秒)`, 'progress');
      
      if (status.status === 'completed') {
        this.runner.log('故事生成完成!', 'success');
        return status;
      } else if (status.status === 'failed') {
        throw new Error('故事生成失败');
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, CONFIG.POLL_INTERVAL));
    }
    
    throw new Error('故事生成超时');
  }
  
  async testGetStory(storyId) {
    const response = await HttpClient.get(`${this.baseUrl}/api/stories/${storyId}`);
    
    if (!response.ok) {
      throw new Error(`获取故事失败: HTTP ${response.status}`);
    }
    
    if (!response.data.success) {
      throw new Error('获取故事返回失败状态');
    }
    
    const story = response.data.data;
    
    // 验证故事数据完整性
    const requiredFields = ['id', 'title', 'characterName', 'pages', 'status'];
    for (const field of requiredFields) {
      if (!story[field]) {
        throw new Error(`故事缺少必需字段: ${field}`);
      }
    }
    
    if (!Array.isArray(story.pages) || story.pages.length === 0) {
      throw new Error('故事页面数据无效');
    }
    
    // 统计多媒体资源
    const stats = {
      pages: story.pages.length,
      hasAudio: !!story.audioUrl,
      hasCover: !!story.coverImageUrl,
      imagesCount: story.pages.filter(p => p.imageUrl).length
    };
    
    this.runner.log(`故事统计: ${JSON.stringify(stats)}`, 'info');
    
    return story;
  }
  
  async testInvalidRequest() {
    // 测试无效的故事创建请求
    const invalidRequest = {
      characterName: '', // 空名字
      characterAge: -1,  // 无效年龄
    };
    
    const response = await HttpClient.post(`${this.baseUrl}/api/stories`, invalidRequest);
    
    // 应该返回错误
    if (response.ok && response.data.success) {
      throw new Error('应该拒绝无效请求但没有');
    }
    
    return response.data;
  }
}

// 主函数
async function main() {
  console.log('🚀 StoryWeaver API 真实环境测试');
  console.log('='.repeat(80));
  
  // 解析命令行参数
  const args = process.argv.slice(2);
  let apiUrl = CONFIG.API_BASE_URL;
  
  const urlIndex = args.indexOf('--url');
  if (urlIndex !== -1 && args[urlIndex + 1]) {
    apiUrl = args[urlIndex + 1];
  }
  
  if (args.includes('--help')) {
    console.log(`
用法: node test-real-api.js [选项]

选项:
  --url <url>    指定API基础URL (默认: ${CONFIG.API_BASE_URL})
  --help         显示帮助信息

示例:
  node test-real-api.js
  node test-real-api.js --url https://api.storyweaver.com
  node test-real-api.js --url http://localhost:8787
`);
    process.exit(0);
  }
  
  console.log(`📍 测试目标: ${apiUrl}`);
  console.log(`⏱️  超时设置: ${CONFIG.TIMEOUT}ms`);
  console.log(`🔄 轮询间隔: ${CONFIG.POLL_INTERVAL}ms`);
  console.log('='.repeat(80));
  
  const tester = new StoryAPITests(apiUrl);
  
  try {
    const success = await tester.runAll();
    
    if (success) {
      console.log('\n🎉 所有测试通过！API运行正常。');
      process.exit(0);
    } else {
      console.log('\n⚠️  部分测试失败，请检查API服务。');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n❌ 测试执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序启动失败:', error);
    process.exit(1);
  });
}

module.exports = { StoryAPITests, HttpClient, CONFIG };