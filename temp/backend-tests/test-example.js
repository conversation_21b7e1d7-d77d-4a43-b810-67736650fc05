/**
 * StoryWeaver 测试使用示例
 * 演示如何使用测试脚本验证故事创作链路
 */

const { runLocalTests } = require('./test-local-pipeline');
const { StoryAPITests } = require('./test-real-api');

async function demonstrateTests() {
  console.log('🎯 StoryWeaver 测试演示');
  console.log('='.repeat(60));
  
  try {
    // 1. 演示本地测试
    console.log('\n📍 步骤 1: 运行本地管道测试');
    console.log('这将测试核心业务逻辑，不需要启动服务器');
    console.log('-'.repeat(40));
    
    await runLocalTests();
    
    console.log('\n✅ 本地测试完成！');
    
    // 2. 演示API测试配置
    console.log('\n📍 步骤 2: API测试配置示例');
    console.log('-'.repeat(40));
    
    const apiUrls = [
      'http://localhost:8787',
      'https://api-staging.storyweaver.com',
      'https://api.storyweaver.com'
    ];
    
    console.log('可测试的API环境:');
    apiUrls.forEach((url, index) => {
      const env = ['本地开发', '预发布', '生产'][index];
      console.log(`  ${index + 1}. ${env}: ${url}`);
    });
    
    // 3. 演示测试命令
    console.log('\n📍 步骤 3: 常用测试命令');
    console.log('-'.repeat(40));
    
    const commands = [
      {
        cmd: './run-tests.sh local',
        desc: '运行本地逻辑测试'
      },
      {
        cmd: './run-tests.sh mock',
        desc: '运行模拟API测试'
      },
      {
        cmd: './run-tests.sh real --url http://localhost:8787',
        desc: '测试本地API服务'
      },
      {
        cmd: './run-tests.sh all --verbose',
        desc: '运行所有测试（详细输出）'
      },
      {
        cmd: 'node test-real-api.js --url https://api.storyweaver.com',
        desc: '直接测试生产API'
      }
    ];
    
    commands.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.desc}`);
      console.log(`     ${item.cmd}`);
      console.log('');
    });
    
    // 4. 演示测试场景
    console.log('📍 步骤 4: 典型测试场景');
    console.log('-'.repeat(40));
    
    const scenarios = [
      {
        scenario: '开发阶段',
        steps: [
          '1. 运行本地测试验证逻辑: ./run-tests.sh local',
          '2. 启动开发服务器: npm run dev',
          '3. 测试本地API: ./run-tests.sh real --url http://localhost:8787'
        ]
      },
      {
        scenario: '部署前验证',
        steps: [
          '1. 运行完整测试套件: ./run-tests.sh all',
          '2. 测试预发布环境: ./run-tests.sh real --url https://api-staging.storyweaver.com',
          '3. 检查测试报告: cat test-reports/latest.md'
        ]
      },
      {
        scenario: '生产环境监控',
        steps: [
          '1. 定期健康检查: curl https://api.storyweaver.com/health',
          '2. 功能验证: ./run-tests.sh real --url https://api.storyweaver.com',
          '3. 性能监控: 查看响应时间和成功率'
        ]
      }
    ];
    
    scenarios.forEach((item, index) => {
      console.log(`\n${index + 1}. ${item.scenario}:`);
      item.steps.forEach(step => {
        console.log(`   ${step}`);
      });
    });
    
    // 5. 演示故障排除
    console.log('\n📍 步骤 5: 常见问题排除');
    console.log('-'.repeat(40));
    
    const troubleshooting = [
      {
        problem: '连接失败',
        solution: '检查服务是否启动: npm run dev'
      },
      {
        problem: '测试超时',
        solution: '增加超时时间: --timeout 600000'
      },
      {
        problem: 'API密钥错误',
        solution: '设置环境变量: export GEMINI_API_KEY="your-key"'
      },
      {
        problem: '依赖缺失',
        solution: '安装依赖: npm install'
      }
    ];
    
    troubleshooting.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.problem}`);
      console.log(`     解决方案: ${item.solution}`);
      console.log('');
    });
    
    console.log('='.repeat(60));
    console.log('🎉 测试演示完成！');
    console.log('💡 提示: 查看 TESTING.md 获取详细文档');
    
  } catch (error) {
    console.error('❌ 演示过程中出现错误:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  demonstrateTests().catch(error => {
    console.error('❌ 演示启动失败:', error);
    process.exit(1);
  });
}

module.exports = { demonstrateTests };