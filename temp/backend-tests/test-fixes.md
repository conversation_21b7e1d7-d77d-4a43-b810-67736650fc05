# StoryWeaver 修复验证测试指南

## 测试环境准备

### 1. 启动开发服务器

```bash
cd backend
npm run dev
```

### 2. 验证服务器启动

访问健康检查端点：
```bash
curl http://localhost:8787/health
```

预期响应：
```json
{
  "status": "healthy",
  "timestamp": "2024-07-02T...",
  "version": "1.0.0",
  "services": {
    "api": "healthy",
    "database": "healthy", 
    "ai": "healthy"
  }
}
```

## 测试1: 数据一致性检查功能

### 测试步骤

1. **访问数据一致性检查端点**：
```bash
curl http://localhost:8787/admin/check-data-consistency
```

2. **预期成功响应**：
```json
{
  "success": true,
  "message": "Data consistency check completed successfully",
  "report": {
    "totalStories": 10,
    "completedStories": 8,
    "storiesWithAudio": 6,
    "storiesWithCover": 7
  },
  "checkResult": {
    "totalStories": 8,
    "totalIssues": 3,
    "fixedIssues": 2,
    "remainingIssues": 1,
    "details": [...]
  }
}
```

3. **检查服务器日志**：
应该看到详细的检查过程日志：
```
🔍 [DataConsistencyChecker] 开始检查数据一致性...
📊 [DataConsistencyChecker] 找到 X 个已完成的故事
📖 [DataConsistencyChecker] 检查故事: XXX (story-id)
   ✅ 数据一致性正常
📋 [DataConsistencyChecker] 检查完成:
   总故事数: X
   总问题数: X
   已修复: X
   剩余问题: X
```

### 错误情况测试

1. **生产环境访问限制**：
```bash
# 如果ENVIRONMENT=production，应该返回403
curl http://localhost:8787/admin/check-data-consistency
```

预期响应：
```json
{
  "error": "This endpoint is only available in development environment"
}
```

## 测试2: 图片生成错误处理

### 测试步骤

1. **创建新故事**：
```bash
curl -X POST http://localhost:8787/api/stories \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "characterName": "测试角色",
    "characterAge": 8,
    "characterTraits": ["勇敢", "善良"],
    "theme": "adventure",
    "setting": "森林",
    "style": "cartoon",
    "voice": "gentle_female"
  }'
```

2. **观察服务器日志**：
应该看到改进的图片生成日志：
```
🎨 [GeminiService] 开始生成 X 张图片，风格: cartoon
🖼️ [GeminiService] 生成第 1/X 张图片
   提示词: XXX...
   🔄 [GeminiService] 第 1 张图片，尝试 1/2
   ✅ [GeminiService] 第 1 张图片生成成功，数据大小: XXXX 字符
🎯 [GeminiService] 图片生成完成 - 成功: X, 失败: X, 总计: X
```

3. **模拟错误情况**：
如果图片生成失败，应该看到：
```
❌ [GeminiService] 第 X 张图片生成失败: API请求频率限制
   ⚠️ [GeminiService] 第 X 张图片第 1 次尝试失败: API请求频率限制
   ⏳ [GeminiService] 2 秒后重试...
   🔄 [GeminiService] 第 X 张图片，尝试 2/2
🔄 [GeminiService] 使用占位符图片替代第 X 张图片
```

## 测试3: 存储服务错误处理

### 测试步骤

1. **检查上传日志**：
在故事生成过程中，应该看到详细的上传日志：
```
[StorageService] 开始上传图片: images/story-id_page_1.jpg
[StorageService] 从data URL提取base64数据，长度: XXXX
[StorageService] base64转换完成，buffer大小: XXXX bytes
[StorageService] 图片上传成功: https://assets.storyweaver.com/images/story-id_page_1.jpg
```

2. **验证URL格式**：
确保生成的URL格式正确：
- 图片: `https://assets.storyweaver.com/images/story-id_page_X.jpg`
- 音频: `https://assets.storyweaver.com/audio/story-id.mp3`

## 验证清单

### ✅ 功能验证

- [ ] 数据一致性检查端点正常响应
- [ ] 返回详细的检查报告和结果
- [ ] 服务器日志格式清晰易读
- [ ] 生产环境访问限制正常工作

### ✅ 图片生成验证

- [ ] 图片生成过程日志详细
- [ ] 重试机制正常工作
- [ ] 超时控制有效
- [ ] 错误分类准确
- [ ] 占位符图片正常生成

### ✅ 存储服务验证

- [ ] data URL格式正确处理
- [ ] base64数据转换成功
- [ ] R2上传过程正常
- [ ] URL格式验证有效
- [ ] 错误处理完善

### ✅ 兼容性验证

- [ ] 无Node.js语法错误
- [ ] ES6模块导入正常
- [ ] Cloudflare Workers环境兼容
- [ ] 动态导入路径正确

## 故障排除

### 常见问题

1. **"module is not defined" 错误**：
   - 检查是否还有Node.js语法残留
   - 确认使用ES6模块语法

2. **导入路径错误**：
   - 确认相对路径正确
   - 检查文件是否存在

3. **图片生成超时**：
   - 检查网络连接
   - 验证API密钥配置
   - 查看Gemini API状态

4. **R2上传失败**：
   - 检查R2存储桶配置
   - 验证权限设置
   - 确认环境变量正确

## 成功标准

修复成功的标准：
1. 数据一致性检查端点正常工作，无模块导入错误
2. 图片生成过程有详细日志和错误处理
3. 重试机制和超时控制正常工作
4. 所有代码兼容Cloudflare Workers环境
5. 错误信息清晰，便于调试
