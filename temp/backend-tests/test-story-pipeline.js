/**
 * StoryWeaver 故事创作链路完整测试脚本
 * 测试从用户输入到故事生成完成的整个流程
 */

const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  // API 基础URL
  API_BASE_URL: process.env.API_BASE_URL || 'http://localhost:8787',
  
  // 测试用户信息
  TEST_USER: {
    id: 'test-user-' + Date.now(),
    email: '<EMAIL>',
    name: '测试用户',
    credits: 10
  },
  
  // 测试故事参数
  TEST_STORY_REQUEST: {
    characterName: '小明',
    characterAge: 6,
    characterTraits: ['勇敢', '善良', '好奇'],
    theme: 'adventure',
    setting: '神秘的森林',
    style: 'cartoon',
    voice: 'gentle_female',
    customPrompt: '希望故事能教会孩子勇敢面对困难'
  },
  
  // 超时设置
  TIMEOUTS: {
    API_CALL: 30000,        // 30秒
    STORY_GENERATION: 300000, // 5分钟
    POLLING_INTERVAL: 5000   // 5秒轮询间隔
  }
};

// 测试结果记录
const testResults = {
  startTime: new Date(),
  tests: [],
  summary: {
    total: 0,
    passed: 0,
    failed: 0,
    errors: []
  }
};

// 工具函数
class TestUtils {
  static async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  static async makeRequest(method, endpoint, data = null, headers = {}) {
    const url = `${TEST_CONFIG.API_BASE_URL}${endpoint}`;
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (data) {
      options.body = JSON.stringify(data);
    }
    
    console.log(`🌐 ${method} ${url}`);
    if (data) {
      console.log(`📤 Request data:`, JSON.stringify(data, null, 2));
    }
    
    try {
      // 在Node.js环境中模拟fetch
      const response = await this.mockFetch(url, options);
      const responseData = await response.json();
      
      console.log(`📥 Response (${response.status}):`, JSON.stringify(responseData, null, 2));
      
      return {
        ok: response.ok,
        status: response.status,
        data: responseData
      };
    } catch (error) {
      console.error(`❌ Request failed:`, error.message);
      throw error;
    }
  }
  
  // 模拟fetch请求（用于测试环境）
  static async mockFetch(url, options) {
    const urlObj = new URL(url);
    const path = urlObj.pathname;
    const method = options.method || 'GET';
    
    // 模拟不同的API响应
    if (path === '/health') {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          status: 'healthy',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        })
      };
    }
    
    if (path === '/api/stories/themes') {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: [
            { id: 'adventure', name: '冒险', description: '充满刺激的冒险故事' },
            { id: 'friendship', name: '友谊', description: '关于友谊的温暖故事' },
            { id: 'family', name: '家庭', description: '温馨的家庭故事' }
          ]
        })
      };
    }
    
    if (path === '/api/stories/styles') {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: [
            { id: 'cartoon', name: '卡通', description: '可爱的卡通风格' },
            { id: 'watercolor', name: '水彩', description: '温柔的水彩画风' },
            { id: 'realistic', name: '写实', description: '真实的写实风格' }
          ]
        })
      };
    }
    
    if (path === '/api/stories/voices') {
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: [
            { id: 'gentle_female', name: '温柔女声', description: '温柔亲切的女性声音' },
            { id: 'warm_male', name: '温暖男声', description: '温暖有力的男性声音' },
            { id: 'child_friendly', name: '儿童友好', description: '适合儿童的活泼声音' }
          ]
        })
      };
    }
    
    if (path === '/api/stories' && method === 'POST') {
      const storyId = 'story_' + Date.now();
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: {
            storyId: storyId,
            status: {
              storyId: storyId,
              status: 'generating',
              progress: { text: false, images: false, audio: false },
              estimatedTimeRemaining: 120
            }
          },
          message: '故事创建成功，正在生成中...'
        })
      };
    }
    
    if (path.startsWith('/api/stories/') && path.endsWith('/status')) {
      const storyId = path.split('/')[3];
      // 模拟生成进度
      const elapsed = Date.now() - parseInt(storyId.split('_')[1]);
      let status, progress;
      
      if (elapsed < 30000) { // 前30秒：生成文本
        status = 'generating';
        progress = { text: false, images: false, audio: false };
      } else if (elapsed < 90000) { // 30-90秒：生成图片
        status = 'generating';
        progress = { text: true, images: false, audio: false };
      } else if (elapsed < 120000) { // 90-120秒：生成音频
        status = 'generating';
        progress = { text: true, images: true, audio: false };
      } else { // 120秒后：完成
        status = 'completed';
        progress = { text: true, images: true, audio: true };
      }
      
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: {
            storyId: storyId,
            status: status,
            progress: progress,
            estimatedTimeRemaining: Math.max(0, 120 - Math.floor(elapsed / 1000))
          }
        })
      };
    }
    
    if (path.startsWith('/api/stories/') && !path.includes('/status')) {
      const storyId = path.split('/')[3];
      return {
        ok: true,
        status: 200,
        json: async () => ({
          success: true,
          data: {
            id: storyId,
            title: `${TEST_CONFIG.TEST_STORY_REQUEST.characterName}的冒险故事`,
            characterName: TEST_CONFIG.TEST_STORY_REQUEST.characterName,
            characterAge: TEST_CONFIG.TEST_STORY_REQUEST.characterAge,
            characterTraits: TEST_CONFIG.TEST_STORY_REQUEST.characterTraits,
            theme: TEST_CONFIG.TEST_STORY_REQUEST.theme,
            setting: TEST_CONFIG.TEST_STORY_REQUEST.setting,
            style: TEST_CONFIG.TEST_STORY_REQUEST.style,
            voice: TEST_CONFIG.TEST_STORY_REQUEST.voice,
            pages: [
              {
                pageNumber: 1,
                text: '从前，有一个叫小明的小男孩，他非常勇敢和善良...',
                imageUrl: `https://assets.storyweaver.com/images/${storyId}_page_1.jpg`,
                imagePrompt: '一个勇敢的小男孩站在神秘森林的入口'
              },
              {
                pageNumber: 2,
                text: '小明走进了神秘的森林，发现了许多奇妙的事物...',
                imageUrl: `https://assets.storyweaver.com/images/${storyId}_page_2.jpg`,
                imagePrompt: '小男孩在森林中探索，周围有神奇的植物和动物'
              }
            ],
            audioUrl: `https://assets.storyweaver.com/audio/${storyId}.mp3`,
            coverImageUrl: `https://assets.storyweaver.com/images/${storyId}_page_1.jpg`,
            status: 'completed',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        })
      };
    }
    
    // 默认404响应
    return {
      ok: false,
      status: 404,
      json: async () => ({
        success: false,
        error: 'API endpoint not found',
        code: 'NOT_FOUND'
      })
    };
  }
  
  static logTest(name, status, details = '') {
    const test = {
      name,
      status,
      details,
      timestamp: new Date().toISOString()
    };
    
    testResults.tests.push(test);
    testResults.summary.total++;
    
    if (status === 'PASS') {
      testResults.summary.passed++;
      console.log(`✅ ${name}`);
    } else {
      testResults.summary.failed++;
      testResults.summary.errors.push(`${name}: ${details}`);
      console.log(`❌ ${name}: ${details}`);
    }
    
    if (details) {
      console.log(`   ${details}`);
    }
  }
  
  static generateTestReport() {
    const endTime = new Date();
    const duration = endTime - testResults.startTime;
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 测试报告');
    console.log('='.repeat(80));
    console.log(`🕐 测试时间: ${testResults.startTime.toLocaleString()} - ${endTime.toLocaleString()}`);
    console.log(`⏱️  总耗时: ${Math.round(duration / 1000)}秒`);
    console.log(`📈 测试结果: ${testResults.summary.passed}/${testResults.summary.total} 通过`);
    
    if (testResults.summary.failed > 0) {
      console.log('\n❌ 失败的测试:');
      testResults.summary.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    // 保存详细报告到文件
    const reportPath = path.join(__dirname, 'test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      ...testResults,
      endTime,
      duration
    }, null, 2));
    
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
    
    return testResults.summary.failed === 0;
  }
}

// 测试用例
class StoryPipelineTests {
  
  // 1. 健康检查测试
  static async testHealthCheck() {
    console.log('\n🔍 测试 1: API健康检查');
    
    try {
      const response = await TestUtils.makeRequest('GET', '/health');
      
      if (response.ok && response.data.success) {
        TestUtils.logTest('API健康检查', 'PASS', `服务状态: ${response.data.status}`);
      } else {
        TestUtils.logTest('API健康检查', 'FAIL', `响应异常: ${JSON.stringify(response.data)}`);
      }
    } catch (error) {
      TestUtils.logTest('API健康检查', 'FAIL', `请求失败: ${error.message}`);
    }
  }
  
  // 2. 配置数据获取测试
  static async testConfigurationData() {
    console.log('\n🔍 测试 2: 配置数据获取');
    
    const endpoints = [
      { path: '/api/stories/themes', name: '故事主题' },
      { path: '/api/stories/styles', name: '绘画风格' },
      { path: '/api/stories/voices', name: '声音类型' }
    ];
    
    for (const endpoint of endpoints) {
      try {
        const response = await TestUtils.makeRequest('GET', endpoint.path);
        
        if (response.ok && response.data.success && Array.isArray(response.data.data)) {
          TestUtils.logTest(`获取${endpoint.name}`, 'PASS', `获取到 ${response.data.data.length} 个选项`);
        } else {
          TestUtils.logTest(`获取${endpoint.name}`, 'FAIL', '数据格式不正确');
        }
      } catch (error) {
        TestUtils.logTest(`获取${endpoint.name}`, 'FAIL', error.message);
      }
    }
  }
  
  // 3. 故事创建测试
  static async testStoryCreation() {
    console.log('\n🔍 测试 3: 故事创建');
    
    try {
      const response = await TestUtils.makeRequest('POST', '/api/stories', TEST_CONFIG.TEST_STORY_REQUEST);
      
      if (response.ok && response.data.success && response.data.data.storyId) {
        const storyId = response.data.data.storyId;
        TestUtils.logTest('故事创建', 'PASS', `故事ID: ${storyId}`);
        return storyId;
      } else {
        TestUtils.logTest('故事创建', 'FAIL', `创建失败: ${JSON.stringify(response.data)}`);
        return null;
      }
    } catch (error) {
      TestUtils.logTest('故事创建', 'FAIL', error.message);
      return null;
    }
  }
  
  // 4. 故事生成进度监控测试
  static async testStoryGenerationProgress(storyId) {
    console.log('\n🔍 测试 4: 故事生成进度监控');
    
    if (!storyId) {
      TestUtils.logTest('生成进度监控', 'SKIP', '没有有效的故事ID');
      return false;
    }
    
    const maxAttempts = Math.ceil(TEST_CONFIG.TIMEOUTS.STORY_GENERATION / TEST_CONFIG.TIMEOUTS.POLLING_INTERVAL);
    let attempts = 0;
    let lastProgress = null;
    
    while (attempts < maxAttempts) {
      try {
        const response = await TestUtils.makeRequest('GET', `/api/stories/${storyId}/status`);
        
        if (response.ok && response.data.success) {
          const status = response.data.data;
          
          // 检查进度是否有更新
          if (!lastProgress || JSON.stringify(status.progress) !== JSON.stringify(lastProgress)) {
            const progressText = Object.entries(status.progress)
              .map(([key, value]) => `${key}: ${value ? '✅' : '⏳'}`)
              .join(', ');
            
            console.log(`   📊 进度更新: ${progressText} (剩余: ${status.estimatedTimeRemaining}秒)`);
            lastProgress = status.progress;
          }
          
          // 检查是否完成
          if (status.status === 'completed') {
            TestUtils.logTest('生成进度监控', 'PASS', `故事生成完成，耗时: ${attempts * 5}秒`);
            return true;
          } else if (status.status === 'failed') {
            TestUtils.logTest('生成进度监控', 'FAIL', '故事生成失败');
            return false;
          }
        } else {
          TestUtils.logTest('生成进度监控', 'FAIL', '无法获取生成状态');
          return false;
        }
        
        attempts++;
        await TestUtils.sleep(TEST_CONFIG.TIMEOUTS.POLLING_INTERVAL);
        
      } catch (error) {
        TestUtils.logTest('生成进度监控', 'FAIL', error.message);
        return false;
      }
    }
    
    TestUtils.logTest('生成进度监控', 'FAIL', '生成超时');
    return false;
  }
  
  // 5. 完整故事数据验证测试
  static async testCompleteStoryData(storyId) {
    console.log('\n🔍 测试 5: 完整故事数据验证');
    
    if (!storyId) {
      TestUtils.logTest('故事数据验证', 'SKIP', '没有有效的故事ID');
      return;
    }
    
    try {
      const response = await TestUtils.makeRequest('GET', `/api/stories/${storyId}`);
      
      if (response.ok && response.data.success) {
        const story = response.data.data;
        
        // 验证基本信息
        const requiredFields = ['id', 'title', 'characterName', 'characterAge', 'pages', 'status'];
        const missingFields = requiredFields.filter(field => !story[field]);
        
        if (missingFields.length > 0) {
          TestUtils.logTest('故事数据验证', 'FAIL', `缺少字段: ${missingFields.join(', ')}`);
          return;
        }
        
        // 验证页面数据
        if (!Array.isArray(story.pages) || story.pages.length === 0) {
          TestUtils.logTest('故事数据验证', 'FAIL', '故事页面数据无效');
          return;
        }
        
        // 验证每页数据
        for (let i = 0; i < story.pages.length; i++) {
          const page = story.pages[i];
          if (!page.text || !page.pageNumber) {
            TestUtils.logTest('故事数据验证', 'FAIL', `第${i+1}页数据不完整`);
            return;
          }
        }
        
        // 验证多媒体资源
        let mediaScore = 0;
        if (story.audioUrl) mediaScore++;
        if (story.coverImageUrl) mediaScore++;
        if (story.pages.some(page => page.imageUrl)) mediaScore++;
        
        const details = [
          `页数: ${story.pages.length}`,
          `音频: ${story.audioUrl ? '✅' : '❌'}`,
          `封面: ${story.coverImageUrl ? '✅' : '❌'}`,
          `插图: ${story.pages.filter(p => p.imageUrl).length}/${story.pages.length}`
        ].join(', ');
        
        TestUtils.logTest('故事数据验证', 'PASS', details);
        
      } else {
        TestUtils.logTest('故事数据验证', 'FAIL', '无法获取故事数据');
      }
    } catch (error) {
      TestUtils.logTest('故事数据验证', 'FAIL', error.message);
    }
  }
  
  // 6. 边界条件测试
  static async testEdgeCases() {
    console.log('\n🔍 测试 6: 边界条件测试');
    
    const edgeCases = [
      {
        name: '无效参数测试',
        data: { characterName: '', characterAge: 0 },
        expectFail: true
      },
      {
        name: '极端年龄测试',
        data: { ...TEST_CONFIG.TEST_STORY_REQUEST, characterAge: 15 },
        expectFail: false
      },
      {
        name: '长名字测试',
        data: { ...TEST_CONFIG.TEST_STORY_REQUEST, characterName: '非常非常非常长的角色名字测试' },
        expectFail: false
      },
      {
        name: '特殊字符测试',
        data: { ...TEST_CONFIG.TEST_STORY_REQUEST, characterName: '小明@#$%' },
        expectFail: false
      }
    ];
    
    for (const testCase of edgeCases) {
      try {
        const response = await TestUtils.makeRequest('POST', '/api/stories', testCase.data);
        
        if (testCase.expectFail) {
          if (!response.ok) {
            TestUtils.logTest(testCase.name, 'PASS', '正确拒绝了无效请求');
          } else {
            TestUtils.logTest(testCase.name, 'FAIL', '应该拒绝无效请求但没有');
          }
        } else {
          if (response.ok && response.data.success) {
            TestUtils.logTest(testCase.name, 'PASS', '正确处理了边界情况');
          } else {
            TestUtils.logTest(testCase.name, 'FAIL', '未能正确处理边界情况');
          }
        }
      } catch (error) {
        if (testCase.expectFail) {
          TestUtils.logTest(testCase.name, 'PASS', '正确抛出了错误');
        } else {
          TestUtils.logTest(testCase.name, 'FAIL', error.message);
        }
      }
    }
  }
  
  // 7. 性能测试
  static async testPerformance() {
    console.log('\n🔍 测试 7: 性能测试');
    
    const performanceTests = [
      {
        name: '并发创建测试',
        test: async () => {
          const promises = [];
          for (let i = 0; i < 3; i++) {
            promises.push(TestUtils.makeRequest('POST', '/api/stories', {
              ...TEST_CONFIG.TEST_STORY_REQUEST,
              characterName: `测试角色${i + 1}`
            }));
          }
          
          const startTime = Date.now();
          const results = await Promise.all(promises);
          const duration = Date.now() - startTime;
          
          const successCount = results.filter(r => r.ok).length;
          return {
            success: successCount === 3,
            details: `${successCount}/3 成功，耗时: ${duration}ms`
          };
        }
      },
      {
        name: '快速轮询测试',
        test: async () => {
          const storyId = 'test_story_' + Date.now();
          const promises = [];
          
          for (let i = 0; i < 5; i++) {
            promises.push(TestUtils.makeRequest('GET', `/api/stories/${storyId}/status`));
          }
          
          const startTime = Date.now();
          const results = await Promise.all(promises);
          const duration = Date.now() - startTime;
          
          const successCount = results.filter(r => r.ok).length;
          return {
            success: successCount === 5,
            details: `${successCount}/5 成功，耗时: ${duration}ms`
          };
        }
      }
    ];
    
    for (const perfTest of performanceTests) {
      try {
        const result = await perfTest.test();
        TestUtils.logTest(perfTest.name, result.success ? 'PASS' : 'FAIL', result.details);
      } catch (error) {
        TestUtils.logTest(perfTest.name, 'FAIL', error.message);
      }
    }
  }
}

// 主测试流程
async function runStoryPipelineTests() {
  console.log('🚀 开始 StoryWeaver 故事创作链路测试');
  console.log('='.repeat(80));
  console.log(`📍 测试环境: ${TEST_CONFIG.API_BASE_URL}`);
  console.log(`👤 测试用户: ${TEST_CONFIG.TEST_USER.name} (${TEST_CONFIG.TEST_USER.email})`);
  console.log(`📖 测试故事: ${TEST_CONFIG.TEST_STORY_REQUEST.characterName}的${TEST_CONFIG.TEST_STORY_REQUEST.theme}故事`);
  console.log('='.repeat(80));
  
  let storyId = null;
  
  try {
    // 执行所有测试
    await StoryPipelineTests.testHealthCheck();
    await StoryPipelineTests.testConfigurationData();
    
    storyId = await StoryPipelineTests.testStoryCreation();
    
    if (storyId) {
      const generationSuccess = await StoryPipelineTests.testStoryGenerationProgress(storyId);
      if (generationSuccess) {
        await StoryPipelineTests.testCompleteStoryData(storyId);
      }
    }
    
    await StoryPipelineTests.testEdgeCases();
    await StoryPipelineTests.testPerformance();
    
  } catch (error) {
    console.error('❌ 测试过程中发生严重错误:', error);
    TestUtils.logTest('测试执行', 'FAIL', error.message);
  }
  
  // 生成测试报告
  const allTestsPassed = TestUtils.generateTestReport();
  
  if (allTestsPassed) {
    console.log('\n🎉 所有测试通过！故事创作链路运行正常。');
    process.exit(0);
  } else {
    console.log('\n⚠️  部分测试失败，请检查上述错误信息。');
    process.exit(1);
  }
}

// 命令行参数处理
if (require.main === module) {
  // 处理命令行参数
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
StoryWeaver 故事创作链路测试脚本

用法:
  node test-story-pipeline.js [选项]

选项:
  --api-url <url>     指定API基础URL (默认: http://localhost:8787)
  --timeout <ms>      设置超时时间 (默认: 300000ms)
  --help, -h          显示帮助信息

示例:
  node test-story-pipeline.js
  node test-story-pipeline.js --api-url https://api.storyweaver.com
  node test-story-pipeline.js --timeout 600000
`);
    process.exit(0);
  }
  
  // 解析命令行参数
  const apiUrlIndex = args.indexOf('--api-url');
  if (apiUrlIndex !== -1 && args[apiUrlIndex + 1]) {
    TEST_CONFIG.API_BASE_URL = args[apiUrlIndex + 1];
  }
  
  const timeoutIndex = args.indexOf('--timeout');
  if (timeoutIndex !== -1 && args[timeoutIndex + 1]) {
    TEST_CONFIG.TIMEOUTS.STORY_GENERATION = parseInt(args[timeoutIndex + 1]);
  }
  
  // 运行测试
  runStoryPipelineTests().catch(error => {
    console.error('❌ 测试启动失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runStoryPipelineTests,
  TestUtils,
  StoryPipelineTests,
  TEST_CONFIG
};