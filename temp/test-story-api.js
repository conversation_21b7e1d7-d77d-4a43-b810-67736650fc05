const https = require('https');

const API_BASE = 'https://storyweaver-api.stawky.workers.dev';
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************.pyz9M2bz8E9hD1PmPm8W_LZjIXClgOyVCuOYaJwNdaM';

// HTTP请求工具函数
function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(API_BASE + path);
    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${JWT_TOKEN}`
      }
    };

    const req = https.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// 测试故事创建
async function testStoryCreation() {
  console.log('🧪 测试故事创建API...\n');

  const storyRequest = {
    title: "小兔子的冒险",
    characterName: "小明",
    characterAge: 5,
    characterTraits: ["勇敢", "好奇", "善良"],
    theme: "adventure",
    setting: "森林",
    style: "cartoon",
    voice: "warm",
    customPrompt: "一个关于勇气和友谊的故事"
  };

  try {
    console.log('📝 发送故事创建请求...');
    const response = await makeRequest('POST', '/api/stories', storyRequest);
    
    console.log(`📊 响应状态: ${response.status}`);
    console.log('📋 响应内容:', JSON.stringify(response.data, null, 2));

    if (response.status === 200 && response.data.success) {
      const storyId = response.data.data.storyId;
      console.log(`✅ 故事创建成功! ID: ${storyId}`);
      
      // 监控故事生成进度
      await monitorStoryProgress(storyId);
    } else {
      console.log('❌ 故事创建失败:', response.data.error || '未知错误');
    }

  } catch (error) {
    console.error('🚨 请求失败:', error.message);
  }
}

// 监控故事生成进度
async function monitorStoryProgress(storyId) {
  console.log(`\n🔍 开始监控故事 ${storyId} 的生成进度...`);

  const maxAttempts = 60; // 最多监控5分钟 (60 * 5秒)
  let attempts = 0;
  let lastStatus = '';

  while (attempts < maxAttempts) {
    try {
      const response = await makeRequest('GET', `/api/stories/${storyId}`);

      if (response.status === 200 && response.data.success) {
        const story = response.data.data;

        // 只在状态变化时输出详细信息
        if (story.status !== lastStatus) {
          console.log(`\n📈 状态变化 [${attempts + 1}/${maxAttempts}]: ${lastStatus} → ${story.status}`);
          lastStatus = story.status;

          // 输出详细进度信息
          if (story.pages && story.pages.length > 0) {
            console.log(`📚 已生成页面数: ${story.pages.length}`);
            story.pages.forEach((page, index) => {
              console.log(`  📄 第${index + 1}页:`);
              if (page.text) console.log(`    📝 文本: ${page.text.substring(0, 50)}...`);
              if (page.imageUrl) console.log(`    🖼️  图片: ${page.imageUrl}`);
              if (page.audioUrl) console.log(`    🔊 音频: ${page.audioUrl}`);
            });
          }

          if (story.coverImageUrl) {
            console.log(`🎨 封面图片: ${story.coverImageUrl}`);
          }

          if (story.audioUrl) {
            console.log(`🎵 完整音频: ${story.audioUrl}`);
          }
        } else {
          // 状态未变化时只显示简单进度
          process.stdout.write(`📈 [${attempts + 1}/${maxAttempts}] `);
        }

        if (story.status === 'completed') {
          console.log('\n🎉 故事生成完成!');
          console.log('\n📖 最终故事详情:');
          console.log('=' .repeat(50));
          console.log(`📚 标题: ${story.title}`);
          console.log(`👤 角色: ${story.characterName} (${story.characterAge}岁)`);
          console.log(`🎭 特征: ${story.characterTraits.join(', ')}`);
          console.log(`🌟 主题: ${story.theme}`);
          console.log(`🏞️  场景: ${story.setting}`);
          console.log(`🎨 风格: ${story.style}`);
          console.log(`🔊 声音: ${story.voice}`);
          console.log(`📄 页面数: ${story.pages.length}`);

          if (story.coverImageUrl) {
            console.log(`🎨 封面图片: ${story.coverImageUrl}`);
          }

          if (story.audioUrl) {
            console.log(`🎵 完整音频: ${story.audioUrl}`);
          }

          console.log('\n📚 页面内容:');
          story.pages.forEach((page, index) => {
            console.log(`\n📄 第${index + 1}页:`);
            console.log(`📝 文本: ${page.text}`);
            if (page.imageUrl) console.log(`🖼️  图片: ${page.imageUrl}`);
            if (page.audioUrl) console.log(`🔊 音频: ${page.audioUrl}`);
          });

          console.log('\n' + '=' .repeat(50));
          break;
        } else if (story.status === 'failed') {
          console.log('\n❌ 故事生成失败');
          console.log('📋 错误详情:', JSON.stringify(story, null, 2));
          break;
        }
      } else {
        console.log('\n⚠️  获取故事状态失败:', response.data.error || '未知错误');
      }

      // 等待5秒后继续检查
      await new Promise(resolve => setTimeout(resolve, 5000));
      attempts++;

    } catch (error) {
      console.error('\n🚨 监控请求失败:', error.message);
      break;
    }
  }

  if (attempts >= maxAttempts) {
    console.log('\n⏰ 监控超时，故事可能仍在生成中');
    console.log('💡 您可以稍后手动检查故事状态');
  }
}

// 测试健康检查
async function testHealthCheck() {
  console.log('🏥 测试健康检查...');
  try {
    const response = await makeRequest('GET', '/health');
    console.log(`📊 健康检查状态: ${response.status}`);
    console.log('📋 健康检查结果:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.error('🚨 健康检查失败:', error.message);
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始API测试...\n');
  
  await testHealthCheck();
  console.log('\n' + '='.repeat(50) + '\n');
  await testStoryCreation();
  
  console.log('\n✨ 测试完成!');
}

// 运行测试
runTests().catch(console.error);