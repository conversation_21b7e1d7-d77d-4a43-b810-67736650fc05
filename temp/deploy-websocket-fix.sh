#!/bin/bash

# WebSocket连接修复部署脚本
# 解决WebSocket连接成功但没有数据传输的问题

echo "🚀 开始部署WebSocket连接修复..."
echo "=================================="

# 检查当前目录
if [ ! -f "package.json" ] && [ ! -d "frontend" ] && [ ! -d "backend" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

echo "📋 修复内容:"
echo "1. ✅ 统一环境变量配置（使用VITE_API_BASE_URL）"
echo "2. ✅ 修复API请求URL构建逻辑"
echo "3. ✅ 添加故事生成触发逻辑"
echo "4. ✅ WebSocket连接成功后立即启动生成"
echo "5. ✅ 完善错误处理和调试日志"
echo "6. ✅ 同步修复开发和生产版本"
echo ""

echo "🔍 问题根源:"
echo "- 前端只连接WebSocket但没有触发故事生成"
echo "- 环境变量不匹配（VITE_API_URL vs VITE_API_BASE_URL）"
echo "- AITaskQueueDO等待前端触发才开始执行任务"
echo ""

# 1. 构建前端
echo "🔨 构建前端..."
cd frontend
if npm run build; then
    echo "✅ 前端构建成功"
else
    echo "❌ 前端构建失败"
    exit 1
fi
cd ..

# 2. 构建生产版本
echo "🔨 构建生产版本..."
cd frontend-production
if npm run build; then
    echo "✅ 生产版本构建成功"
else
    echo "❌ 生产版本构建失败"
    exit 1
fi
cd ..

# 3. 部署前端
echo "🚀 部署前端..."
cd frontend-production
if npx wrangler pages deploy dist --project-name storyweaver; then
    echo "✅ 前端部署成功"
else
    echo "❌ 前端部署失败"
    exit 1
fi
cd ..

echo ""
echo "🎉 WebSocket连接修复部署完成!"
echo "=================================="
echo "📊 修复验证清单:"
echo "□ 访问 https://storyweaver.pages.dev"
echo "□ 创建新故事"
echo "□ 立即点击进入故事详情页面"
echo "□ 打开浏览器开发者工具 → 控制台"
echo "□ 观察WebSocket连接日志"
echo "□ 验证是否调用了startGeneration"
echo "□ 检查进度是否开始更新（不再卡在0%）"
echo "□ 观察三个阶段的进度变化"
echo ""
echo "🔍 关键日志检查:"
echo "浏览器控制台应该显示:"
echo "- 'Connecting to WebSocket: wss://storyweaver-api.stawky.workers.dev/ai-queue/.../websocket'"
echo "- '✅ WebSocket connected successfully for story: ...'"
echo "- 'WebSocket connected, starting story generation...'"
echo "- 'Triggering story generation with params: {...}'"
echo "- 'Starting story generation: {...}'"
echo "- 'Story generation triggered successfully'"
echo "- 'WebSocket message received: {...}'"
echo "- 'Task progress received: {...}'"
echo ""
echo "🎯 预期修复效果:"
echo "修复前:"
echo "- ❌ WebSocket连接成功但无数据传输"
echo "- ❌ 进度一直卡在0%"
echo "- ❌ 时间在更新但进度条不动"
echo "- ❌ 三个阶段都没有进展"
echo ""
echo "修复后:"
echo "- ✅ WebSocket连接后立即开始生成"
echo "- ✅ 进度条实时更新"
echo "- ✅ 三个阶段依次完成"
echo "- ✅ 最终跳转到完成的故事"
echo ""
echo "🔧 技术修复点:"
echo "1. 环境变量统一:"
echo "   - 统一使用VITE_API_BASE_URL"
echo "   - 自动移除/api后缀用于Durable Objects请求"
echo ""
echo "2. 生成触发逻辑:"
echo "   - WebSocket连接成功后立即调用triggerStoryGeneration()"
echo "   - 从故事数据中获取生成参数"
echo "   - 调用wsClient.startGeneration()发送POST请求"
echo "   - 触发AITaskQueueDO开始执行任务"
echo ""
echo "3. 完整数据流:"
echo "   前端创建故事 → 显示进度界面 → 连接WebSocket → 触发生成"
echo "   → AITaskQueueDO执行任务 → 通过WebSocket发送进度 → 前端更新界面"
echo ""
echo "📈 故事生成流程:"
echo "1. 创作故事文本 (0% → 100%)"
echo "2. 绘制精美插图 (0% → 100%)"
echo "3. 合成语音朗读 (0% → 100%)"
echo "4. 故事生成完成 → 自动跳转"
echo ""
echo "如果仍有问题，请检查："
echo "1. 浏览器控制台的WebSocket连接日志"
echo "2. 是否成功调用了startGeneration方法"
echo "3. 后端AITaskQueueDO是否收到生成请求"
echo "4. 网络请求是否正常（开发者工具 → 网络）"
echo ""
echo "🎊 现在WebSocket应该有实际的数据传输了！"
