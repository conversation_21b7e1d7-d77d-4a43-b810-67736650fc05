// 检查现有故事状态的脚本
const API_BASE = 'https://storyweaver-api.stawky.workers.dev';

async function checkStoryStatus() {
  console.log('🔍 检查现有故事状态...');
  
  // 之前创建的故事ID
  const storyId = 'c66abe3b-174d-447c-a1ce-ab68498fb48e';
  
  try {
    console.log(`📖 检查故事 ${storyId} 的状态...`);
    
    // 尝试获取故事状态（不使用认证）
    const statusResponse = await fetch(`${API_BASE}/api/stories/${storyId}/status`);
    
    if (statusResponse.ok) {
      const status = await statusResponse.json();
      console.log('✅ 故事状态:', status);
    } else {
      const errorText = await statusResponse.text();
      console.log('⚠️ 无法获取故事状态:', errorText);
    }
    
    // 尝试获取完整故事数据
    console.log(`📚 尝试获取完整故事数据...`);
    const storyResponse = await fetch(`${API_BASE}/api/stories/${storyId}`);
    
    if (storyResponse.ok) {
      const story = await storyResponse.json();
      console.log('✅ 故事数据:', {
        title: story.title,
        status: story.status,
        pages: story.pages?.length || 0,
        hasImages: story.pages?.some(p => p.imageUrl) || false,
        hasAudio: story.pages?.some(p => p.audioUrl) || false,
        createdAt: story.createdAt,
        updatedAt: story.updatedAt
      });
      
      // 检查每页的详细信息
      if (story.pages && story.pages.length > 0) {
        console.log('\n📄 页面详情:');
        story.pages.forEach((page, index) => {
          console.log(`  第${index + 1}页:`, {
            text: page.text ? `${page.text.substring(0, 50)}...` : '无文本',
            hasImage: !!page.imageUrl,
            hasAudio: !!page.audioUrl,
            imageUrl: page.imageUrl ? `${page.imageUrl.substring(0, 50)}...` : '无图片',
            audioUrl: page.audioUrl ? `${page.audioUrl.substring(0, 50)}...` : '无音频'
          });
        });
      }
    } else {
      const errorText = await storyResponse.text();
      console.log('⚠️ 无法获取故事数据:', errorText);
    }
    
    // 测试API健康状态
    console.log('\n🏥 检查API健康状态...');
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      console.log('✅ API健康:', health);
    }
    
  } catch (error) {
    console.error('💥 检查过程中发生错误:', error);
  }
}

// 运行检查
checkStoryStatus();
