-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id TEXT PRIMARY KEY,
    category TEXT NOT NULL,  -- 'ai', 'payment', 'auth', 'system'
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    updated_by TEXT,
    updated_at TEXT NOT NULL,
    UNIQUE(category, key)
);

-- 配置变更日志表
CREATE TABLE IF NOT EXISTS config_audit_logs (
    id TEXT PRIMARY KEY,
    config_id TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by TEXT NOT NULL,
    changed_at TEXT NOT NULL,
    FOREIGN KEY (config_id) REFERENCES system_configs(id)
);

-- 插入默认配置
INSERT OR REPLACE INTO system_configs (id, category, key, value, description, is_sensitive, updated_by, updated_at) VALUES
-- AI服务配置
('ai_gemini_api_key', 'ai', 'gemini_api_key', 'AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw', 'Gemini API密钥', TRUE, 'system', datetime('now')),
('ai_gemini_model', 'ai', 'gemini_model', 'gemini-2.5-flash', 'Gemini模型版本', FALSE, 'system', datetime('now')),
('ai_max_tokens', 'ai', 'max_tokens', '2048', 'AI生成最大令牌数', FALSE, 'system', datetime('now')),
('ai_temperature', 'ai', 'temperature', '0.7', 'AI生成温度参数', FALSE, 'system', datetime('now')),
('ai_safety_enabled', 'ai', 'safety_enabled', 'true', '是否启用内容安全检查', FALSE, 'system', datetime('now')),

-- 支付配置
('payment_stripe_secret', 'payment', 'stripe_secret_key', 'sk_test_...', 'Stripe密钥', TRUE, 'system', datetime('now')),
('payment_stripe_webhook', 'payment', 'stripe_webhook_secret', 'whsec_...', 'Stripe Webhook密钥', TRUE, 'system', datetime('now')),
('payment_credit_price', 'payment', 'credit_price_usd', '10.00', '积分包价格（美元）', FALSE, 'system', datetime('now')),
('payment_subscription_price', 'payment', 'subscription_price_usd', '15.00', '月订阅价格（美元）', FALSE, 'system', datetime('now')),
('payment_book_price', 'payment', 'physical_book_price_usd', '29.99', '实体书价格（美元）', FALSE, 'system', datetime('now')),

-- 认证配置
('auth_jwt_secret', 'auth', 'jwt_secret', 'storyweaver-jwt-secret-key-2024', 'JWT密钥', TRUE, 'system', datetime('now')),
('auth_jwt_expires', 'auth', 'jwt_expires_in', '7d', 'JWT过期时间', FALSE, 'system', datetime('now')),
('auth_google_client_id', 'auth', 'google_client_id', '463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com', 'Google OAuth客户端ID', FALSE, 'system', datetime('now')),
('auth_google_client_secret', 'auth', 'google_client_secret', 'GOCSPX-...', 'Google OAuth客户端密钥', TRUE, 'system', datetime('now')),

-- 系统配置
('system_max_story_pages', 'system', 'max_story_pages', '12', '故事最大页数', FALSE, 'system', datetime('now')),
('system_min_story_pages', 'system', 'min_story_pages', '6', '故事最小页数', FALSE, 'system', datetime('now')),
('system_rate_limit', 'system', 'rate_limit_per_minute', '60', '每分钟API调用限制', FALSE, 'system', datetime('now')),
('system_maintenance_mode', 'system', 'maintenance_mode', 'false', '维护模式开关', FALSE, 'system', datetime('now')),
('system_debug_mode', 'system', 'debug_mode', 'false', '调试模式开关', FALSE, 'system', datetime('now'));

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_configs_category ON system_configs(category);
CREATE INDEX IF NOT EXISTS idx_system_configs_updated_at ON system_configs(updated_at);
CREATE INDEX IF NOT EXISTS idx_config_audit_logs_config_id ON config_audit_logs(config_id);
CREATE INDEX IF NOT EXISTS idx_config_audit_logs_changed_at ON config_audit_logs(changed_at);
