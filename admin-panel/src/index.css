/**
 * 全局样式
 */

/* 重置样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Segoe UI', -apple-system, BlinkMacSystemFont, 'Roboto', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

#root {
  height: 100%;
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 暗色主题滚动条 */
[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 焦点样式 */
*:focus-visible {
  outline: 2px solid #0078d4;
  outline-offset: 2px;
}

/* 禁用元素样式 */
*:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 选择文本样式 */
::selection {
  background-color: #0078d4;
  color: white;
}

/* 工具提示样式 */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip:hover::after {
  opacity: 1;
}

/* 响应式工具类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }
  
  .desktop-only {
    display: none;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 加载状态样式 */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

[data-theme="dark"] .loading-skeleton {
  background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 表格样式增强 */
.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  text-align: left;
  padding: 12px;
  border-bottom: 1px solid #e0e0e0;
}

[data-theme="dark"] .data-table th,
[data-theme="dark"] .data-table td {
  border-bottom-color: #3a3a3a;
}

.data-table th {
  font-weight: 600;
  background-color: #f8f9fa;
}

[data-theme="dark"] .data-table th {
  background-color: #2a2a2a;
}

.data-table tr:hover {
  background-color: #f8f9fa;
}

[data-theme="dark"] .data-table tr:hover {
  background-color: #2a2a2a;
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.success {
  background-color: #d4edda;
  color: #155724;
}

.status-indicator.warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-indicator.error {
  background-color: #f8d7da;
  color: #721c24;
}

.status-indicator.info {
  background-color: #d1ecf1;
  color: #0c5460;
}

[data-theme="dark"] .status-indicator.success {
  background-color: #1e4620;
  color: #4caf50;
}

[data-theme="dark"] .status-indicator.warning {
  background-color: #4a3c00;
  color: #ff9800;
}

[data-theme="dark"] .status-indicator.error {
  background-color: #4a1e1e;
  color: #f44336;
}

[data-theme="dark"] .status-indicator.info {
  background-color: #1e3a4a;
  color: #2196f3;
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .page-break {
    page-break-before: always;
  }
}
