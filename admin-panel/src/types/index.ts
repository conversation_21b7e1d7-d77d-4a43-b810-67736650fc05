/**
 * 管理面板类型定义
 */

/// <reference path="./cloudflare.d.ts" />

// 用户相关类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  google_id: string;
  credits: number;
  isAdmin?: boolean;
  created_at: string;
  updated_at: string;
}

// 故事相关类型
export interface Story {
  id: string;
  user_id: string;
  title: string;
  character_name: string;
  character_age: number;
  character_traits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  pages: StoryPage[];
  audio_url?: string;
  cover_image_url?: string;
  status: 'generating' | 'completed' | 'failed';
  created_at: string;
  updated_at: string;
}

export interface StoryPage {
  text: string;
  imagePrompt: string;
  imageUrl?: string;
}

// 订阅相关类型
export interface Subscription {
  id: string;
  user_id: string;
  plan: 'free' | 'credits' | 'unlimited';
  status: 'active' | 'canceled' | 'expired';
  current_period_start: string;
  current_period_end: string;
  stripe_subscription_id?: string;
  created_at: string;
  updated_at: string;
}

// 实体书订单类型
export interface PhysicalBookOrder {
  id: string;
  story_id: string;
  user_id: string;
  order_number: string;
  customization: BookCustomization;
  shipping_info: ShippingInfo;
  status: 'pending' | 'printing' | 'shipped' | 'delivered' | 'canceled';
  price: number;
  stripe_payment_intent_id?: string;
  tracking_number?: string;
  created_at: string;
  updated_at: string;
}

export interface BookCustomization {
  size: string;
  cover_type: string;
  paper_type: string;
  binding: string;
}

export interface ShippingInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
}

// 配置相关类型
export interface ConfigItem {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  is_sensitive: boolean;
  updated_by: string;
  updated_at: string;
}

export interface ConfigCategory {
  key: string;
  name: string;
  description: string;
  icon: string;
}

// 统计数据类型
export interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalStories: number;
  completedStories: number;
  generatingStories: number;
  failedStories: number;
  totalOrders: number;
  pendingOrders: number;
  monthlyRevenue: number;
  systemHealth: 'healthy' | 'warning' | 'error';
}

export interface UserStats {
  totalUsers: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
  activeUsersToday: number;
  userGrowthTrend: Array<{ date: string; count: number }>;
}

export interface StoryStats {
  totalStories: number;
  storiesCreatedToday: number;
  storiesCreatedThisWeek: number;
  storiesCreatedThisMonth: number;
  averageGenerationTime: number;
  successRate: number;
  storyCreationTrend: Array<{ date: string; count: number }>;
}

// AI服务监控类型
export interface AIServiceStats {
  dailyCalls: number;
  monthlyCost: number;
  avgResponseTime: number;
  successRate: number;
  errorRate: number;
  costTrend: Array<{ date: string; cost: number }>;
  callsTrend: Array<{ date: string; calls: number }>;
}

// Durable Objects监控类型
export interface DOStats {
  aiTaskQueue: {
    active: number;
    requests: number;
    avgDuration: number;
  };
  storyGeneration: {
    active: number;
    requests: number;
    avgDuration: number;
  };
}

export interface ActiveTask {
  id: string;
  storyId: string;
  type: 'text' | 'image' | 'audio';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  duration?: number;
  error?: string;
}

// 系统监控类型
export interface SystemHealth {
  apiGateway: 'healthy' | 'warning' | 'error';
  database: 'healthy' | 'warning' | 'error';
  aiServices: 'healthy' | 'warning' | 'error';
  storage: 'healthy' | 'warning' | 'error';
  apiResponseTime: number;
  dbResponseTime: number;
  aiResponseTime: number;
  storageResponseTime: number;
  apiResponseTrend: Array<{ time: string; value: number }>;
  errorRateTrend: Array<{ time: string; value: number }>;
  concurrentUsersTrend: Array<{ time: string; value: number }>;
}

export interface Alert {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
  source: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  code?: string;
  message?: string;
}

// 分页类型
export interface PaginationParams {
  page: number;
  limit: number;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 认证相关类型
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  isAdmin: boolean;
  accessToken: string;
  refreshToken?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// 环境变量类型
export interface Env {
  DB: D1Database;
  CACHE: KVNamespace;
  ASSETS: R2Bucket;
  AI_TASK_QUEUE: DurableObjectNamespace;
  STORY_GENERATION: DurableObjectNamespace;
  JWT_SECRET: string;
  ENVIRONMENT: string;
  ADMIN_EMAIL: string;
}
