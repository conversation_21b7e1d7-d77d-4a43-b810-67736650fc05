/**
 * JWT工具函数
 * 用于测试JWT token的生成和验证
 */

import { AuthUser } from '@/types';

// 简单的Base64编码/解码（仅用于测试）
function base64UrlEncode(str: string): string {
  return btoa(str)
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

function base64UrlDecode(str: string): string {
  // 添加填充
  str += '='.repeat((4 - str.length % 4) % 4);
  // 替换URL安全字符
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  return atob(str);
}

// 解析JWT token（仅用于客户端验证，不验证签名）
export function parseJWT(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    const payload = base64UrlDecode(parts[1]);
    return JSON.parse(payload);
  } catch (error) {
    console.error('Failed to parse JWT:', error);
    return null;
  }
}

// 检查token是否过期
export function isTokenExpired(token: string): boolean {
  try {
    const payload = parseJWT(token);
    if (!payload || !payload.exp) {
      return true;
    }

    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    return true;
  }
}

// 从token中提取用户信息
export function extractUserFromToken(token: string): Partial<AuthUser> | null {
  try {
    const payload = parseJWT(token);
    if (!payload) {
      return null;
    }

    return {
      id: payload.sub || payload.userId,
      email: payload.email,
      name: payload.name,
      isAdmin: payload.isAdmin || false,
    };
  } catch (error) {
    console.error('Failed to extract user from token:', error);
    return null;
  }
}

// 验证管理员权限
export function hasAdminPermission(token: string): boolean {
  try {
    const user = extractUserFromToken(token);
    return user?.isAdmin === true;
  } catch (error) {
    return false;
  }
}

// 创建模拟的管理员token（仅用于测试）
export function createMockAdminToken(): string {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    sub: 'admin-test-user',
    email: '<EMAIL>',
    name: '系统管理员',
    isAdmin: true,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24小时后过期
  };

  // 注意：这里不包含真实的签名，仅用于前端测试
  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  return `${encodedHeader}.${encodedPayload}.mock-signature`;
}

// 创建模拟的普通用户token（用于测试权限拒绝）
export function createMockUserToken(): string {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const payload = {
    sub: 'regular-test-user',
    email: '<EMAIL>',
    name: '普通用户',
    isAdmin: false,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60),
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  return `${encodedHeader}.${encodedPayload}.mock-signature`;
}
