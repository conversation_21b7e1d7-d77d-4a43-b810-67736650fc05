/**
 * 受保护的路由组件
 */

import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spinner } from '@fluentui/react-components';
import { useAuthStore } from '@/stores/authStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, isLoading, user, loginWithToken } = useAuthStore();
  const location = useLocation();

  useEffect(() => {
    // 如果没有认证但有存储的token，尝试验证
    if (!isAuthenticated && !isLoading) {
      const storedAuth = localStorage.getItem('admin-auth-storage');
      if (storedAuth) {
        try {
          const parsed = JSON.parse(storedAuth);
          if (parsed.state?.user?.accessToken) {
            loginWithToken(parsed.state.user.accessToken).catch(() => {
              // Token验证失败，清除存储
              localStorage.removeItem('admin-auth-storage');
            });
          }
        } catch (error) {
          console.error('Failed to parse stored auth:', error);
          localStorage.removeItem('admin-auth-storage');
        }
      }
    }
  }, [isAuthenticated, isLoading, loginWithToken]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: '16px',
        }}
      >
        <Spinner size="large" />
        <div>验证身份中...</div>
      </div>
    );
  }

  // 未认证，重定向到登录页
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 检查管理员权限
  if (!user.isAdmin) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column',
          gap: '16px',
        }}
      >
        <div>❌ 访问被拒绝</div>
        <div>您需要管理员权限才能访问此页面</div>
      </div>
    );
  }

  return <>{children}</>;
};
