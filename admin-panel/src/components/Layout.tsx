/**
 * 主布局组件
 */

import React, { useState } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import {
  makeStyles,
  shorthands,
  tokens,
  Button,
  Avatar,
  Text,
  Menu,
  MenuTrigger,
  MenuPopover,
  MenuList,
  MenuItem,
  MenuDivider,
} from '@fluentui/react-components';
import {
  Navigation24Regular,
  WeatherMoon24Regular,
  WeatherSunny24Regular,
  SignOut24Regular,
  Person24Regular,
} from '@fluentui/react-icons';
import { Sidebar } from './Sidebar';
import { useAuthStore } from '@/stores/authStore';
import { useTheme } from '@/providers/ThemeProvider';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    height: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  sidebar: {
    width: '280px',
    flexShrink: 0,
    borderRight: `1px solid ${tokens.colorNeutralStroke2}`,
    backgroundColor: tokens.colorNeutralBackground2,
    transition: 'margin-left 0.3s ease',
  },
  sidebarCollapsed: {
    marginLeft: '-280px',
  },
  main: {
    flex: 1,
    display: 'flex',
    flexDirection: 'column',
    overflow: 'hidden',
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    height: '64px',
    ...shorthands.padding('0', '24px'),
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
    backgroundColor: tokens.colorNeutralBackground1,
  },
  headerLeft: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('16px'),
  },
  headerRight: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  mainContent: {
    flex: 1,
    ...shorthands.padding('24px'),
    overflow: 'auto',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  title: {
    fontSize: tokens.fontSizeBase500,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  userInfo: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('8px'),
  },
  // 响应式样式
  '@media (max-width: 768px)': {
    sidebar: {
      position: 'fixed',
      top: 0,
      left: 0,
      height: '100vh',
      zIndex: 1000,
      boxShadow: tokens.shadow16,
    },
    sidebarCollapsed: {
      marginLeft: '-280px',
    },
    mainContent: {
      ...shorthands.padding('16px'),
    },
  },
});

export const Layout: React.FC = () => {
  const styles = useStyles();
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();
  const { isDark, toggleTheme } = useTheme();
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // 获取当前页面标题
  const getPageTitle = () => {
    const path = location.pathname;
    switch (path) {
      case '/dashboard':
        return '仪表板';
      case '/users':
        return '用户管理';
      case '/stories':
        return '内容管理';
      case '/orders':
        return '订单管理';
      case '/configs':
        return '系统配置';
      case '/monitoring':
        return '系统监控';
      default:
        return 'StoryWeaver 管理面板';
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className={styles.root}>
      {/* 侧边栏 */}
      <div className={`${styles.sidebar} ${sidebarCollapsed ? styles.sidebarCollapsed : ''}`}>
        <Sidebar onItemClick={() => setSidebarCollapsed(true)} />
      </div>

      {/* 主内容区域 */}
      <div className={styles.main}>
        {/* 头部 */}
        <header className={styles.header}>
          <div className={styles.headerLeft}>
            <Button
              appearance="subtle"
              icon={<Navigation24Regular />}
              onClick={toggleSidebar}
              aria-label="切换侧边栏"
            />
            <Text className={styles.title}>{getPageTitle()}</Text>
          </div>

          <div className={styles.headerRight}>
            {/* 主题切换按钮 */}
            <Button
              appearance="subtle"
              icon={isDark ? <WeatherSunny24Regular /> : <WeatherMoon24Regular />}
              onClick={toggleTheme}
              aria-label={isDark ? '切换到亮色主题' : '切换到暗色主题'}
            />

            {/* 用户菜单 */}
            <Menu>
              <MenuTrigger disableButtonEnhancement>
                <Button appearance="subtle" className={styles.userInfo}>
                  <Avatar
                    name={user?.name}
                    image={user?.avatar ? { src: user.avatar } : undefined}
                    size={32}
                  />
                  <Text>{user?.name}</Text>
                </Button>
              </MenuTrigger>
              <MenuPopover>
                <MenuList>
                  <MenuItem icon={<Person24Regular />}>
                    个人资料
                  </MenuItem>
                  <MenuDivider />
                  <MenuItem
                    icon={<SignOut24Regular />}
                    onClick={handleLogout}
                  >
                    退出登录
                  </MenuItem>
                </MenuList>
              </MenuPopover>
            </Menu>
          </div>
        </header>

        {/* 内容区域 */}
        <main className={styles.mainContent}>
          <React.Suspense
            fallback={
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                }}
              >
                加载中...
              </div>
            }
          >
            <Outlet />
          </React.Suspense>
        </main>
      </div>

      {/* 移动端遮罩层 */}
      {!sidebarCollapsed && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.3)',
            zIndex: 999,
            display: window.innerWidth <= 768 ? 'block' : 'none',
          }}
          onClick={() => setSidebarCollapsed(true)}
        />
      )}
    </div>
  );
};
