/**
 * 折线图组件
 */

import React from 'react';
import {
  LineChart as Re<PERSON>rtsLine<PERSON><PERSON>,
  Line,
  XAxis,
  Y<PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import { tokens } from '@fluentui/react-components';

interface DataPoint {
  name: string;
  value: number;
  date?: string;
}

interface LineChartProps {
  data: DataPoint[];
  title?: string;
  color?: string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
}

export const LineChart: React.FC<LineChartProps> = ({
  data,
  title,
  color = tokens.colorBrandBackground,
  height = 300,
  showGrid = true,
  showTooltip = true,
}) => {
  return (
    <div style={{ width: '100%', height }}>
      {title && (
        <h3 style={{ 
          margin: '0 0 16px 0', 
          fontSize: tokens.fontSizeBase400,
          fontWeight: tokens.fontWeightSemibold,
          color: tokens.colorNeutralForeground1
        }}>
          {title}
        </h3>
      )}
      <ResponsiveContainer width="100%" height="100%">
        <RechartsLineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          {showGrid && (
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={tokens.colorNeutralStroke2}
            />
          )}
          <XAxis 
            dataKey="name" 
            stroke={tokens.colorNeutralForeground3}
            fontSize={12}
          />
          <YAxis 
            stroke={tokens.colorNeutralForeground3}
            fontSize={12}
          />
          {showTooltip && (
            <Tooltip
              contentStyle={{
                backgroundColor: tokens.colorNeutralBackground1,
                border: `1px solid ${tokens.colorNeutralStroke2}`,
                borderRadius: tokens.borderRadiusMedium,
                boxShadow: tokens.shadow8,
              }}
              labelStyle={{ color: tokens.colorNeutralForeground1 }}
            />
          )}
          <Line
            type="monotone"
            dataKey="value"
            stroke={color}
            strokeWidth={2}
            dot={{ fill: color, strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: color, strokeWidth: 2 }}
          />
        </RechartsLineChart>
      </ResponsiveContainer>
    </div>
  );
};
