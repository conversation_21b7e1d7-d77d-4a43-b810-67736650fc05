/**
 * 侧边栏导航组件
 */

import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Button,
} from '@fluentui/react-components';
import {
  Home24Regular,
  Home24Filled,
  People24Regular,
  People24Filled,
  Document24Regular,
  Document24Filled,
  ShoppingBag24Regular,
  ShoppingBag24Filled,
  Settings24Regular,
  Settings24Filled,
  ChartMultiple24Regular,
  ChartMultiple24Filled,
} from '@fluentui/react-icons';
import { navigationItems } from '@/router';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    backgroundColor: tokens.colorNeutralBackground2,
  },
  header: {
    display: 'flex',
    alignItems: 'center',
    height: '64px',
    ...shorthands.padding('0', '24px'),
    borderBottom: `1px solid ${tokens.colorNeutralStroke2}`,
  },
  logo: {
    fontSize: tokens.fontSizeBase500,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorBrandForeground1,
  },
  nav: {
    flex: 1,
    ...shorthands.padding('16px', '12px'),
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('4px'),
  },
  navItem: {
    width: '100%',
    justifyContent: 'flex-start',
    ...shorthands.padding('12px', '16px'),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightRegular,
    color: tokens.colorNeutralForeground2,
    backgroundColor: 'transparent',
    '&:hover': {
      backgroundColor: tokens.colorNeutralBackground1Hover,
      color: tokens.colorNeutralForeground1,
    },
  },
  navItemActive: {
    backgroundColor: tokens.colorBrandBackground,
    color: tokens.colorNeutralForegroundOnBrand,
    '&:hover': {
      backgroundColor: tokens.colorBrandBackgroundHover,
      color: tokens.colorNeutralForegroundOnBrand,
    },
  },
  navItemIcon: {
    marginRight: '12px',
    fontSize: '20px',
  },
  footer: {
    ...shorthands.padding('16px', '24px'),
    borderTop: `1px solid ${tokens.colorNeutralStroke2}`,
  },
  version: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground3,
  },
});

interface SidebarProps {
  onItemClick?: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {
  const styles = useStyles();
  const location = useLocation();
  const navigate = useNavigate();

  // 图标映射
  const iconMap: Record<string, { regular: React.ReactElement; filled: React.ReactElement }> = {
    Home: {
      regular: <Home24Regular />,
      filled: <Home24Filled />,
    },
    People: {
      regular: <People24Regular />,
      filled: <People24Filled />,
    },
    Document: {
      regular: <Document24Regular />,
      filled: <Document24Filled />,
    },
    ShoppingCart: {
      regular: <ShoppingBag24Regular />,
      filled: <ShoppingBag24Filled />,
    },
    Settings: {
      regular: <Settings24Regular />,
      filled: <Settings24Filled />,
    },
    LineChart: {
      regular: <ChartMultiple24Regular />,
      filled: <ChartMultiple24Filled />,
    },
  };

  const handleNavigation = (path: string) => {
    navigate(path);
    onItemClick?.();
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getIcon = (iconName: string, active: boolean) => {
    const icons = iconMap[iconName];
    if (!icons) return null;
    return active ? icons.filled : icons.regular;
  };

  return (
    <div className={styles.root}>
      {/* 头部Logo */}
      <div className={styles.header}>
        <Text className={styles.logo}>StoryWeaver</Text>
      </div>

      {/* 导航菜单 */}
      <nav className={styles.nav}>
        {navigationItems.map((item) => {
          const active = isActive(item.path);
          return (
            <Button
              key={item.key}
              appearance="subtle"
              className={`${styles.navItem} ${active ? styles.navItemActive : ''}`}
              onClick={() => handleNavigation(item.path)}
            >
              <span className={styles.navItemIcon}>
                {getIcon(item.icon, active)}
              </span>
              {item.name}
            </Button>
          );
        })}
      </nav>

      {/* 底部信息 */}
      <div className={styles.footer}>
        <Text className={styles.version}>v1.0.0</Text>
      </div>
    </div>
  );
};
