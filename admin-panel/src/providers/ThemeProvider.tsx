/**
 * Fluent UI 2 主题提供者
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import {
  FluentProvider,
  webLightTheme,
  webDarkTheme,
  createLightTheme,
  createDarkTheme,
  BrandVariants,
  Theme,
} from '@fluentui/react-components';

// StoryWeaver 品牌色彩
const storyWeaverBrandRamp: BrandVariants = {
  10: "#020305",
  20: "#111418",
  30: "#16202D",
  40: "#1B2B42",
  50: "#203757",
  60: "#25446C",
  70: "#2A5281",
  80: "#2F5F96",
  90: "#346DAB",
  100: "#387AC0",
  110: "#4A87C7",
  120: "#5C94CE",
  130: "#6EA1D5",
  140: "#80AEDC",
  150: "#92BBE3",
  160: "#A4C8EA"
};

// 创建自定义主题
const customLightTheme: Theme = {
  ...createLightTheme(storyWeaverBrandRamp),
  colorBrandBackground: storyWeaverBrandRamp[100],
  colorBrandBackgroundHover: storyWeaverBrandRamp[110],
  colorBrandBackgroundPressed: storyWeaverBrandRamp[90],
};

const customDarkTheme: Theme = {
  ...createDarkTheme(storyWeaverBrandRamp),
  colorBrandBackground: storyWeaverBrandRamp[100],
  colorBrandBackgroundHover: storyWeaverBrandRamp[110],
  colorBrandBackgroundPressed: storyWeaverBrandRamp[90],
};

interface ThemeContextType {
  isDark: boolean;
  toggleTheme: () => void;
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [isDark, setIsDark] = useState(() => {
    // 从localStorage读取主题偏好
    const saved = localStorage.getItem('admin-theme');
    if (saved) {
      return saved === 'dark';
    }
    // 默认使用系统主题
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  const theme = isDark ? customDarkTheme : customLightTheme;

  const toggleTheme = () => {
    setIsDark(prev => {
      const newValue = !prev;
      localStorage.setItem('admin-theme', newValue ? 'dark' : 'light');
      return newValue;
    });
  };

  // 监听系统主题变化
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      const saved = localStorage.getItem('admin-theme');
      if (!saved) {
        setIsDark(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // 更新document的data-theme属性
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');
  }, [isDark]);

  const contextValue: ThemeContextType = {
    isDark,
    toggleTheme,
    theme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <FluentProvider theme={theme}>
        {children}
      </FluentProvider>
    </ThemeContext.Provider>
  );
};
