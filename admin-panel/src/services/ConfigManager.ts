/**
 * 配置管理服务
 * 负责系统配置的读取、更新和缓存管理
 */

export interface ConfigItem {
  id: string;
  category: string;
  key: string;
  value: string;
  description: string;
  is_sensitive: boolean;
  updated_by: string;
  updated_at: string;
}

export interface ConfigAuditLog {
  id: string;
  config_id: string;
  old_value: string | null;
  new_value: string;
  changed_by: string;
  changed_at: string;
}

export class ConfigManager {
  constructor(
    private kv: KVNamespace,
    private db: D1Database
  ) {}

  /**
   * 获取配置值
   */
  async getConfig(category: string, key: string, fallback?: string): Promise<string | null> {
    try {
      // 1. 先从KV缓存获取（5分钟缓存）
      const cacheKey = `config:${category}:${key}`;
      let value = await this.kv.get(cacheKey);
      
      if (!value) {
        // 2. 从数据库获取并缓存
        const result = await this.db.prepare(
          'SELECT value FROM system_configs WHERE category = ? AND key = ?'
        ).bind(category, key).first();
        
        if (result) {
          value = result.value as string;
          await this.kv.put(cacheKey, value, { expirationTtl: 300 });
        } else if (fallback) {
          value = fallback;
        }
      }
      
      return value;
    } catch (error) {
      console.error(`Failed to get config ${category}.${key}:`, error);
      return fallback || null;
    }
  }

  /**
   * 更新配置值
   */
  async updateConfig(category: string, key: string, value: string, updatedBy: string): Promise<void> {
    const configId = `${category}_${key}`;
    
    try {
      // 1. 获取旧值用于审计
      const oldConfig = await this.db.prepare(
        'SELECT value FROM system_configs WHERE category = ? AND key = ?'
      ).bind(category, key).first();
      
      // 2. 更新配置
      await this.db.prepare(`
        INSERT OR REPLACE INTO system_configs (id, category, key, value, updated_by, updated_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(configId, category, key, value, updatedBy, new Date().toISOString()).run();

      // 3. 记录审计日志
      await this.db.prepare(`
        INSERT INTO config_audit_logs (id, config_id, old_value, new_value, changed_by, changed_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `).bind(
        crypto.randomUUID(),
        configId,
        oldConfig?.value || null,
        value,
        updatedBy,
        new Date().toISOString()
      ).run();

      // 4. 更新KV缓存
      const cacheKey = `config:${category}:${key}`;
      await this.kv.put(cacheKey, value, { expirationTtl: 300 });

      // 5. 发送配置更新通知
      await this.kv.put(`config_update:${Date.now()}`, JSON.stringify({
        category, key, value, updatedBy, timestamp: new Date().toISOString()
      }), { expirationTtl: 60 });

      console.log(`Config updated: ${category}.${key} = ${value} by ${updatedBy}`);
    } catch (error) {
      console.error(`Failed to update config ${category}.${key}:`, error);
      throw error;
    }
  }

  /**
   * 获取指定分类的所有配置
   */
  async getConfigsByCategory(category: string): Promise<ConfigItem[]> {
    try {
      const results = await this.db.prepare(
        'SELECT * FROM system_configs WHERE category = ? ORDER BY key'
      ).bind(category).all();
      
      return (results.results || []) as ConfigItem[];
    } catch (error) {
      console.error(`Failed to get configs for category ${category}:`, error);
      return [];
    }
  }

  /**
   * 获取所有配置分类
   */
  async getCategories(): Promise<string[]> {
    try {
      const results = await this.db.prepare(
        'SELECT DISTINCT category FROM system_configs ORDER BY category'
      ).all();
      
      return (results.results || []).map(r => r.category as string);
    } catch (error) {
      console.error('Failed to get config categories:', error);
      return [];
    }
  }

  /**
   * 获取配置变更历史
   */
  async getConfigHistory(configId: string, limit: number = 50): Promise<ConfigAuditLog[]> {
    try {
      const results = await this.db.prepare(`
        SELECT * FROM config_audit_logs 
        WHERE config_id = ? 
        ORDER BY changed_at DESC 
        LIMIT ?
      `).bind(configId, limit).all();
      
      return (results.results || []) as ConfigAuditLog[];
    } catch (error) {
      console.error(`Failed to get config history for ${configId}:`, error);
      return [];
    }
  }

  /**
   * 批量更新配置
   */
  async batchUpdateConfigs(updates: Array<{
    category: string;
    key: string;
    value: string;
  }>, updatedBy: string): Promise<void> {
    try {
      // 使用事务批量更新
      const statements = updates.map(({ category, key, value }) => {
        const configId = `${category}_${key}`;
        return this.db.prepare(`
          INSERT OR REPLACE INTO system_configs (id, category, key, value, updated_by, updated_at)
          VALUES (?, ?, ?, ?, ?, ?)
        `).bind(configId, category, key, value, updatedBy, new Date().toISOString());
      });

      await this.db.batch(statements);

      // 清除相关缓存
      for (const { category, key } of updates) {
        const cacheKey = `config:${category}:${key}`;
        await this.kv.delete(cacheKey);
      }

      console.log(`Batch updated ${updates.length} configs by ${updatedBy}`);
    } catch (error) {
      console.error('Failed to batch update configs:', error);
      throw error;
    }
  }

  /**
   * 清除配置缓存
   */
  async clearCache(category?: string, key?: string): Promise<void> {
    try {
      if (category && key) {
        const cacheKey = `config:${category}:${key}`;
        await this.kv.delete(cacheKey);
      } else if (category) {
        // 清除整个分类的缓存（需要列出所有键）
        const configs = await this.getConfigsByCategory(category);
        for (const config of configs) {
          const cacheKey = `config:${config.category}:${config.key}`;
          await this.kv.delete(cacheKey);
        }
      }
      console.log(`Cache cleared for ${category || 'all'}.${key || 'all'}`);
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }
}
