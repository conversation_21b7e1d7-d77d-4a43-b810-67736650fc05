/**
 * API客户端
 */

import { ApiResponse, PaginatedResponse, PaginationParams } from '@/types';
import { useAuthStore } from '@/stores/authStore';

class ApiClient {
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_BASE_URL || '/api';
  }

  private async request<T = any>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const { user } = useAuthStore.getState();
    
    const url = `${this.baseURL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(user?.accessToken && {
          'Authorization': `Bearer ${user.accessToken}`,
        }),
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      
      // 处理401错误（token过期）
      if (response.status === 401) {
        try {
          await useAuthStore.getState().refreshToken();
          // 重试请求
          const retryResponse = await fetch(url, {
            ...config,
            headers: {
              ...config.headers,
              'Authorization': `Bearer ${useAuthStore.getState().user?.accessToken}`,
            },
          });
          return await retryResponse.json();
        } catch (refreshError) {
          useAuthStore.getState().logout();
          throw new Error('认证已过期，请重新登录');
        }
      }

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || data.message || `HTTP ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // GET请求
  async get<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'GET' });
  }

  // POST请求
  async post<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT请求
  async put<T = any>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE请求
  async delete<T = any>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }

  // 分页查询
  async getPaginated<T = any>(
    endpoint: string,
    params: PaginationParams
  ): Promise<ApiResponse<PaginatedResponse<T>>> {
    const searchParams = new URLSearchParams();
    searchParams.append('page', params.page.toString());
    searchParams.append('limit', params.limit.toString());
    
    if (params.search) {
      searchParams.append('search', params.search);
    }
    if (params.sortBy) {
      searchParams.append('sortBy', params.sortBy);
    }
    if (params.sortOrder) {
      searchParams.append('sortOrder', params.sortOrder);
    }

    return this.get<PaginatedResponse<T>>(`${endpoint}?${searchParams.toString()}`);
  }

  // 上传文件
  async uploadFile<T = any>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, any>
  ): Promise<ApiResponse<T>> {
    const { user } = useAuthStore.getState();
    
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
      });
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: {
        ...(user?.accessToken && {
          'Authorization': `Bearer ${user.accessToken}`,
        }),
      },
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || '文件上传失败');
    }

    return await response.json();
  }
}

// 创建单例实例
export const apiClient = new ApiClient();

// 便捷的API方法
export const api = {
  // 认证相关
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiClient.post('/auth/admin/login', credentials),
    me: () => apiClient.get('/auth/me'),
    refresh: (refreshToken: string) =>
      apiClient.post('/auth/refresh', { refreshToken }),
  },

  // 用户管理
  users: {
    list: (params: PaginationParams) =>
      apiClient.getPaginated('/admin/users', params),
    get: (id: string) => apiClient.get(`/admin/users/${id}`),
    update: (id: string, data: any) =>
      apiClient.put(`/admin/users/${id}`, data),
    delete: (id: string) => apiClient.delete(`/admin/users/${id}`),
    stats: () => apiClient.get('/admin/users/stats'),
  },

  // 故事管理
  stories: {
    list: (params: PaginationParams) =>
      apiClient.getPaginated('/admin/stories', params),
    get: (id: string) => apiClient.get(`/admin/stories/${id}`),
    update: (id: string, data: any) =>
      apiClient.put(`/admin/stories/${id}`, data),
    delete: (id: string) => apiClient.delete(`/admin/stories/${id}`),
    stats: () => apiClient.get('/admin/stories/stats'),
  },

  // 订单管理
  orders: {
    list: (params: PaginationParams) =>
      apiClient.getPaginated('/admin/orders', params),
    get: (id: string) => apiClient.get(`/admin/orders/${id}`),
    update: (id: string, data: any) =>
      apiClient.put(`/admin/orders/${id}`, data),
    stats: () => apiClient.get('/admin/orders/stats'),
  },

  // 配置管理
  configs: {
    categories: () => apiClient.get('/admin/configs/categories'),
    list: (category: string) => apiClient.get(`/admin/configs/${category}`),
    update: (category: string, key: string, value: string) =>
      apiClient.put(`/admin/configs/${category}/${key}`, { value }),
    history: (configId: string) =>
      apiClient.get(`/admin/configs/history/${configId}`),
  },

  // 系统监控
  monitoring: {
    dashboard: () => apiClient.get('/admin/monitoring/dashboard'),
    aiServices: () => apiClient.get('/admin/monitoring/ai-services'),
    durableObjects: () => apiClient.get('/admin/monitoring/durable-objects'),
    systemHealth: () => apiClient.get('/admin/monitoring/system-health'),
    alerts: () => apiClient.get('/admin/monitoring/alerts'),
  },
};
