/**
 * 故事管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
  Button,
  SearchBox,
  Dropdown,
  Option,
  DataGrid,
  DataGridHeader,
  DataGridRow,
  DataGridHeaderCell,
  DataGridCell,
  DataGridBody,
  createTableColumn,
  TableColumnDefinition,
  Badge,
  Avatar,
  Spinner,
  MessageBar,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogBody,
  Image,
  Tooltip,
} from '@fluentui/react-components';
import {
  Add24Regular,
  Search24Regular,
  Filter24Regular,
  Eye24Regular,
  Edit24Regular,
  Delete24Regular,
  Document24Regular,
  Play24Regular,

} from '@fluentui/react-icons';
import { Story, PaginationParams } from '@/types';
import { api } from '@/services/apiClient';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...shorthands.gap('16px'),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  filters: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
    ...shorthands.padding('16px'),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  storyCard: {
    ...shorthands.padding('20px'),
  },
  storyCell: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  storyInfo: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('4px'),
  },
  storyTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  storyMeta: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  actionButtons: {
    display: 'flex',
    ...shorthands.gap('8px'),
  },
  statusBadge: {
    textTransform: 'capitalize',
  },
  coverImage: {
    width: '40px',
    height: '40px',
    ...shorthands.borderRadius(tokens.borderRadiusSmall),
    objectFit: 'cover',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  emptyState: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    textAlign: 'center',
    color: tokens.colorNeutralForeground3,
  },
  previewDialog: {
    minWidth: '600px',
    maxWidth: '800px',
  },
  storyPreview: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  pagePreview: {
    display: 'flex',
    ...shorthands.gap('16px'),
    ...shorthands.padding('12px'),
    ...shorthands.border('1px', 'solid', tokens.colorNeutralStroke2),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  pageImage: {
    width: '80px',
    height: '80px',
    ...shorthands.borderRadius(tokens.borderRadiusSmall),
    objectFit: 'cover',
    backgroundColor: tokens.colorNeutralBackground3,
  },
  pageText: {
    flex: 1,
    fontSize: tokens.fontSizeBase200,
    lineHeight: '1.4',
  },
});

const StoriesPage: React.FC = () => {
  const styles = useStyles();
  const [stories, setStories] = useState<Story[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedStory, setSelectedStory] = useState<Story | null>(null);
  const [previewDialogOpen, setPreviewDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // 分页参数
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    limit: 20,
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  useEffect(() => {
    loadStories();
  }, [pagination]);

  const loadStories = async () => {
    try {
      setLoading(true);
      setError(null);

      // 尝试调用真实API，如果失败则使用模拟数据
      try {
        const response = await api.stories.list(pagination);
        if (response.success && response.data) {
          setStories(response.data.items || response.data);
          return;
        }
      } catch (apiError) {
        console.warn('Stories API调用失败，使用模拟数据:', apiError);
      }

      // 如果API调用失败，使用模拟数据
      const mockStories: Story[] = [
        {
          id: '1',
          user_id: '1',
          title: '小兔子的冒险之旅',
          character_name: '小白',
          character_age: 5,
          character_traits: ['勇敢', '善良', '好奇'],
          theme: '冒险',
          setting: '森林',
          style: '温馨',
          voice: '旁白',
          pages: [
            {
              text: '从前，有一只小兔子叫小白，它住在美丽的森林里。',
              imagePrompt: 'A cute white rabbit in a beautiful forest',
              imageUrl: 'https://via.placeholder.com/400x300/87CEEB/FFFFFF?text=Story+Page+1',
            },
            {
              text: '有一天，小白决定去探索森林深处的秘密。',
              imagePrompt: 'A white rabbit exploring deep forest',
              imageUrl: 'https://via.placeholder.com/400x300/98FB98/FFFFFF?text=Story+Page+2',
            },
          ],
          audio_url: 'https://example.com/audio/story1.mp3',
          cover_image_url: 'https://via.placeholder.com/200x300/FFB6C1/FFFFFF?text=Story+Cover',
          status: 'completed',
          created_at: '2024-07-20T10:30:00Z',
          updated_at: '2024-07-20T11:45:00Z',
        },
        {
          id: '2',
          user_id: '2',
          title: '太空小英雄',
          character_name: '小明',
          character_age: 8,
          character_traits: ['聪明', '勇敢'],
          theme: '科幻',
          setting: '太空',
          style: '现代',
          voice: '第一人称',
          pages: [
            {
              text: '我是小明，一个太空探险家。今天我要去火星探险！',
              imagePrompt: 'A young astronaut boy in space suit',
              imageUrl: 'https://via.placeholder.com/400x300/4169E1/FFFFFF?text=Space+Adventure',
            },
          ],
          status: 'generating',
          created_at: '2024-07-22T09:15:00Z',
          updated_at: '2024-07-22T09:15:00Z',
        },
        {
          id: '3',
          user_id: '3',
          title: '魔法学校的秘密',
          character_name: '小莉',
          character_age: 10,
          character_traits: ['聪明', '好学'],
          theme: '魔法',
          setting: '学校',
          style: '奇幻',
          voice: '旁白',
          pages: [],
          status: 'failed',
          created_at: '2024-07-21T14:20:00Z',
          updated_at: '2024-07-21T14:25:00Z',
        },
      ];

      // 应用搜索过滤
      let filteredStories = mockStories;
      if (searchQuery) {
        filteredStories = mockStories.filter(story =>
          story.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          story.character_name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // 应用状态过滤
      if (statusFilter !== 'all') {
        filteredStories = filteredStories.filter(story => story.status === statusFilter);
      }

      setStories(filteredStories);
    } catch (err) {
      console.error('Failed to load stories:', err);
      setError(err instanceof Error ? err.message : '加载故事失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, search: query, page: 1 }));
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    loadStories(); // 重新加载数据
  };

  const handlePreviewStory = (story: Story) => {
    setSelectedStory(story);
    setPreviewDialogOpen(true);
  };

  const handleEditStory = (story: Story) => {
    // TODO: 实现编辑故事功能
    console.log('编辑故事:', story);
    // 这里可以导航到编辑页面或打开编辑对话框
  };

  const handleDeleteStory = (story: Story) => {
    setSelectedStory(story);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteStory = async () => {
    if (!selectedStory) return;

    try {
      // 实际应该调用 api.stories.delete(selectedStory.id)
      console.log('Deleting story:', selectedStory.id);
      setStories(prev => prev.filter(s => s.id !== selectedStory.id));
      setDeleteDialogOpen(false);
      setSelectedStory(null);
    } catch (err) {
      console.error('Failed to delete story:', err);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return (
          <Badge appearance="filled" color="success" className={styles.statusBadge}>
            已完成
          </Badge>
        );
      case 'generating':
        return (
          <Badge appearance="filled" color="warning" className={styles.statusBadge}>
            生成中
          </Badge>
        );
      case 'failed':
        return (
          <Badge appearance="filled" color="danger" className={styles.statusBadge}>
            失败
          </Badge>
        );
      default:
        return (
          <Badge appearance="outline" className={styles.statusBadge}>
            未知
          </Badge>
        );
    }
  };

  // 定义表格列
  const columns: TableColumnDefinition<Story>[] = [
    createTableColumn<Story>({
      columnId: 'story',
      compare: (a, b) => a.title.localeCompare(b.title),
      renderHeaderCell: () => '故事',
      renderCell: (story) => (
        <div className={styles.storyCell}>
          {story.cover_image_url ? (
            <Image
              src={story.cover_image_url}
              alt={story.title}
              className={styles.coverImage}
            />
          ) : (
            <Avatar
              name={story.title}
              size={40}
              shape="square"
            />
          )}
          <div className={styles.storyInfo}>
            <div className={styles.storyTitle}>{story.title}</div>
            <div className={styles.storyMeta}>
              主角: {story.character_name} ({story.character_age}岁) • {story.theme}
            </div>
          </div>
        </div>
      ),
    }),
    createTableColumn<Story>({
      columnId: 'status',
      compare: (a, b) => a.status.localeCompare(b.status),
      renderHeaderCell: () => '状态',
      renderCell: (story) => getStatusBadge(story.status),
    }),
    createTableColumn<Story>({
      columnId: 'pages',
      compare: (a, b) => b.pages.length - a.pages.length,
      renderHeaderCell: () => '页数',
      renderCell: (story) => (
        <Text>{story.pages.length} 页</Text>
      ),
    }),
    createTableColumn<Story>({
      columnId: 'created_at',
      compare: (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      renderHeaderCell: () => '创建时间',
      renderCell: (story) => (
        <Text>{new Date(story.created_at).toLocaleDateString('zh-CN')}</Text>
      ),
    }),
    createTableColumn<Story>({
      columnId: 'actions',
      renderHeaderCell: () => '操作',
      renderCell: (story) => (
        <div className={styles.actionButtons}>
          <Tooltip content="预览故事" relationship="label">
            <Button
              appearance="subtle"
              icon={<Eye24Regular />}
              size="small"
              onClick={() => handlePreviewStory(story)}
            />
          </Tooltip>
          <Tooltip content="编辑故事" relationship="label">
            <Button
              appearance="subtle"
              icon={<Edit24Regular />}
              size="small"
              disabled={story.status === 'generating'}
              onClick={() => handleEditStory(story)}
            />
          </Tooltip>
          <Tooltip content="删除故事" relationship="label">
            <Button
              appearance="subtle"
              icon={<Delete24Regular />}
              size="small"
              onClick={() => handleDeleteStory(story)}
            />
          </Tooltip>
        </div>
      ),
    }),
  ];

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载故事数据...</Text>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          内容管理
        </Text>
        <div className={styles.headerActions}>
          <Button
            appearance="primary"
            icon={<Add24Regular />}
          >
            创建故事
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <MessageBar intent="error">
          {error}
        </MessageBar>
      )}

      {/* 搜索和过滤 */}
      <div className={styles.filters}>
        <SearchBox
          placeholder="搜索故事标题或主角名..."
          value={searchQuery}
          onChange={(_, data) => handleSearch(data.value)}
          contentBefore={<Search24Regular />}
          style={{ minWidth: '300px' }}
        />
        <Dropdown
          placeholder="状态筛选"
          value={statusFilter}
          onOptionSelect={(_, data) => handleStatusFilter(data.optionValue || 'all')}
        >
          <Option value="all">全部状态</Option>
          <Option value="completed">已完成</Option>
          <Option value="generating">生成中</Option>
          <Option value="failed">失败</Option>
        </Dropdown>
        <Button
          appearance="subtle"
          icon={<Filter24Regular />}
        >
          更多筛选
        </Button>
      </div>

      {/* 故事列表 */}
      <Card className={styles.storyCard}>
        {stories.length === 0 ? (
          <div className={styles.emptyState}>
            <Document24Regular style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Text>暂无故事数据</Text>
          </div>
        ) : (
          <DataGrid
            items={stories}
            columns={columns}
            sortable
            getRowId={(item) => item.id}
          >
            <DataGridHeader>
              <DataGridRow>
                {({ renderHeaderCell }) => (
                  <DataGridHeaderCell>{renderHeaderCell()}</DataGridHeaderCell>
                )}
              </DataGridRow>
            </DataGridHeader>
            <DataGridBody<Story>>
              {({ item, rowId }) => (
                <DataGridRow<Story> key={rowId}>
                  {({ renderCell }) => (
                    <DataGridCell>{renderCell(item)}</DataGridCell>
                  )}
                </DataGridRow>
              )}
            </DataGridBody>
          </DataGrid>
        )}
      </Card>

      {/* 故事预览对话框 */}
      <Dialog open={previewDialogOpen} onOpenChange={(_, data) => setPreviewDialogOpen(data.open)}>
        <DialogSurface className={styles.previewDialog}>
          <DialogBody>
            <DialogTitle>{selectedStory?.title}</DialogTitle>
            <DialogContent>
              {selectedStory && (
                <div className={styles.storyPreview}>
                  <div style={{ marginBottom: '16px' }}>
                    <Text weight="semibold">故事信息</Text>
                    <div style={{ marginTop: '8px', fontSize: tokens.fontSizeBase200 }}>
                      <div>主角: {selectedStory.character_name} ({selectedStory.character_age}岁)</div>
                      <div>特征: {selectedStory.character_traits.join(', ')}</div>
                      <div>主题: {selectedStory.theme} • 场景: {selectedStory.setting}</div>
                      <div>风格: {selectedStory.style} • 叙述: {selectedStory.voice}</div>
                    </div>
                  </div>

                  <div>
                    <Text weight="semibold">故事内容 ({selectedStory.pages.length} 页)</Text>
                    <div style={{ marginTop: '12px' }}>
                      {selectedStory.pages.map((page, index) => (
                        <div key={index} className={styles.pagePreview}>
                          <div className={styles.pageImage}>
                            {page.imageUrl ? (
                              <Image
                                src={page.imageUrl}
                                alt={`第${index + 1}页`}
                                fit="cover"
                                style={{ width: '100%', height: '100%' }}
                              />
                            ) : (
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                                color: tokens.colorNeutralForeground3
                              }}>
                                第{index + 1}页
                              </div>
                            )}
                          </div>
                          <div className={styles.pageText}>
                            <Text weight="semibold" style={{ marginBottom: '8px' }}>
                              第 {index + 1} 页
                            </Text>
                            <Text>{page.text}</Text>
                          </div>
                        </div>
                      ))}
                      {selectedStory.pages.length === 0 && (
                        <div style={{ textAlign: 'center', padding: '20px', color: tokens.colorNeutralForeground3 }}>
                          暂无故事内容
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">关闭</Button>
              </DialogTrigger>
              {selectedStory?.audio_url && (
                <Button appearance="primary" icon={<Play24Regular />}>
                  播放音频
                </Button>
              )}
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={(_, data) => setDeleteDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>确认删除故事</DialogTitle>
            <DialogContent>
              确定要删除故事 "{selectedStory?.title}" 吗？此操作不可撤销。
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button appearance="primary" onClick={confirmDeleteStory}>
                确认删除
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};

export default StoriesPage;
