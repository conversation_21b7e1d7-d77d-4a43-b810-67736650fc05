/**
 * 订单管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
  Button,
  SearchBox,
  Dropdown,
  Option,
  DataGrid,
  DataGridHeader,
  DataGridRow,
  DataGridHeaderCell,
  DataGridCell,
  DataGridBody,
  createTableColumn,
  TableColumnDefinition,
  Badge,
  Spinner,
  MessageBar,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogBody,
  Field,
  Input,

  Tooltip,
} from '@fluentui/react-components';
import {
  Search24Regular,
  Filter24Regular,
  Eye24Regular,
  Edit24Regular,
  ShoppingBag24Regular,

  Print24Regular,
} from '@fluentui/react-icons';
import { PhysicalBookOrder, PaginationParams } from '@/types';
import { api } from '@/services/apiClient';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...shorthands.gap('16px'),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  filters: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
    ...shorthands.padding('16px'),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  orderCard: {
    ...shorthands.padding('20px'),
  },
  orderCell: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('4px'),
  },
  orderNumber: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  orderMeta: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  actionButtons: {
    display: 'flex',
    ...shorthands.gap('8px'),
  },
  statusBadge: {
    textTransform: 'capitalize',
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  emptyState: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    textAlign: 'center',
    color: tokens.colorNeutralForeground3,
  },
  orderDetails: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  detailSection: {
    ...shorthands.padding('12px'),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  detailTitle: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    marginBottom: '8px',
  },
  detailContent: {
    fontSize: tokens.fontSizeBase200,
    lineHeight: '1.4',
  },
});

const OrdersPage: React.FC = () => {
  const styles = useStyles();
  const [orders, setOrders] = useState<PhysicalBookOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedOrder, setSelectedOrder] = useState<PhysicalBookOrder | null>(null);
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [updateDialogOpen, setUpdateDialogOpen] = useState(false);
  const [trackingNumber, setTrackingNumber] = useState('');

  // 分页参数
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    limit: 20,
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  useEffect(() => {
    loadOrders();
  }, [pagination]);

  const loadOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      // 尝试调用真实API，如果失败则使用模拟数据
      try {
        const response = await api.orders.list(pagination);
        if (response.success && response.data) {
          setOrders(response.data.items || response.data);
          return;
        }
      } catch (apiError) {
        console.warn('Orders API调用失败，使用模拟数据:', apiError);
      }

      // 如果API调用失败，使用模拟数据
      const mockOrders: PhysicalBookOrder[] = [
        {
          id: '1',
          story_id: '1',
          user_id: '1',
          order_number: 'SW-2024-001',
          customization: {
            size: 'A4',
            cover_type: '硬封面',
            paper_type: '铜版纸',
            binding: '精装',
          },
          shipping_info: {
            name: '张小明',
            address: '北京市朝阳区xxx街道xxx号',
            city: '北京',
            state: '北京',
            postal_code: '100000',
            country: '中国',
            phone: '13800138000',
          },
          status: 'printing',
          price: 29.99,
          stripe_payment_intent_id: 'pi_1234567890',
          created_at: '2024-07-20T10:30:00Z',
          updated_at: '2024-07-22T09:15:00Z',
        },
        {
          id: '2',
          story_id: '2',
          user_id: '2',
          order_number: 'SW-2024-002',
          customization: {
            size: 'A5',
            cover_type: '软封面',
            paper_type: '普通纸',
            binding: '平装',
          },
          shipping_info: {
            name: '李小红',
            address: '上海市浦东新区xxx路xxx号',
            city: '上海',
            state: '上海',
            postal_code: '200000',
            country: '中国',
            phone: '13900139000',
          },
          status: 'shipped',
          price: 19.99,
          tracking_number: 'SF1234567890',
          created_at: '2024-07-18T14:20:00Z',
          updated_at: '2024-07-21T16:45:00Z',
        },
        {
          id: '3',
          story_id: '3',
          user_id: '3',
          order_number: 'SW-2024-003',
          customization: {
            size: 'A4',
            cover_type: '硬封面',
            paper_type: '铜版纸',
            binding: '精装',
          },
          shipping_info: {
            name: '王小华',
            address: '广州市天河区xxx大道xxx号',
            city: '广州',
            state: '广东',
            postal_code: '510000',
            country: '中国',
          },
          status: 'pending',
          price: 29.99,
          created_at: '2024-07-22T08:00:00Z',
          updated_at: '2024-07-22T08:00:00Z',
        },
      ];

      // 应用搜索过滤
      let filteredOrders = mockOrders;
      if (searchQuery) {
        filteredOrders = mockOrders.filter(order =>
          order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
          order.shipping_info.name.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      // 应用状态过滤
      if (statusFilter !== 'all') {
        filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
      }

      setOrders(filteredOrders);
    } catch (err) {
      console.error('Failed to load orders:', err);
      setError(err instanceof Error ? err.message : '加载订单失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, search: query, page: 1 }));
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
    loadOrders(); // 重新加载数据
  };

  const handleViewDetails = (order: PhysicalBookOrder) => {
    setSelectedOrder(order);
    setDetailsDialogOpen(true);
  };

  const handleUpdateOrder = (order: PhysicalBookOrder) => {
    setSelectedOrder(order);
    setTrackingNumber(order.tracking_number || '');
    setUpdateDialogOpen(true);
  };

  const saveOrderUpdate = async () => {
    if (!selectedOrder) return;

    try {
      // 实际应该调用 api.orders.update(selectedOrder.id, { tracking_number: trackingNumber, status: 'shipped' })
      console.log('Updating order:', selectedOrder.id, 'tracking:', trackingNumber);

      setOrders(prev => prev.map(order =>
        order.id === selectedOrder.id
          ? { ...order, tracking_number: trackingNumber, status: 'shipped', updated_at: new Date().toISOString() }
          : order
      ));

      setUpdateDialogOpen(false);
      setSelectedOrder(null);
      setTrackingNumber('');
    } catch (err) {
      console.error('Failed to update order:', err);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <Badge appearance="outline" color="warning" className={styles.statusBadge}>
            待处理
          </Badge>
        );
      case 'printing':
        return (
          <Badge appearance="filled" color="brand" className={styles.statusBadge}>
            印刷中
          </Badge>
        );
      case 'shipped':
        return (
          <Badge appearance="filled" color="success" className={styles.statusBadge}>
            已发货
          </Badge>
        );
      case 'delivered':
        return (
          <Badge appearance="filled" color="success" className={styles.statusBadge}>
            已送达
          </Badge>
        );
      case 'canceled':
        return (
          <Badge appearance="filled" color="danger" className={styles.statusBadge}>
            已取消
          </Badge>
        );
      default:
        return (
          <Badge appearance="outline" className={styles.statusBadge}>
            未知
          </Badge>
        );
    }
  };

  // 定义表格列
  const columns: TableColumnDefinition<PhysicalBookOrder>[] = [
    createTableColumn<PhysicalBookOrder>({
      columnId: 'order',
      compare: (a, b) => a.order_number.localeCompare(b.order_number),
      renderHeaderCell: () => '订单',
      renderCell: (order) => (
        <div className={styles.orderCell}>
          <div className={styles.orderNumber}>{order.order_number}</div>
          <div className={styles.orderMeta}>
            收件人: {order.shipping_info.name}
          </div>
        </div>
      ),
    }),
    createTableColumn<PhysicalBookOrder>({
      columnId: 'customization',
      renderHeaderCell: () => '定制信息',
      renderCell: (order) => (
        <div className={styles.orderCell}>
          <div>{order.customization.size} • {order.customization.cover_type}</div>
          <div className={styles.orderMeta}>
            {order.customization.paper_type} • {order.customization.binding}
          </div>
        </div>
      ),
    }),
    createTableColumn<PhysicalBookOrder>({
      columnId: 'status',
      compare: (a, b) => a.status.localeCompare(b.status),
      renderHeaderCell: () => '状态',
      renderCell: (order) => getStatusBadge(order.status),
    }),
    createTableColumn<PhysicalBookOrder>({
      columnId: 'price',
      compare: (a, b) => b.price - a.price,
      renderHeaderCell: () => '价格',
      renderCell: (order) => (
        <Text>${order.price.toFixed(2)}</Text>
      ),
    }),
    createTableColumn<PhysicalBookOrder>({
      columnId: 'created_at',
      compare: (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      renderHeaderCell: () => '下单时间',
      renderCell: (order) => (
        <Text>{new Date(order.created_at).toLocaleDateString('zh-CN')}</Text>
      ),
    }),
    createTableColumn<PhysicalBookOrder>({
      columnId: 'actions',
      renderHeaderCell: () => '操作',
      renderCell: (order) => (
        <div className={styles.actionButtons}>
          <Tooltip content="查看详情" relationship="label">
            <Button
              appearance="subtle"
              icon={<Eye24Regular />}
              size="small"
              onClick={() => handleViewDetails(order)}
            />
          </Tooltip>
          <Tooltip content="更新订单" relationship="label">
            <Button
              appearance="subtle"
              icon={<Edit24Regular />}
              size="small"
              onClick={() => handleUpdateOrder(order)}
              disabled={order.status === 'delivered' || order.status === 'canceled'}
            />
          </Tooltip>
          <Tooltip content="打印标签" relationship="label">
            <Button
              appearance="subtle"
              icon={<Print24Regular />}
              size="small"
              disabled={order.status === 'pending'}
            />
          </Tooltip>
        </div>
      ),
    }),
  ];

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载订单数据...</Text>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          订单管理
        </Text>
        <div className={styles.headerActions}>
          <Button
            appearance="secondary"
            icon={<Print24Regular />}
          >
            批量打印
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <MessageBar intent="error">
          {error}
        </MessageBar>
      )}

      {/* 搜索和过滤 */}
      <div className={styles.filters}>
        <SearchBox
          placeholder="搜索订单号或收件人..."
          value={searchQuery}
          onChange={(_, data) => handleSearch(data.value)}
          contentBefore={<Search24Regular />}
          style={{ minWidth: '300px' }}
        />
        <Dropdown
          placeholder="状态筛选"
          value={statusFilter}
          onOptionSelect={(_, data) => handleStatusFilter(data.optionValue || 'all')}
        >
          <Option value="all">全部状态</Option>
          <Option value="pending">待处理</Option>
          <Option value="printing">印刷中</Option>
          <Option value="shipped">已发货</Option>
          <Option value="delivered">已送达</Option>
          <Option value="canceled">已取消</Option>
        </Dropdown>
        <Button
          appearance="subtle"
          icon={<Filter24Regular />}
        >
          更多筛选
        </Button>
      </div>

      {/* 订单列表 */}
      <Card className={styles.orderCard}>
        {orders.length === 0 ? (
          <div className={styles.emptyState}>
            <ShoppingBag24Regular style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Text>暂无订单数据</Text>
          </div>
        ) : (
          <DataGrid
            items={orders}
            columns={columns}
            sortable
            getRowId={(item) => item.id}
          >
            <DataGridHeader>
              <DataGridRow>
                {({ renderHeaderCell }) => (
                  <DataGridHeaderCell>{renderHeaderCell()}</DataGridHeaderCell>
                )}
              </DataGridRow>
            </DataGridHeader>
            <DataGridBody<PhysicalBookOrder>>
              {({ item, rowId }) => (
                <DataGridRow<PhysicalBookOrder> key={rowId}>
                  {({ renderCell }) => (
                    <DataGridCell>{renderCell(item)}</DataGridCell>
                  )}
                </DataGridRow>
              )}
            </DataGridBody>
          </DataGrid>
        )}
      </Card>

      {/* 订单详情对话框 */}
      <Dialog open={detailsDialogOpen} onOpenChange={(_, data) => setDetailsDialogOpen(data.open)}>
        <DialogSurface style={{ minWidth: '600px' }}>
          <DialogBody>
            <DialogTitle>订单详情 - {selectedOrder?.order_number}</DialogTitle>
            <DialogContent>
              {selectedOrder && (
                <div className={styles.orderDetails}>
                  <div className={styles.detailSection}>
                    <div className={styles.detailTitle}>基本信息</div>
                    <div className={styles.detailContent}>
                      <div>订单号: {selectedOrder.order_number}</div>
                      <div>状态: {getStatusBadge(selectedOrder.status)}</div>
                      <div>价格: ${selectedOrder.price.toFixed(2)}</div>
                      <div>下单时间: {new Date(selectedOrder.created_at).toLocaleString('zh-CN')}</div>
                      {selectedOrder.tracking_number && (
                        <div>物流单号: {selectedOrder.tracking_number}</div>
                      )}
                    </div>
                  </div>

                  <div className={styles.detailSection}>
                    <div className={styles.detailTitle}>定制信息</div>
                    <div className={styles.detailContent}>
                      <div>尺寸: {selectedOrder.customization.size}</div>
                      <div>封面: {selectedOrder.customization.cover_type}</div>
                      <div>纸张: {selectedOrder.customization.paper_type}</div>
                      <div>装订: {selectedOrder.customization.binding}</div>
                    </div>
                  </div>

                  <div className={styles.detailSection}>
                    <div className={styles.detailTitle}>收货信息</div>
                    <div className={styles.detailContent}>
                      <div>收件人: {selectedOrder.shipping_info.name}</div>
                      <div>电话: {selectedOrder.shipping_info.phone || '未提供'}</div>
                      <div>地址: {selectedOrder.shipping_info.address}</div>
                      <div>城市: {selectedOrder.shipping_info.city}, {selectedOrder.shipping_info.state}</div>
                      <div>邮编: {selectedOrder.shipping_info.postal_code}</div>
                      <div>国家: {selectedOrder.shipping_info.country}</div>
                    </div>
                  </div>
                </div>
              )}
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">关闭</Button>
              </DialogTrigger>
              <Button appearance="primary" icon={<Print24Regular />}>
                打印详情
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 更新订单对话框 */}
      <Dialog open={updateDialogOpen} onOpenChange={(_, data) => setUpdateDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>更新订单 - {selectedOrder?.order_number}</DialogTitle>
            <DialogContent>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <Field label="物流单号">
                  <Input
                    value={trackingNumber}
                    onChange={(_, data) => setTrackingNumber(data.value)}
                    placeholder="请输入物流单号"
                  />
                </Field>
                <Text style={{ fontSize: tokens.fontSizeBase200, color: tokens.colorNeutralForeground2 }}>
                  添加物流单号后，订单状态将自动更新为"已发货"
                </Text>
              </div>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button
                appearance="primary"
                onClick={saveOrderUpdate}
                disabled={!trackingNumber.trim()}
              >
                保存更新
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};

export default OrdersPage;
