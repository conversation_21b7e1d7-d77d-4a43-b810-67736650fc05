/**
 * 订单管理页面
 */

import React from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
} from '@fluentui/react-components';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  placeholder: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '400px',
    ...shorthands.padding('40px'),
    textAlign: 'center',
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  icon: {
    fontSize: '64px',
    marginBottom: '16px',
  },
  title: {
    fontSize: tokens.fontSizeBase500,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: '8px',
  },
  description: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
    maxWidth: '400px',
  },
});

const OrdersPage: React.FC = () => {
  const styles = useStyles();

  return (
    <div className={styles.root}>
      <Text
        style={{
          fontSize: tokens.fontSizeBase500,
          fontWeight: tokens.fontWeightSemibold,
          color: tokens.colorNeutralForeground1,
        }}
      >
        订单管理
      </Text>

      <Card className={styles.placeholder}>
        <div className={styles.icon}>🛒</div>
        <Text className={styles.title}>订单管理功能开发中</Text>
        <Text className={styles.description}>
          此页面将包含实体书订单、支付记录、物流管理等功能。
          预计在第二阶段开发中完成。
        </Text>
      </Card>
    </div>
  );
};

export default OrdersPage;
