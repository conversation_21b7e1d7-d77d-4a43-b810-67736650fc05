/**
 * 用户管理页面
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
  CardHeader,
  CardPreview,
  Button,
  SearchBox,
  Dropdown,
  Option,
  DataGrid,
  DataGridHeader,
  DataGridRow,
  DataGridHeaderCell,
  DataGridCell,
  DataGridBody,
  createTableColumn,
  TableColumnDefinition,
  Badge,
  Avatar,
  Spinner,
  MessageBar,
  Dialog,
  DialogTrigger,
  DialogSurface,
  DialogTitle,
  DialogContent,
  DialogActions,
  DialogBody,
  Field,
  Input,
} from '@fluentui/react-components';
import {
  Add24Regular,
  Search24Regular,
  Filter24Regular,
  MoreHorizontal24Regular,
  Edit24Regular,
  Delete24Regular,
  Person24Regular,
} from '@fluentui/react-icons';
import { api } from '@/services/apiClient';
import { User, PaginationParams } from '@/types';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...shorthands.gap('16px'),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  filters: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
    ...shorthands.padding('16px'),
    backgroundColor: tokens.colorNeutralBackground2,
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  userCard: {
    ...shorthands.padding('20px'),
  },
  userCell: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  userInfo: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('4px'),
  },
  userName: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  userEmail: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  actionButtons: {
    display: 'flex',
    ...shorthands.gap('8px'),
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  emptyState: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '300px',
    textAlign: 'center',
    color: tokens.colorNeutralForeground3,
  },
});

const UsersPage: React.FC = () => {
  const styles = useStyles();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // 分页参数
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    limit: 20,
    search: '',
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  useEffect(() => {
    loadUsers();
  }, [pagination]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      // 模拟API调用，实际应该调用 api.users.list(pagination)
      const mockUsers: User[] = [
        {
          id: '1',
          email: '<EMAIL>',
          name: '系统管理员',
          avatar: undefined,
          google_id: 'google_123',
          credits: 1000,
          isAdmin: true,
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-07-22T15:45:00Z',
        },
        {
          id: '2',
          email: '<EMAIL>',
          name: '张小明',
          avatar: undefined,
          google_id: 'google_456',
          credits: 50,
          isAdmin: false,
          created_at: '2024-03-20T14:20:00Z',
          updated_at: '2024-07-20T09:15:00Z',
        },
        {
          id: '3',
          email: '<EMAIL>',
          name: '李小红',
          avatar: undefined,
          google_id: 'google_789',
          credits: 25,
          isAdmin: false,
          created_at: '2024-05-10T16:45:00Z',
          updated_at: '2024-07-21T11:30:00Z',
        },
      ];

      // 应用搜索过滤
      let filteredUsers = mockUsers;
      if (searchQuery) {
        filteredUsers = mockUsers.filter(user =>
          user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          user.email.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      setUsers(filteredUsers);
    } catch (err) {
      console.error('Failed to load users:', err);
      setError(err instanceof Error ? err.message : '加载用户失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, search: query, page: 1 }));
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      // 实际应该调用 api.users.delete(selectedUser.id)
      console.log('Deleting user:', selectedUser.id);
      setUsers(prev => prev.filter(u => u.id !== selectedUser.id));
      setDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (err) {
      console.error('Failed to delete user:', err);
    }
  };

  // 定义表格列
  const columns: TableColumnDefinition<User>[] = [
    createTableColumn<User>({
      columnId: 'user',
      compare: (a, b) => a.name.localeCompare(b.name),
      renderHeaderCell: () => '用户',
      renderCell: (user) => (
        <div className={styles.userCell}>
          <Avatar
            name={user.name}
            image={user.avatar ? { src: user.avatar } : undefined}
            size={32}
          />
          <div className={styles.userInfo}>
            <div className={styles.userName}>{user.name}</div>
            <div className={styles.userEmail}>{user.email}</div>
          </div>
        </div>
      ),
    }),
    createTableColumn<User>({
      columnId: 'role',
      compare: (a, b) => Number(b.isAdmin) - Number(a.isAdmin),
      renderHeaderCell: () => '角色',
      renderCell: (user) => (
        <Badge
          appearance={user.isAdmin ? 'filled' : 'outline'}
          color={user.isAdmin ? 'danger' : 'brand'}
        >
          {user.isAdmin ? '管理员' : '普通用户'}
        </Badge>
      ),
    }),
    createTableColumn<User>({
      columnId: 'credits',
      compare: (a, b) => b.credits - a.credits,
      renderHeaderCell: () => '积分',
      renderCell: (user) => (
        <Text>{user.credits.toLocaleString()}</Text>
      ),
    }),
    createTableColumn<User>({
      columnId: 'created_at',
      compare: (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      renderHeaderCell: () => '注册时间',
      renderCell: (user) => (
        <Text>{new Date(user.created_at).toLocaleDateString('zh-CN')}</Text>
      ),
    }),
    createTableColumn<User>({
      columnId: 'actions',
      renderHeaderCell: () => '操作',
      renderCell: (user) => (
        <div className={styles.actionButtons}>
          <Button
            appearance="subtle"
            icon={<Edit24Regular />}
            size="small"
            onClick={() => handleEditUser(user)}
          />
          <Button
            appearance="subtle"
            icon={<Delete24Regular />}
            size="small"
            onClick={() => handleDeleteUser(user)}
            disabled={user.isAdmin} // 不能删除管理员
          />
        </div>
      ),
    }),
  ];

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载用户数据...</Text>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          用户管理
        </Text>
        <div className={styles.headerActions}>
          <Button
            appearance="primary"
            icon={<Add24Regular />}
          >
            添加用户
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <MessageBar intent="error">
          {error}
        </MessageBar>
      )}

      {/* 搜索和过滤 */}
      <div className={styles.filters}>
        <SearchBox
          placeholder="搜索用户名或邮箱..."
          value={searchQuery}
          onChange={(_, data) => handleSearch(data.value)}
          contentBefore={<Search24Regular />}
          style={{ minWidth: '300px' }}
        />
        <Dropdown
          placeholder="状态筛选"
          value={statusFilter}
          onOptionSelect={(_, data) => setStatusFilter(data.optionValue || 'all')}
        >
          <Option value="all">全部用户</Option>
          <Option value="admin">管理员</Option>
          <Option value="user">普通用户</Option>
        </Dropdown>
        <Button
          appearance="subtle"
          icon={<Filter24Regular />}
        >
          更多筛选
        </Button>
      </div>

      {/* 用户列表 */}
      <Card className={styles.userCard}>
        {users.length === 0 ? (
          <div className={styles.emptyState}>
            <Person24Regular style={{ fontSize: '48px', marginBottom: '16px' }} />
            <Text>暂无用户数据</Text>
          </div>
        ) : (
          <DataGrid
            items={users}
            columns={columns}
            sortable
            getRowId={(item) => item.id}
          >
            <DataGridHeader>
              <DataGridRow>
                {({ renderHeaderCell }) => (
                  <DataGridHeaderCell>{renderHeaderCell()}</DataGridHeaderCell>
                )}
              </DataGridRow>
            </DataGridHeader>
            <DataGridBody<User>>
              {({ item, rowId }) => (
                <DataGridRow<User> key={rowId}>
                  {({ renderCell }) => (
                    <DataGridCell>{renderCell(item)}</DataGridCell>
                  )}
                </DataGridRow>
              )}
            </DataGridBody>
          </DataGrid>
        )}
      </Card>

      {/* 删除确认对话框 */}
      <Dialog open={deleteDialogOpen} onOpenChange={(_, data) => setDeleteDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>确认删除用户</DialogTitle>
            <DialogContent>
              确定要删除用户 "{selectedUser?.name}" 吗？此操作不可撤销。
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button appearance="primary" onClick={confirmDeleteUser}>
                确认删除
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>

      {/* 编辑用户对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={(_, data) => setEditDialogOpen(data.open)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>编辑用户</DialogTitle>
            <DialogContent>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                <Field label="用户名">
                  <Input defaultValue={selectedUser?.name} />
                </Field>
                <Field label="邮箱">
                  <Input defaultValue={selectedUser?.email} />
                </Field>
                <Field label="积分">
                  <Input type="number" defaultValue={selectedUser?.credits.toString()} />
                </Field>
              </div>
            </DialogContent>
            <DialogActions>
              <DialogTrigger disableButtonEnhancement>
                <Button appearance="secondary">取消</Button>
              </DialogTrigger>
              <Button appearance="primary">
                保存更改
              </Button>
            </DialogActions>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};

export default UsersPage;
