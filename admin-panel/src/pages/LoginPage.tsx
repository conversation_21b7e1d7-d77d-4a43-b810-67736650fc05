/**
 * 登录页面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  makeStyles,
  shorthands,
  tokens,
  Card,
  CardHeader,
  CardPreview,
  Text,
  Input,
  Button,
  Field,
  MessageBar,
  Spinner,
} from '@fluentui/react-components';
import { Eye24Regular, EyeOff24Regular } from '@fluentui/react-icons';
import { useAuthStore } from '@/stores/authStore';
import { createMockAdminToken, createMockUserToken } from '@/utils/jwtUtils';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: '100vh',
    backgroundColor: tokens.colorNeutralBackground1,
    ...shorthands.padding('20px'),
  },
  loginCard: {
    width: '100%',
    maxWidth: '400px',
    ...shorthands.padding('32px'),
  },
  header: {
    textAlign: 'center',
    marginBottom: '32px',
  },
  title: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: '8px',
  },
  subtitle: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
  },
  form: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('20px'),
  },
  passwordField: {
    position: 'relative',
  },
  passwordToggle: {
    position: 'absolute',
    right: '8px',
    top: '50%',
    transform: 'translateY(-50%)',
    zIndex: 1,
  },
  submitButton: {
    marginTop: '8px',
  },
  footer: {
    textAlign: 'center',
    marginTop: '24px',
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground3,
  },
});

const LoginPage: React.FC = () => {
  const styles = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, loginWithToken, isLoading, error, clearError, isAuthenticated } = useAuthStore();

  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // 如果已经登录，重定向到目标页面
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.email) {
      errors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = '请输入有效的邮箱地址';
    }

    if (!formData.password) {
      errors.password = '请输入密码';
    } else if (formData.password.length < 6) {
      errors.password = '密码至少需要6个字符';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      // 在开发环境中，如果是测试账户，使用模拟token
      if (import.meta.env.DEV && formData.email === '<EMAIL>') {
        const mockToken = createMockAdminToken();
        await loginWithToken(mockToken);
        return;
      }

      await login(formData);
      // 登录成功后会通过useEffect重定向
    } catch (error) {
      // 错误已经在store中处理
      console.error('Login failed:', error);
    }
  };

  // 测试登录函数（仅开发环境）
  const handleTestLogin = async (isAdmin: boolean) => {
    try {
      const mockToken = isAdmin ? createMockAdminToken() : createMockUserToken();
      await loginWithToken(mockToken);
    } catch (error) {
      console.error('Test login failed:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // 清除该字段的验证错误
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className={styles.root}>
      <Card className={styles.loginCard}>
        <CardHeader>
          <div className={styles.header}>
            <Text className={styles.title}>StoryWeaver 管理面板</Text>
            <Text className={styles.subtitle}>请使用管理员账户登录</Text>
          </div>
        </CardHeader>

        <CardPreview>
          {error && (
            <MessageBar intent="error" style={{ marginBottom: '20px' }}>
              {error}
            </MessageBar>
          )}

          <form className={styles.form} onSubmit={handleSubmit}>
            <Field
              label="邮箱地址"
              required
              validationMessage={validationErrors.email}
              validationState={validationErrors.email ? 'error' : 'none'}
            >
              <Input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="请输入邮箱地址"
                disabled={isLoading}
              />
            </Field>

            <Field
              label="密码"
              required
              validationMessage={validationErrors.password}
              validationState={validationErrors.password ? 'error' : 'none'}
            >
              <div className={styles.passwordField}>
                <Input
                  type={showPassword ? 'text' : 'password'}
                  value={formData.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="请输入密码"
                  disabled={isLoading}
                />
                <Button
                  appearance="subtle"
                  icon={showPassword ? <EyeOff24Regular /> : <Eye24Regular />}
                  onClick={() => setShowPassword(!showPassword)}
                  className={styles.passwordToggle}
                  disabled={isLoading}
                  aria-label={showPassword ? '隐藏密码' : '显示密码'}
                />
              </div>
            </Field>

            <Button
              type="submit"
              appearance="primary"
              size="large"
              disabled={isLoading}
              className={styles.submitButton}
            >
              {isLoading ? (
                <>
                  <Spinner size="tiny" />
                  登录中...
                </>
              ) : (
                '登录'
              )}
            </Button>
          </form>

          {/* 开发环境测试按钮 */}
          {import.meta.env.DEV && (
            <div style={{ marginTop: '20px', textAlign: 'center' }}>
              <Text style={{ fontSize: tokens.fontSizeBase200, color: tokens.colorNeutralForeground3, marginBottom: '12px', display: 'block' }}>
                开发环境测试登录
              </Text>
              <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
                <Button
                  appearance="secondary"
                  size="small"
                  onClick={() => handleTestLogin(true)}
                  disabled={isLoading}
                >
                  管理员登录
                </Button>
                <Button
                  appearance="secondary"
                  size="small"
                  onClick={() => handleTestLogin(false)}
                  disabled={isLoading}
                >
                  普通用户登录
                </Button>
              </div>
            </div>
          )}

          <div className={styles.footer}>
            <Text>© 2024 StoryWeaver. All rights reserved.</Text>
          </div>
        </CardPreview>
      </Card>
    </div>
  );
};

export default LoginPage;
