/**
 * 仪表板页面
 */

import React, { useEffect, useState } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Card,
  CardHeader,
  CardPreview,
  Text,
  Button,
  Spinner,
} from '@fluentui/react-components';
import {
  People24Regular,
  Document24Regular,
  ShoppingBag24Regular,
  CheckmarkCircle24Regular,
} from '@fluentui/react-icons';
import { api } from '@/services/apiClient';
import { DashboardStats } from '@/types';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  statsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    ...shorthands.gap('20px'),
  },
  statsCard: {
    ...shorthands.padding('24px'),
  },
  statsHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '16px',
  },
  statsIcon: {
    fontSize: '24px',
    color: tokens.colorBrandForeground1,
  },
  statsValue: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: '4px',
  },
  statsLabel: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
  },
  chartsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    ...shorthands.gap('20px'),
  },
  chartCard: {
    ...shorthands.padding('24px'),
    minHeight: '300px',
  },
  quickActions: {
    display: 'flex',
    flexWrap: 'wrap',
    ...shorthands.gap('12px'),
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
  errorContainer: {
    textAlign: 'center',
    ...shorthands.padding('40px'),
    color: tokens.colorPaletteRedForeground1,
  },
});

interface StatsCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ title, value, icon, trend }) => {
  const styles = useStyles();

  return (
    <Card className={styles.statsCard}>
      <div className={styles.statsHeader}>
        <div className={styles.statsIcon}>{icon}</div>
        {trend && (
          <Text
            style={{
              color: trend.isPositive ? tokens.colorPaletteGreenForeground1 : tokens.colorPaletteRedForeground1,
              fontSize: tokens.fontSizeBase200,
            }}
          >
            {trend.isPositive ? '+' : ''}{trend.value}%
          </Text>
        )}
      </div>
      <div className={styles.statsValue}>{value}</div>
      <div className={styles.statsLabel}>{title}</div>
    </Card>
  );
};

const DashboardPage: React.FC = () => {
  const styles = useStyles();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.monitoring.dashboard();
      if (response.success) {
        setStats(response.data);
      } else {
        throw new Error(response.error || '加载数据失败');
      }
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      setError(err instanceof Error ? err.message : '加载数据失败');
      
      // 设置模拟数据用于演示
      setStats({
        totalUsers: 1234,
        activeUsers: 567,
        totalStories: 2345,
        completedStories: 2100,
        generatingStories: 45,
        failedStories: 200,
        totalOrders: 156,
        pendingOrders: 23,
        monthlyRevenue: 12345.67,
        systemHealth: 'healthy',
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载仪表板数据...</Text>
      </div>
    );
  }

  if (error && !stats) {
    return (
      <div className={styles.errorContainer}>
        <Text>❌ {error}</Text>
        <Button
          appearance="primary"
          onClick={loadDashboardData}
          style={{ marginTop: '16px' }}
        >
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面标题 */}
      <div>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          欢迎回来！
        </Text>
        <Text
          style={{
            fontSize: tokens.fontSizeBase300,
            color: tokens.colorNeutralForeground2,
            display: 'block',
            marginTop: '4px',
          }}
        >
          这里是您的StoryWeaver管理面板概览
        </Text>
      </div>

      {/* 统计卡片 */}
      {stats && (
        <div className={styles.statsGrid}>
          <StatsCard
            title="总用户数"
            value={stats.totalUsers.toLocaleString()}
            icon={<People24Regular />}
            trend={{ value: 12, isPositive: true }}
          />
          <StatsCard
            title="活跃用户"
            value={stats.activeUsers.toLocaleString()}
            icon={<People24Regular />}
            trend={{ value: 8, isPositive: true }}
          />
          <StatsCard
            title="总故事数"
            value={stats.totalStories.toLocaleString()}
            icon={<Document24Regular />}
            trend={{ value: 15, isPositive: true }}
          />
          <StatsCard
            title="实体书订单"
            value={stats.totalOrders.toLocaleString()}
            icon={<ShoppingBag24Regular />}
            trend={{ value: 23, isPositive: true }}
          />
        </div>
      )}

      {/* 图表区域 */}
      <div className={styles.chartsGrid}>
        <Card className={styles.chartCard}>
          <CardHeader
            header={<Text weight="semibold">用户增长趋势</Text>}
          />
          <CardPreview>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '200px',
              color: tokens.colorNeutralForeground3 
            }}>
              📈 图表组件将在后续版本中实现
            </div>
          </CardPreview>
        </Card>

        <Card className={styles.chartCard}>
          <CardHeader
            header={<Text weight="semibold">故事创作分布</Text>}
          />
          <CardPreview>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'center', 
              alignItems: 'center', 
              height: '200px',
              color: tokens.colorNeutralForeground3 
            }}>
              📊 图表组件将在后续版本中实现
            </div>
          </CardPreview>
        </Card>
      </div>

      {/* 快速操作 */}
      <Card style={{ padding: '24px' }}>
        <CardHeader
          header={<Text weight="semibold">快速操作</Text>}
        />
        <CardPreview>
          <div className={styles.quickActions}>
            <Button appearance="primary" icon={<People24Regular />}>
              查看用户
            </Button>
            <Button appearance="secondary" icon={<Document24Regular />}>
              管理内容
            </Button>
            <Button appearance="secondary" icon={<ShoppingBag24Regular />}>
              处理订单
            </Button>
            <Button appearance="secondary" icon={<CheckmarkCircle24Regular />}>
              系统状态
            </Button>
          </div>
        </CardPreview>
      </Card>

      {/* 系统状态 */}
      {stats && (
        <Card style={{ padding: '24px' }}>
          <CardHeader
            header={<Text weight="semibold">系统状态</Text>}
          />
          <CardPreview>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div
                style={{
                  width: '12px',
                  height: '12px',
                  borderRadius: '50%',
                  backgroundColor: stats.systemHealth === 'healthy' 
                    ? tokens.colorPaletteGreenBackground3 
                    : tokens.colorPaletteRedBackground3,
                }}
              />
              <Text>
                系统运行{stats.systemHealth === 'healthy' ? '正常' : '异常'}
              </Text>
            </div>
          </CardPreview>
        </Card>
      )}
    </div>
  );
};

export default DashboardPage;
