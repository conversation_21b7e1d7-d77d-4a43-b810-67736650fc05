/**
 * 系统监控页面
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
  CardHeader,
  CardPreview,
  Button,
  Badge,
  Spinner,
  MessageBar,
  Tab,
  TabList,
  SelectTabData,
  SelectTabEvent,
  ProgressBar,
} from '@fluentui/react-components';
import {
  ArrowClockwise24Regular,
  Warning24Regular,
  CheckmarkCircle24Regular,
  DismissCircle24Regular,
  Clock24Regular,
  Server24Regular,
  Database24Regular,
  Bot24Regular,
} from '@fluentui/react-icons';
import { LineChart } from '@/components/charts/LineChart';
import { Bar<PERSON><PERSON> } from '@/components/charts/BarChart';
import { SystemHealth, AIServiceStats, DOStats, Alert } from '@/types';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...shorthands.gap('16px'),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  tabContent: {
    ...shorthands.padding('20px', '0'),
  },
  metricsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    ...shorthands.gap('20px'),
    marginBottom: '24px',
  },
  metricCard: {
    ...shorthands.padding('20px'),
  },
  metricHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '12px',
  },
  metricIcon: {
    fontSize: '24px',
  },
  metricValue: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: '4px',
  },
  metricLabel: {
    fontSize: tokens.fontSizeBase300,
    color: tokens.colorNeutralForeground2,
  },
  healthIndicator: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('8px'),
  },
  healthDot: {
    width: '12px',
    height: '12px',
    borderRadius: '50%',
  },
  chartsGrid: {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
    ...shorthands.gap('20px'),
    marginBottom: '24px',
  },
  chartCard: {
    ...shorthands.padding('20px'),
    minHeight: '350px',
  },
  alertsSection: {
    marginTop: '24px',
  },
  alertCard: {
    ...shorthands.padding('16px'),
    ...shorthands.margin('0', '0', '12px', '0'),
    ...shorthands.border('1px', 'solid', tokens.colorNeutralStroke2),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
  },
  alertHeader: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: '8px',
  },
  alertContent: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
});

const MonitoringPage: React.FC = () => {
  const styles = useStyles();
  const [selectedTab, setSelectedTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [aiStats, setAiStats] = useState<AIServiceStats | null>(null);
  const [doStats, setDoStats] = useState<DOStats | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);

  useEffect(() => {
    loadMonitoringData();
    // 设置定时刷新
    const interval = setInterval(loadMonitoringData, 30000); // 30秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const loadMonitoringData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 模拟API调用，实际应该调用相应的监控API
      const mockSystemHealth: SystemHealth = {
        apiGateway: 'healthy',
        database: 'healthy',
        aiServices: 'warning',
        storage: 'healthy',
        apiResponseTime: 145,
        dbResponseTime: 23,
        aiResponseTime: 2340,
        storageResponseTime: 89,
        apiResponseTrend: [
          { time: '10:00', value: 120 },
          { time: '10:30', value: 135 },
          { time: '11:00', value: 145 },
          { time: '11:30', value: 140 },
          { time: '12:00', value: 145 },
        ],
        errorRateTrend: [
          { time: '10:00', value: 0.1 },
          { time: '10:30', value: 0.2 },
          { time: '11:00', value: 0.15 },
          { time: '11:30', value: 0.3 },
          { time: '12:00', value: 0.25 },
        ],
        concurrentUsersTrend: [
          { time: '10:00', value: 45 },
          { time: '10:30', value: 52 },
          { time: '11:00', value: 48 },
          { time: '11:30', value: 61 },
          { time: '12:00', value: 58 },
        ],
      };

      const mockAiStats: AIServiceStats = {
        dailyCalls: 1247,
        monthlyCost: 234.56,
        avgResponseTime: 2340,
        successRate: 97.8,
        errorRate: 2.2,
        costTrend: [
          { date: '7-18', cost: 45.2 },
          { date: '7-19', cost: 52.1 },
          { date: '7-20', cost: 48.7 },
          { date: '7-21', cost: 61.3 },
          { date: '7-22', cost: 58.9 },
        ],
        callsTrend: [
          { date: '7-18', calls: 980 },
          { date: '7-19', calls: 1120 },
          { date: '7-20', calls: 1050 },
          { date: '7-21', calls: 1340 },
          { date: '7-22', calls: 1247 },
        ],
      };

      const mockDoStats: DOStats = {
        aiTaskQueue: {
          active: 12,
          requests: 1247,
          avgDuration: 2.3,
        },
        storyGeneration: {
          active: 8,
          requests: 456,
          avgDuration: 15.7,
        },
      };

      const mockAlerts: Alert[] = [
        {
          id: '1',
          severity: 'medium',
          message: 'AI服务响应时间超过2秒阈值',
          timestamp: '2024-07-22T11:45:00Z',
          acknowledged: false,
          source: 'AI服务监控',
        },
        {
          id: '2',
          severity: 'low',
          message: '数据库连接池使用率达到80%',
          timestamp: '2024-07-22T11:30:00Z',
          acknowledged: true,
          source: '数据库监控',
        },
      ];

      setSystemHealth(mockSystemHealth);
      setAiStats(mockAiStats);
      setDoStats(mockDoStats);
      setAlerts(mockAlerts);
    } catch (err) {
      console.error('Failed to load monitoring data:', err);
      setError(err instanceof Error ? err.message : '加载监控数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTabSelect = (_event: SelectTabEvent, data: SelectTabData) => {
    setSelectedTab(data.value as string);
  };

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return tokens.colorPaletteGreenBackground3;
      case 'warning':
        return tokens.colorPaletteYellowBackground3;
      case 'error':
        return tokens.colorPaletteRedBackground3;
      default:
        return tokens.colorNeutralBackground3;
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckmarkCircle24Regular style={{ color: tokens.colorPaletteGreenForeground1 }} />;
      case 'warning':
        return <Warning24Regular style={{ color: tokens.colorPaletteYellowForeground1 }} />;
      case 'error':
        return <DismissCircle24Regular style={{ color: tokens.colorPaletteRedForeground1 }} />;
      default:
        return <Clock24Regular style={{ color: tokens.colorNeutralForeground3 }} />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge appearance="filled" color="danger">严重</Badge>;
      case 'high':
        return <Badge appearance="filled" color="warning">高</Badge>;
      case 'medium':
        return <Badge appearance="outline" color="warning">中</Badge>;
      case 'low':
        return <Badge appearance="outline" color="brand">低</Badge>;
      default:
        return <Badge appearance="outline">未知</Badge>;
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载监控数据...</Text>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          系统监控
        </Text>
        <div className={styles.headerActions}>
          <Button
            appearance="secondary"
            icon={<ArrowClockwise24Regular />}
            onClick={loadMonitoringData}
          >
            刷新数据
          </Button>
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <MessageBar intent="error">
          {error}
        </MessageBar>
      )}

      {/* 监控分类标签 */}
      <TabList selectedValue={selectedTab} onTabSelect={handleTabSelect}>
        <Tab value="overview">📊 总览</Tab>
        <Tab value="ai">🤖 AI服务</Tab>
        <Tab value="system">⚙️ 系统性能</Tab>
        <Tab value="alerts">🚨 告警</Tab>
      </TabList>

      {/* 监控内容 */}
      <div className={styles.tabContent}>
        {selectedTab === 'overview' && systemHealth && (
          <div>
            {/* 系统健康状态 */}
            <div className={styles.metricsGrid}>
              <Card className={styles.metricCard}>
                <div className={styles.metricHeader}>
                  <div className={styles.metricIcon}>
                    <Server24Regular />
                  </div>
                  {getHealthIcon(systemHealth.apiGateway)}
                </div>
                <div className={styles.metricValue}>API网关</div>
                <div className={styles.metricLabel}>
                  响应时间: {systemHealth.apiResponseTime}ms
                </div>
                <div className={styles.healthIndicator}>
                  <div
                    className={styles.healthDot}
                    style={{ backgroundColor: getHealthColor(systemHealth.apiGateway) }}
                  />
                  <Text>{systemHealth.apiGateway === 'healthy' ? '正常' : systemHealth.apiGateway === 'warning' ? '警告' : '错误'}</Text>
                </div>
              </Card>

              <Card className={styles.metricCard}>
                <div className={styles.metricHeader}>
                  <div className={styles.metricIcon}>
                    <Database24Regular />
                  </div>
                  {getHealthIcon(systemHealth.database)}
                </div>
                <div className={styles.metricValue}>数据库</div>
                <div className={styles.metricLabel}>
                  响应时间: {systemHealth.dbResponseTime}ms
                </div>
                <div className={styles.healthIndicator}>
                  <div
                    className={styles.healthDot}
                    style={{ backgroundColor: getHealthColor(systemHealth.database) }}
                  />
                  <Text>{systemHealth.database === 'healthy' ? '正常' : systemHealth.database === 'warning' ? '警告' : '错误'}</Text>
                </div>
              </Card>

              <Card className={styles.metricCard}>
                <div className={styles.metricHeader}>
                  <div className={styles.metricIcon}>
                    <Bot24Regular />
                  </div>
                  {getHealthIcon(systemHealth.aiServices)}
                </div>
                <div className={styles.metricValue}>AI服务</div>
                <div className={styles.metricLabel}>
                  响应时间: {systemHealth.aiResponseTime}ms
                </div>
                <div className={styles.healthIndicator}>
                  <div
                    className={styles.healthDot}
                    style={{ backgroundColor: getHealthColor(systemHealth.aiServices) }}
                  />
                  <Text>{systemHealth.aiServices === 'healthy' ? '正常' : systemHealth.aiServices === 'warning' ? '警告' : '错误'}</Text>
                </div>
              </Card>

              <Card className={styles.metricCard}>
                <div className={styles.metricHeader}>
                  <div className={styles.metricIcon}>
                    <Server24Regular />
                  </div>
                  {getHealthIcon(systemHealth.storage)}
                </div>
                <div className={styles.metricValue}>存储服务</div>
                <div className={styles.metricLabel}>
                  响应时间: {systemHealth.storageResponseTime}ms
                </div>
                <div className={styles.healthIndicator}>
                  <div
                    className={styles.healthDot}
                    style={{ backgroundColor: getHealthColor(systemHealth.storage) }}
                  />
                  <Text>{systemHealth.storage === 'healthy' ? '正常' : systemHealth.storage === 'warning' ? '警告' : '错误'}</Text>
                </div>
              </Card>
            </div>

            {/* 性能图表 */}
            <div className={styles.chartsGrid}>
              <Card className={styles.chartCard}>
                <CardHeader header={<Text weight="semibold">API响应时间趋势</Text>} />
                <CardPreview>
                  <LineChart
                    data={systemHealth.apiResponseTrend.map(item => ({ name: item.time, value: item.value }))}
                    height={250}
                    color={tokens.colorBrandBackground}
                  />
                </CardPreview>
              </Card>

              <Card className={styles.chartCard}>
                <CardHeader header={<Text weight="semibold">并发用户数</Text>} />
                <CardPreview>
                  <LineChart
                    data={systemHealth.concurrentUsersTrend.map(item => ({ name: item.time, value: item.value }))}
                    height={250}
                    color={tokens.colorPaletteGreenBackground3}
                  />
                </CardPreview>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'ai' && aiStats && (
          <div>
            {/* AI服务指标 */}
            <div className={styles.metricsGrid}>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{aiStats.dailyCalls.toLocaleString()}</div>
                <div className={styles.metricLabel}>今日API调用</div>
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>${aiStats.monthlyCost.toFixed(2)}</div>
                <div className={styles.metricLabel}>本月成本</div>
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{aiStats.avgResponseTime}ms</div>
                <div className={styles.metricLabel}>平均响应时间</div>
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{aiStats.successRate}%</div>
                <div className={styles.metricLabel}>成功率</div>
              </Card>
            </div>

            {/* AI服务图表 */}
            <div className={styles.chartsGrid}>
              <Card className={styles.chartCard}>
                <CardHeader header={<Text weight="semibold">API调用趋势</Text>} />
                <CardPreview>
                  <LineChart
                    data={aiStats.callsTrend.map(item => ({ name: item.date, value: item.calls }))}
                    height={250}
                    color={tokens.colorPaletteBlueForeground2}
                  />
                </CardPreview>
              </Card>

              <Card className={styles.chartCard}>
                <CardHeader header={<Text weight="semibold">成本趋势</Text>} />
                <CardPreview>
                  <BarChart
                    data={aiStats.costTrend.map(item => ({ name: item.date, value: item.cost }))}
                    height={250}
                    color={tokens.colorPaletteYellowBackground3}
                  />
                </CardPreview>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'system' && doStats && (
          <div>
            {/* Durable Objects状态 */}
            <div className={styles.metricsGrid}>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{doStats.aiTaskQueue.active}</div>
                <div className={styles.metricLabel}>AI任务队列活跃数</div>
                <ProgressBar value={doStats.aiTaskQueue.active / 20} />
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{doStats.aiTaskQueue.requests.toLocaleString()}</div>
                <div className={styles.metricLabel}>总请求数</div>
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{doStats.storyGeneration.active}</div>
                <div className={styles.metricLabel}>故事生成活跃数</div>
                <ProgressBar value={doStats.storyGeneration.active / 15} />
              </Card>
              <Card className={styles.metricCard}>
                <div className={styles.metricValue}>{doStats.storyGeneration.avgDuration}s</div>
                <div className={styles.metricLabel}>平均生成时间</div>
              </Card>
            </div>
          </div>
        )}

        {selectedTab === 'alerts' && (
          <div className={styles.alertsSection}>
            <Text
              style={{
                fontSize: tokens.fontSizeBase400,
                fontWeight: tokens.fontWeightSemibold,
                marginBottom: '16px',
                display: 'block',
              }}
            >
              系统告警 ({alerts.filter(a => !a.acknowledged).length} 未处理)
            </Text>

            {alerts.length === 0 ? (
              <Card style={{ padding: '40px', textAlign: 'center' }}>
                <CheckmarkCircle24Regular style={{ fontSize: '48px', color: tokens.colorPaletteGreenForeground1, marginBottom: '16px' }} />
                <Text>暂无系统告警</Text>
              </Card>
            ) : (
              alerts.map(alert => (
                <div key={alert.id} className={styles.alertCard}>
                  <div className={styles.alertHeader}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      {getSeverityBadge(alert.severity)}
                      <Text weight="semibold">{alert.message}</Text>
                      {alert.acknowledged && (
                        <Badge appearance="outline" color="success">已确认</Badge>
                      )}
                    </div>
                    <Text style={{ fontSize: tokens.fontSizeBase200, color: tokens.colorNeutralForeground3 }}>
                      {new Date(alert.timestamp).toLocaleString('zh-CN')}
                    </Text>
                  </div>
                  <div className={styles.alertContent}>
                    来源: {alert.source}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MonitoringPage;
