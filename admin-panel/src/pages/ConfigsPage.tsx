/**
 * 系统配置页面
 */

import React, { useState, useEffect } from 'react';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Card,
  CardHeader,
  CardPreview,
  Button,
  Input,

  Spinner,
  MessageBar,
  Tab,
  TabList,
  SelectTabData,
  SelectTabEvent,
  Badge,

} from '@fluentui/react-components';
import {
  Save24Regular,
  ArrowClockwise24Regular,
  Eye24Regular,
  EyeOff24Regular,
  CheckmarkCircle24Regular,
  Warning24Regular,
  Play24Regular,
} from '@fluentui/react-icons';
import { ConfigItem } from '@/types';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('24px'),
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    flexWrap: 'wrap',
    ...shorthands.gap('16px'),
  },
  headerActions: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('12px'),
  },
  tabContent: {
    ...shorthands.padding('20px', '0'),
  },
  configSection: {
    ...shorthands.margin('0', '0', '24px', '0'),
  },
  configCard: {
    ...shorthands.padding('20px'),
    ...shorthands.margin('0', '0', '16px', '0'),
  },
  configItem: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('12px'),
    ...shorthands.padding('16px'),
    ...shorthands.border('1px', 'solid', tokens.colorNeutralStroke2),
    ...shorthands.borderRadius(tokens.borderRadiusMedium),
    ...shorthands.margin('0', '0', '12px', '0'),
  },
  configHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  configInfo: {
    display: 'flex',
    flexDirection: 'column',
    ...shorthands.gap('4px'),
  },
  configKey: {
    fontSize: tokens.fontSizeBase300,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
  },
  configDescription: {
    fontSize: tokens.fontSizeBase200,
    color: tokens.colorNeutralForeground2,
  },
  configValue: {
    display: 'flex',
    alignItems: 'center',
    ...shorthands.gap('8px'),
  },
  sensitiveValue: {
    fontFamily: 'monospace',
    backgroundColor: tokens.colorNeutralBackground3,
    ...shorthands.padding('4px', '8px'),
    ...shorthands.borderRadius(tokens.borderRadiusSmall),
  },
  loadingContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '200px',
    flexDirection: 'column',
    ...shorthands.gap('16px'),
  },
});

// 配置分类定义
const configCategories = [
  { key: 'ai', name: 'AI服务', icon: '🤖', description: 'AI模型和服务配置' },
  { key: 'payment', name: '支付配置', icon: '💳', description: '支付网关和价格设置' },
  { key: 'auth', name: '认证配置', icon: '🔐', description: '用户认证和安全设置' },
  { key: 'system', name: '系统配置', icon: '⚙️', description: '系统运行参数' },
];

const ConfigsPage: React.FC = () => {
  const styles = useStyles();
  const [selectedTab, setSelectedTab] = useState('ai');
  const [configs, setConfigs] = useState<Record<string, ConfigItem[]>>({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [visibleSensitive, setVisibleSensitive] = useState<Set<string>>(new Set());
  const [editedValues, setEditedValues] = useState<Record<string, string>>({});
  const [testingConfigs, setTestingConfigs] = useState<Set<string>>(new Set());
  const [testResults, setTestResults] = useState<Record<string, { success: boolean; message: string }>>({});

  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      setError(null);

      // 模拟加载配置数据
      const mockConfigs: Record<string, ConfigItem[]> = {
        ai: [
          {
            id: 'ai_gemini_api_key',
            category: 'ai',
            key: 'gemini_api_key',
            value: 'AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw',
            description: 'Gemini API密钥',
            is_sensitive: true,
            updated_by: 'system',
            updated_at: '2024-07-22T10:30:00Z',
          },
          {
            id: 'ai_gemini_model',
            category: 'ai',
            key: 'gemini_model',
            value: 'gemini-2.5-flash',
            description: 'Gemini模型版本',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-20T15:45:00Z',
          },
          {
            id: 'ai_max_tokens',
            category: 'ai',
            key: 'max_tokens',
            value: '2048',
            description: 'AI生成最大令牌数',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-18T09:20:00Z',
          },
        ],
        payment: [
          {
            id: 'payment_stripe_secret',
            category: 'payment',
            key: 'stripe_secret_key',
            value: 'sk_test_51234567890abcdef...',
            description: 'Stripe密钥（后端使用）',
            is_sensitive: true,
            updated_by: 'admin',
            updated_at: '2024-07-15T14:30:00Z',
          },
          {
            id: 'payment_stripe_public',
            category: 'payment',
            key: 'stripe_public_key',
            value: 'pk_test_51234567890abcdef...',
            description: 'Stripe公钥（前端使用）',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-15T14:30:00Z',
          },
          {
            id: 'payment_stripe_webhook',
            category: 'payment',
            key: 'stripe_webhook_secret',
            value: 'whsec_1234567890abcdef...',
            description: 'Stripe Webhook密钥',
            is_sensitive: true,
            updated_by: 'admin',
            updated_at: '2024-07-15T14:30:00Z',
          },
          {
            id: 'payment_credit_price',
            category: 'payment',
            key: 'credit_price_usd',
            value: '10.00',
            description: '积分包价格（美元）',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-10T11:15:00Z',
          },
          {
            id: 'payment_book_base_price',
            category: 'payment',
            key: 'book_base_price_usd',
            value: '19.99',
            description: '实体书基础价格（美元）',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-10T11:15:00Z',
          },
        ],
        auth: [
          {
            id: 'auth_google_client_id',
            category: 'auth',
            key: 'google_client_id',
            value: '123456789012-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com',
            description: 'Google OAuth客户端ID（前端使用）',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-22T10:00:00Z',
          },
          {
            id: 'auth_google_client_secret',
            category: 'auth',
            key: 'google_client_secret',
            value: 'GOCSPX-abcdefghijklmnopqrstuvwxyz',
            description: 'Google OAuth客户端密钥（后端使用）',
            is_sensitive: true,
            updated_by: 'admin',
            updated_at: '2024-07-22T10:00:00Z',
          },
          {
            id: 'auth_google_redirect_uri',
            category: 'auth',
            key: 'google_redirect_uri',
            value: 'https://storyweaver.com/auth/google/callback',
            description: 'Google OAuth重定向URI',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-22T10:00:00Z',
          },
          {
            id: 'auth_jwt_secret',
            category: 'auth',
            key: 'jwt_secret',
            value: 'storyweaver-jwt-secret-key-2024',
            description: 'JWT密钥',
            is_sensitive: true,
            updated_by: 'system',
            updated_at: '2024-07-01T08:00:00Z',
          },
          {
            id: 'auth_jwt_expires_in',
            category: 'auth',
            key: 'jwt_expires_in',
            value: '7d',
            description: 'JWT过期时间',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-01T08:00:00Z',
          },
        ],
        system: [
          {
            id: 'system_max_story_pages',
            category: 'system',
            key: 'max_story_pages',
            value: '12',
            description: '故事最大页数',
            is_sensitive: false,
            updated_by: 'admin',
            updated_at: '2024-07-12T16:45:00Z',
          },
        ],
      };

      setConfigs(mockConfigs);
    } catch (err) {
      console.error('Failed to load configs:', err);
      setError(err instanceof Error ? err.message : '加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTabSelect = (_event: SelectTabEvent, data: SelectTabData) => {
    setSelectedTab(data.value as string);
  };

  const toggleSensitiveVisibility = (configId: string) => {
    setVisibleSensitive(prev => {
      const newSet = new Set(prev);
      if (newSet.has(configId)) {
        newSet.delete(configId);
      } else {
        newSet.add(configId);
      }
      return newSet;
    });
  };

  const handleValueChange = (configId: string, value: string) => {
    setEditedValues(prev => ({ ...prev, [configId]: value }));
  };

  const saveConfig = async (config: ConfigItem) => {
    const newValue = editedValues[config.id] || config.value;
    if (newValue === config.value) return;

    try {
      setSaving(true);
      // 实际应该调用 api.configs.update(config.category, config.key, newValue)
      console.log('Saving config:', config.key, '=', newValue);

      // 更新本地状态
      setConfigs(prev => ({
        ...prev,
        [config.category]: prev[config.category].map(c =>
          c.id === config.id ? { ...c, value: newValue, updated_at: new Date().toISOString() } : c
        ),
      }));

      // 清除编辑状态
      setEditedValues(prev => {
        const newValues = { ...prev };
        delete newValues[config.id];
        return newValues;
      });

      setSuccess(`配置 ${config.key} 已保存`);
      setTimeout(() => setSuccess(null), 3000);
    } catch (err) {
      console.error('Failed to save config:', err);
      setError(err instanceof Error ? err.message : '保存配置失败');
    } finally {
      setSaving(false);
    }
  };

  const resetConfig = (config: ConfigItem) => {
    setEditedValues(prev => {
      const newValues = { ...prev };
      delete newValues[config.id];
      return newValues;
    });
  };

  const testConfig = async (config: ConfigItem) => {
    const currentValue = editedValues[config.id] ?? config.value;

    try {
      setTestingConfigs(prev => new Set(prev).add(config.id));

      // 根据配置类型进行不同的测试
      let testResult = { success: false, message: '' };

      switch (config.key) {
        case 'google_client_id':
          testResult = await testGoogleOAuth(currentValue, 'client_id');
          break;
        case 'google_client_secret':
          testResult = await testGoogleOAuth(currentValue, 'client_secret');
          break;
        case 'stripe_public_key':
          testResult = await testStripeKey(currentValue, 'public');
          break;
        case 'stripe_secret_key':
          testResult = await testStripeKey(currentValue, 'secret');
          break;
        case 'gemini_api_key':
          testResult = await testGeminiAPI(currentValue);
          break;
        default:
          testResult = { success: true, message: '配置格式验证通过' };
      }

      setTestResults(prev => ({ ...prev, [config.id]: testResult }));
    } catch (err) {
      setTestResults(prev => ({
        ...prev,
        [config.id]: {
          success: false,
          message: err instanceof Error ? err.message : '测试失败'
        }
      }));
    } finally {
      setTestingConfigs(prev => {
        const newSet = new Set(prev);
        newSet.delete(config.id);
        return newSet;
      });
    }
  };

  // Google OAuth测试
  const testGoogleOAuth = async (value: string, type: 'client_id' | 'client_secret') => {
    if (!value) {
      return { success: false, message: '配置值不能为空' };
    }

    if (type === 'client_id') {
      if (!value.includes('.apps.googleusercontent.com')) {
        return { success: false, message: 'Google Client ID格式不正确' };
      }
    } else {
      if (!value.startsWith('GOCSPX-')) {
        return { success: false, message: 'Google Client Secret格式不正确' };
      }
    }

    // 模拟API测试
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { success: true, message: 'Google OAuth配置验证通过' };
  };

  // Stripe密钥测试
  const testStripeKey = async (value: string, type: 'public' | 'secret') => {
    if (!value) {
      return { success: false, message: '配置值不能为空' };
    }

    const expectedPrefix = type === 'public' ? 'pk_' : 'sk_';
    if (!value.startsWith(expectedPrefix)) {
      return { success: false, message: `Stripe ${type} key格式不正确，应以${expectedPrefix}开头` };
    }

    // 模拟API测试
    await new Promise(resolve => setTimeout(resolve, 1500));
    return { success: true, message: 'Stripe密钥验证通过' };
  };

  // Gemini API测试
  const testGeminiAPI = async (value: string) => {
    if (!value) {
      return { success: false, message: '配置值不能为空' };
    }

    if (!value.startsWith('AIza')) {
      return { success: false, message: 'Gemini API Key格式不正确' };
    }

    // 模拟API测试
    await new Promise(resolve => setTimeout(resolve, 2000));
    return { success: true, message: 'Gemini API连接测试通过' };
  };

  // 批量测试当前分类的所有可测试配置
  const testAllConfigs = async () => {
    const categoryConfigs = configs[selectedTab] || [];
    const testableConfigs = categoryConfigs.filter(config =>
      ['google_client_id', 'google_client_secret', 'stripe_public_key', 'stripe_secret_key', 'gemini_api_key'].includes(config.key)
    );

    for (const config of testableConfigs) {
      await testConfig(config);
      // 添加延迟避免并发过多
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  };

  const renderConfigItem = (config: ConfigItem) => {
    const currentValue = editedValues[config.id] ?? config.value;
    const hasChanges = editedValues[config.id] !== undefined && editedValues[config.id] !== config.value;
    const isVisible = visibleSensitive.has(config.id);
    const isTesting = testingConfigs.has(config.id);
    const testResult = testResults[config.id];

    // 判断是否可以测试
    const canTest = ['google_client_id', 'google_client_secret', 'stripe_public_key', 'stripe_secret_key', 'gemini_api_key'].includes(config.key);

    return (
      <div key={config.id} className={styles.configItem}>
        <div className={styles.configHeader}>
          <div className={styles.configInfo}>
            <div className={styles.configKey}>
              {config.key}
              {config.is_sensitive && (
                <Badge color="warning" style={{ marginLeft: '8px' }}>
                  敏感
                </Badge>
              )}
              {hasChanges && (
                <Badge color="brand" style={{ marginLeft: '8px' }}>
                  已修改
                </Badge>
              )}
            </div>
            <div className={styles.configDescription}>{config.description}</div>
          </div>
          <div style={{ display: 'flex', gap: '8px' }}>
            {config.is_sensitive && (
              <Button
                appearance="subtle"
                icon={isVisible ? <EyeOff24Regular /> : <Eye24Regular />}
                size="small"
                onClick={() => toggleSensitiveVisibility(config.id)}
              />
            )}
            {canTest && (
              <Button
                appearance="subtle"
                icon={<Play24Regular />}
                size="small"
                onClick={() => testConfig(config)}
                disabled={isTesting || saving}
              >
                {isTesting ? '测试中...' : '测试'}
              </Button>
            )}
            {hasChanges && (
              <>
                <Button
                  appearance="subtle"
                  icon={<ArrowClockwise24Regular />}
                  size="small"
                  onClick={() => resetConfig(config)}
                />
                <Button
                  appearance="primary"
                  icon={<Save24Regular />}
                  size="small"
                  onClick={() => saveConfig(config)}
                  disabled={saving}
                />
              </>
            )}
          </div>
        </div>
        <div className={styles.configValue}>
          {config.is_sensitive && !isVisible ? (
            <div className={styles.sensitiveValue}>
              {'•'.repeat(20)}
            </div>
          ) : (
            <Input
              value={currentValue}
              onChange={(_, data) => handleValueChange(config.id, data.value)}
              style={{ flex: 1 }}
              type={config.is_sensitive && !isVisible ? 'password' : 'text'}
            />
          )}
        </div>

        {/* 测试结果显示 */}
        {testResult && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '8px 12px',
            backgroundColor: testResult.success ? tokens.colorPaletteGreenBackground1 : tokens.colorPaletteRedBackground1,
            borderRadius: tokens.borderRadiusSmall,
            marginTop: '8px'
          }}>
            {testResult.success ? (
              <CheckmarkCircle24Regular style={{ color: tokens.colorPaletteGreenForeground1 }} />
            ) : (
              <Warning24Regular style={{ color: tokens.colorPaletteRedForeground1 }} />
            )}
            <Text style={{
              fontSize: tokens.fontSizeBase200,
              color: testResult.success ? tokens.colorPaletteGreenForeground1 : tokens.colorPaletteRedForeground1
            }}>
              {testResult.message}
            </Text>
          </div>
        )}

        <div style={{ fontSize: tokens.fontSizeBase100, color: tokens.colorNeutralForeground3 }}>
          最后更新: {new Date(config.updated_at).toLocaleString('zh-CN')} by {config.updated_by}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spinner size="large" />
        <Text>加载配置数据...</Text>
      </div>
    );
  }

  return (
    <div className={styles.root}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Text
          style={{
            fontSize: tokens.fontSizeBase500,
            fontWeight: tokens.fontWeightSemibold,
            color: tokens.colorNeutralForeground1,
          }}
        >
          系统配置
        </Text>
        <div className={styles.headerActions}>
          <Button
            appearance="secondary"
            icon={<Play24Regular />}
            onClick={testAllConfigs}
            disabled={testingConfigs.size > 0}
          >
            测试所有配置
          </Button>
          <Button
            appearance="secondary"
            icon={<ArrowClockwise24Regular />}
            onClick={loadConfigs}
          >
            刷新配置
          </Button>
        </div>
      </div>

      {/* 成功/错误提示 */}
      {error && (
        <MessageBar intent="error">
          {error}
        </MessageBar>
      )}
      {success && (
        <MessageBar intent="success">
          {success}
        </MessageBar>
      )}

      {/* 配置分类标签 */}
      <TabList selectedValue={selectedTab} onTabSelect={handleTabSelect}>
        {configCategories.map(category => (
          <Tab key={category.key} value={category.key}>
            {category.icon} {category.name}
          </Tab>
        ))}
      </TabList>

      {/* 配置内容 */}
      <div className={styles.tabContent}>
        {configCategories.map(category => {
          if (category.key !== selectedTab) return null;

          const categoryConfigs = configs[category.key] || [];

          return (
            <div key={category.key}>
              <Card className={styles.configCard}>
                <CardHeader
                  header={
                    <div>
                      <Text weight="semibold">{category.name}</Text>
                      <Text style={{ fontSize: tokens.fontSizeBase200, color: tokens.colorNeutralForeground2 }}>
                        {category.description}
                      </Text>
                    </div>
                  }
                />
                <CardPreview>
                  {categoryConfigs.length === 0 ? (
                    <div style={{ textAlign: 'center', padding: '40px', color: tokens.colorNeutralForeground3 }}>
                      暂无配置项
                    </div>
                  ) : (
                    <div>
                      {categoryConfigs.map(renderConfigItem)}
                    </div>
                  )}
                </CardPreview>
              </Card>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default ConfigsPage;
