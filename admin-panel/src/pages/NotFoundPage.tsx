/**
 * 404页面
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  makeStyles,
  shorthands,
  tokens,
  Text,
  Button,
} from '@fluentui/react-components';
import { Home24Regular, ArrowLeft24Regular } from '@fluentui/react-icons';

const useStyles = makeStyles({
  root: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    ...shorthands.padding('20px'),
    textAlign: 'center',
    backgroundColor: tokens.colorNeutralBackground1,
  },
  errorCode: {
    fontSize: '120px',
    fontWeight: tokens.fontWeightBold,
    color: tokens.colorBrandForeground1,
    lineHeight: 1,
    marginBottom: '20px',
  },
  title: {
    fontSize: tokens.fontSizeBase600,
    fontWeight: tokens.fontWeightSemibold,
    color: tokens.colorNeutralForeground1,
    marginBottom: '12px',
  },
  description: {
    fontSize: tokens.fontSizeBase400,
    color: tokens.colorNeutralForeground2,
    marginBottom: '40px',
    maxWidth: '500px',
    lineHeight: 1.6,
  },
  actions: {
    display: 'flex',
    ...shorthands.gap('16px'),
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
});

const NotFoundPage: React.FC = () => {
  const styles = useStyles();
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className={styles.root}>
      <div className={styles.errorCode}>404</div>
      <Text className={styles.title}>页面未找到</Text>
      <Text className={styles.description}>
        抱歉，您访问的页面不存在。可能是页面已被删除、移动或您输入的地址有误。
      </Text>
      <div className={styles.actions}>
        <Button
          appearance="primary"
          icon={<Home24Regular />}
          onClick={handleGoHome}
        >
          返回首页
        </Button>
        <Button
          appearance="secondary"
          icon={<ArrowLeft24Regular />}
          onClick={handleGoBack}
        >
          返回上页
        </Button>
      </div>
    </div>
  );
};

export default NotFoundPage;
