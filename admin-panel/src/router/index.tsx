/**
 * 路由配置
 */

import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { ProtectedRoute } from '@/components/ProtectedRoute';

// 页面组件（懒加载）
const LoginPage = React.lazy(() => import('@/pages/LoginPage'));
const DashboardPage = React.lazy(() => import('@/pages/DashboardPage'));
const UsersPage = React.lazy(() => import('@/pages/UsersPage'));
const StoriesPage = React.lazy(() => import('@/pages/StoriesPage'));
const OrdersPage = React.lazy(() => import('@/pages/OrdersPage'));
const ConfigsPage = React.lazy(() => import('@/pages/ConfigsPage'));
const MonitoringPage = React.lazy(() => import('@/pages/MonitoringPage'));
const NotFoundPage = React.lazy(() => import('@/pages/NotFoundPage'));

export const router = createBrowserRouter([
  {
    path: '/login',
    element: <LoginPage />,
  },
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <Layout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'users',
        element: <UsersPage />,
      },
      {
        path: 'stories',
        element: <StoriesPage />,
      },
      {
        path: 'orders',
        element: <OrdersPage />,
      },
      {
        path: 'configs',
        element: <ConfigsPage />,
      },
      {
        path: 'monitoring',
        element: <MonitoringPage />,
      },
    ],
  },
  {
    path: '*',
    element: <NotFoundPage />,
  },
]);

// 导航配置
export interface NavItem {
  key: string;
  name: string;
  path: string;
  icon: string;
  children?: NavItem[];
}

export const navigationItems: NavItem[] = [
  {
    key: 'dashboard',
    name: '仪表板',
    path: '/dashboard',
    icon: 'Home',
  },
  {
    key: 'users',
    name: '用户管理',
    path: '/users',
    icon: 'People',
  },
  {
    key: 'stories',
    name: '内容管理',
    path: '/stories',
    icon: 'Document',
  },
  {
    key: 'orders',
    name: '订单管理',
    path: '/orders',
    icon: 'ShoppingCart',
  },
  {
    key: 'configs',
    name: '系统配置',
    path: '/configs',
    icon: 'Settings',
  },
  {
    key: 'monitoring',
    name: '系统监控',
    path: '/monitoring',
    icon: 'LineChart',
  },
];
