/**
 * 认证状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthUser } from '@/types';
import { api, apiClient } from '@/services/apiClient';

interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: { email: string; password: string }) => Promise<void>;
  googleLogin: (googleToken: string) => Promise<void>;
  loginWithToken: (token: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  refreshToken: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (_credentials) => {
        // 暂时保留传统登录方式，但实际使用Google OAuth
        throw new Error('请使用Google账户登录');
      },

      googleLogin: async (googleToken) => {
        set({ isLoading: true, error: null });

        try {
          const response = await api.auth.googleLogin(googleToken);

          if (!response.success) {
            throw new Error(response.error || '登录失败');
          }

          // 检查用户是否有管理员权限
          if (!response.data.user.isAdmin) {
            throw new Error('需要管理员权限才能访问管理面板');
          }

          const user: AuthUser = {
            id: response.data.user.id,
            email: response.data.user.email,
            name: response.data.user.name,
            avatar: response.data.user.avatar,
            isAdmin: response.data.user.isAdmin,
            accessToken: response.data.tokens.accessToken,
            refreshToken: response.data.tokens.refreshToken,
          };

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : '登录失败',
          });
          throw error;
        }
      },

      loginWithToken: async (token) => {
        set({ isLoading: true, error: null });

        try {
          // 在开发环境中，如果是模拟token，直接解析
          if (import.meta.env.DEV && token.includes('mock-signature')) {
            const { extractUserFromToken } = await import('@/utils/jwtUtils');
            const userData = extractUserFromToken(token);

            if (!userData || !userData.isAdmin) {
              throw new Error('需要管理员权限');
            }

            const user: AuthUser = {
              id: userData.id!,
              email: userData.email!,
              name: userData.name!,
              avatar: userData.avatar,
              isAdmin: userData.isAdmin!,
              accessToken: token,
            };

            set({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return;
          }

          // 生产环境使用真实API验证
          // 设置Authorization header
          const originalToken = apiClient.token;
          apiClient.setToken(token);

          const response = await api.auth.me();

          if (!response.success) {
            throw new Error('Token验证失败');
          }

          // 检查管理员权限
          if (!response.data.isAdmin) {
            throw new Error('需要管理员权限');
          }

          const user: AuthUser = {
            id: response.data.id,
            email: response.data.email,
            name: response.data.name,
            avatar: response.data.avatar,
            isAdmin: response.data.isAdmin,
            accessToken: token,
          };

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Token验证失败',
          });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      refreshToken: async () => {
        const { user } = get();
        if (!user?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              refreshToken: user.refreshToken,
            }),
          });

          if (!response.ok) {
            throw new Error('Token刷新失败');
          }

          const data = await response.json();
          
          set({
            user: {
              ...user,
              accessToken: data.data.accessToken,
              refreshToken: data.data.refreshToken || user.refreshToken,
            },
          });
        } catch (error) {
          // 刷新失败，清除认证状态
          get().logout();
          throw error;
        }
      },
    }),
    {
      name: 'admin-auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
