/**
 * 认证状态管理
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthUser } from '@/types';

interface AuthState {
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: { email: string; password: string }) => Promise<void>;
  loginWithToken: (token: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  refreshToken: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await fetch('/api/auth/admin/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(credentials),
          });

          if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '登录失败');
          }

          const data = await response.json();
          
          if (!data.success || !data.data.user.isAdmin) {
            throw new Error('需要管理员权限');
          }

          const user: AuthUser = {
            id: data.data.user.id,
            email: data.data.user.email,
            name: data.data.user.name,
            avatar: data.data.user.avatar,
            isAdmin: data.data.user.isAdmin,
            accessToken: data.data.tokens.accessToken,
            refreshToken: data.data.tokens.refreshToken,
          };

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : '登录失败',
          });
          throw error;
        }
      },

      loginWithToken: async (token) => {
        set({ isLoading: true, error: null });
        
        try {
          const response = await fetch('/api/auth/me', {
            headers: {
              'Authorization': `Bearer ${token}`,
            },
          });

          if (!response.ok) {
            throw new Error('Token验证失败');
          }

          const data = await response.json();
          
          if (!data.success || !data.data.isAdmin) {
            throw new Error('需要管理员权限');
          }

          const user: AuthUser = {
            id: data.data.id,
            email: data.data.email,
            name: data.data.name,
            avatar: data.data.avatar,
            isAdmin: data.data.isAdmin,
            accessToken: token,
          };

          set({
            user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Token验证失败',
          });
          throw error;
        }
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading) => {
        set({ isLoading: loading });
      },

      refreshToken: async () => {
        const { user } = get();
        if (!user?.refreshToken) {
          throw new Error('No refresh token available');
        }

        try {
          const response = await fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              refreshToken: user.refreshToken,
            }),
          });

          if (!response.ok) {
            throw new Error('Token刷新失败');
          }

          const data = await response.json();
          
          set({
            user: {
              ...user,
              accessToken: data.data.accessToken,
              refreshToken: data.data.refreshToken || user.refreshToken,
            },
          });
        } catch (error) {
          // 刷新失败，清除认证状态
          get().logout();
          throw error;
        }
      },
    }),
    {
      name: 'admin-auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
