name = "storyweaver-admin-panel"
main = "src/worker/index.ts"
compatibility_date = "2024-07-01"
compatibility_flags = ["nodejs_compat"]

# 与主backend共享相同的数据库绑定
[[d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"

[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

# Durable Objects绑定
[durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" },
  { name = "STORY_GENERATION", class_name = "StoryGenerationDO" }
]

# 环境变量
[vars]
ENVIRONMENT = "development"
JWT_SECRET = "storyweaver-jwt-secret-key-2024"
ADMIN_EMAIL = "<EMAIL>"

# 生产环境配置
[env.production]
name = "storyweaver-admin-panel-production"

[env.production.vars]
ENVIRONMENT = "production"

# Pages配置（用于前端部署）
[pages]
compatibility_date = "2024-07-01"
build_output_dir = "dist"
