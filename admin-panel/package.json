{"name": "storyweaver-admin-panel", "version": "1.0.0", "description": "StoryWeaver Admin Panel with Fluent UI 2", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "deploy": "npm run build && wrangler pages deploy dist", "deploy:production": "npm run build && wrangler pages deploy dist --env production", "db:init": "node scripts/init-database.mjs", "db:migrate": "npx wrangler d1 migrations apply storyweaver-dev --local", "db:test": "node scripts/test-config-manager.mjs", "auth:test": "node scripts/test-auth.mjs", "test:integration": "node scripts/integration-test.mjs"}, "dependencies": {"@fluentui/react-components": "^9.54.0", "@fluentui/react-icons": "^2.0.258", "@fluentui/react-theme": "^9.1.19", "@fluentui/react-table": "^9.15.0", "@fluentui/react-nav": "^9.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "zustand": "^4.4.0", "@tanstack/react-query": "^4.32.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "hono": "^4.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "typescript": "^5.0.2", "vite": "^4.4.0", "wrangler": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}