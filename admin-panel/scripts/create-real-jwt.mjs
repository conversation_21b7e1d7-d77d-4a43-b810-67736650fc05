#!/usr/bin/env node

/**
 * 创建真实的JWT token用于测试
 * 使用与backend相同的JWT_SECRET和签名算法
 */

import crypto from 'crypto';
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const JWT_SECRET = 'storyweaver-jwt-secret-key-2024';

// Base64URL编码
function base64UrlEncode(str) {
  return Buffer.from(str)
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// HMAC SHA256签名
function sign(data, secret) {
  return crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

// 创建JWT token
function createJWT(payload, secret) {
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };

  const encodedHeader = base64UrlEncode(JSON.stringify(header));
  const encodedPayload = base64UrlEncode(JSON.stringify(payload));
  
  const data = `${encodedHeader}.${encodedPayload}`;
  const signature = sign(data, secret);
  
  return `${data}.${signature}`;
}

async function createTestTokens() {
  try {
    console.log('🔑 创建真实的JWT测试token...\n');
    
    // 首先创建测试用户在数据库中
    console.log('👤 创建测试用户...');
    
    const adminUserId = 'admin-test-user-real';
    const regularUserId = 'regular-test-user-real';
    
    // 创建管理员用户
    const createAdminCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="INSERT OR REPLACE INTO users (id, email, name, google_id, credits, isAdmin, created_at, updated_at) VALUES ('${adminUserId}', '<EMAIL>', '系统管理员', 'google_admin_real', 1000, 1, datetime('now'), datetime('now'));"`;
    
    try {
      execSync(createAdminCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 管理员用户创建成功');
    } catch (error) {
      console.log('⚠️  管理员用户创建失败（可能已存在）');
    }
    
    // 创建普通用户
    const createUserCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="INSERT OR REPLACE INTO users (id, email, name, google_id, credits, isAdmin, created_at, updated_at) VALUES ('${regularUserId}', '<EMAIL>', '普通用户', 'google_user_real', 50, 0, datetime('now'), datetime('now'));"`;
    
    try {
      execSync(createUserCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 普通用户创建成功');
    } catch (error) {
      console.log('⚠️  普通用户创建失败（可能已存在）');
    }
    
    // 创建JWT tokens
    console.log('\n🎫 生成JWT tokens...');
    
    const now = Math.floor(Date.now() / 1000);
    const expiry = now + (60 * 60 * 24); // 24小时后过期
    
    // 管理员token
    const adminPayload = {
      userId: adminUserId,
      email: '<EMAIL>',
      type: 'access',
      iat: now,
      exp: expiry
    };
    
    const adminToken = createJWT(adminPayload, JWT_SECRET);
    
    // 普通用户token
    const userPayload = {
      userId: regularUserId,
      email: '<EMAIL>',
      type: 'access',
      iat: now,
      exp: expiry
    };
    
    const userToken = createJWT(userPayload, JWT_SECRET);
    
    console.log('✅ JWT tokens生成成功');
    
    // 验证tokens
    console.log('\n🔍 验证生成的tokens...');
    
    // 验证管理员token
    const verifyAdminCommand = `curl -s -X POST "${process.env.API_BASE_URL || 'http://localhost:8787/api'}/auth/verify" -H "Content-Type: application/json" -d '{"token":"${adminToken}"}'`;
    
    console.log('管理员Token (前50字符):', adminToken.substring(0, 50) + '...');
    console.log('普通用户Token (前50字符):', userToken.substring(0, 50) + '...');
    
    // 保存tokens到文件
    const tokensData = {
      adminToken,
      userToken,
      adminUserId,
      regularUserId,
      createdAt: new Date().toISOString(),
      expiresAt: new Date(expiry * 1000).toISOString()
    };
    
    const tokensFile = path.join(__dirname, '../test-tokens.json');
    const fs = await import('fs');
    fs.writeFileSync(tokensFile, JSON.stringify(tokensData, null, 2));
    
    console.log('\n📁 Tokens已保存到:', tokensFile);
    
    // 提供使用说明
    console.log('\n📋 使用说明:');
    console.log('1. 管理员Token已生成，可用于API测试');
    console.log('2. 普通用户Token已生成，用于权限测试');
    console.log('3. Tokens有效期: 24小时');
    console.log('4. 用户已在数据库中创建');
    
    console.log('\n🧪 测试命令示例:');
    console.log(`curl -H "Authorization: Bearer ${adminToken.substring(0, 50)}..." http://localhost:8787/api/users`);
    
    console.log('\n🔄 重新运行管理API测试:');
    console.log('npm run test:admin-apis');
    
    return { adminToken, userToken };
    
  } catch (error) {
    console.error('❌ 创建JWT tokens失败:', error.message);
    process.exit(1);
  }
}

// 运行
createTestTokens();
