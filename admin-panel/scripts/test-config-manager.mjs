#!/usr/bin/env node

/**
 * ConfigManager测试脚本
 * 测试配置的读取和写入功能
 */

import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testConfigManager() {
  try {
    console.log('🧪 开始测试ConfigManager...');
    
    // 测试1: 查询现有配置
    console.log('\n📋 测试1: 查询现有配置');
    const queryCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="SELECT * FROM system_configs LIMIT 5;"`;
    
    try {
      const queryOutput = execSync(queryCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 查询成功!');
      console.log('配置数据:', queryOutput);
    } catch (queryError) {
      console.log('❌ 查询失败:', queryError.message);
    }
    
    // 测试2: 插入测试配置
    console.log('\n📝 测试2: 插入测试配置');
    const insertCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="INSERT OR REPLACE INTO system_configs (category, key, value, description, is_sensitive, updated_by, updated_at) VALUES ('test', 'admin_panel_test', 'test_value_123', '管理面板测试配置', 0, 'admin_panel_test', datetime('now'));"`;
    
    try {
      const insertOutput = execSync(insertCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 插入成功!');
      console.log('插入结果:', insertOutput);
    } catch (insertError) {
      console.log('❌ 插入失败:', insertError.message);
    }
    
    // 测试3: 验证插入的配置
    console.log('\n🔍 测试3: 验证插入的配置');
    const verifyCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="SELECT * FROM system_configs WHERE category = 'test' AND key = 'admin_panel_test';"`;
    
    try {
      const verifyOutput = execSync(verifyCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 验证成功!');
      console.log('验证结果:', verifyOutput);
    } catch (verifyError) {
      console.log('❌ 验证失败:', verifyError.message);
    }
    
    // 测试4: 查询配置分类
    console.log('\n📂 测试4: 查询配置分类');
    const categoriesCommand = `npx wrangler d1 execute storyweaver-dev --env development --local --command="SELECT DISTINCT category FROM system_configs ORDER BY category;"`;
    
    try {
      const categoriesOutput = execSync(categoriesCommand, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 分类查询成功!');
      console.log('配置分类:', categoriesOutput);
    } catch (categoriesError) {
      console.log('❌ 分类查询失败:', categoriesError.message);
    }
    
    console.log('\n🎉 ConfigManager测试完成!');
    
  } catch (error) {
    console.error('❌ ConfigManager测试失败:', error.message);
    process.exit(1);
  }
}

// 运行测试
testConfigManager();
