#!/usr/bin/env node

/**
 * 数据库初始化脚本
 * 用于创建system_configs表和插入默认配置
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function initDatabase() {
  try {
    console.log('🚀 开始初始化数据库...');
    
    // 读取SQL文件
    const sqlPath = path.join(__dirname, '../schemas/system_configs.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 读取SQL文件成功');
    
    // 将SQL内容写入临时文件
    const tempSqlPath = path.join(__dirname, '../temp_init.sql');
    fs.writeFileSync(tempSqlPath, sqlContent);
    
    console.log('📝 创建临时SQL文件');
    
    // 使用wrangler d1 execute执行SQL
    const command = `npx wrangler d1 execute storyweaver-dev --local --file=${tempSqlPath}`;
    
    console.log('⚡ 执行SQL命令:', command);
    
    try {
      const output = execSync(command, { 
        cwd: path.join(__dirname, '..'),
        encoding: 'utf8',
        stdio: 'pipe'
      });
      console.log('✅ 数据库初始化成功!');
      console.log('输出:', output);
    } catch (execError) {
      console.log('⚠️  执行命令时出现错误，但可能是正常的（表已存在）');
      console.log('错误信息:', execError.message);
    }
    
    // 清理临时文件
    if (fs.existsSync(tempSqlPath)) {
      fs.unlinkSync(tempSqlPath);
      console.log('🧹 清理临时文件');
    }
    
    console.log('🎉 数据库初始化流程完成!');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// 运行初始化
initDatabase();
