{"project": "StoryWeaver", "lastUpdated": "2025-01-04", "totalDocs": 61, "docsRoot": "./docs/", "categories": {"core": {"path": "./docs/core/", "description": "核心项目文档", "count": 6, "files": [{"name": "README.md", "path": "./docs/core/README.md", "description": "项目完整技术文档"}, {"name": "项目介绍.md", "path": "./docs/core/项目介绍.md", "description": "项目概念和愿景"}, {"name": "项目状态总结_2025-01-02.md", "path": "./docs/core/项目状态总结_2025-01-02.md", "description": "最新项目状态"}, {"name": "CODE_OF_CONDUCT.md", "path": "./docs/core/CODE_OF_CONDUCT.md", "description": "行为准则"}, {"name": "CONTRIBUTING.md", "path": "./docs/core/CONTRIBUTING.md", "description": "贡献指南"}, {"name": "SECURITY.md", "path": "./docs/core/SECURITY.md", "description": "安全指南"}]}, "architecture": {"path": "./docs/architecture/", "description": "架构设计文档", "count": 5, "files": [{"name": "StoryWeaver_完整项目计划.md", "path": "./docs/architecture/StoryWeaver_完整项目计划.md", "description": "项目整体规划"}, {"name": "StoryWeaver_DurableObjects_实施计划.md", "path": "./docs/architecture/StoryWeaver_DurableObjects_实施计划.md", "description": "实时功能架构"}, {"name": "technical_implementation.md", "path": "./docs/architecture/technical_implementation.md", "description": "技术实现方案"}, {"name": "website_framework.md", "path": "./docs/architecture/website_framework.md", "description": "网站框架设计"}, {"name": "ui_ux_design_guide.md", "path": "./docs/architecture/ui_ux_design_guide.md", "description": "UI/UX设计指南"}]}, "development": {"path": "./docs/development/", "description": "开发指南", "count": 7, "files": [{"name": "DEVELOPMENT_GUIDE.md", "path": "./docs/development/DEVELOPMENT_GUIDE.md", "description": "基础开发指南"}, {"name": "DEVELOPMENT_GUIDE_PART2.md", "path": "./docs/development/DEVELOPMENT_GUIDE_PART2.md", "description": "高级开发技巧"}, {"name": "DEVELOPMENT_GUIDE_PART3.md", "path": "./docs/development/DEVELOPMENT_GUIDE_PART3.md", "description": "测试和调试"}, {"name": "DEVELOPMENT_GUIDE_PART4.md", "path": "./docs/development/DEVELOPMENT_GUIDE_PART4.md", "description": "部署和维护"}, {"name": "FRONTEND_DEVELOPMENT_GUIDE.md", "path": "./docs/development/FRONTEND_DEVELOPMENT_GUIDE.md", "description": "前端开发指南"}, {"name": "DurableObjects_快速开始指南.md", "path": "./docs/development/DurableObjects_快速开始指南.md", "description": "实时功能开发"}, {"name": "PERFORMANCE_GUIDE.md", "path": "./docs/development/PERFORMANCE_GUIDE.md", "description": "性能优化指南"}]}, "deployment": {"path": "./docs/deployment/", "description": "部署文档", "count": 3, "files": [{"name": "DEPLOYMENT_GUIDE.md", "path": "./docs/deployment/DEPLOYMENT_GUIDE.md", "description": "基础部署指南"}, {"name": "PRODUCTION_DEPLOYMENT_GUIDE.md", "path": "./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md", "description": "生产环境部署"}, {"name": "PRODUCTION_DEPLOYMENT_REPORT.md", "path": "./docs/deployment/PRODUCTION_DEPLOYMENT_REPORT.md", "description": "部署状态报告"}]}, "api": {"path": "./docs/api/", "description": "API文档", "count": 5, "files": [{"name": "README.md", "path": "./docs/api/README.md", "description": "后端API概览"}, {"name": "API_DOCUMENTATION.md", "path": "./docs/api/API_DOCUMENTATION.md", "description": "完整API文档"}, {"name": "ENVIRONMENT_VARIABLES.md", "path": "./docs/api/ENVIRONMENT_VARIABLES.md", "description": "环境变量配置"}, {"name": "DEPLOYMENT.md", "path": "./docs/api/DEPLOYMENT.md", "description": "后端部署指南"}, {"name": "TESTING.md", "path": "./docs/api/TESTING.md", "description": "API测试指南"}]}, "frontend": {"path": "./docs/frontend/", "description": "前端文档", "count": 11, "files": [{"name": "README.md", "path": "./docs/frontend/README.md", "description": "前端项目概览"}, {"name": "README-StoryWeaver.md", "path": "./docs/frontend/README-StoryWeaver.md", "description": "StoryWeaver前端说明"}, {"name": "BUILD-GUIDE.md", "path": "./docs/frontend/BUILD-GUIDE.md", "description": "前端构建指南"}, {"name": "DEPLOYMENT-FULL.md", "path": "./docs/frontend/DEPLOYMENT-FULL.md", "description": "前端部署指南"}, {"name": "STRIPE_INTEGRATION.md", "path": "./docs/frontend/STRIPE_INTEGRATION.md", "description": "Stripe支付集成"}, {"name": "STRIPE_401_FIX_GUIDE.md", "path": "./docs/frontend/STRIPE_401_FIX_GUIDE.md", "description": "Stripe 401错误修复"}, {"name": "STRIPE_PAYMENT_FIXES_COMPLETE_REPORT.md", "path": "./docs/frontend/STRIPE_PAYMENT_FIXES_COMPLETE_REPORT.md", "description": "支付修复完整报告"}, {"name": "STRIPE_PAYMENT_IMPLEMENTATION.md", "path": "./docs/frontend/STRIPE_PAYMENT_IMPLEMENTATION.md", "description": "支付系统实现"}, {"name": "STRIPE_PAYMENT_TEST_REPORT.md", "path": "./docs/frontend/STRIPE_PAYMENT_TEST_REPORT.md", "description": "支付测试报告"}, {"name": "STRIPE_PAYMENT_TEST_REPORT_FINAL.md", "path": "./docs/frontend/STRIPE_PAYMENT_TEST_REPORT_FINAL.md", "description": "最终支付测试报告"}, {"name": "test-stripe-payment.md", "path": "./docs/frontend/test-stripe-payment.md", "description": "支付功能测试"}]}, "archive": {"path": "./docs/archive/", "description": "历史归档", "count": 23, "subdirectories": {"fixes": {"path": "./docs/archive/fixes/", "description": "历史修复记录", "count": 22}}, "files": [{"name": "DOCUMENTATION_ARCHIVE_REPORT.md", "path": "./docs/archive/DOCUMENTATION_ARCHIVE_REPORT.md", "description": "文档归档报告"}]}}, "quickAccess": {"newDeveloper": ["./docs/core/README.md", "./docs/core/项目介绍.md", "./docs/development/DEVELOPMENT_GUIDE.md"], "frontendDeveloper": ["./docs/frontend/README.md", "./docs/frontend/BUILD-GUIDE.md", "./docs/development/FRONTEND_DEVELOPMENT_GUIDE.md"], "backendDeveloper": ["./docs/api/README.md", "./docs/api/API_DOCUMENTATION.md", "./docs/api/ENVIRONMENT_VARIABLES.md"], "devops": ["./docs/deployment/DEPLOYMENT_GUIDE.md", "./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md"], "architect": ["./docs/architecture/StoryWeaver_完整项目计划.md", "./docs/architecture/technical_implementation.md"]}, "searchTags": {"payment": ["./docs/frontend/STRIPE_INTEGRATION.md", "./docs/frontend/STRIPE_PAYMENT_IMPLEMENTATION.md", "./docs/frontend/test-stripe-payment.md"], "realtime": ["./docs/architecture/StoryWeaver_DurableObjects_实施计划.md", "./docs/development/DurableObjects_快速开始指南.md"], "performance": ["./docs/development/PERFORMANCE_GUIDE.md"], "deployment": ["./docs/deployment/DEPLOYMENT_GUIDE.md", "./docs/deployment/PRODUCTION_DEPLOYMENT_GUIDE.md", "./docs/api/DEPLOYMENT.md", "./docs/frontend/DEPLOYMENT-FULL.md"]}}