# Google OAuth 配置修复指南

## 🔍 问题诊断

通过Playwright自动化测试发现，StoryWeaver前端登录页面缺少Google登录按钮，经过分析发现根本原因：

### 问题根源
**Google OAuth重定向URI不匹配**
- 配置的重定向URI: `https://storyweaver.jamintextiles.com/auth/callback`
- 实际前端域名: `https://storyweaver.pages.dev/`
- 后端API域名: `https://storyweaver-api.stawky.workers.dev/`

## 🛠️ 修复方案

### 方案一：更新Google OAuth配置（推荐）

1. **访问Google Cloud Console**
   - 登录 [Google Cloud Console](https://console.cloud.google.com/)
   - 选择StoryWeaver项目

2. **更新OAuth 2.0客户端配置**
   - 导航到 "APIs & Services" > "Credentials"
   - 找到客户端ID: `463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com`
   - 点击编辑

3. **添加正确的重定向URI**
   ```
   已授权的重定向URI:
   - https://storyweaver.pages.dev/auth/callback
   - https://storyweaver.jamintextiles.com/auth/callback (保留原有)
   - http://localhost:3000/auth/callback (开发环境)
   ```

4. **保存配置**
   - 点击"保存"按钮
   - 等待配置生效（通常需要几分钟）

### 方案二：更新后端重定向URI配置

如果无法修改Google OAuth配置，需要更新后端代码中的默认重定向URI：

**文件位置**: `backend/src/handlers/auth.ts` 和 `deploy-backend/src/handlers/auth.ts`

**修改内容**:
```typescript
// 将第40行的默认重定向URI从：
redirect_uri: redirectUri || 'https://storyweaver.com/auth/callback'

// 修改为：
redirect_uri: redirectUri || 'https://storyweaver.pages.dev/auth/callback'
```

## 🔧 验证修复

### 1. 检查后端API状态
```bash
curl https://storyweaver-api.stawky.workers.dev/api/health
```

### 2. 测试Google OAuth端点
```bash
curl -X POST https://storyweaver-api.stawky.workers.dev/api/auth/google \
  -H "Content-Type: application/json" \
  -d '{"code":"test","redirectUri":"https://storyweaver.pages.dev/auth/callback"}'
```

### 3. 前端测试
- 访问 https://storyweaver.pages.dev/auth
- 确认Google登录按钮显示
- 测试登录流程

## 📋 配置检查清单

### ✅ 已确认正常的配置
- [x] 后端API运行正常
- [x] 前端环境变量配置正确
- [x] Google Client ID已设置
- [x] 后端Google OAuth处理逻辑正常

### ⚠️ 需要修复的配置
- [ ] Google OAuth重定向URI匹配
- [ ] 前端Google登录按钮显示
- [ ] 完整的OAuth登录流程测试

## 🚀 部署后验证

修复完成后，使用以下步骤验证：

1. **重新部署后端**（如果修改了代码）
   ```bash
   cd deploy-backend
   npm run deploy
   ```

2. **清除浏览器缓存**
   - 清除 storyweaver.pages.dev 的缓存
   - 或使用无痕模式测试

3. **完整流程测试**
   - 访问首页
   - 点击"登录"或"创作故事"
   - 确认Google登录按钮出现
   - 测试Google登录流程

## 📞 故障排除

### 如果Google登录按钮仍未显示

1. **检查浏览器控制台错误**
   - 打开开发者工具
   - 查看Console标签页的错误信息

2. **检查网络请求**
   - 查看Network标签页
   - 确认API请求是否成功

3. **验证环境变量**
   - 确认前端环境变量正确设置
   - 检查VITE_GOOGLE_CLIENT_ID是否正确

### 如果OAuth流程失败

1. **检查重定向URI**
   - 确认Google Cloud Console中的URI配置
   - 验证前端回调处理逻辑

2. **检查后端日志**
   - 查看Cloudflare Workers日志
   - 确认OAuth处理是否有错误

---

**修复优先级**: 高
**预计修复时间**: 15-30分钟
**影响范围**: 用户登录功能