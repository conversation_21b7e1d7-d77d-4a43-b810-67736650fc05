# StoryWeaver 订阅支付错误修复报告

## 问题描述

用户在测试订阅支付功能时遇到错误：
- 前端错误：`{"success":false,"error":"不支持的支付金额: $19.99","code":"VALIDATION_ERROR"}`
- 后端错误：`❌ Unsupported payment amount: 19.99`

## 问题分析

### 根本原因
后端的支付验证逻辑中，只支持积分包的价格验证（$2.99, $7.99, $14.99），但没有包含订阅计划的价格验证（$19.99, $39.99等）。

### 问题位置
1. **后端文件**: `backend/src/handlers/payments.ts`
   - 第415行：`purchase-credits` 端点的金额验证
   - 第503行：`verify-checkout-session` 端点的金额验证

2. **前端配置**: `frontend/src/config/pricing.ts`
   - 专业会员价格：$19.99/月
   - 无限会员价格：$39.99/月

### 代码问题
后端的金额验证逻辑只包含积分包价格：
```typescript
// 当前的验证逻辑（有问题）
if (amount === 2.99) {
  credits = 100; // 基础包
} else if (amount === 7.99) {
  credits = 350; // 超值包
} else if (amount === 14.99) {
  credits = 750; // 豪华包
} else {
  console.error('❌ Unsupported payment amount:', amount);
  return c.json<ApiResponse>({
    success: false,
    error: `不支持的支付金额: $${amount}`,
    code: ErrorCodes.VALIDATION_ERROR
  }, 400);
}
```

## 解决方案

### 1. 修复后端支付验证逻辑

需要在两个地方修复金额验证：

#### A. `purchase-credits` 端点（第407-421行）
```typescript
// 修复后的验证逻辑
const amount = paymentIntent.amount / 100; // 转换为美元
let credits = 0;

// 检查是否为订阅支付
const isSubscription = paymentIntent.metadata?.type === 'subscription';

if (isSubscription) {
  // 订阅支付：根据计划设置积分
  const planId = paymentIntent.metadata?.planId;
  switch (planId) {
    case 'basic_monthly':
      credits = 50; // 基础会员月积分
      break;
    case 'pro_monthly':
      credits = 200; // 专业会员月积分
      break;
    case 'unlimited_monthly':
      credits = 999999; // 无限会员
      break;
    default:
      credits = 100; // 默认积分
  }
} else {
  // 积分包支付：根据金额计算积分
  if (amount === 2.99) {
    credits = 100; // 基础包
  } else if (amount === 7.99) {
    credits = 350; // 超值包
  } else if (amount === 14.99) {
    credits = 750; // 豪华包
  } else {
    console.error('❌ Unsupported payment amount:', amount);
    return c.json<ApiResponse>({
      success: false,
      error: `不支持的支付金额: $${amount}`,
      code: ErrorCodes.VALIDATION_ERROR
    }, 400);
  }
}
```

#### B. `verify-checkout-session` 端点（第496-509行）
```typescript
// 修复后的验证逻辑
const amount = session.amount_total / 100; // 转换为美元
let credits = 0;

// 检查是否为订阅类型支付
const isSubscription = session.metadata?.type === 'subscription';

if (isSubscription) {
  // 订阅支付：根据计划设置积分
  const planId = session.metadata?.planId;
  switch (planId) {
    case 'basic_monthly':
      credits = 50;
      break;
    case 'pro_monthly':
      credits = 200;
      break;
    case 'unlimited_monthly':
      credits = 999999;
      break;
    default:
      credits = 100;
  }
} else {
  // 积分包支付：根据金额计算积分
  if (amount === 2.99) {
    credits = 100;
  } else if (amount === 7.99) {
    credits = 350;
  } else if (amount === 14.99) {
    credits = 750;
  } else {
    console.error('❌ Unsupported payment amount:', amount);
    return c.json<ApiResponse>({
      success: false,
      error: `不支持的支付金额: $${amount}`,
      code: ErrorCodes.VALIDATION_ERROR
    }, 400);
  }
}
```

### 2. 优化建议

#### A. 创建统一的价格验证函数
```typescript
function validatePaymentAmount(amount: number, type: string, planId?: string): { isValid: boolean; credits: number } {
  if (type === 'subscription') {
    // 订阅支付验证
    const subscriptionPrices = {
      'basic_monthly': { price: 9.99, credits: 50 },
      'pro_monthly': { price: 19.99, credits: 200 },
      'unlimited_monthly': { price: 39.99, credits: 999999 }
    };
    
    const plan = subscriptionPrices[planId];
    if (plan && amount === plan.price) {
      return { isValid: true, credits: plan.credits };
    }
  } else {
    // 积分包支付验证
    const creditPackages = {
      2.99: 100,
      7.99: 350,
      14.99: 750
    };
    
    if (creditPackages[amount]) {
      return { isValid: true, credits: creditPackages[amount] };
    }
  }
  
  return { isValid: false, credits: 0 };
}
```

#### B. 添加更详细的日志
```typescript
console.log('💰 Payment validation:', {
  amount,
  type: session.metadata?.type,
  planId: session.metadata?.planId,
  isSubscription,
  sessionId: session.id
});
```

## 实施步骤

1. **立即修复**：更新后端的两个金额验证逻辑
2. **测试验证**：测试所有订阅计划的支付流程
3. **代码优化**：实施统一的价格验证函数
4. **监控部署**：部署后监控支付成功率

## 预期结果

修复后，用户应该能够成功购买：
- ✅ 基础会员 ($9.99/月)
- ✅ 专业会员 ($19.99/月) 
- ✅ 无限会员 ($39.99/月)
- ✅ 积分包 ($2.99, $7.99, $14.99)

## 测试计划

1. 测试专业会员订阅 ($19.99)
2. 测试无限会员订阅 ($39.99)
3. 测试基础会员订阅 ($9.99)
4. 验证积分包购买仍然正常工作
5. 检查支付成功后的积分和订阅状态更新

---

**修复优先级**: 🔥 高优先级
**预计修复时间**: 30分钟
**影响范围**: 订阅支付功能
**风险评估**: 低风险（仅修复验证逻辑）