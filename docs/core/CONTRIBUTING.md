# 贡献指南 🤝

感谢您对 StoryWeaver 项目的关注！我们欢迎所有形式的贡献，无论是代码、文档、设计还是反馈建议。

## 📋 贡献方式

### 🐛 报告Bug
- 使用 [GitHub Issues](https://github.com/your-username/storyweaver/issues) 报告bug
- 请提供详细的复现步骤和环境信息
- 包含错误截图或日志（如果适用）

### 💡 功能建议
- 通过 [GitHub Issues](https://github.com/your-username/storyweaver/issues) 提交功能请求
- 详细描述功能需求和使用场景
- 说明该功能如何改善用户体验

### 📝 改进文档
- 修复文档中的错误或不清楚的地方
- 添加缺失的文档或示例
- 翻译文档到其他语言

### 💻 代码贡献
- 修复已知bug
- 实现新功能
- 优化性能
- 改进代码质量

## 🚀 开发流程

### 1. 准备开发环境

```bash
# 1. Fork 项目到您的GitHub账户
# 2. 克隆您的fork
git clone https://github.com/your-username/storyweaver.git
cd storyweaver

# 3. 添加上游仓库
git remote add upstream https://github.com/original-username/storyweaver.git

# 4. 安装依赖
cd backend && npm install
cd ../frontend && npm install  # 如果有前端代码
```

### 2. 创建功能分支

```bash
# 从最新的main分支创建新分支
git checkout main
git pull upstream main
git checkout -b feature/your-feature-name

# 或者修复bug
git checkout -b fix/bug-description
```

### 3. 开发和测试

```bash
# 启动开发服务器
cd backend && npm run dev

# 运行测试
npm test

# 检查代码风格
npm run lint
```

### 4. 提交更改

```bash
# 添加更改的文件
git add .

# 提交更改（请遵循提交信息规范）
git commit -m "feat: add new story generation feature"

# 推送到您的fork
git push origin feature/your-feature-name
```

### 5. 创建Pull Request

1. 访问您的GitHub fork页面
2. 点击 "New Pull Request"
3. 选择正确的分支
4. 填写PR描述模板
5. 提交Pull Request

## 📝 代码规范

### TypeScript/JavaScript

```typescript
// ✅ 好的示例
interface User {
  id: string;
  email: string;
  name: string;
}

const createUser = async (userData: User): Promise<User> => {
  // 实现逻辑
  return userData;
};

// ❌ 避免的写法
const createUser = (userData: any) => {
  // 缺少类型定义
};
```

### 命名规范

- **变量和函数**: camelCase (`userName`, `createStory`)
- **类和接口**: PascalCase (`User`, `StoryService`)
- **常量**: UPPER_SNAKE_CASE (`API_BASE_URL`)
- **文件名**: kebab-case (`story-service.ts`, `user-profile.tsx`)

### 注释规范

```typescript
/**
 * 生成AI故事
 * @param request 故事创建请求
 * @returns 生成的故事数据
 */
async function generateStory(request: CreateStoryRequest): Promise<Story> {
  // 实现逻辑
}
```

## 🧪 测试规范

### 单元测试

```typescript
// tests/services/story.test.ts
import { StoryService } from '../src/services/story';

describe('StoryService', () => {
  it('should generate story successfully', async () => {
    const service = new StoryService();
    const request = {
      characterName: 'Alice',
      theme: 'adventure'
    };
    
    const story = await service.generateStory(request);
    
    expect(story).toBeDefined();
    expect(story.title).toBeTruthy();
  });
});
```

### 集成测试

```typescript
// tests/api/stories.test.ts
import { testClient } from '../helpers/test-client';

describe('Stories API', () => {
  it('POST /api/stories should create new story', async () => {
    const response = await testClient.post('/api/stories', {
      characterName: 'Bob',
      theme: 'friendship'
    });
    
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
  });
});
```

## 📋 提交信息规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 类型说明

- `feat`: 新功能
- `fix`: Bug修复
- `docs`: 文档更新
- `style`: 代码格式化（不影响功能）
- `refactor`: 代码重构
- `test`: 添加或修改测试
- `chore`: 构建过程或辅助工具的变动

### 示例

```
feat(auth): add Google OAuth integration

- Implement Google OAuth 2.0 flow
- Add user profile management
- Update authentication middleware

Closes #123
```

## 🔍 Pull Request 模板

创建PR时，请填写以下信息：

```markdown
## 📝 更改描述
简要描述这个PR的目的和更改内容。

## 🔗 相关Issue
Fixes #(issue number)

## 📋 更改类型
- [ ] Bug修复
- [ ] 新功能
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化
- [ ] 其他: ___________

## ✅ 检查清单
- [ ] 代码遵循项目规范
- [ ] 已添加必要的测试
- [ ] 所有测试通过
- [ ] 已更新相关文档
- [ ] 代码已经过自我审查

## 🧪 测试说明
描述如何测试这些更改。

## 📸 截图（如果适用）
添加截图来说明更改。

## 📝 额外说明
任何其他相关信息。
```

## 🎯 开发优先级

### 高优先级
1. 🐛 Bug修复
2. 🔒 安全问题
3. 📱 核心功能完善

### 中优先级
1. ✨ 新功能开发
2. 🎨 UI/UX改进
3. 📊 性能优化

### 低优先级
1. 📝 文档改进
2. 🧹 代码清理
3. 🔧 开发工具优化

## 🌍 国际化

如果您想帮助翻译项目：

1. 检查 `src/locales/` 目录
2. 添加新的语言文件
3. 翻译所有文本字符串
4. 测试翻译效果

支持的语言：
- 🇨🇳 中文 (简体)
- 🇺🇸 English
- 🇯🇵 日本語 (计划中)
- 🇰🇷 한국어 (计划中)

## 📞 获取帮助

如果您在贡献过程中遇到问题：

1. 📖 查看项目文档
2. 🔍 搜索已有的Issues
3. 💬 在相关Issue中提问
4. 📧 发送邮件到 <EMAIL>

## 🏆 贡献者认可

我们会在以下地方认可贡献者：

- 📄 README.md 贡献者列表
- 🎉 发布说明中的特别感谢
- 🏅 GitHub贡献者徽章
- 📱 应用内贡献者页面

## 📜 行为准则

请遵守我们的 [行为准则](CODE_OF_CONDUCT.md)：

- 🤝 尊重所有参与者
- 💬 使用友善和包容的语言
- 🎯 专注于对项目最有利的事情
- 🙏 优雅地接受建设性批评

## 🎉 感谢

感谢您考虑为 StoryWeaver 做出贡献！每一个贡献，无论大小，都让这个项目变得更好。

---

<div align="center">
  <p>一起用代码编织美好的故事 ✨</p>
  <p>Happy Coding! 🚀</p>
</div>