# 安全政策 🔒

> StoryWeaver 项目的安全指南和漏洞报告流程

## 📋 概述

StoryWeaver 致力于为用户提供安全可靠的AI儿童故事创作平台。我们重视安全问题，并建立了完善的安全政策来保护用户数据和隐私。

## 🛡️ 支持的版本

我们为以下版本提供安全更新：

| 版本 | 支持状态 | 支持期限 |
| --- | --- | --- |
| 1.x.x | ✅ 完全支持 | 长期支持 |
| 0.x.x | ⚠️ 有限支持 | 2024年6月前 |
| < 0.1.0 | ❌ 不支持 | - |

## 🚨 报告安全漏洞

### 如何报告

如果您发现了安全漏洞，请**不要**在公开的GitHub Issues中报告。请通过以下安全渠道联系我们：

#### 📧 邮件报告
- **安全邮箱**: <EMAIL>
- **PGP公钥**: [下载公钥](https://storyweaver.com/.well-known/pgp-key.asc)
- **响应时间**: 24小时内确认收到

#### 🔒 加密报告
```
-----BEGIN PGP PUBLIC KEY BLOCK-----
[PGP公钥内容]
-----END PGP PUBLIC KEY BLOCK-----
```

### 报告内容

请在报告中包含以下信息：

1. **漏洞描述**
   - 详细的漏洞说明
   - 影响范围和严重程度
   - 可能的攻击场景

2. **复现步骤**
   - 详细的复现步骤
   - 必要的环境信息
   - 相关的代码片段

3. **影响评估**
   - 受影响的组件/功能
   - 潜在的数据泄露风险
   - 对用户的影响程度

4. **建议修复**
   - 可能的修复方案
   - 临时缓解措施
   - 相关的安全最佳实践

### 报告模板

```markdown
## 安全漏洞报告

### 基本信息
- **发现日期**: YYYY-MM-DD
- **报告人**: 您的姓名/组织
- **联系方式**: 您的邮箱
- **漏洞类型**: [XSS/SQL注入/权限提升/等]

### 漏洞详情
**描述**: 
[详细描述漏洞]

**影响范围**: 
[说明影响的组件和功能]

**严重程度**: 
[低/中/高/严重]

### 复现步骤
1. 步骤一
2. 步骤二
3. 步骤三
...

### 环境信息
- **浏览器**: Chrome/Firefox/Safari 版本
- **操作系统**: Windows/macOS/Linux
- **应用版本**: v1.0.0

### 证据材料
[截图、视频、日志等]

### 建议修复
[您的修复建议]
```

## 🔍 安全响应流程

### 1. 确认阶段 (24小时内)
- ✅ 确认收到报告
- 🔍 初步评估漏洞
- 📋 分配跟踪编号
- 👥 组建响应团队

### 2. 调查阶段 (7天内)
- 🔬 深入分析漏洞
- 📊 评估影响范围
- 🎯 确定修复优先级
- 📝 制定修复计划

### 3. 修复阶段 (根据严重程度)
- 🚨 **严重**: 24小时内
- 🔴 **高危**: 72小时内
- 🟠 **中危**: 7天内
- 🟡 **低危**: 30天内

### 4. 验证阶段 (3天内)
- ✅ 验证修复效果
- 🧪 进行安全测试
- 📋 更新安全文档
- 🔄 部署到生产环境

### 5. 披露阶段 (修复后)
- 📢 发布安全公告
- 🏆 致谢报告者
- 📚 更新安全指南
- 📊 总结经验教训

## 🏆 安全研究者奖励

我们设立了漏洞奖励计划来感谢安全研究者的贡献：

### 奖励等级

| 严重程度 | 奖励金额 | 条件 |
|---------|---------|------|
| 🚨 严重 | $1000-5000 | 远程代码执行、数据泄露 |
| 🔴 高危 | $500-1000 | 权限提升、身份验证绕过 |
| 🟠 中危 | $100-500 | XSS、CSRF、信息泄露 |
| 🟡 低危 | $50-100 | 配置问题、轻微信息泄露 |

### 奖励条件

- ✅ 首次发现的原创漏洞
- ✅ 提供详细的复现步骤
- ✅ 遵循负责任的披露原则
- ✅ 不在生产环境中测试
- ✅ 不访问或修改用户数据

### 不符合奖励的情况

- ❌ 已知的漏洞或重复报告
- ❌ 社会工程学攻击
- ❌ 物理安全问题
- ❌ DoS/DDoS攻击
- ❌ 垃圾邮件或钓鱼攻击

## 🔐 安全最佳实践

### 开发安全

#### 代码安全
```typescript
// ✅ 好的做法：输入验证
const validateInput = (input: string): boolean => {
  const sanitized = input.trim().replace(/[<>]/g, '');
  return sanitized.length > 0 && sanitized.length <= 100;
};

// ❌ 避免：直接使用用户输入
const dangerousHTML = `<div>${userInput}</div>`;
```

#### API安全
```typescript
// ✅ 好的做法：认证和授权
app.use('/api/stories', authMiddleware);
app.use('/api/stories', rateLimitMiddleware);

// ✅ 好的做法：输入验证
const createStorySchema = z.object({
  characterName: z.string().min(1).max(50),
  theme: z.enum(['adventure', 'friendship', 'family'])
});
```

#### 数据库安全
```sql
-- ✅ 好的做法：参数化查询
SELECT * FROM stories WHERE user_id = ? AND id = ?;

-- ❌ 避免：字符串拼接
SELECT * FROM stories WHERE user_id = '" + userId + "';
```

### 部署安全

#### 环境变量
```bash
# ✅ 好的做法：使用强密码
JWT_SECRET=$(openssl rand -base64 32)
DATABASE_PASSWORD=$(openssl rand -base64 24)

# ❌ 避免：弱密码或默认密码
JWT_SECRET=secret123
DATABASE_PASSWORD=password
```

#### HTTPS配置
```nginx
# ✅ 好的做法：强制HTTPS
server {
    listen 80;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
}
```

### 用户安全

#### 密码安全
- 🔒 使用强密码策略
- 🔄 定期更新密码
- 🚫 不重复使用密码
- 📱 启用双因素认证

#### 数据保护
- 🔐 加密敏感数据
- 🗑️ 定期清理无用数据
- 📋 最小权限原则
- 🔍 定期安全审计

## 🛡️ 安全功能

### 内容安全

#### AI内容过滤
```typescript
// Gemini Safety API 配置
const safetySettings = [
  {
    category: 'HARM_CATEGORY_HARASSMENT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE'
  },
  {
    category: 'HARM_CATEGORY_HATE_SPEECH',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE'
  },
  {
    category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE'
  },
  {
    category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
    threshold: 'BLOCK_MEDIUM_AND_ABOVE'
  }
];
```

#### 儿童保护
- 🔒 严格的内容审核
- 👨‍👩‍👧‍👦 家长控制功能
- 🚫 禁止不当内容
- 📊 内容质量监控

### 数据安全

#### 加密存储
```typescript
// 敏感数据加密
const encryptSensitiveData = (data: string): string => {
  const cipher = crypto.createCipher('aes-256-gcm', process.env.ENCRYPTION_KEY);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
};
```

#### 访问控制
```typescript
// 基于角色的访问控制
const checkPermission = (user: User, resource: string, action: string): boolean => {
  const userRoles = user.roles;
  const requiredPermissions = getRequiredPermissions(resource, action);
  return userRoles.some(role => 
    role.permissions.some(permission => 
      requiredPermissions.includes(permission)
    )
  );
};
```

## 📊 安全监控

### 日志记录
```typescript
// 安全事件日志
const logSecurityEvent = (event: SecurityEvent) => {
  logger.warn('Security Event', {
    type: event.type,
    userId: event.userId,
    ip: event.ipAddress,
    userAgent: event.userAgent,
    timestamp: new Date().toISOString(),
    details: event.details
  });
};
```

### 异常检测
- 🔍 异常登录检测
- 📊 API调用频率监控
- 🚨 可疑行为告警
- 📈 安全指标统计

## 🔄 安全更新

### 依赖管理
```bash
# 定期检查安全漏洞
npm audit
npm audit fix

# 更新依赖
npm update
```

### 自动化安全检查
```yaml
# GitHub Actions 安全检查
name: Security Check
on: [push, pull_request]
jobs:
  security:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run security audit
        run: npm audit
      - name: Run CodeQL analysis
        uses: github/codeql-action/analyze@v1
```

## 📞 联系我们

### 安全团队
- 📧 **安全邮箱**: <EMAIL>
- 🚨 **紧急联系**: <EMAIL>
- 💬 **安全讨论**: [GitHub Security Advisories](https://github.com/your-username/storyweaver/security/advisories)

### 响应时间承诺
- 🚨 **严重漏洞**: 2小时内响应
- 🔴 **高危漏洞**: 8小时内响应
- 🟠 **中危漏洞**: 24小时内响应
- 🟡 **低危漏洞**: 72小时内响应

## 📚 相关资源

### 安全标准
- [OWASP Top 10](https://owasp.org/www-project-top-ten/)
- [NIST Cybersecurity Framework](https://www.nist.gov/cyberframework)
- [ISO 27001](https://www.iso.org/isoiec-27001-information-security.html)

### 安全工具
- [Snyk](https://snyk.io/) - 依赖漏洞扫描
- [SonarQube](https://www.sonarqube.org/) - 代码质量检查
- [OWASP ZAP](https://zaproxy.org/) - Web应用安全测试

---

<div align="center">
  <p>安全是我们共同的责任 🛡️</p>
  <p>感谢您帮助我们保护用户安全！</p>
</div>