# StoryWeaver (故事织梦) 🎨📚

> AI驱动的个性化儿童有声绘本创作平台

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen)](https://nodejs.org/)
[![Cloudflare Workers](https://img.shields.io/badge/Cloudflare-Workers-orange)](https://workers.cloudflare.com/)
[![React](https://img.shields.io/badge/React-18-blue)](https://reactjs.org/)

## 🌟 项目简介

StoryWeaver是一个充满魔力的AI平台，让父母和孩子能够共同创作个性化的、配有精美插图和生动旁白的故事书。只需提供主角的名字、性格特点和故事主题，平台就能生成独一无二的故事，配上统一风格的插图，并制作成带有生动旁白的有声读物。

### ✨ 核心特性

- 🤖 **AI故事生成**: 基于Gemini 2.5 Flash的智能故事创作
- 🎨 **AI插图绘制**: 使用Imagen 3生成一致风格的精美插图
- 🎵 **AI语音合成**: Gemini TTS生成富有表现力的有声旁白
- 📱 **响应式设计**: 适配所有设备的现代化界面
- 🔒 **内容安全**: 儿童友好的内容过滤和安全检查
- 📖 **实体书定制**: 将数字故事印刷成精装实体书
- 💳 **灵活付费**: 免费试用、积分购买、订阅等多种方案

### 🎯 目标用户

- **主要用户**: 25-40岁的父母和教育工作者
- **次要用户**: 3-8岁的儿童
- **使用场景**: 睡前故事、亲子时光、教育娱乐

## 🏗️ 技术架构

### 前端技术栈
```
React 18 + TypeScript + Tailwind CSS
├── 状态管理: Zustand
├── 路由: React Router v6
├── 构建工具: Vite
└── 部署: Cloudflare Pages
```

### 后端技术栈
```
Cloudflare Workers + Hono Framework
├── 数据存储: D1 Database (SQLite)
├── 文件存储: R2 Storage
├── 缓存: KV Store
├── 认证: Google OAuth 2.0
└── 支付: Stripe API
```

### AI服务集成
```
Google AI Platform
├── 文本生成: Gemini 2.5 Flash
├── 图像生成: Imagen 3
├── 语音合成: Gemini TTS
└── 内容安全: Gemini Safety API
```

## 🚀 快速开始

### 环境要求

- Node.js >= 18.0.0
- npm 或 yarn
- Cloudflare 账户
- Google Cloud 账户
- Stripe 账户

### 1. 克隆项目

```bash
git clone https://github.com/your-username/storyweaver.git
cd storyweaver
```

### 2. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖 (如果有前端代码)
cd ../frontend
npm install
```

### 3. 配置环境

```bash
cd backend

# 创建 Cloudflare 资源
chmod +x setup-cloudflare-resources.sh
./setup-cloudflare-resources.sh

# 配置环境变量
chmod +x setup-secrets.sh
./setup-secrets.sh

# 手动设置 Google OAuth (获取密钥后)
wrangler secret put GOOGLE_CLIENT_ID
wrangler secret put GOOGLE_CLIENT_SECRET
```

### 4. 初始化数据库

```bash
# 开发环境
npm run migrate:local

# 生产环境
npm run migrate
```

### 5. 启动开发服务器

```bash
# 启动后端
cd backend
npm run dev

# 启动前端 (新终端)
cd frontend
npm run dev
```

### 6. 访问应用

- 前端: http://localhost:3000
- 后端: http://localhost:8787

## 📁 项目结构

```
StoryWeaver/
├── 📄 README.md                    # 项目说明文档
├── 📄 DEPLOYMENT_GUIDE.md          # 部署指南
├── 📄 项目介绍.md                  # 项目详细介绍
├── 📄 StoryWeaver_完整项目计划.md   # 完整项目计划
├── 📄 technical_implementation.md  # 技术实现方案
├── 📄 ui_ux_design_guide.md       # UI/UX设计规范
├── 📄 website_framework.md        # 网站框架设计
├── 🗂️ backend/                    # 后端服务
│   ├── 📂 src/                    # 源代码
│   │   ├── 📂 handlers/           # API路由处理器
│   │   ├── 📂 services/           # 业务服务层
│   │   ├── 📂 middleware/         # 中间件
│   │   ├── 📂 utils/              # 工具函数
│   │   └── 📂 types/              # TypeScript类型
│   ├── 📂 schemas/                # 数据库Schema
│   ├── 📄 wrangler.toml           # Cloudflare配置
│   ├── 📄 package.json            # 依赖配置
│   ├── 📄 API_DOCUMENTATION.md    # API文档
│   ├── 📄 DEPLOYMENT.md           # 后端部署指南
│   └── 📄 README.md               # 后端说明
├── 🗂️ deploy-backend/             # 部署版本后端
├── 🗂️ frontend/                   # 前端应用 (待开发)
└── 🗂️ docs/                       # 项目文档
```

## 💰 商业模式

| 方案 | 价格 | 功能 |
|------|------|------|
| 🆓 **免费体验** | $0 | 1个数字故事 |
| 💎 **积分包** | $10 | 5个数字故事 + 有声书 |
| 🔄 **月订阅** | $15/月 | 无限数字故事 + 有声书 |
| 📚 **实体书** | $29.99/本 | 专业印刷精装书 |

## 🔧 开发指南

### API文档
详细的API文档请查看: [API_DOCUMENTATION.md](backend/API_DOCUMENTATION.md)

### 部署指南
完整的部署说明请查看: [DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)

### UI/UX设计
设计规范请查看: [ui_ux_design_guide.md](ui_ux_design_guide.md)

### 技术实现
技术细节请查看: [technical_implementation.md](technical_implementation.md)

## 🧪 测试

```bash
# 运行后端测试
cd backend
npm test

# 运行前端测试
cd frontend
npm test
```

## 📊 项目状态

### ✅ 已完成
- [x] 项目架构设计
- [x] 后端API实现
- [x] 数据库设计
- [x] AI服务集成
- [x] 认证系统
- [x] 支付系统
- [x] 部署配置

### 🚧 进行中
- [ ] 前端React应用开发
- [ ] UI组件库实现
- [ ] 用户界面设计

### 📋 待开始
- [ ] 移动端适配
- [ ] 性能优化
- [ ] 监控和日志
- [ ] 自动化测试

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📝 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 📧 Email: <EMAIL>
- 🌐 Website: https://storyweaver.jamintextiles.com
- 📱 Twitter: @StoryWeaverAI

## 🙏 致谢

感谢以下技术和服务提供商：

- [Google AI Platform](https://ai.google.dev/) - AI服务支持
- [Cloudflare](https://cloudflare.com/) - 基础设施支持
- [Stripe](https://stripe.com/) - 支付服务支持
- [React](https://reactjs.org/) - 前端框架
- [Hono](https://hono.dev/) - 后端框架

---

<div align="center">
  <p>用AI的魔力，为每个孩子编织独特的故事 ✨</p>
  <p>Made with ❤️ by StoryWeaver Team</p>
</div>