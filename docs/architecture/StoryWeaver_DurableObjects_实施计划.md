# StoryWeaver Durable Objects 实施计划

## 📋 项目当前状态

### 🏗️ 现有架构
- **运行环境**: Cloudflare Workers
- **数据存储**: 
  - KV Store (用户会话、缓存)
  - R2 Storage (图片、音频文件)
  - D1 Database (用户数据、故事元数据)
- **AI服务**: Gemini API (文本生成 + 图像生成 + TTS)
- **认证**: Google OAuth 2.0
- **支付**: Stripe API

### 🔧 技术栈
- **前端**: React 18 + TypeScript + Vite + Tailwind CSS + Zustand
- **后端**: Cloudflare Workers + Hono框架
- **部署**: 
  - 前端: Cloudflare Pages (https://storyweaver.pages.dev)
  - 后端: Cloudflare Workers (storyweaver-api.stawky.workers.dev)

### 📁 关键文件位置
```
backend/
├── src/
│   ├── handlers/stories.ts      # 故事处理逻辑
│   ├── services/
│   │   ├── gemini.ts           # AI服务集成
│   │   └── storage.ts          # 存储服务（当前使用KV）
│   ├── types/hono.ts           # 类型定义
│   └── index.ts                # 入口文件
├── wrangler.toml               # Cloudflare配置
└── schemas/database.sql        # 数据库结构
```

### 🎯 已识别的痛点
1. **AI生成流程可靠性问题**
   - 文本→图像→语音的复杂异步流程
   - 缺乏有效的任务状态管理和重试机制
   - 用户无法获得准确的生成进度

2. **状态一致性问题**
   - KV Store最终一致性导致状态不同步
   - 用户刷新页面看到过期状态
   - 多设备访问时状态混乱

## 🎯 第一阶段实施计划

### 优先级1: AI任务队列管理 (AITaskQueueDO)

#### 📋 实施目标
- 创建可靠的AI生成任务队列
- 实现任务状态跟踪和进度报告
- 支持失败重试和错误处理
- 提供实时进度更新

#### 🔧 技术实现
```typescript
// 新建文件: backend/src/durable-objects/AITaskQueueDO.ts
class AITaskQueueDO extends DurableObject {
  async generateStory(request: StoryGenerationRequest) {
    // 1. 创建任务队列
    const tasks = [
      { type: 'text', priority: 1, status: 'pending' },
      { type: 'image', priority: 2, status: 'pending' },
      { type: 'audio', priority: 3, status: 'pending' }
    ];
    
    // 2. 顺序执行任务
    for (const task of tasks) {
      await this.executeTaskWithRetry(task);
      await this.broadcastProgress(task);
    }
  }
  
  async executeTaskWithRetry(task: Task, maxRetries = 3) {
    // 实现重试逻辑
  }
  
  async broadcastProgress(task: Task) {
    // WebSocket实时进度推送
  }
}
```

#### 📝 需要修改的文件
1. **backend/src/handlers/stories.ts**
   - 修改故事创建逻辑，使用DO而不是直接调用AI服务
   - 添加进度查询端点

2. **backend/wrangler.toml**
   - 添加Durable Objects绑定配置

3. **frontend/src/services/storyService.ts**
   - 添加实时进度监听
   - 实现WebSocket连接管理

#### 🎯 预期收益
- ✅ 99%+ 的任务完成率（当前可能有丢失）
- ✅ 实时进度显示，提升用户体验
- ✅ 自动重试机制，减少人工干预
- ✅ 更好的错误处理和用户反馈

### 优先级2: 故事生成状态管理 (StoryGenerationDO)

#### 📋 实施目标
- 替换KV Store的故事状态缓存
- 提供强一致性的状态管理
- 支持实时状态推送
- 多设备状态同步

#### 🔧 技术实现
```typescript
// 新建文件: backend/src/durable-objects/StoryGenerationDO.ts
class StoryGenerationDO extends DurableObject {
  async updateStatus(storyId: string, status: GenerationStatus) {
    // 强一致性状态更新
    await this.ctx.storage.put(`status:${storyId}`, status);
    
    // 实时推送给所有连接的客户端
    this.broadcast({ 
      type: 'statusUpdate', 
      storyId, 
      status,
      timestamp: Date.now()
    });
  }
  
  async getStatus(storyId: string): Promise<GenerationStatus> {
    return await this.ctx.storage.get(`status:${storyId}`);
  }
  
  async handleWebSocket(request: Request) {
    // WebSocket连接管理
  }
}
```

#### 📝 需要修改的文件
1. **backend/src/services/storage.ts**
   - 移除KV Store的故事状态相关方法
   - 添加DO调用逻辑

2. **frontend/src/components/features/story-creator/**
   - 添加WebSocket状态监听
   - 实现实时状态更新UI

#### 🎯 预期收益
- ✅ 消除状态不一致问题
- ✅ 实时状态更新，无需轮询
- ✅ 多设备完美同步
- ✅ 更好的用户体验

## 🛠️ 实施步骤

### 第1步: 环境准备 (1天)
1. 更新wrangler.toml配置
2. 创建DO类型定义
3. 设置开发环境

### 第2步: 实现AITaskQueueDO (3-4天)
1. 创建DO类文件
2. 实现任务队列逻辑
3. 添加WebSocket支持
4. 修改后端API调用

### 第3步: 前端集成 (2-3天)
1. 实现WebSocket客户端
2. 更新UI组件显示实时进度
3. 添加错误处理

### 第4步: 测试和优化 (2天)
1. 端到端测试
2. 性能优化
3. 错误场景测试

### 第5步: 实现StoryGenerationDO (2-3天)
1. 创建状态管理DO
2. 替换KV Store调用
3. 前端状态同步

### 第6步: 部署和监控 (1天)
1. 生产环境部署
2. 监控配置
3. 性能指标收集

## 📊 成功指标

### 技术指标
- AI生成任务成功率 > 99%
- 状态更新延迟 < 100ms
- WebSocket连接稳定性 > 99.5%

### 用户体验指标
- 用户等待时间感知改善
- 状态不一致投诉减少
- 整体满意度提升

## ⚠️ 风险和缓解措施

### 技术风险
1. **DO学习曲线** - 预留额外学习时间
2. **WebSocket复杂性** - 实现降级方案
3. **调试困难** - 完善日志和监控

### 业务风险
1. **用户体验中断** - 分阶段发布，保留回滚能力
2. **成本增加** - 监控使用量，设置预算警报

## 📚 参考资源

### 官方文档
- [Cloudflare Durable Objects](https://developers.cloudflare.com/durable-objects/)
- [WebSocket API](https://developers.cloudflare.com/durable-objects/examples/websocket/)

### 代码示例
- [任务队列模式](https://developers.cloudflare.com/durable-objects/examples/task-queue/)
- [实时协作](https://developers.cloudflare.com/durable-objects/examples/websocket-coordinated-state/)

---

## 🚀 开始实施

准备好开始实施时，请按照以下顺序进行：

1. **创建新分支**: `git checkout -b feature/durable-objects-phase1`
2. **从AITaskQueueDO开始**: 这是最有价值的改进
3. **逐步测试**: 每个组件完成后立即测试
4. **保持文档更新**: 记录实施过程中的发现和调整

**预计总工期**: 10-12个工作日
**建议团队规模**: 1-2名开发者
**技术难度**: 中等到高等

## 💻 详细技术实现

### AITaskQueueDO 完整实现示例

```typescript
// backend/src/durable-objects/AITaskQueueDO.ts
export class AITaskQueueDO extends DurableObject {
  private sessions: Set<WebSocket> = new Set();

  constructor(ctx: DurableObjectState, env: Env) {
    super(ctx, env);
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);

    switch (url.pathname) {
      case '/websocket':
        return this.handleWebSocket(request);
      case '/generate':
        return this.handleGenerate(request);
      case '/status':
        return this.handleStatus(request);
      default:
        return new Response('Not found', { status: 404 });
    }
  }

  async handleWebSocket(request: Request): Promise<Response> {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    server.accept();
    this.sessions.add(server);

    server.addEventListener('close', () => {
      this.sessions.delete(server);
    });

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  async handleGenerate(request: Request): Promise<Response> {
    const { storyId, ...params } = await request.json();

    // 创建任务队列
    const tasks = [
      { id: `${storyId}-text`, type: 'text', status: 'pending', progress: 0 },
      { id: `${storyId}-image`, type: 'image', status: 'pending', progress: 0 },
      { id: `${storyId}-audio`, type: 'audio', status: 'pending', progress: 0 }
    ];

    // 存储任务状态
    await this.ctx.storage.put(`tasks:${storyId}`, tasks);

    // 异步执行任务
    this.executeTasksAsync(storyId, tasks, params);

    return Response.json({ success: true, storyId, tasks });
  }

  private async executeTasksAsync(storyId: string, tasks: Task[], params: any) {
    for (const task of tasks) {
      try {
        await this.executeTask(storyId, task, params);
      } catch (error) {
        await this.handleTaskError(storyId, task, error);
      }
    }
  }

  private async executeTask(storyId: string, task: Task, params: any) {
    // 更新任务状态为进行中
    task.status = 'running';
    await this.updateTaskStatus(storyId, task);

    // 根据任务类型执行相应的AI生成
    switch (task.type) {
      case 'text':
        await this.generateText(storyId, task, params);
        break;
      case 'image':
        await this.generateImage(storyId, task, params);
        break;
      case 'audio':
        await this.generateAudio(storyId, task, params);
        break;
    }

    // 标记任务完成
    task.status = 'completed';
    task.progress = 100;
    await this.updateTaskStatus(storyId, task);
  }

  private async updateTaskStatus(storyId: string, task: Task) {
    // 更新存储中的任务状态
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as Task[];
    const taskIndex = tasks.findIndex(t => t.id === task.id);
    if (taskIndex !== -1) {
      tasks[taskIndex] = task;
      await this.ctx.storage.put(`tasks:${storyId}`, tasks);
    }

    // 广播状态更新
    this.broadcast({
      type: 'taskUpdate',
      storyId,
      task,
      timestamp: Date.now()
    });
  }

  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    this.sessions.forEach(session => {
      try {
        session.send(messageStr);
      } catch (error) {
        // 移除断开的连接
        this.sessions.delete(session);
      }
    });
  }
}
```

### wrangler.toml 配置更新

```toml
# 在现有配置基础上添加
[durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" },
  { name = "STORY_GENERATION", class_name = "StoryGenerationDO" }
]

[[migrations]]
tag = "v1"
new_classes = ["AITaskQueueDO", "StoryGenerationDO"]
```

### 前端WebSocket客户端

```typescript
// frontend/src/services/durableObjectsClient.ts
export class StoryGenerationClient {
  private ws: WebSocket | null = null;
  private listeners: Map<string, Function[]> = new Map();

  connect(storyId: string) {
    const wsUrl = `wss://storyweaver-api.stawky.workers.dev/ai-queue/${storyId}/websocket`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.notifyListeners(message.type, message);
    };

    this.ws.onclose = () => {
      // 重连逻辑
      setTimeout(() => this.connect(storyId), 5000);
    };
  }

  onTaskUpdate(callback: (task: Task) => void) {
    this.addListener('taskUpdate', callback);
  }

  private addListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  private notifyListeners(event: string, data: any) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }
}
```

---

*文档创建时间: 2025-01-02*
*项目状态: 准备实施第一阶段*
*预计完成时间: 2025-01-14*
