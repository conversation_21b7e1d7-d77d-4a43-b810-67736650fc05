# StoryWeaver (故事织梦) - 网站设计框架

## 1. 网站整体架构

### 1.1 技术栈建议
- **前端**: React.js + TypeScript + Tailwind CSS
- **后端**: Node.js + Express.js + MongoDB
- **AI集成**: Gemini API (文本生成 + TTS) + Imagen 3 (图像生成)
- **支付**: Stripe 集成
- **部署**: Vercel (前端) + AWS/Google Cloud (后端)

### 1.2 核心页面结构
```
├── 首页 (Landing Page)
├── 故事创作器 (Story Creator)
├── 我的故事库 (My Stories)
├── 定价方案 (Pricing)
├── 用户账户 (Account)
├── 实体书定制 (Physical Book)
└── 帮助中心 (Help Center)
```

## 2. 详细页面设计

### 2.1 首页 (Landing Page)
**目标**: 吸引用户，展示产品魅力，引导注册

**布局结构**:
```
┌─────────────────────────────────────┐
│           导航栏                     │
├─────────────────────────────────────┤
│         英雄区域 (Hero Section)       │
│   - 主标题: "为您的孩子创造独特故事"    │
│   - 副标题: 个性化AI绘本生成器        │
│   - CTA按钮: "免费创作第一个故事"      │
│   - 演示视频/动画                    │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         功能展示区                   │
│   - 三步骤流程图                     │
│   - 样本故事展示                     │
│   - 音频播放器演示                   │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         用户评价区                   │
│   - 家长反馈                        │
│   - 使用统计                        │
└─────────────────────────────────────┘
┌─────────────────────────────────────┐
│         定价预览                     │
│   - 简化版定价卡片                   │
│   - "查看详细定价"链接               │
└─────────────────────────────────────┘
```

**关键元素**:
- 温馨的色彩搭配 (柔和的蓝色、粉色、黄色)
- 儿童友好的插画风格
- 清晰的价值主张
- 社会证明 (用户评价、使用数据)

### 2.2 故事创作器 (Story Creator)
**目标**: 核心功能页面，用户输入信息生成故事

**多步骤表单设计**:

**步骤1: 角色设定**
```
┌─────────────────────────────────────┐
│         进度条 (1/4)                 │
├─────────────────────────────────────┤
│    "让我们认识一下故事的主角"         │
│                                     │
│    主角姓名: [输入框]                │
│    年龄: [下拉选择] 3-8岁            │
│    性别: [单选按钮] 男孩/女孩/其他    │
│                                     │
│    性格特征 (最多选择3个):           │
│    [勇敢] [善良] [聪明] [好奇]       │
│    [活泼] [安静] [爱冒险] [爱学习]   │
│                                     │
│    [下一步] 按钮                    │
└─────────────────────────────────────┘
```

**步骤2: 故事主题**
```
┌─────────────────────────────────────┐
│         进度条 (2/4)                 │
├─────────────────────────────────────┤
│      "选择一个精彩的冒险主题"         │
│                                     │
│    主题分类:                        │
│    ○ 奇幻冒险 (魔法森林、龙、城堡)    │
│    ○ 日常生活 (学校、家庭、友谊)      │
│    ○ 科学探索 (太空、海洋、动物)      │
│    ○ 节日庆典 (生日、圣诞、春节)      │
│                                     │
│    具体场景: [文本框]               │
│    例如: "在糖果王国寻找失落的魔法饼干" │
│                                     │
│    [上一步] [下一步] 按钮            │
└─────────────────────────────────────┘
```

**步骤3: 风格选择**
```
┌─────────────────────────────────────┐
│         进度条 (3/4)                 │
├─────────────────────────────────────┤
│        "选择插画风格"                │
│                                     │
│    [卡通风格]  [水彩风格]  [简笔画]   │
│    [样图]     [样图]     [样图]      │
│                                     │
│    [梦幻风格]  [现实风格]  [动漫风格] │
│    [样图]     [样图]     [样图]      │
│                                     │
│    旁白声音:                        │
│    ○ 温柔女声  ○ 活泼男声  ○ 童声    │
│                                     │
│    [上一步] [下一步] 按钮            │
└─────────────────────────────────────┘
```

**步骤4: 确认与生成**
```
┌─────────────────────────────────────┐
│         进度条 (4/4)                 │
├─────────────────────────────────────┤
│          "故事预览"                  │
│                                     │
│    主角: 莉莉 (勇敢、善良的女孩)      │
│    主题: 在糖果王国寻找魔法饼干        │
│    风格: 卡通风格 + 温柔女声          │
│                                     │
│    预计生成时间: 2-3分钟             │
│    包含: 8-10页插图 + 完整音频        │
│                                     │
│    [修改] [开始创作故事] 按钮         │
└─────────────────────────────────────┘
```

### 2.3 生成进度页面
```
┌─────────────────────────────────────┐
│        "正在为您创作故事..."          │
│                                     │
│    [进度动画 - 魔法棒挥舞]           │
│                                     │
│    ✓ 构思故事情节... (完成)          │
│    ⟳ 绘制精美插图... (进行中)        │
│    ○ 录制音频旁白... (等待中)        │
│    ○ 整合最终作品... (等待中)        │
│                                     │
│    "好故事值得等待，请稍候..."        │
└─────────────────────────────────────┘
```

### 2.4 故事展示页面
```
┌─────────────────────────────────────┐
│           故事标题                   │
│       "莉莉的糖果王国冒险"           │
├─────────────────────────────────────┤
│                                     │
│    [故事封面插图]                   │
│                                     │
│    [▶️ 播放完整音频] [📖 阅读模式]   │
│    [💾 保存到我的故事] [🔗 分享]      │
│                                     │
│    故事信息:                        │
│    - 时长: 8分钟                    │
│    - 页数: 10页                     │
│    - 创作时间: 刚刚                 │
│                                     │
│    [📚 定制实体书 $29.99]           │
│    [🎨 创作新故事]                  │
└─────────────────────────────────────┘
```

### 2.5 我的故事库 (My Stories)
```
┌─────────────────────────────────────┐
│           我的故事库                 │
│                                     │
│    筛选: [全部] [最新] [最爱] [已购买]│
│    搜索: [搜索框]                   │
├─────────────────────────────────────┤
│                                     │
│  [故事1缩略图]  [故事2缩略图]  [故事3] │
│   莉莉的冒险     小明的太空旅行  ...   │
│   2024/1/15     2024/1/10           │
│   [播放][编辑]   [播放][编辑]        │
│                                     │
│  [故事4缩略图]  [故事5缩略图]  [+新建] │
│   ...           ...            故事  │
│                                     │
└─────────────────────────────────────┘
```

### 2.6 定价页面 (Pricing)
```
┌─────────────────────────────────────┐
│            选择您的方案              │
├─────────────────────────────────────┤
│                                     │
│  [免费体验]    [积分包]    [无限订阅]  │
│     $0          $10        $15/月   │
│                                     │
│  ✓ 1个故事      ✓ 5个故事   ✓ 无限故事│
│  ✓ 数字版本     ✓ 数字版本   ✓ 数字版本│
│  ✗ 音频旁白     ✓ 音频旁白   ✓ 音频旁白│
│  ✗ 实体书       ✓ 实体书选项  ✓ 实体书选项│
│                                     │
│  [开始免费]    [购买积分]   [立即订阅] │
│                                     │
│            实体书定制                │
│              $29.99/本              │
│        ✓ 专业印刷 ✓ 精装封面         │
│        ✓ 个性化封面 ✓ 快速配送       │
└─────────────────────────────────────┘
```

## 3. 用户体验流程

### 3.1 新用户注册流程
1. 访问首页 → 点击"免费创作"
2. 简单注册 (邮箱+密码 或 Google登录)
3. 欢迎引导 (产品介绍视频)
4. 直接进入故事创作器
5. 完成第一个免费故事

### 3.2 付费转化流程
1. 免费故事完成后显示升级提示
2. 展示付费功能对比 (音频、更多故事)
3. 提供多种付费选项
4. 简化支付流程 (Stripe集成)

### 3.3 实体书订购流程
1. 故事完成页面显示"定制实体书"选项
2. 预览实体书效果 (3D翻页效果)
3. 个性化选项 (封面文字、颜色)
4. 配送信息填写
5. 支付确认
6. 订单跟踪

## 4. 响应式设计

### 4.1 移动端适配
- 简化导航 (汉堡菜单)
- 垂直布局的故事创作流程
- 触摸友好的按钮尺寸
- 优化的音频播放器

### 4.2 平板端适配
- 保持桌面端功能
- 调整布局比例
- 优化触摸交互

## 5. 性能优化

### 5.1 加载优化
- 图片懒加载
- 音频预加载策略
- CDN加速
- 代码分割

### 5.2 AI生成优化
- 显示实时进度
- 错误重试机制
- 缓存常用结果
- 批量处理优化

## 6. 安全与内容审核

### 6.1 内容安全
- AI生成内容预过滤
- 敏感词检测
- 人工审核机制
- 用户举报系统

### 6.2 用户数据保护
- GDPR合规
- 儿童隐私保护
- 数据加密存储
- 定期安全审计

## 7. 分析与优化

### 7.1 关键指标
- 用户注册转化率
- 免费到付费转化率
- 故事完成率
- 实体书订购率

### 7.2 A/B测试计划
- 定价页面优化
- 创作流程简化
- CTA按钮优化
- 邮件营销效果

这个框架为StoryWeaver提供了完整的网站设计蓝图，涵盖了从用户获取到付费转化的全流程体验。