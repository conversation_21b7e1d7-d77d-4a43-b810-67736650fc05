# StoryWeaver 管理面板 Fluent UI 2 完整技术方案

**文档版本**: v1.0  
**创建时间**: 2025-07-22  
**作者**: Augment Agent  
**状态**: 设计完成

---

## 📋 目录

1. [项目概述](#项目概述)
2. [技术架构设计](#技术架构设计)
3. [数据访问解决方案](#数据访问解决方案)
4. [配置管理系统](#配置管理系统)
5. [UI设计规范](#ui设计规范)
6. [功能模块设计](#功能模块设计)
7. [实施计划](#实施计划)
8. [风险评估](#风险评估)

---

## 🎯 项目概述

### 背景
StoryWeaver项目现有admin-panel使用苹果风格设计，存在以下核心问题：
1. **数据访问隔离**：admin-panel无法直接访问主系统数据
2. **配置硬编码**：所有配置都在wrangler.toml中硬编码
3. **无法动态配置**：无法通过管理面板修改系统配置
4. **系统响应滞后**：主系统无法实时响应配置变更

### 目标
- 基于Microsoft Fluent UI 2设计系统重构管理面板
- 实现数据库共享访问机制
- 建立动态配置管理系统
- 提供完整的管理功能模块

### 核心价值
- **统一数据源**：确保数据一致性和实时性
- **动态配置**：支持配置热更新，提高运维效率
- **现代化UI**：提供优秀的用户体验
- **安全可控**：完善的权限控制和审计机制

---

## 🏗️ 技术架构设计

### 整体架构
```
┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │  Main Backend   │
│  (Fluent UI 2)  │    │   (Hono API)    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │   Shared Resources    │
         │  ┌─────────────────┐  │
         │  │  D1 Database    │  │
         │  │  KV Cache       │  │
         │  │  R2 Storage     │  │
         │  └─────────────────┘  │
         └───────────────────────┘
```

### 技术栈
- **前端**: React 18 + TypeScript + Fluent UI 2
- **状态管理**: Zustand + React Query
- **后端**: Cloudflare Workers + Hono
- **数据库**: Cloudflare D1 (SQLite)
- **缓存**: Cloudflare KV
- **存储**: Cloudflare R2

---

## 🔗 数据访问解决方案

### 共享数据库绑定
```toml
# admin-panel/wrangler.toml
[[d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "4b944057-392e-4167-9d7a-2e837d89db3a"  # 与主backend相同

[[kv_namespaces]]
binding = "CACHE"
id = "be3f96dcec0149869df496d54acabdc5"  # 与主backend相同

[[r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"  # 与主backend相同
```

### 数据访问权限控制
```typescript
// 管理员认证中间件
export async function AdminAuthMiddleware(c: Context, next: Next) {
  const token = c.req.header('Authorization')?.replace('Bearer ', '');
  if (!token) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  const payload = await verify(token, c.env.JWT_SECRET);
  if (!payload || !payload.isAdmin) {
    return c.json({ error: 'Admin access required' }, 403);
  }

  c.set('user', payload);
  await next();
}
```

### API设计模式
```typescript
// 只读数据查询API
app.get('/api/admin/users', async (c) => {
  const { page = 1, limit = 20, search } = c.req.query();
  const users = await getUsersWithPagination(c.env.DB, { page, limit, search });
  return c.json({ success: true, data: users });
});

// 配置管理API
app.put('/api/admin/configs/:category/:key', async (c) => {
  const { category, key } = c.req.param();
  const { value } = await c.req.json();
  const user = c.get('user');
  
  const configManager = new ConfigManager(c.env.CACHE, c.env.DB);
  await configManager.updateConfig(category, key, value, user.id);
  
  return c.json({ success: true, message: '配置已更新' });
});
```

---

## ⚙️ 配置管理系统

### 数据库设计
```sql
-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id TEXT PRIMARY KEY,
    category TEXT NOT NULL,  -- 'ai', 'payment', 'auth', 'system'
    key TEXT NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    is_sensitive BOOLEAN DEFAULT FALSE,
    updated_by TEXT,
    updated_at TEXT NOT NULL,
    UNIQUE(category, key)
);

-- 配置变更日志表
CREATE TABLE IF NOT EXISTS config_audit_logs (
    id TEXT PRIMARY KEY,
    config_id TEXT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by TEXT NOT NULL,
    changed_at TEXT NOT NULL,
    FOREIGN KEY (config_id) REFERENCES system_configs(id)
);
```

### ConfigManager服务
```typescript
export class ConfigManager {
  constructor(private kv: KVNamespace, private db: D1Database) {}

  async getConfig(category: string, key: string, fallback?: string): Promise<string | null> {
    // 1. 先从KV缓存获取（5分钟缓存）
    const cacheKey = `config:${category}:${key}`;
    let value = await this.kv.get(cacheKey);
    
    if (!value) {
      // 2. 从数据库获取并缓存
      const result = await this.db.prepare(
        'SELECT value FROM system_configs WHERE category = ? AND key = ?'
      ).bind(category, key).first();
      
      if (result) {
        value = result.value;
        await this.kv.put(cacheKey, value, { expirationTtl: 300 });
      } else if (fallback) {
        value = fallback;
      }
    }
    
    return value;
  }

  async updateConfig(category: string, key: string, value: string, updatedBy: string): Promise<void> {
    const configId = `${category}_${key}`;
    
    // 1. 获取旧值用于审计
    const oldConfig = await this.db.prepare(
      'SELECT value FROM system_configs WHERE category = ? AND key = ?'
    ).bind(category, key).first();
    
    // 2. 更新配置
    await this.db.prepare(`
      INSERT OR REPLACE INTO system_configs (id, category, key, value, updated_by, updated_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(configId, category, key, value, updatedBy, new Date().toISOString()).run();

    // 3. 记录审计日志
    await this.db.prepare(`
      INSERT INTO config_audit_logs (id, config_id, old_value, new_value, changed_by, changed_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(
      crypto.randomUUID(),
      configId,
      oldConfig?.value || null,
      value,
      updatedBy,
      new Date().toISOString()
    ).run();

    // 4. 更新KV缓存
    const cacheKey = `config:${category}:${key}`;
    await this.kv.put(cacheKey, value, { expirationTtl: 300 });

    // 5. 发送配置更新通知
    await this.kv.put(`config_update:${Date.now()}`, JSON.stringify({
      category, key, value, updatedBy, timestamp: new Date().toISOString()
    }), { expirationTtl: 60 });
  }
}
```

### 主系统配置响应
```typescript
// 主系统配置加载器
export class ConfigLoader {
  private static instance: ConfigLoader;
  private configCache = new Map<string, { value: string; expires: number }>();
  
  constructor(private kv: KVNamespace, private db: D1Database) {}

  static getInstance(kv: KVNamespace, db: D1Database): ConfigLoader {
    if (!ConfigLoader.instance) {
      ConfigLoader.instance = new ConfigLoader(kv, db);
    }
    return ConfigLoader.instance;
  }

  async getConfig(category: string, key: string, fallback?: string): Promise<string> {
    const cacheKey = `${category}:${key}`;
    const cached = this.configCache.get(cacheKey);
    
    // 检查内存缓存
    if (cached && cached.expires > Date.now()) {
      return cached.value;
    }

    // 从KV获取最新配置
    const kvKey = `config:${category}:${key}`;
    let value = await this.kv.get(kvKey);
    
    if (!value) {
      // 从数据库获取
      const result = await this.db.prepare(
        'SELECT value FROM system_configs WHERE category = ? AND key = ?'
      ).bind(category, key).first();
      
      if (result) {
        value = result.value;
        await this.kv.put(kvKey, value, { expirationTtl: 300 });
      } else if (fallback) {
        value = fallback;
      }
    }

    if (value) {
      // 更新内存缓存
      this.configCache.set(cacheKey, {
        value,
        expires: Date.now() + 300000 // 5分钟缓存
      });
    }

    return value || '';
  }
}
```

---

## 🎨 UI设计规范

### Fluent UI 2组件选择

#### 导航组件
- **Nav**: 主导航容器
- **AppItem**: 导航项目
- **AppItemStatic**: 静态导航项

#### 数据展示组件
- **DataGrid**: 数据表格，支持排序、筛选、分页
- **Card**: 内容卡片容器
- **StatsCard**: 统计数据卡片

#### 交互组件
- **Dialog**: 模态对话框
- **Panel**: 侧边面板
- **CommandBar**: 操作工具栏
- **Button**: 按钮组件

#### 表单组件
- **Input**: 输入框
- **Dropdown**: 下拉选择
- **SearchBox**: 搜索框
- **DatePicker**: 日期选择器

#### 反馈组件
- **MessageBar**: 消息通知
- **Spinner**: 加载指示器
- **ProgressBar**: 进度条

### 主题配置
```typescript
import { 
  FluentProvider, 
  webLightTheme, 
  webDarkTheme,
  createLightTheme,
  BrandVariants 
} from '@fluentui/react-components';

// 自定义品牌主题
const storyWeaverBrandRamp: BrandVariants = {
  10: "#020305",
  20: "#111418",
  30: "#16202D",
  40: "#1B2B42",
  50: "#203757",
  60: "#25446C",
  70: "#2A5281",
  80: "#2F5F96",
  90: "#346DAB",
  100: "#387AC0",
  110: "#4A87C7",
  120: "#5C94CE",
  130: "#6EA1D5",
  140: "#80AEDC",
  150: "#92BBE3",
  160: "#A4C8EA"
};

const customLightTheme = createLightTheme(storyWeaverBrandRamp);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isDark, setIsDark] = useState(false);
  
  return (
    <FluentProvider theme={isDark ? webDarkTheme : customLightTheme}>
      {children}
    </FluentProvider>
  );
};
```

### 响应式设计
```typescript
// 响应式断点
const breakpoints = {
  mobile: '(max-width: 768px)',
  tablet: '(max-width: 1024px)',
  desktop: '(min-width: 1025px)'
};

// 响应式布局组件
export const ResponsiveLayout: React.FC = ({ children }) => {
  const isMobile = useMediaQuery(breakpoints.mobile);
  const isTablet = useMediaQuery(breakpoints.tablet);
  
  return (
    <div className={`layout ${isMobile ? 'mobile' : isTablet ? 'tablet' : 'desktop'}`}>
      {children}
    </div>
  );
};
```

---

## 📊 功能模块设计

### 1. 仪表板模块
```typescript
export const DashboardPage: React.FC = () => {
  return (
    <div className="dashboard-container">
      {/* KPI指标卡片 */}
      <div className="stats-grid">
        <StatsCard title="总用户数" value="1,234" change={{ value: 12, type: 'increase' }} />
        <StatsCard title="今日故事" value="56" change={{ value: 8, type: 'increase' }} />
        <StatsCard title="月度收入" value="¥12,345" change={{ value: 15, type: 'increase' }} />
        <StatsCard title="系统状态" value="正常" />
      </div>
      
      {/* 图表区域 */}
      <div className="charts-grid">
        <Card>
          <CardHeader header={<Text weight="semibold">用户增长趋势</Text>} />
          <CardPreview>
            <LineChart data={userGrowthData} />
          </CardPreview>
        </Card>
        
        <Card>
          <CardHeader header={<Text weight="semibold">故事创作分布</Text>} />
          <CardPreview>
            <BarChart data={storyDistributionData} />
          </CardPreview>
        </Card>
      </div>
      
      {/* 快速操作 */}
      <Card>
        <CardHeader header={<Text weight="semibold">快速操作</Text>} />
        <CardPreview>
          <CommandBar items={quickActionItems} />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### 2. 用户管理模块
```typescript
export const UserManagementPage: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [panelOpen, setPanelOpen] = useState(false);

  const columns: TableColumnDefinition<User>[] = [
    createTableColumn<User>({
      columnId: 'name',
      compare: (a, b) => a.name.localeCompare(b.name),
      renderHeaderCell: () => '用户名',
      renderCell: (user) => (
        <div className="user-cell">
          <Avatar name={user.name} image={{ src: user.avatar }} />
          <Text weight="semibold">{user.name}</Text>
        </div>
      ),
    }),
    createTableColumn<User>({
      columnId: 'email',
      renderHeaderCell: () => '邮箱',
      renderCell: (user) => <Text>{user.email}</Text>,
    }),
    createTableColumn<User>({
      columnId: 'credits',
      renderHeaderCell: () => '积分',
      renderCell: (user) => <Badge appearance="filled">{user.credits}</Badge>,
    }),
    createTableColumn<User>({
      columnId: 'actions',
      renderHeaderCell: () => '操作',
      renderCell: (user) => (
        <Button 
          appearance="primary" 
          size="small"
          onClick={() => {
            setSelectedUser(user);
            setPanelOpen(true);
          }}
        >
          查看详情
        </Button>
      ),
    }),
  ];

  return (
    <div className="user-management-container">
      <div className="page-header">
        <Text size={600} weight="semibold">用户管理</Text>
        <div className="header-actions">
          <SearchBox placeholder="搜索用户..." />
          <Dropdown placeholder="筛选条件">
            <option value="all">全部用户</option>
            <option value="active">活跃用户</option>
            <option value="inactive">非活跃用户</option>
          </Dropdown>
        </div>
      </div>

      <Card>
        <DataGrid
          items={users}
          columns={columns}
          sortable
          getRowId={(user) => user.id}
        >
          <DataGridHeader>
            <DataGridRow>
              {({ renderHeaderCell }) => (
                <DataGridHeaderCell>{renderHeaderCell()}</DataGridHeaderCell>
              )}
            </DataGridRow>
          </DataGridHeader>
          <DataGridBody<User>>
            {({ item, rowId }) => (
              <DataGridRow<User> key={rowId}>
                {({ renderCell }) => (
                  <DataGridCell>{renderCell(item)}</DataGridCell>
                )}
              </DataGridRow>
            )}
          </DataGridBody>
        </DataGrid>
      </Card>

      {/* 用户详情面板 */}
      <Panel
        isOpen={panelOpen}
        onDismiss={() => setPanelOpen(false)}
        headerText={selectedUser?.name}
      >
        {selectedUser && <UserDetailPanel user={selectedUser} />}
      </Panel>
    </div>
  );
};
```

### 3. 配置管理模块
```typescript
export const SystemConfigPage: React.FC = () => {
  const [configs, setConfigs] = useState<ConfigItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('ai');
  const [editingConfig, setEditingConfig] = useState<ConfigItem | null>(null);

  const categories = [
    { key: 'ai', text: 'AI服务配置' },
    { key: 'payment', text: '支付配置' },
    { key: 'auth', text: '认证配置' },
    { key: 'system', text: '系统配置' }
  ];

  return (
    <div className="config-management-container">
      <div className="page-header">
        <Text size={600} weight="semibold">系统配置管理</Text>
        <Text size={300} className="text-gray-600">
          管理系统运行时配置，支持热更新
        </Text>
      </div>

      <div className="config-categories">
        <Pivot selectedKey={selectedCategory} onLinkClick={(item) => setSelectedCategory(item?.props.itemKey || 'ai')}>
          {categories.map(category => (
            <PivotItem key={category.key} itemKey={category.key} headerText={category.text}>
              <ConfigCategoryPanel 
                category={category.key} 
                configs={configs.filter(c => c.category === category.key)}
                onEdit={setEditingConfig}
              />
            </PivotItem>
          ))}
        </Pivot>
      </div>

      {/* 配置编辑对话框 */}
      <Dialog open={!!editingConfig} onOpenChange={(_, data) => !data.open && setEditingConfig(null)}>
        <DialogSurface>
          <DialogBody>
            <DialogTitle>编辑配置: {editingConfig?.key}</DialogTitle>
            <DialogContent>
              {editingConfig && (
                <ConfigEditForm 
                  config={editingConfig} 
                  onSave={handleConfigSave}
                  onCancel={() => setEditingConfig(null)}
                />
              )}
            </DialogContent>
          </DialogBody>
        </DialogSurface>
      </Dialog>
    </div>
  );
};
```

---

## 🚀 实施计划

### 第一阶段：基础架构升级（1-2周）
- [ ] 安装和配置Fluent UI 2依赖
- [ ] 修改admin-panel的wrangler.toml配置
- [ ] 创建system_configs表和初始数据
- [ ] 实现ConfigManager服务
- [ ] 设置FluentProvider和主题系统

### 第二阶段：核心页面开发（3-4周）
- [ ] 开发仪表板页面（KPI卡片、图表、快速操作）
- [ ] 开发用户管理页面（列表、搜索、详情面板）
- [ ] 开发内容管理页面（故事列表、审核工具）
- [ ] 开发配置管理页面（分类管理、编辑对话框）
- [ ] 实现响应式布局和移动端适配

### 第三阶段：高级功能和优化（3-4周）
- [ ] 开发订单管理和财务报表功能
- [ ] 实现系统管理和日志监控页面
- [ ] 开发AI服务监控和成本管理页面
- [ ] 实现Durable Objects监控管理
- [ ] 开发内容安全和审核管理界面
- [ ] 实现国际化内容管理功能
- [ ] 开发实体书订单和物流管理
- [ ] 实现系统监控和告警管理
- [ ] 添加实时数据更新和通知系统
- [ ] 集成主系统ConfigLoader
- [ ] 性能优化和无障碍访问性改进

### 第四阶段：测试和部署（1周）
- [ ] 全面功能测试和bug修复
- [ ] 安全性和权限测试
- [ ] 用户验收测试
- [ ] 生产环境部署和监控配置

---

## ⚠️ 风险评估

### 技术风险
- **复杂性风险**: 多系统集成和配置管理的技术复杂度
- **性能风险**: 多级缓存和实时更新可能影响性能
- **兼容性风险**: Fluent UI 2组件与现有系统的兼容性

### 业务风险
- **数据安全**: 敏感配置信息的安全存储和访问控制
- **服务中断**: 配置更新过程中可能影响服务稳定性
- **用户适应**: 新界面需要用户学习和适应

### 缓解措施
- **分阶段实施**: 渐进式迁移，降低整体风险
- **充分测试**: 全面的功能、性能和安全测试
- **回滚机制**: 配置变更的回滚和版本管理
- **监控告警**: 实时监控和异常告警机制
- **用户培训**: 提供详细的使用文档和培训

---

## 🔍 补充管理需求分析

### AI服务监控和成本管理
```typescript
// AI服务使用统计管理
export const AIServiceMonitoringPage: React.FC = () => {
  const [aiStats, setAiStats] = useState<AIServiceStats>();
  const [costAnalysis, setCostAnalysis] = useState<CostAnalysis>();

  return (
    <div className="ai-monitoring-container">
      {/* AI服务使用统计 */}
      <Card>
        <CardHeader header={<Text weight="semibold">AI服务使用统计</Text>} />
        <CardPreview>
          <div className="stats-grid">
            <StatsCard title="今日API调用" value={aiStats?.dailyCalls} />
            <StatsCard title="本月成本" value={`$${aiStats?.monthlyCost}`} />
            <StatsCard title="平均响应时间" value={`${aiStats?.avgResponseTime}ms`} />
            <StatsCard title="成功率" value={`${aiStats?.successRate}%`} />
          </div>
        </CardPreview>
      </Card>

      {/* 成本分析图表 */}
      <Card>
        <CardHeader header={<Text weight="semibold">成本趋势分析</Text>} />
        <CardPreview>
          <LineChart data={costAnalysis?.trends} />
        </CardPreview>
      </Card>

      {/* 服务配置管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">AI服务配置</Text>} />
        <CardPreview>
          <DataGrid
            items={[
              { service: 'Gemini Text', model: 'gemini-2.5-flash', costPerCall: '$0.002' },
              { service: 'Imagen 4.0', model: 'imagen-4.0-generate', costPerCall: '$0.04' },
              { service: 'Gemini TTS', model: 'gemini-tts', costPerCall: '$0.016' }
            ]}
            columns={aiServiceColumns}
          />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### Durable Objects监控管理
```typescript
// DO状态监控和管理
export const DurableObjectsMonitoringPage: React.FC = () => {
  const [doStats, setDoStats] = useState<DOStats>();
  const [activeTasks, setActiveTasks] = useState<ActiveTask[]>([]);

  return (
    <div className="do-monitoring-container">
      {/* DO实例状态 */}
      <Card>
        <CardHeader header={<Text weight="semibold">Durable Objects状态</Text>} />
        <CardPreview>
          <div className="do-instances-grid">
            <DOInstanceCard
              name="AITaskQueue"
              activeInstances={doStats?.aiTaskQueue.active}
              totalRequests={doStats?.aiTaskQueue.requests}
              avgDuration={doStats?.aiTaskQueue.avgDuration}
            />
            <DOInstanceCard
              name="StoryGeneration"
              activeInstances={doStats?.storyGeneration.active}
              totalRequests={doStats?.storyGeneration.requests}
              avgDuration={doStats?.storyGeneration.avgDuration}
            />
          </div>
        </CardPreview>
      </Card>

      {/* 活跃任务监控 */}
      <Card>
        <CardHeader header={<Text weight="semibold">活跃任务监控</Text>} />
        <CardPreview>
          <DataGrid
            items={activeTasks}
            columns={activeTaskColumns}
            sortable
          />
        </CardPreview>
      </Card>

      {/* 任务队列管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">任务队列管理</Text>} />
        <CardPreview>
          <CommandBar items={[
            { key: 'clearFailed', text: '清理失败任务', iconProps: { iconName: 'Delete' } },
            { key: 'retryAll', text: '重试所有', iconProps: { iconName: 'Refresh' } },
            { key: 'pauseQueue', text: '暂停队列', iconProps: { iconName: 'Pause' } }
          ]} />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### 内容安全和审核管理
```typescript
// 内容审核管理界面
export const ContentModerationPage: React.FC = () => {
  const [pendingContent, setPendingContent] = useState<PendingContent[]>([]);
  const [safetyStats, setSafetyStats] = useState<SafetyStats>();

  return (
    <div className="content-moderation-container">
      {/* 内容安全统计 */}
      <Card>
        <CardHeader header={<Text weight="semibold">内容安全统计</Text>} />
        <CardPreview>
          <div className="safety-stats-grid">
            <StatsCard title="今日审核" value={safetyStats?.dailyReviews} />
            <StatsCard title="自动通过率" value={`${safetyStats?.autoPassRate}%`} />
            <StatsCard title="待人工审核" value={safetyStats?.pendingReview} />
            <StatsCard title="违规内容" value={safetyStats?.violationCount} />
          </div>
        </CardPreview>
      </Card>

      {/* 待审核内容列表 */}
      <Card>
        <CardHeader header={<Text weight="semibold">待审核内容</Text>} />
        <CardPreview>
          <DataGrid
            items={pendingContent}
            columns={[
              createTableColumn({
                columnId: 'storyId',
                renderHeaderCell: () => '故事ID',
                renderCell: (item) => <Text>{item.storyId}</Text>
              }),
              createTableColumn({
                columnId: 'contentType',
                renderHeaderCell: () => '内容类型',
                renderCell: (item) => <Badge>{item.contentType}</Badge>
              }),
              createTableColumn({
                columnId: 'riskLevel',
                renderHeaderCell: () => '风险等级',
                renderCell: (item) => (
                  <Badge appearance={item.riskLevel === 'high' ? 'filled' : 'outline'}>
                    {item.riskLevel}
                  </Badge>
                )
              }),
              createTableColumn({
                columnId: 'actions',
                renderHeaderCell: () => '操作',
                renderCell: (item) => (
                  <div className="action-buttons">
                    <Button size="small" appearance="primary">批准</Button>
                    <Button size="small" appearance="secondary">拒绝</Button>
                    <Button size="small">详情</Button>
                  </div>
                )
              })
            ]}
          />
        </CardPreview>
      </Card>

      {/* 安全规则配置 */}
      <Card>
        <CardHeader header={<Text weight="semibold">安全规则配置</Text>} />
        <CardPreview>
          <SafetyRulesConfig />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### 国际化内容管理
```typescript
// 多语言内容管理
export const InternationalizationPage: React.FC = () => {
  const [languages, setLanguages] = useState<Language[]>([]);
  const [translations, setTranslations] = useState<Translation[]>([]);

  return (
    <div className="i18n-management-container">
      {/* 支持语言管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">支持语言</Text>} />
        <CardPreview>
          <DataGrid
            items={languages}
            columns={[
              createTableColumn({
                columnId: 'code',
                renderHeaderCell: () => '语言代码',
                renderCell: (lang) => <Text weight="semibold">{lang.code}</Text>
              }),
              createTableColumn({
                columnId: 'name',
                renderHeaderCell: () => '语言名称',
                renderCell: (lang) => <Text>{lang.name}</Text>
              }),
              createTableColumn({
                columnId: 'status',
                renderHeaderCell: () => '状态',
                renderCell: (lang) => (
                  <Badge appearance={lang.enabled ? 'filled' : 'outline'}>
                    {lang.enabled ? '启用' : '禁用'}
                  </Badge>
                )
              }),
              createTableColumn({
                columnId: 'completeness',
                renderHeaderCell: () => '翻译完成度',
                renderCell: (lang) => (
                  <ProgressBar value={lang.completeness} max={100} />
                )
              })
            ]}
          />
        </CardPreview>
      </Card>

      {/* 翻译管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">翻译管理</Text>} />
        <CardPreview>
          <div className="translation-tools">
            <SearchBox placeholder="搜索翻译键..." />
            <Dropdown placeholder="选择语言">
              {languages.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.name}</option>
              ))}
            </Dropdown>
            <Button appearance="primary">添加翻译</Button>
          </div>
          <DataGrid
            items={translations}
            columns={translationColumns}
            sortable
          />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### 实体书订单和物流管理
```typescript
// 实体书订单管理
export const PhysicalBookOrdersPage: React.FC = () => {
  const [orders, setOrders] = useState<PhysicalBookOrder[]>([]);
  const [logistics, setLogistics] = useState<LogisticsStats>();

  return (
    <div className="physical-orders-container">
      {/* 订单统计 */}
      <Card>
        <CardHeader header={<Text weight="semibold">订单统计</Text>} />
        <CardPreview>
          <div className="order-stats-grid">
            <StatsCard title="待处理订单" value={logistics?.pendingOrders} />
            <StatsCard title="印刷中" value={logistics?.printingOrders} />
            <StatsCard title="已发货" value={logistics?.shippedOrders} />
            <StatsCard title="本月收入" value={`$${logistics?.monthlyRevenue}`} />
          </div>
        </CardPreview>
      </Card>

      {/* 订单列表 */}
      <Card>
        <CardHeader header={<Text weight="semibold">订单管理</Text>} />
        <CardPreview>
          <div className="order-filters">
            <SearchBox placeholder="搜索订单号..." />
            <Dropdown placeholder="订单状态">
              <option value="pending">待处理</option>
              <option value="printing">印刷中</option>
              <option value="shipped">已发货</option>
              <option value="delivered">已送达</option>
            </Dropdown>
            <DatePicker placeholder="选择日期范围" />
          </div>
          <DataGrid
            items={orders}
            columns={[
              createTableColumn({
                columnId: 'orderNumber',
                renderHeaderCell: () => '订单号',
                renderCell: (order) => <Text weight="semibold">{order.orderNumber}</Text>
              }),
              createTableColumn({
                columnId: 'customerName',
                renderHeaderCell: () => '客户',
                renderCell: (order) => <Text>{order.customerName}</Text>
              }),
              createTableColumn({
                columnId: 'storyTitle',
                renderHeaderCell: () => '故事标题',
                renderCell: (order) => <Text>{order.storyTitle}</Text>
              }),
              createTableColumn({
                columnId: 'status',
                renderHeaderCell: () => '状态',
                renderCell: (order) => (
                  <Badge appearance={getStatusAppearance(order.status)}>
                    {getStatusText(order.status)}
                  </Badge>
                )
              }),
              createTableColumn({
                columnId: 'actions',
                renderHeaderCell: () => '操作',
                renderCell: (order) => (
                  <div className="order-actions">
                    <Button size="small">查看详情</Button>
                    <Button size="small">更新状态</Button>
                    <Button size="small">打印标签</Button>
                  </div>
                )
              })
            ]}
          />
        </CardPreview>
      </Card>

      {/* 物流合作伙伴管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">物流合作伙伴</Text>} />
        <CardPreview>
          <LogisticsPartnerManagement />
        </CardPreview>
      </Card>
    </div>
  );
};
```

### 系统监控和告警管理
```typescript
// 系统监控仪表板
export const SystemMonitoringPage: React.FC = () => {
  const [systemHealth, setSystemHealth] = useState<SystemHealth>();
  const [alerts, setAlerts] = useState<Alert[]>([]);

  return (
    <div className="system-monitoring-container">
      {/* 系统健康状态 */}
      <Card>
        <CardHeader header={<Text weight="semibold">系统健康状态</Text>} />
        <CardPreview>
          <div className="health-indicators">
            <HealthIndicator
              service="API Gateway"
              status={systemHealth?.apiGateway}
              responseTime={systemHealth?.apiResponseTime}
            />
            <HealthIndicator
              service="Database"
              status={systemHealth?.database}
              responseTime={systemHealth?.dbResponseTime}
            />
            <HealthIndicator
              service="AI Services"
              status={systemHealth?.aiServices}
              responseTime={systemHealth?.aiResponseTime}
            />
            <HealthIndicator
              service="Storage"
              status={systemHealth?.storage}
              responseTime={systemHealth?.storageResponseTime}
            />
          </div>
        </CardPreview>
      </Card>

      {/* 性能指标 */}
      <Card>
        <CardHeader header={<Text weight="semibold">性能指标</Text>} />
        <CardPreview>
          <div className="performance-charts">
            <LineChart
              title="API响应时间"
              data={systemHealth?.apiResponseTrend}
            />
            <LineChart
              title="错误率"
              data={systemHealth?.errorRateTrend}
            />
            <LineChart
              title="并发用户数"
              data={systemHealth?.concurrentUsersTrend}
            />
          </div>
        </CardPreview>
      </Card>

      {/* 告警管理 */}
      <Card>
        <CardHeader header={<Text weight="semibold">系统告警</Text>} />
        <CardPreview>
          <DataGrid
            items={alerts}
            columns={[
              createTableColumn({
                columnId: 'severity',
                renderHeaderCell: () => '严重程度',
                renderCell: (alert) => (
                  <Badge appearance={getSeverityAppearance(alert.severity)}>
                    {alert.severity}
                  </Badge>
                )
              }),
              createTableColumn({
                columnId: 'message',
                renderHeaderCell: () => '告警信息',
                renderCell: (alert) => <Text>{alert.message}</Text>
              }),
              createTableColumn({
                columnId: 'timestamp',
                renderHeaderCell: () => '时间',
                renderCell: (alert) => <Text>{formatTimestamp(alert.timestamp)}</Text>
              }),
              createTableColumn({
                columnId: 'actions',
                renderHeaderCell: () => '操作',
                renderCell: (alert) => (
                  <div className="alert-actions">
                    <Button size="small" appearance="primary">确认</Button>
                    <Button size="small">忽略</Button>
                  </div>
                )
              })
            ]}
          />
        </CardPreview>
      </Card>
    </div>
  );
};
```

---

**文档状态**: 设计完成（已补充完整管理需求）
**下一步**: 开始第一阶段实施
**负责人**: 开发团队
**审核人**: 技术负责人
