# StoryWeaver 技术实现方案

## 1. 项目结构

```
storyweaver/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   │   ├── StoryCreator/
│   │   │   ├── AudioPlayer/
│   │   │   ├── StoryViewer/
│   │   │   └── PaymentModal/
│   │   ├── pages/          # 页面组件
│   │   │   ├── HomePage.tsx
│   │   │   ├── CreateStory.tsx
│   │   │   ├── MyStories.tsx
│   │   │   └── Pricing.tsx
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── services/       # API服务
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/                 # Node.js后端API
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   │   ├── geminiService.ts
│   │   │   ├── imagenService.ts
│   │   │   └── ttsService.ts
│   │   ├── models/         # 数据模型
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   └── utils/          # 工具函数
│   ├── config/
│   └── package.json
└── shared/                 # 共享类型和工具
    └── types/
```

## 2. 核心API集成

### 2.1 Gemini API 故事生成服务

```typescript
// backend/src/services/geminiService.ts
import { GoogleGenerativeAI } from '@google/generative-ai';

interface StoryRequest {
  characterName: string;
  age: number;
  traits: string[];
  theme: string;
  setting: string;
}

interface StoryResponse {
  title: string;
  pages: Array<{
    pageNumber: number;
    text: string;
    imagePrompt: string;
  }>;
  fullText: string;
}

class GeminiService {
  private genAI: GoogleGenerativeAI;
  
  constructor() {
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);
  }

  async generateStory(request: StoryRequest): Promise<StoryResponse> {
    const model = this.genAI.getGenerativeModel({ model: "gemini-2.5-flash" });
    
    const prompt = this.buildStoryPrompt(request);
    
    try {
      const result = await model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseStoryResponse(text);
    } catch (error) {
      throw new Error(`故事生成失败: ${error.message}`);
    }
  }

  private buildStoryPrompt(request: StoryRequest): string {
    return `
      创作一个适合${request.age}岁儿童的故事，要求：
      
      主角信息：
      - 姓名：${request.characterName}
      - 性格特征：${request.traits.join('、')}
      
      故事设定：
      - 主题：${request.theme}
      - 场景：${request.setting}
      
      要求：
      1. 故事长度：8-10页，每页50-80字
      2. 内容积极正面，富有教育意义
      3. 语言简单易懂，适合儿童理解
      4. 包含冒险、友谊、成长等元素
      5. 结局温馨美好
      
      输出格式（JSON）：
      {
        "title": "故事标题",
        "pages": [
          {
            "pageNumber": 1,
            "text": "第一页文字内容",
            "imagePrompt": "详细的插图描述，包含角色外观、场景、动作等"
          }
        ],
        "fullText": "完整故事文本"
      }
      
      请确保每页的imagePrompt都包含主角的一致性描述。
    `;
  }

  private parseStoryResponse(text: string): StoryResponse {
    try {
      // 清理可能的markdown格式
      const cleanText = text.replace(/```json\n?|\n?```/g, '').trim();
      return JSON.parse(cleanText);
    } catch (error) {
      throw new Error('故事格式解析失败');
    }
  }
}

export default GeminiService;
```

### 2.2 Imagen 3 图像生成服务

```typescript
// backend/src/services/imagenService.ts
interface ImageRequest {
  prompt: string;
  style: 'cartoon' | 'watercolor' | 'sketch' | 'fantasy' | 'realistic' | 'anime';
  aspectRatio?: '1:1' | '4:3' | '16:9';
}

class ImagenService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY!;
  }

  async generateImage(request: ImageRequest): Promise<string> {
    const stylePrompts = {
      cartoon: "卡通风格，色彩鲜艳，线条清晰，适合儿童",
      watercolor: "水彩画风格，柔和色调，艺术感强",
      sketch: "简笔画风格，线条简洁，黑白或淡彩",
      fantasy: "奇幻风格，梦幻色彩，魔法元素",
      realistic: "写实风格，细节丰富，自然色彩",
      anime: "动漫风格，大眼睛，夸张表情"
    };

    const enhancedPrompt = `
      ${request.prompt}
      
      风格要求：${stylePrompts[request.style]}
      画面要求：适合儿童观看，温馨友好，无暴力内容
      质量要求：高清，细节丰富，色彩和谐
    `;

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/imagen-3.0-generate-002:predict?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            instances: [
              {
                prompt: enhancedPrompt,
                image: {
                  bytesBase64Encoded: ""
                }
              }
            ],
            parameters: {
              sampleCount: 1,
              aspectRatio: request.aspectRatio || "4:3",
              safetyFilterLevel: "block_most",
              personGeneration: "allow_adult"
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`图像生成API错误: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.predictions && data.predictions[0]) {
        return data.predictions[0].bytesBase64Encoded;
      } else {
        throw new Error('图像生成返回数据格式错误');
      }
    } catch (error) {
      throw new Error(`图像生成失败: ${error.message}`);
    }
  }

  async generateStoryImages(
    imagePrompts: string[], 
    style: ImageRequest['style']
  ): Promise<string[]> {
    const images: string[] = [];
    
    // 并发生成图像，但限制并发数量
    const batchSize = 3;
    for (let i = 0; i < imagePrompts.length; i += batchSize) {
      const batch = imagePrompts.slice(i, i + batchSize);
      const batchPromises = batch.map(prompt => 
        this.generateImage({ prompt, style })
      );
      
      const batchResults = await Promise.all(batchPromises);
      images.push(...batchResults);
    }
    
    return images;
  }
}

export default ImagenService;
```

### 2.3 TTS 音频生成服务

```typescript
// backend/src/services/ttsService.ts
interface TTSRequest {
  text: string;
  voice: 'gentle_female' | 'lively_male' | 'child_voice';
  speed?: number;
  pitch?: number;
}

class TTSService {
  private apiKey: string;
  
  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY!;
  }

  async generateAudio(request: TTSRequest): Promise<Buffer> {
    const voiceSettings = {
      gentle_female: {
        languageCode: 'zh-CN',
        name: 'zh-CN-XiaoxiaoNeural',
        ssmlGender: 'FEMALE'
      },
      lively_male: {
        languageCode: 'zh-CN', 
        name: 'zh-CN-YunxiNeural',
        ssmlGender: 'MALE'
      },
      child_voice: {
        languageCode: 'zh-CN',
        name: 'zh-CN-XiaoyiNeural', 
        ssmlGender: 'FEMALE'
      }
    };

    const voice = voiceSettings[request.voice];
    
    // 构建SSML格式的文本
    const ssmlText = `
      <speak>
        <prosody rate="${request.speed || 1.0}" pitch="${request.pitch || 0}">
          ${this.addEmotionalMarks(request.text)}
        </prosody>
      </speak>
    `;

    try {
      const response = await fetch(
        `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-tts:synthesize?key=${this.apiKey}`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            input: { ssml: ssmlText },
            voice: voice,
            audioConfig: {
              audioEncoding: 'MP3',
              speakingRate: request.speed || 1.0,
              pitch: request.pitch || 0,
              volumeGainDb: 0.0,
              sampleRateHertz: 24000
            }
          })
        }
      );

      if (!response.ok) {
        throw new Error(`TTS API错误: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.audioContent) {
        return Buffer.from(data.audioContent, 'base64');
      } else {
        throw new Error('TTS返回数据格式错误');
      }
    } catch (error) {
      throw new Error(`音频生成失败: ${error.message}`);
    }
  }

  private addEmotionalMarks(text: string): string {
    // 添加情感标记，让朗读更生动
    return text
      .replace(/[！!]/g, '<emphasis level="strong">$&</emphasis>')
      .replace(/[？?]/g, '<prosody pitch="+10%">$&</prosody>')
      .replace(/[，,]/g, '$&<break time="300ms"/>')
      .replace(/[。.]/g, '$&<break time="500ms"/>');
  }

  async generateStoryAudio(
    storyText: string, 
    voice: TTSRequest['voice']
  ): Promise<Buffer> {
    // 将长文本分段处理，避免超出API限制
    const segments = this.splitTextIntoSegments(storyText, 1000);
    const audioBuffers: Buffer[] = [];

    for (const segment of segments) {
      const audioBuffer = await this.generateAudio({
        text: segment,
        voice: voice
      });
      audioBuffers.push(audioBuffer);
    }

    // 合并音频片段
    return Buffer.concat(audioBuffers);
  }

  private splitTextIntoSegments(text: string, maxLength: number): string[] {
    const segments: string[] = [];
    const sentences = text.split(/[。！？.!?]/);
    
    let currentSegment = '';
    
    for (const sentence of sentences) {
      if (currentSegment.length + sentence.length > maxLength) {
        if (currentSegment) {
          segments.push(currentSegment.trim());
          currentSegment = '';
        }
      }
      currentSegment += sentence + '。';
    }
    
    if (currentSegment) {
      segments.push(currentSegment.trim());
    }
    
    return segments;
  }
}

export default TTSService;
```

## 3. 故事生成控制器

```typescript
// backend/src/controllers/storyController.ts
import { Request, Response } from 'express';
import GeminiService from '../services/geminiService';
import ImagenService from '../services/imagenService';
import TTSService from '../services/ttsService';
import Story from '../models/Story';

interface CreateStoryRequest {
  characterName: string;
  age: number;
  traits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
  userId: string;
}

class StoryController {
  private geminiService: GeminiService;
  private imagenService: ImagenService;
  private ttsService: TTSService;

  constructor() {
    this.geminiService = new GeminiService();
    this.imagenService = new ImagenService();
    this.ttsService = new TTSService();
  }

  async createStory(req: Request, res: Response) {
    try {
      const request: CreateStoryRequest = req.body;
      
      // 1. 生成故事文本
      const storyData = await this.geminiService.generateStory({
        characterName: request.characterName,
        age: request.age,
        traits: request.traits,
        theme: request.theme,
        setting: request.setting
      });

      // 2. 生成插图
      const imagePrompts = storyData.pages.map(page => page.imagePrompt);
      const images = await this.imagenService.generateStoryImages(
        imagePrompts, 
        request.style as any
      );

      // 3. 生成音频
      const audioBuffer = await this.ttsService.generateStoryAudio(
        storyData.fullText,
        request.voice as any
      );

      // 4. 保存到数据库
      const story = new Story({
        userId: request.userId,
        title: storyData.title,
        characterName: request.characterName,
        pages: storyData.pages.map((page, index) => ({
          ...page,
          imageUrl: `data:image/jpeg;base64,${images[index]}`
        })),
        audioUrl: `data:audio/mp3;base64,${audioBuffer.toString('base64')}`,
        createdAt: new Date(),
        style: request.style,
        voice: request.voice
      });

      await story.save();

      res.json({
        success: true,
        storyId: story._id,
        story: {
          id: story._id,
          title: story.title,
          pages: story.pages,
          audioUrl: story.audioUrl
        }
      });

    } catch (error) {
      console.error('故事创建失败:', error);
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  async getStory(req: Request, res: Response) {
    try {
      const { storyId } = req.params;
      const { userId } = req.user;

      const story = await Story.findOne({ 
        _id: storyId, 
        userId: userId 
      });

      if (!story) {
        return res.status(404).json({
          success: false,
          error: '故事不存在'
        });
      }

      res.json({
        success: true,
        story: story
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  async getUserStories(req: Request, res: Response) {
    try {
      const { userId } = req.user;
      const { page = 1, limit = 10 } = req.query;

      const stories = await Story.find({ userId })
        .sort({ createdAt: -1 })
        .limit(Number(limit) * 1)
        .skip((Number(page) - 1) * Number(limit))
        .select('title characterName createdAt style');

      const total = await Story.countDocuments({ userId });

      res.json({
        success: true,
        stories: stories,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: total,
          pages: Math.ceil(total / Number(limit))
        }
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}

export default StoryController;
```

## 4. 前端React组件

### 4.1 故事创作器组件

```typescript
// frontend/src/components/StoryCreator/StoryCreator.tsx
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createStory } from '../../services/storyService';

interface StoryForm {
  characterName: string;
  age: number;
  traits: string[];
  theme: string;
  setting: string;
  style: string;
  voice: string;
}

const StoryCreator: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<StoryForm>({
    characterName: '',
    age: 5,
    traits: [],
    theme: '',
    setting: '',
    style: 'cartoon',
    voice: 'gentle_female'
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const navigate = useNavigate();

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsGenerating(true);
    try {
      const response = await createStory(formData);
      navigate(`/story/${response.storyId}`);
    } catch (error) {
      console.error('故事创建失败:', error);
      alert('故事创建失败，请重试');
    } finally {
      setIsGenerating(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <CharacterStep formData={formData} setFormData={setFormData} />;
      case 2:
        return <ThemeStep formData={formData} setFormData={setFormData} />;
      case 3:
        return <StyleStep formData={formData} setFormData={setFormData} />;
      case 4:
        return <ConfirmStep formData={formData} />;
      default:
        return null;
    }
  };

  if (isGenerating) {
    return <GeneratingProgress />;
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* 进度条 */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          {[1, 2, 3, 4].map((step) => (
            <div
              key={step}
              className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step <= currentStep
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-500'
              }`}
            >
              {step}
            </div>
          ))}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(currentStep / 4) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* 步骤内容 */}
      {renderStep()}

      {/* 导航按钮 */}
      <div className="flex justify-between mt-8">
        <button
          onClick={handlePrevious}
          disabled={currentStep === 1}
          className="px-6 py-2 border border-gray-300 rounded-lg disabled:opacity-50"
        >
          上一步
        </button>
        
        {currentStep === 4 ? (
          <button
            onClick={handleSubmit}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            开始创作故事
          </button>
        ) : (
          <button
            onClick={handleNext}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            下一步
          </button>
        )}
      </div>
    </div>
  );
};

export default StoryCreator;
```

### 4.2 音频播放器组件

```typescript
// frontend/src/components/AudioPlayer/AudioPlayer.tsx
import React, { useState, useRef, useEffect } from 'react';

interface AudioPlayerProps {
  audioUrl: string;
  title: string;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioUrl, title }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);

    audio.addEventListener('timeupdate', updateTime);
    audio.addEventListener('loadedmetadata', updateDuration);
    audio.addEventListener('ended', () => setIsPlaying(false));

    return () => {
      audio.removeEventListener('timeupdate', updateTime);
      audio.removeEventListener('loadedmetadata', updateDuration);
      audio.removeEventListener('ended', () => setIsPlaying(false));
    };
  }, []);

  const togglePlayPause = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
    } else {
      audio.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (parseFloat(e.target.value) / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <audio ref={audioRef} src={audioUrl} preload="metadata" />
      
      <div className="flex items-center space-x-4">
        <button
          onClick={togglePlayPause}
          className="w-12 h-12 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600"
        >
          {isPlaying ? '⏸️' : '▶️'}
        </button>
        
        <div className="flex-1">
          <h3 className="font-semibold text-gray-800 mb-2">{title}</h3>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">
              {formatTime(currentTime)}
            </span>
            
            <input
              type="range"
              min="0"
              max="100"
              value={duration ? (currentTime / duration) * 100 : 0}
              onChange={handleSeek}
              className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            
            <span className="text-sm text-gray-500">
              {formatTime(duration)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
```

## 5. 部署配置

### 5.1 Docker配置

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package.json文件
COPY package*.json ./
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### 5.2 环境变量配置

```bash
# .env
NODE_ENV=production
PORT=3000

# 数据库
MONGODB_URI=mongodb://localhost:27017/storyweaver

# API密钥
GEMINI_API_KEY=your_gemini_api_key_here

# JWT密钥
JWT_SECRET=your_jwt_secret_here

# Stripe支付
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# 文件存储
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=storyweaver-assets

# 邮件服务
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
```

这个技术实现方案提供了完整的后端API集成、前端组件开发和部署配置，可以作为StoryWeaver项目的开发基础。