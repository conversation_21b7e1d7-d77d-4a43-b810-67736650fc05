# StoryWeaver UI/UX 设计规范

## 1. 设计理念

### 1.1 核心原则
- **儿童友好**: 色彩温馨、界面简洁、操作直观
- **家庭共享**: 适合父母和孩子一起使用
- **魔法感**: 营造创作故事的神奇体验
- **安全可靠**: 内容健康、操作安全

### 1.2 目标用户
- **主要用户**: 25-40岁的父母
- **次要用户**: 3-8岁的儿童
- **使用场景**: 睡前故事、亲子时光、教育娱乐

## 2. 视觉设计系统

### 2.1 色彩方案

#### 主色调
```css
/* 温馨蓝 - 主品牌色 */
--primary-blue: #4A90E2;
--primary-blue-light: #7BB3F0;
--primary-blue-dark: #357ABD;

/* 温暖橙 - 强调色 */
--accent-orange: #FF8C42;
--accent-orange-light: #FFB366;
--accent-orange-dark: #E6792B;

/* 柔和粉 - 辅助色 */
--soft-pink: #FFB6C1;
--soft-pink-light: #FFC9D0;
--soft-pink-dark: #E69FA8;

/* 自然绿 - 成功色 */
--nature-green: #7ED321;
--nature-green-light: #95E042;
--nature-green-dark: #6BBE1A;
```

#### 中性色
```css
/* 文字颜色 */
--text-primary: #2C3E50;
--text-secondary: #7F8C8D;
--text-light: #BDC3C7;

/* 背景颜色 */
--bg-primary: #FFFFFF;
--bg-secondary: #F8F9FA;
--bg-accent: #FFF8F0;

/* 边框颜色 */
--border-light: #E9ECEF;
--border-medium: #DEE2E6;
--border-dark: #CED4DA;
```

#### 状态颜色
```css
/* 成功 */
--success: #28A745;
--success-light: #D4EDDA;

/* 警告 */
--warning: #FFC107;
--warning-light: #FFF3CD;

/* 错误 */
--error: #DC3545;
--error-light: #F8D7DA;

/* 信息 */
--info: #17A2B8;
--info-light: #D1ECF1;
```

### 2.2 字体系统

#### 字体族
```css
/* 主要字体 - 中文 */
--font-primary: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;

/* 英文字体 */
--font-english: 'Inter', 'Helvetica Neue', Arial, sans-serif;

/* 装饰字体 */
--font-decorative: 'Comic Sans MS', cursive;
```

#### 字体大小
```css
/* 标题 */
--text-4xl: 2.25rem;  /* 36px - 主标题 */
--text-3xl: 1.875rem; /* 30px - 二级标题 */
--text-2xl: 1.5rem;   /* 24px - 三级标题 */
--text-xl: 1.25rem;   /* 20px - 四级标题 */

/* 正文 */
--text-lg: 1.125rem;  /* 18px - 大正文 */
--text-base: 1rem;    /* 16px - 标准正文 */
--text-sm: 0.875rem;  /* 14px - 小正文 */
--text-xs: 0.75rem;   /* 12px - 辅助文字 */
```

#### 行高
```css
--leading-tight: 1.25;
--leading-normal: 1.5;
--leading-relaxed: 1.75;
```

### 2.3 间距系统

```css
/* 间距单位 (基于8px网格) */
--space-1: 0.25rem;   /* 4px */
--space-2: 0.5rem;    /* 8px */
--space-3: 0.75rem;   /* 12px */
--space-4: 1rem;      /* 16px */
--space-5: 1.25rem;   /* 20px */
--space-6: 1.5rem;    /* 24px */
--space-8: 2rem;      /* 32px */
--space-10: 2.5rem;   /* 40px */
--space-12: 3rem;     /* 48px */
--space-16: 4rem;     /* 64px */
--space-20: 5rem;     /* 80px */
```

### 2.4 圆角系统

```css
--radius-sm: 0.25rem;   /* 4px - 小圆角 */
--radius-md: 0.5rem;    /* 8px - 中等圆角 */
--radius-lg: 0.75rem;   /* 12px - 大圆角 */
--radius-xl: 1rem;      /* 16px - 超大圆角 */
--radius-full: 9999px;  /* 完全圆形 */
```

### 2.5 阴影系统

```css
/* 卡片阴影 */
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

/* 特殊阴影 */
--shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
--shadow-outline: 0 0 0 3px rgba(74, 144, 226, 0.5);
```

## 3. 组件设计规范

### 3.1 按钮设计

#### 主要按钮
```css
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
  color: white;
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
  box-shadow: var(--shadow-md);
  transition: all 0.2s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}
```

#### 次要按钮
```css
.btn-secondary {
  background: white;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: 600;
}
```

#### 可爱按钮 (儿童友好)
```css
.btn-cute {
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
  color: white;
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-full);
  font-family: var(--font-decorative);
  font-size: var(--text-lg);
  box-shadow: 0 4px 15px rgba(255, 140, 66, 0.4);
}
```

### 3.2 卡片设计

#### 故事卡片
```css
.story-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all 0.3s ease;
  overflow: hidden;
}

.story-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.story-card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
}
```

#### 功能卡片
```css
.feature-card {
  background: linear-gradient(135deg, var(--bg-accent), white);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-align: center;
  border: 2px solid var(--border-light);
}
```

### 3.3 表单设计

#### 输入框
```css
.input-field {
  width: 100%;
  padding: var(--space-4);
  border: 2px solid var(--border-medium);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.input-field:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: var(--shadow-outline);
}
```

#### 选择器
```css
.trait-selector {
  display: inline-block;
  padding: var(--space-2) var(--space-4);
  margin: var(--space-1);
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all 0.2s ease;
}

.trait-selector.selected {
  background: var(--primary-blue);
  color: white;
  border-color: var(--primary-blue);
}
```

### 3.4 进度指示器

#### 步骤进度条
```css
.progress-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s ease;
}

.progress-step.active {
  background: var(--primary-blue);
  color: white;
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.5);
}

.progress-step.completed {
  background: var(--nature-green);
  color: white;
}
```

#### 生成进度动画
```css
.generating-animation {
  width: 100px;
  height: 100px;
  margin: 0 auto;
  position: relative;
}

.magic-wand {
  animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
  0%, 100% { transform: rotate(-10deg); }
  50% { transform: rotate(10deg); }
}
```

## 4. 页面布局规范

### 4.1 响应式断点

```css
/* 移动设备 */
@media (max-width: 640px) {
  .container { padding: var(--space-4); }
}

/* 平板设备 */
@media (min-width: 641px) and (max-width: 1024px) {
  .container { padding: var(--space-6); }
}

/* 桌面设备 */
@media (min-width: 1025px) {
  .container { 
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8);
  }
}
```

### 4.2 网格系统

```css
.grid-container {
  display: grid;
  gap: var(--space-6);
}

/* 移动端: 单列 */
@media (max-width: 640px) {
  .grid-container {
    grid-template-columns: 1fr;
  }
}

/* 平板端: 双列 */
@media (min-width: 641px) and (max-width: 1024px) {
  .grid-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 桌面端: 三列 */
@media (min-width: 1025px) {
  .grid-container {
    grid-template-columns: repeat(3, 1fr);
  }
}
```

## 5. 交互设计规范

### 5.1 动画效果

#### 页面转场
```css
.page-transition {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

#### 悬停效果
```css
.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}
```

#### 加载动画
```css
.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80%, 100% { content: '...'; }
}
```

### 5.2 反馈机制

#### 成功提示
```css
.success-toast {
  background: var(--success);
  color: white;
  padding: var(--space-4) var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  animation: slideInRight 0.3s ease;
}
```

#### 错误提示
```css
.error-message {
  background: var(--error-light);
  color: var(--error);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--error);
}
```

## 6. 图标系统

### 6.1 图标风格
- 使用圆润的线条风格
- 统一的线条粗细 (2px)
- 温馨友好的设计
- 支持多种尺寸 (16px, 24px, 32px, 48px)

### 6.2 常用图标

```html
<!-- 播放按钮 -->
<svg class="icon-play" viewBox="0 0 24 24">
  <path d="M8 5v14l11-7z" fill="currentColor"/>
</svg>

<!-- 魔法棒 -->
<svg class="icon-magic" viewBox="0 0 24 24">
  <path d="M7.5 5.6L10 7L8.6 4.5L10 2L7.5 3.4L5 2L6.4 4.5L5 7L7.5 5.6Z" fill="currentColor"/>
</svg>

<!-- 书本 -->
<svg class="icon-book" viewBox="0 0 24 24">
  <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3Z" fill="currentColor"/>
</svg>
```

## 7. 可访问性规范

### 7.1 颜色对比度
- 正文文字与背景对比度 ≥ 4.5:1
- 大文字与背景对比度 ≥ 3:1
- 交互元素对比度 ≥ 3:1

### 7.2 键盘导航
```css
.focusable:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}
```

### 7.3 屏幕阅读器支持
```html
<!-- 为图片添加alt属性 -->
<img src="story-cover.jpg" alt="莉莉在糖果王国的冒险故事封面">

<!-- 为按钮添加aria-label -->
<button aria-label="播放故事音频">▶️</button>

<!-- 为表单添加标签 -->
<label for="character-name">主角姓名</label>
<input id="character-name" type="text">
```

## 8. 性能优化

### 8.1 图片优化
- 使用WebP格式
- 实现懒加载
- 提供多种尺寸
- 压缩质量控制在80-90%

### 8.2 CSS优化
```css
/* 使用CSS变量减少重复 */
:root {
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.6s ease;
}

/* 优化动画性能 */
.animated-element {
  will-change: transform;
  transform: translateZ(0);
}
```

### 8.3 字体优化
```css
/* 字体预加载 */
@font-face {
  font-family: 'PingFang SC';
  font-display: swap;
  src: local('PingFang SC');
}
```

这个UI/UX设计规范确保了StoryWeaver网站具有一致的视觉风格、良好的用户体验和儿童友好的界面设计。