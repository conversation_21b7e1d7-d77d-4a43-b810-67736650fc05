# StoryWeaver Frontend

AI驱动的个性化儿童有声绘本创作平台前端应用。

## 🚀 快速开始

### 环境要求

- Node.js 18+ 
- npm 或 yarn
- 现代浏览器 (Chrome, Firefox, Safari, Edge)

### 安装和运行

1. **安装依赖**
```bash
npm install
```

2. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

3. **启动开发服务器**
```bash
npm run dev
# 或使用启动脚本
./start-dev.sh
```

4. **访问应用**
打开浏览器访问 `http://localhost:5173`

## 🛠 技术栈

- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式**: Tailwind CSS
- **状态管理**: Zustand
- **路由**: React Router v6
- **动画**: Framer Motion
- **HTTP客户端**: Axios
- **认证**: Google OAuth
- **支付**: Stripe

## 🎨 主要功能

### 用户认证
- Google OAuth 登录
- JWT token 管理
- 路由保护

### 故事创作
- 4步向导式创作流程
- 角色设定和个性化
- 主题和风格选择
- AI故事生成

### 故事管理
- 故事列表和搜索
- 在线阅读器
- 音频播放
- 下载和分享

### 支付系统
- 积分购买
- 订阅管理
- Stripe集成

## 🔧 开发命令

```bash
# 开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产构建
npm run preview

# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint
```

## 🌍 环境变量

创建 `.env` 文件并配置以下变量：

```env
# API配置
VITE_API_BASE_URL=http://localhost:8787/api

# Google OAuth
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# Stripe支付
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_key

# 应用配置
VITE_APP_NAME=StoryWeaver
VITE_ENVIRONMENT=development
```

## 📁 项目结构

```
src/
├── components/          # 组件库
│   ├── ui/             # 基础UI组件
│   ├── layout/         # 布局组件
│   ├── features/       # 功能组件
│   ├── common/         # 通用组件
│   └── auth/           # 认证组件
├── pages/              # 页面组件
├── services/           # API服务
├── stores/             # 状态管理 (Zustand)
├── types/              # TypeScript类型定义
├── utils/              # 工具函数
├── router/             # 路由配置
└── hooks/              # 自定义Hooks
```

## 🎯 部署

### Cloudflare Pages

1. 连接GitHub仓库到Cloudflare Pages
2. 设置构建命令: `npm run build`
3. 设置输出目录: `dist`
4. 配置环境变量
5. 部署

---

Made with ❤️ by StoryWeaver Team