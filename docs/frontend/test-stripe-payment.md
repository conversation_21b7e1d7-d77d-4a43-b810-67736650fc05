# Stripe 支付测试指南

## 🧪 测试环境配置

### 1. 环境变量检查

确保 `.env` 文件中包含以下配置：

```bash
# 启用调试模式
VITE_DEBUG_MODE=true
VITE_DEBUG_USE_REAL_STRIPE=true
VITE_DEBUG_MOCK_PAYMENTS=false

# Stripe 测试密钥（需要替换为真实的测试密钥）
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51234567890abcdef_test_1234567890abcdef1234567890
```

### 2. 获取真实的 Stripe 测试密钥

1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 注册或登录账户
3. 在左侧菜单中选择 "Developers" > "API keys"
4. 复制 "Publishable key" (以 `pk_test_` 开头)
5. 将密钥替换到 `.env` 文件中的 `VITE_STRIPE_PUBLISHABLE_KEY`

## 🔧 测试步骤

### 1. 启动开发服务器

```bash
cd frontend
npm run dev
```

### 2. 访问支付页面

1. 打开浏览器访问 `http://localhost:5173`
2. 点击 "购买积分" 或相关支付按钮
3. 选择积分包
4. 进入支付确认页面

### 3. 使用测试卡号

在支付表单中输入以下测试信息：

**成功支付测试卡：**
- 卡号: `4242 4242 4242 4242`
- 有效期: 任意未来日期 (如 `12/25`)
- CVC: 任意3位数字 (如 `123`)
- 邮编: 任意5位数字 (如 `12345`)

**其他测试卡号：**
- Visa: `4242 4242 4242 4242`
- Visa (debit): `4000 0566 5566 5556`
- Mastercard: `5555 5555 5555 4444`
- American Express: `3782 822463 10005`

**失败测试卡：**
- 卡被拒绝: `4000 0000 0000 0002`
- 余额不足: `4000 0000 0000 9995`
- 过期卡: `4000 0000 0000 0069`

### 4. 验证支付流程

1. **支付表单显示**: 确认显示真实的 Stripe Elements 卡片输入框
2. **输入验证**: 输入测试卡号，观察实时验证反馈
3. **支付处理**: 点击"确认支付"，观察支付处理过程
4. **结果处理**: 验证支付成功/失败的处理逻辑

## 🐛 调试信息

### 浏览器控制台日志

支付过程中会输出以下调试信息：

```javascript
// 支付开始
"Processing real Stripe payment..." { paymentIntent: {...} }

// Stripe 响应
"Stripe payment result:" { paymentIntent: {...}, error: null }

// 后端确认
"Payment confirmed with backend:" { success: true, creditsAdded: 100 }
```

### 网络请求监控

在浏览器开发者工具的 Network 标签中监控：

1. **创建 PaymentIntent**: `POST /api/payments/create-payment-intent`
2. **Stripe 确认**: 直接与 Stripe API 通信
3. **后端确认**: `POST /api/payments/confirm`

## ⚠️ 注意事项

### 安全提醒

1. **仅在开发环境使用**: 真实支付测试仅在 localhost 环境启用
2. **测试密钥**: 确保使用 `pk_test_` 开头的测试密钥，不要使用生产密钥
3. **数据隔离**: 测试环境的支付数据与生产环境完全隔离

### 故障排除

**问题**: Stripe Elements 未显示
- **解决**: 检查 `VITE_STRIPE_PUBLISHABLE_KEY` 是否正确配置
- **解决**: 确认 `VITE_DEBUG_USE_REAL_STRIPE=true`

**问题**: 支付失败
- **解决**: 检查网络连接和 Stripe 服务状态
- **解决**: 验证测试卡号格式是否正确

**问题**: 后端确认失败
- **解决**: 确认后端 API 正常运行
- **解决**: 检查后端 Stripe 密钥配置

## 📊 测试检查清单

- [ ] 环境变量正确配置
- [ ] Stripe 测试密钥有效
- [ ] 支付表单正确显示
- [ ] 测试卡号验证通过
- [ ] 支付成功流程完整
- [ ] 支付失败处理正确
- [ ] 积分正确添加到账户
- [ ] 错误信息正确显示

## 🔄 切换回模拟模式

如需切换回模拟支付模式，修改 `.env` 文件：

```bash
VITE_DEBUG_MOCK_PAYMENTS=true
VITE_DEBUG_USE_REAL_STRIPE=false
```

重启开发服务器后生效。
