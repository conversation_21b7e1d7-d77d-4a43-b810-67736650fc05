# Stripe 支付集成指南

## 🎯 概述

StoryWeaver 项目已集成真实的 Stripe 支付系统，支持在开发环境中使用 Stripe 测试环境进行真实的支付流程测试。

## 🔧 配置步骤

### 1. 获取 Stripe 测试密钥

1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 注册或登录账户
3. 确保处于 "测试模式" (Test mode)
4. 导航到 "Developers" > "API keys"
5. 复制 "Publishable key" (以 `pk_test_` 开头)

### 2. 配置环境变量

编辑 `frontend/.env` 文件：

```bash
# 启用真实 Stripe 测试
VITE_DEBUG_MODE=true
VITE_DEBUG_USE_REAL_STRIPE=true
VITE_DEBUG_MOCK_PAYMENTS=false

# 配置 Stripe 测试密钥
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_actual_stripe_test_key_here
```

### 3. 启动测试环境

```bash
cd frontend
./test-stripe.sh
```

或手动启动：

```bash
npm run dev
```

## 🧪 测试流程

### 1. 访问测试页面

打开浏览器访问：`http://localhost:5173/test/stripe`

### 2. 检查配置状态

确认以下状态为 "已启用"：
- 调试模式
- 真实 Stripe
- Stripe 加载

### 3. 使用测试卡号

**成功支付测试：**
- 卡号: `4242 4242 4242 4242`
- 有效期: `12/25` (任意未来日期)
- CVC: `123` (任意3位数字)

**失败测试卡号：**
- 卡被拒绝: `4000 0000 0000 0002`
- 余额不足: `4000 0000 0000 9995`
- 过期卡: `4000 0000 0000 0069`

### 4. 完整支付流程测试

1. **访问主页**: `http://localhost:5173`
2. **点击购买积分**: 触发 PaymentModal
3. **选择积分包**: 选择任意积分包
4. **进入支付页面**: 查看真实的 Stripe Elements
5. **输入测试卡号**: 使用上述测试卡号
6. **确认支付**: 观察真实的 Stripe 支付流程
7. **验证结果**: 检查支付成功/失败处理

## 🔍 调试信息

### 浏览器控制台

支付过程中会输出详细的调试信息：

```javascript
// Stripe 初始化
"Stripe loaded successfully"

// 支付开始
"Processing real Stripe payment..." { paymentIntent: {...} }

// Stripe 响应
"Stripe confirmCardPayment result:" { paymentIntent: {...}, error: null }

// 后端确认
"Payment confirmed with backend:" { success: true }
```

### 网络请求

在开发者工具的 Network 标签中可以看到：

1. **Stripe.js 加载**: `https://js.stripe.com/v3/`
2. **创建 PaymentIntent**: `POST /api/payments/create-payment-intent`
3. **Stripe API 调用**: 直接与 Stripe 服务器通信
4. **后端确认**: `POST /api/payments/confirm`

## 🛡️ 安全特性

### 三重安全检查

真实 Stripe 支付仅在满足以下条件时启用：

1. **开发环境**: `import.meta.env.DEV === true`
2. **环境变量**: `VITE_DEBUG_USE_REAL_STRIPE=true`
3. **本地域名**: `localhost` 或 `127.0.0.1`

### 测试环境隔离

- 使用 `pk_test_` 开头的测试密钥
- 所有支付数据与生产环境完全隔离
- 测试支付不会产生真实费用

## 🔄 模式切换

### 切换到模拟支付

```bash
# 在 .env 文件中设置
VITE_DEBUG_MOCK_PAYMENTS=true
VITE_DEBUG_USE_REAL_STRIPE=false
```

### 切换到演示模式

```bash
# 在 .env 文件中设置
VITE_DEBUG_MODE=false
```

## 🐛 故障排除

### 常见问题

**问题**: Stripe Elements 不显示
- **检查**: `VITE_STRIPE_PUBLISHABLE_KEY` 是否正确配置
- **检查**: `VITE_DEBUG_USE_REAL_STRIPE=true` 是否设置
- **解决**: 重启开发服务器

**问题**: 支付失败
- **检查**: 网络连接是否正常
- **检查**: 测试卡号格式是否正确
- **检查**: Stripe 服务状态

**问题**: 后端确认失败
- **检查**: 后端 API 是否正常运行
- **检查**: 后端 Stripe 密钥是否配置

### 日志分析

启用详细日志：

```bash
# 在浏览器控制台中
localStorage.setItem('debug', 'stripe:*');
```

## 📊 测试检查清单

- [ ] Stripe 测试密钥已配置
- [ ] 环境变量正确设置
- [ ] 开发服务器正常启动
- [ ] 测试页面可以访问
- [ ] Stripe Elements 正确显示
- [ ] 测试卡号验证通过
- [ ] 支付成功流程完整
- [ ] 支付失败处理正确
- [ ] 积分正确添加到账户
- [ ] 错误信息正确显示

## 🚀 生产部署

生产环境部署时需要：

1. 使用生产 Stripe 密钥 (`pk_live_`)
2. 禁用调试模式 (`VITE_DEBUG_MODE=false`)
3. 配置 Webhook 端点处理支付事件
4. 实施额外的安全验证

## 📞 支持

如遇到问题，请检查：

1. [Stripe 文档](https://stripe.com/docs)
2. [Stripe 测试卡号](https://stripe.com/docs/testing#cards)
3. [Stripe Dashboard](https://dashboard.stripe.com/) 中的日志
