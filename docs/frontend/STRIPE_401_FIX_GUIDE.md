# Stripe Checkout 401 错误修复指南

## 🚨 问题描述

当尝试创建Stripe Checkout会话时，收到以下错误：
```
POST https://storyweaver-api.stawky.workers.dev/api/payments/create-checkout-session 401 (Unauthorized)
```

## 🔍 根本原因分析

通过对比Stripe官方文档与StoryWeaver项目实现，发现关键差异：

### Stripe官方实现（简化版）
- 使用简单的HTML表单POST请求
- 无需用户认证
- 直接重定向到Stripe Checkout页面

### StoryWeaver实现（企业级）
- 使用JSON API请求
- **需要用户认证（JWT Bearer Token）**
- 返回JSON响应后前端处理重定向

**核心问题**：StoryWeaver在Stripe Checkout端点上添加了用户认证要求，但前端可能没有正确处理认证状态。

## 🛠️ 修复方案

### 1. 立即修复步骤

#### 步骤1：检查认证状态
```bash
# 访问测试页面
http://localhost:5173/test/stripe-checkout

# 或在浏览器控制台运行
window.authDebug.logDiagnostics()
```

#### 步骤2：验证用户登录
确保用户已正确登录：
- 检查是否显示用户信息
- 验证访问令牌是否存在
- 确认令牌未过期

#### 步骤3：尝试自动修复
```javascript
// 在浏览器控制台运行
await window.authDebug.attemptFix()
```

### 2. 代码修复

#### 修复1：PaymentModal认证检查
已更新 `frontend/src/components/features/PaymentModal.tsx`：
- 添加了详细的认证状态检查
- 集成了自动修复机制
- 改进了错误处理和用户提示

#### 修复2：认证调试工具
新增 `frontend/src/utils/authDebug.ts`：
- 提供详细的认证诊断信息
- 自动修复常见认证问题
- 在开发环境暴露调试函数

#### 修复3：测试页面
新增 `frontend/src/pages/StripeCheckoutTestPage.tsx`：
- 可视化认证状态
- 一键测试Stripe Checkout
- 详细的调试信息

### 3. 环境配置检查

#### 检查环境变量
```bash
# 确保以下环境变量正确配置
VITE_API_BASE_URL=https://storyweaver-api.stawky.workers.dev/api
VITE_DEBUG_MODE=true
VITE_DEBUG_USE_REAL_STRIPE=true
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_key_here
```

#### 检查后端配置
确保后端认证中间件正确配置：
- JWT密钥设置正确
- 认证中间件应用到支付端点
- CORS配置允许前端域名

## 🧪 测试验证

### 1. 使用测试页面
访问 `/test/stripe-checkout` 页面：
1. 检查认证状态是否全部为绿色✅
2. 点击"测试 Stripe Checkout"按钮
3. 观察是否成功重定向到Stripe页面

### 2. 控制台调试
```javascript
// 检查认证状态
window.authDebug.getDiagnostics()

// 查看详细诊断
window.authDebug.logDiagnostics()

// 检查支付认证
window.authDebug.checkPaymentAuth()

// 尝试修复
await window.authDebug.attemptFix()
```

### 3. 网络请求检查
在开发者工具Network标签中检查：
1. 请求是否包含`Authorization: Bearer <token>`头
2. 响应状态码是否为200而不是401
3. 响应是否包含有效的Stripe Checkout URL

## 🔧 常见问题解决

### 问题1：用户未登录
**症状**：`isAuthenticated: false`
**解决**：
```javascript
// 在调试模式下设置调试用户
useAuthStore.getState().setDebugUser()
```

### 问题2：令牌过期
**症状**：`isTokenExpired: true`
**解决**：
```javascript
// 尝试刷新令牌
await useAuthStore.getState().refreshToken()
```

### 问题3：认证数据损坏
**症状**：localStorage有数据但认证失败
**解决**：
```javascript
// 清除认证数据并重新登录
useAuthStore.getState().logout()
```

### 问题4：后端认证中间件问题
**症状**：前端认证正常但后端返回401
**检查**：
- 后端JWT密钥配置
- 认证中间件是否正确应用
- 令牌格式是否正确

## 📋 验证清单

- [ ] 用户已登录（`isAuthenticated: true`）
- [ ] 用户信息存在（`hasUser: true`）
- [ ] 访问令牌存在（`hasTokens: true`）
- [ ] 令牌未过期（`isTokenExpired: false`）
- [ ] 请求包含Authorization头
- [ ] 后端认证中间件正常工作
- [ ] 环境变量配置正确
- [ ] 测试页面显示全部绿色状态

## 🎯 预期结果

修复完成后，应该能够：
1. 成功创建Stripe Checkout会话
2. 重定向到Stripe支付页面
3. 使用测试卡号完成支付流程
4. 正确处理支付成功/失败回调

## 📞 技术支持

如果问题仍然存在，请提供以下信息：
1. 认证诊断输出（`window.authDebug.logDiagnostics()`）
2. 网络请求详情（包括请求头和响应）
3. 浏览器控制台错误信息
4. 环境变量配置（隐藏敏感信息）
