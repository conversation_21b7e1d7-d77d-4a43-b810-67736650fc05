# StoryWeaver 后端部署指南

## 📋 部署前准备

### 1. 环境要求
- Node.js 18+
- npm 或 yarn
- Cloudflare 账户
- Google Cloud 账户（用于Gemini API）
- Stripe 账户

### 2. 获取必要的API密钥

#### Gemini API密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 保存密钥备用

#### Google OAuth 配置
1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建 OAuth 2.0 客户端ID
5. 配置授权重定向URI：`https://storyweaver.com/auth/callback`

#### Stripe 配置
1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 获取可发布密钥和密钥
3. 配置Webhook端点：`https://api.storyweaver.com/api/payments/webhook`
4. 选择需要的事件类型

## 🚀 部署步骤

### 1. 安装依赖
```bash
cd backend
npm install
```

### 2. 配置Cloudflare资源

#### 创建KV命名空间
```bash
# 开发环境
wrangler kv:namespace create "CACHE" --preview

# 生产环境
wrangler kv:namespace create "CACHE"
```

#### 创建R2存储桶
```bash
# 开发环境
wrangler r2 bucket create storyweaver-assets-dev

# 生产环境
wrangler r2 bucket create storyweaver-assets
```

#### 创建D1数据库
```bash
# 开发环境
wrangler d1 create storyweaver-dev

# 生产环境
wrangler d1 create storyweaver
```

### 3. 更新配置文件

编辑 `wrangler.toml`，填入实际的资源ID：

```toml
# 将your-xxx-id替换为实际的ID
[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-actual-kv-namespace-id"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[env.production.d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "your-actual-d1-database-id"
```

### 4. 设置环境变量

```bash
# 设置API密钥
wrangler secret put GEMINI_API_KEY
wrangler secret put GOOGLE_CLIENT_ID
wrangler secret put GOOGLE_CLIENT_SECRET
wrangler secret put STRIPE_SECRET_KEY
wrangler secret put STRIPE_WEBHOOK_SECRET
wrangler secret put JWT_SECRET
```

### 5. 初始化数据库

```bash
# 开发环境
npm run migrate:local

# 生产环境
npm run migrate
```

### 6. 本地开发测试

```bash
npm run dev
```

访问 `http://localhost:8787` 测试API

### 7. 部署到生产环境

```bash
# 部署到生产环境
npm run deploy
```

## 🔧 配置域名

### 1. 添加自定义域名

在Cloudflare Dashboard中：
1. 进入Workers & Pages
2. 选择你的Worker
3. 点击"Triggers"标签
4. 添加自定义域名：`api.storyweaver.com`

### 2. 配置DNS记录

在你的域名DNS设置中添加：
```
Type: CNAME
Name: api
Content: your-worker-name.your-subdomain.workers.dev
```

## 📊 监控和日志

### 1. 查看实时日志
```bash
wrangler tail
```

### 2. 查看分析数据
在Cloudflare Dashboard的Analytics部分查看：
- 请求数量
- 错误率
- 响应时间
- 地理分布

### 3. 设置告警
配置Cloudflare的告警规则：
- 错误率超过5%
- 响应时间超过5秒
- 请求量异常

## 🛠️ 维护操作

### 1. 数据库操作

#### 查看数据库内容
```bash
wrangler d1 console storyweaver
```

#### 备份数据库
```bash
npm run db:backup
```

#### 执行SQL查询
```bash
wrangler d1 execute storyweaver --command="SELECT COUNT(*) FROM users"
```

### 2. 存储管理

#### 查看R2存储使用情况
```bash
wrangler r2 object list storyweaver-assets
```

#### 清理过期文件
可以设置定期任务清理过期的临时文件

### 3. 缓存管理

#### 清除KV缓存
```bash
wrangler kv:key delete --binding=CACHE "cache-key"
```

#### 批量操作
```bash
wrangler kv:bulk delete --binding=CACHE keys.json
```

## 🔒 安全配置

### 1. API限流
已在代码中实现：
- 全局限流：每分钟60次请求
- 用户限流：每分钟30次请求
- 故事生成限流：每小时5次

### 2. CORS配置
在 `src/index.ts` 中配置允许的域名：
```typescript
origin: ['https://storyweaver.com', 'https://www.storyweaver.com']
```

### 3. 内容安全
- 使用Gemini Safety API进行内容过滤
- 敏感词检测
- 用户输入验证和清理

## 📈 性能优化

### 1. 缓存策略
- 用户会话缓存：1小时
- 故事状态缓存：30分钟
- API限流缓存：1分钟

### 2. 文件存储优化
- 图片压缩和格式优化
- CDN加速
- 懒加载策略

### 3. 数据库优化
- 适当的索引
- 查询优化
- 定期清理过期数据

## 🐛 故障排除

### 1. 常见错误

#### API密钥错误
```
Error: Invalid API key
```
解决：检查环境变量设置是否正确

#### 数据库连接错误
```
Error: D1_ERROR
```
解决：检查数据库ID配置和权限

#### 存储访问错误
```
Error: R2_ERROR
```
解决：检查存储桶名称和权限配置

### 2. 调试技巧

#### 查看详细日志
```bash
wrangler tail --format=pretty
```

#### 本地调试
```bash
npm run dev
```

#### 检查环境变量
```bash
wrangler secret list
```

## 📞 支持和联系

如果遇到部署问题：
1. 查看Cloudflare文档
2. 检查GitHub Issues
3. 联系开发团队

## 🔄 更新和升级

### 1. 代码更新
```bash
git pull origin main
npm install
npm run deploy
```

### 2. 数据库迁移
```bash
# 创建新的迁移文件
# 执行迁移
npm run migrate
```

### 3. 依赖更新
```bash
npm update
npm audit fix
```

---

*部署完成后，请测试所有API端点确保正常工作。*