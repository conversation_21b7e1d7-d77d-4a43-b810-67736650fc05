# StoryWeaver 后端测试指南

本文档介绍如何使用各种测试脚本来验证 StoryWeaver 故事创作链路的功能。

## 📋 测试概览

我们提供了三种类型的测试来全面验证系统功能：

1. **本地管道测试** - 测试核心业务逻辑，不依赖外部服务
2. **模拟API测试** - 测试API接口和数据流，使用模拟响应
3. **真实API测试** - 测试实际部署的API服务

## 🚀 快速开始

### 使用测试运行器（推荐）

```bash
# 给脚本执行权限
chmod +x run-tests.sh

# 运行所有测试
./run-tests.sh all

# 运行特定类型的测试
./run-tests.sh local    # 本地逻辑测试
./run-tests.sh mock     # 模拟API测试
./run-tests.sh real     # 真实API测试

# 测试真实API（指定URL）
./run-tests.sh real --url http://localhost:8787
./run-tests.sh real --url https://api.storyweaver.com

# 显示帮助信息
./run-tests.sh --help
```

### 直接运行测试脚本

```bash
# 本地管道测试
node test-local-pipeline.js

# 模拟API测试
node test-story-pipeline.js

# 真实API测试
node test-real-api.js --url http://localhost:8787
```

## 📝 测试详情

### 1. 本地管道测试 (`test-local-pipeline.js`)

**目的**: 验证核心业务逻辑和数据处理流程

**测试内容**:
- ✅ 存储服务功能（KV、R2、D1模拟）
- ✅ Gemini AI服务集成
- ✅ 完整故事生成管道
- ✅ 数据验证和错误处理

**特点**:
- 不需要启动服务器
- 使用模拟的Cloudflare服务
- 快速执行（通常1-2分钟）
- 适合开发阶段的快速验证

**运行示例**:
```bash
node test-local-pipeline.js
```

**输出示例**:
```
🚀 StoryWeaver 本地管道测试
================================================================================
📍 测试环境: 本地模拟
🔧 使用模拟服务: KV, R2, D1, Gemini
================================================================================

🔄 [10:30:15] 开始测试: 存储服务测试
🎭 使用模拟Gemini API生成故事...
✅ [10:30:17] ✓ 存储服务测试 (2156ms)

🔄 [10:30:17] 开始测试: Gemini服务测试
🎭 使用模拟Gemini API生成故事...
🎨 模拟生成图片...
🎵 模拟生成音频...
✅ [10:30:22] ✓ Gemini服务测试 (5234ms)

🔄 [10:30:22] 开始测试: 完整故事管道测试
✅ [10:30:29] ✓ 完整故事管道测试 (7123ms)

================================================================================
📊 本地测试报告
================================================================================
总测试数: 3
通过: 3
失败: 0
总耗时: 15秒

🎉 所有本地测试通过！故事创作管道逻辑正常。
```

### 2. 模拟API测试 (`test-story-pipeline.js`)

**目的**: 验证API接口设计和数据流

**测试内容**:
- ✅ API健康检查
- ✅ 配置数据获取（主题、风格、声音）
- ✅ 故事创建流程
- ✅ 生成进度监控
- ✅ 完整故事数据验证
- ✅ 边界条件处理
- ✅ 性能测试

**特点**:
- 模拟HTTP请求和响应
- 测试API设计的合理性
- 验证数据格式和错误处理
- 不需要真实的AI服务

**运行示例**:
```bash
node test-story-pipeline.js
```

### 3. 真实API测试 (`test-real-api.js`)

**目的**: 验证实际部署的API服务

**测试内容**:
- ✅ 服务健康状态
- ✅ 真实API响应
- ✅ 故事创建和生成
- ✅ 错误处理机制
- ✅ 性能指标

**特点**:
- 测试真实的HTTP服务
- 验证部署环境的正确性
- 可以测试不同环境（本地、预发布、生产）
- 需要服务器正在运行

**运行示例**:
```bash
# 测试本地开发服务器
node test-real-api.js --url http://localhost:8787

# 测试生产环境
node test-real-api.js --url https://api.storyweaver.com
```

## 🔧 环境配置

### 环境变量

```bash
# API基础URL
export API_BASE_URL="http://localhost:8787"

# Gemini API密钥（用于真实AI测试）
export GEMINI_API_KEY="your-gemini-api-key"

# 启用真实Gemini API（可选）
export USE_REAL_GEMINI="true"

# 测试超时时间
export TEST_TIMEOUT="300000"

# 详细输出
export VERBOSE="true"
```

### 测试配置文件

编辑 `test-config.json` 来自定义测试参数：

```json
{
  "environments": {
    "local": {
      "apiUrl": "http://localhost:8787",
      "timeout": 30000
    }
  },
  "testData": {
    "validStoryRequest": {
      "characterName": "小明",
      "characterAge": 6,
      "characterTraits": ["勇敢", "善良"],
      "theme": "adventure",
      "setting": "神秘的森林",
      "style": "cartoon",
      "voice": "gentle_female"
    }
  }
}
```

## 📊 测试报告

测试完成后会生成详细的报告：

- **控制台输出**: 实时显示测试进度和结果
- **JSON报告**: `test-report.json` 包含详细的测试数据
- **Markdown报告**: `test-reports/test_report_YYYYMMDD_HHMMSS.md`

### 报告内容

```json
{
  "startTime": "2024-01-15T10:30:00.000Z",
  "tests": [
    {
      "name": "API健康检查",
      "status": "PASS",
      "duration": 156,
      "timestamp": "2024-01-15T10:30:01.000Z"
    }
  ],
  "summary": {
    "total": 7,
    "passed": 7,
    "failed": 0,
    "errors": []
  }
}
```

## 🐛 故障排除

### 常见问题

#### 1. 连接错误
```
❌ Request failed: connect ECONNREFUSED 127.0.0.1:8787
```

**解决方案**:
- 确保开发服务器正在运行: `npm run dev`
- 检查端口是否正确
- 验证防火墙设置

#### 2. 超时错误
```
❌ 故事生成超时
```

**解决方案**:
- 增加超时时间: `--timeout 600000`
- 检查AI服务是否正常
- 验证网络连接

#### 3. API密钥错误
```
❌ Gemini API error: 401 Unauthorized
```

**解决方案**:
- 检查 `GEMINI_API_KEY` 环境变量
- 验证API密钥是否有效
- 确认API配额是否充足

#### 4. 依赖缺失
```
❌ Cannot find module 'xxx'
```

**解决方案**:
- 安装依赖: `npm install`
- 检查Node.js版本: `node --version`
- 清理并重新安装: `rm -rf node_modules && npm install`

### 调试技巧

#### 启用详细输出
```bash
./run-tests.sh local --verbose
```

#### 查看网络请求
```bash
export DEBUG=true
node test-real-api.js --url http://localhost:8787
```

#### 检查服务状态
```bash
curl -s http://localhost:8787/health | jq
```

## 🔄 持续集成

### GitHub Actions 示例

```yaml
name: StoryWeaver Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm install
      
    - name: Run local tests
      run: ./run-tests.sh local
      
    - name: Start dev server
      run: npm run dev &
      
    - name: Wait for server
      run: sleep 10
      
    - name: Run API tests
      run: ./run-tests.sh real --url http://localhost:8787
      
    - name: Upload test reports
      uses: actions/upload-artifact@v2
      with:
        name: test-reports
        path: test-reports/
```

## 📈 性能基准

### 预期性能指标

| 测试类型 | 预期时间 | 最大时间 |
|---------|---------|---------|
| 健康检查 | < 1秒 | 5秒 |
| 配置获取 | < 2秒 | 10秒 |
| 故事创建 | < 5秒 | 30秒 |
| 故事生成 | 1-3分钟 | 5分钟 |
| 完整测试 | 3-5分钟 | 10分钟 |

### 性能优化建议

1. **并发限制**: 避免同时创建过多故事
2. **缓存利用**: 充分利用KV缓存
3. **超时设置**: 合理设置各阶段超时时间
4. **错误重试**: 实现智能重试机制

## 🎯 最佳实践

### 开发阶段
1. 优先运行本地测试验证逻辑
2. 使用模拟测试验证API设计
3. 定期运行真实API测试

### 部署前
1. 运行完整测试套件
2. 验证所有环境的API
3. 检查性能指标

### 生产环境
1. 定期运行健康检查
2. 监控关键指标
3. 设置告警机制

---

## 📞 支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查测试日志和报告
3. 提交Issue并附上详细的错误信息

**测试愉快！** 🎉