# StoryWeaver API 文档

## 📋 概述

StoryWeaver API 是一个基于 Cloudflare Workers 的 RESTful API，为 AI 驱动的儿童故事创作平台提供后端服务。

**Base URL**: `https://api.storyweaver.com`

## 🔐 认证

API 使用 JWT Bearer Token 进行认证。

### 请求头格式
```
Authorization: Bearer <your-jwt-token>
```

### 获取Token
通过 Google OAuth 登录获取访问令牌。

## 📚 API 端点

### 🔑 认证相关 `/api/auth`

#### Google OAuth 登录
```http
POST /api/auth/google
```

**请求体**:
```json
{
  "code": "google-auth-code",
  "redirectUri": "https://storyweaver.com/auth/callback"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-uuid",
      "email": "<EMAIL>",
      "name": "User Name",
      "avatar": "https://...",
      "credits": 1,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "tokens": {
      "accessToken": "jwt-access-token",
      "refreshToken": "jwt-refresh-token",
      "expiresIn": 3600
    }
  }
}
```

#### 刷新访问令牌
```http
POST /api/auth/refresh
```

**请求体**:
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

#### 登出
```http
POST /api/auth/logout
```

#### 获取当前用户信息
```http
GET /api/auth/me
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "https://...",
    "credits": 5,
    "subscription": {
      "plan": "unlimited",
      "status": "active",
      "currentPeriodEnd": "2024-02-01T00:00:00Z"
    }
  }
}
```

### 📖 故事相关 `/api/stories`

#### 创建新故事
```http
POST /api/stories
```

**请求体**:
```json
{
  "characterName": "莉莉",
  "characterAge": 6,
  "characterTraits": ["勇敢", "善良", "爱冒险"],
  "theme": "魔法王国",
  "setting": "糖果城堡",
  "style": "cartoon",
  "voice": "gentle_female"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "storyId": "story-uuid",
    "status": {
      "storyId": "story-uuid",
      "status": "generating",
      "progress": {
        "text": false,
        "images": false,
        "audio": false
      },
      "estimatedTimeRemaining": 120
    }
  }
}
```

#### 获取故事生成状态
```http
GET /api/stories/{storyId}/status
```

**响应**:
```json
{
  "success": true,
  "data": {
    "storyId": "story-uuid",
    "status": "completed",
    "progress": {
      "text": true,
      "images": true,
      "audio": true
    },
    "estimatedTimeRemaining": 0
  }
}
```

#### 获取故事详情
```http
GET /api/stories/{storyId}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "story-uuid",
    "title": "莉莉的糖果王国冒险",
    "characterName": "莉莉",
    "characterAge": 6,
    "characterTraits": ["勇敢", "善良", "爱冒险"],
    "theme": "魔法王国",
    "setting": "糖果城堡",
    "style": "cartoon",
    "voice": "gentle_female",
    "pages": [
      {
        "pageNumber": 1,
        "text": "从前，有一个叫莉莉的小女孩...",
        "imageUrl": "https://assets.storyweaver.com/images/...",
        "imagePrompt": "一个勇敢的小女孩站在糖果城堡前..."
      }
    ],
    "audioUrl": "https://assets.storyweaver.com/audio/...",
    "coverImageUrl": "https://assets.storyweaver.com/images/...",
    "status": "completed",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

#### 获取用户故事列表
```http
GET /api/stories?page=1&limit=10
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10, 最大: 100)

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "story-uuid",
      "title": "莉莉的糖果王国冒险",
      "characterName": "莉莉",
      "status": "completed",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

#### 删除故事
```http
DELETE /api/stories/{storyId}
```

### 👤 用户相关 `/api/users`

#### 获取当前用户信息
```http
GET /api/users/me
```

#### 更新用户信息
```http
PUT /api/users/me
```

**请求体**:
```json
{
  "name": "新的用户名",
  "avatar": "https://new-avatar-url.com/avatar.jpg"
}
```

#### 获取用户统计信息
```http
GET /api/users/stats
```

**响应**:
```json
{
  "success": true,
  "data": {
    "totalStories": 15,
    "completedStories": 12,
    "generatingStories": 1,
    "failedStories": 2,
    "totalBooksOrdered": 3,
    "deliveredBooks": 2,
    "credits": 5,
    "joinDate": "2024-01-01T00:00:00Z",
    "recentStories": [
      {
        "id": "story-uuid",
        "title": "最新故事",
        "status": "completed",
        "createdAt": "2024-01-15T00:00:00Z"
      }
    ]
  }
}
```

#### 获取用户活动日志
```http
GET /api/users/activities?page=1&limit=20
```

#### 提交用户反馈
```http
POST /api/users/feedback
```

**请求体**:
```json
{
  "type": "bug_report",
  "subject": "故事生成失败",
  "content": "详细的问题描述..."
}
```

#### 删除用户账户
```http
DELETE /api/users/me
```

### 💳 支付相关 `/api/payments`

#### 获取订阅计划
```http
GET /api/payments/plans
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": "credits_5",
      "name": "积分包",
      "description": "5个故事创作额度",
      "price": 10,
      "currency": "USD",
      "interval": "month",
      "features": [
        "5个故事创作",
        "多种插图风格",
        "高质量音频旁白"
      ],
      "stripePriceId": "price_credits_5"
    }
  ]
}
```

#### 创建支付意图
```http
POST /api/payments/create-payment-intent
```

**请求体**:
```json
{
  "planId": "credits_5",
  "amount": 10
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "pi_xxx",
    "amount": 1000,
    "currency": "usd",
    "status": "requires_payment_method",
    "clientSecret": "pi_xxx_secret_xxx"
  }
}
```

#### 创建订阅
```http
POST /api/payments/create-subscription
```

**请求体**:
```json
{
  "priceId": "price_unlimited_monthly",
  "paymentMethodId": "pm_xxx"
}
```

#### 取消订阅
```http
POST /api/payments/cancel-subscription
```

#### 购买积分
```http
POST /api/payments/purchase-credits
```

**请求体**:
```json
{
  "paymentIntentId": "pi_xxx"
}
```

### 📚 实体书相关 `/api/books`

#### 创建实体书订单
```http
POST /api/books
```

**请求体**:
```json
{
  "storyId": "story-uuid",
  "customization": {
    "coverTitle": "莉莉的专属故事",
    "coverColor": "#FF6B6B",
    "coverStyle": "premium",
    "dedication": "献给我最爱的女儿",
    "giftWrap": true
  },
  "shippingInfo": {
    "recipientName": "张三",
    "address": {
      "street": "某某街道123号",
      "city": "北京",
      "state": "北京市",
      "postalCode": "100000",
      "country": "中国"
    },
    "phone": "+86 138 0000 0000",
    "email": "<EMAIL>"
  }
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "bookId": "book-uuid",
    "orderNumber": "SW20240115ABC123",
    "price": 39.99,
    "paymentIntent": {
      "id": "pi_xxx",
      "clientSecret": "pi_xxx_secret_xxx",
      "amount": 3999,
      "currency": "usd"
    }
  }
}
```

#### 获取实体书订单列表
```http
GET /api/books?page=1&limit=10&status=pending
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 订单状态 (pending, printing, shipped, delivered, canceled)

#### 获取实体书订单详情
```http
GET /api/books/{bookId}
```

#### 取消实体书订单
```http
DELETE /api/books/{bookId}
```

#### 确认实体书支付
```http
POST /api/books/{bookId}/confirm-payment
```

**请求体**:
```json
{
  "paymentIntentId": "pi_xxx"
}
```

#### 跟踪实体书订单
```http
GET /api/books/{bookId}/tracking
```

**响应**:
```json
{
  "success": true,
  "data": {
    "orderNumber": "SW20240115ABC123",
    "status": "shipped",
    "trackingNumber": "1234567890",
    "timeline": [
      {
        "status": "pending",
        "title": "订单创建",
        "description": "订单已创建，等待支付",
        "timestamp": "2024-01-15T10:00:00Z",
        "completed": true
      },
      {
        "status": "printing",
        "title": "开始制作",
        "description": "支付成功，开始制作实体书",
        "timestamp": "2024-01-15T12:00:00Z",
        "completed": true
      },
      {
        "status": "shipped",
        "title": "已发货",
        "description": "实体书已发货，正在配送中",
        "timestamp": "2024-01-18T09:00:00Z",
        "completed": true
      }
    ]
  }
}
```

## 📊 响应格式

### 成功响应
```json
{
  "success": true,
  "data": {}, // 响应数据
  "message": "操作成功" // 可选的消息
}
```

### 分页响应
```json
{
  "success": true,
  "data": [], // 数据数组
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误描述",
  "code": "ERROR_CODE"
}
```

## 🚫 错误代码

| 错误代码 | HTTP状态码 | 描述 |
|---------|-----------|------|
| `UNAUTHORIZED` | 401 | 未授权访问 |
| `INVALID_TOKEN` | 401 | 无效的访问令牌 |
| `TOKEN_EXPIRED` | 401 | 令牌已过期 |
| `USER_NOT_FOUND` | 404 | 用户不存在 |
| `STORY_NOT_FOUND` | 404 | 故事不存在 |
| `INSUFFICIENT_CREDITS` | 403 | 积分不足 |
| `VALIDATION_ERROR` | 400 | 数据验证错误 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `PAYMENT_FAILED` | 400 | 支付失败 |
| `AI_SERVICE_ERROR` | 500 | AI服务错误 |
| `INTERNAL_ERROR` | 500 | 内部服务器错误 |

## 🔄 限流规则

| 端点类型 | 限制 | 时间窗口 |
|---------|------|---------|
| 全局API | 60次请求 | 每分钟 |
| 用户API | 30次请求 | 每分钟 |
| 故事生成 | 5次请求 | 每小时 |
| 支付API | 10次请求 | 每分钟 |

## 📝 数据类型

### 故事风格 (StoryStyle)
- `cartoon`: 卡通风格
- `watercolor`: 水彩画风格
- `sketch`: 简笔画风格
- `fantasy`: 奇幻风格
- `realistic`: 写实风格
- `anime`: 动漫风格

### 语音类型 (VoiceType)
- `gentle_female`: 温柔女声
- `warm_male`: 温暖男声
- `child_friendly`: 儿童友好声音
- `storyteller`: 故事讲述者声音

### 订阅计划 (SubscriptionPlan)
- `free`: 免费用户
- `credits`: 积分用户
- `unlimited`: 无限订阅用户

### 订单状态 (OrderStatus)
- `pending`: 待支付
- `printing`: 制作中
- `shipped`: 已发货
- `delivered`: 已送达
- `canceled`: 已取消

## 🔧 SDK 和工具

### JavaScript/TypeScript SDK
```bash
npm install @storyweaver/api-client
```

```typescript
import { StoryWeaverClient } from '@storyweaver/api-client';

const client = new StoryWeaverClient({
  baseURL: 'https://api.storyweaver.com',
  accessToken: 'your-access-token'
});

// 创建故事
const story = await client.stories.create({
  characterName: '莉莉',
  characterAge: 6,
  characterTraits: ['勇敢', '善良'],
  theme: '魔法王国',
  setting: '糖果城堡',
  style: 'cartoon',
  voice: 'gentle_female'
});
```

### cURL 示例
```bash
# 创建故事
curl -X POST https://api.storyweaver.com/api/stories \
  -H "Authorization: Bearer your-access-token" \
  -H "Content-Type: application/json" \
  -d '{
    "characterName": "莉莉",
    "characterAge": 6,
    "characterTraits": ["勇敢", "善良"],
    "theme": "魔法王国",
    "setting": "糖果城堡",
    "style": "cartoon",
    "voice": "gentle_female"
  }'
```

## 📞 支持

如果您在使用API时遇到问题：

1. 查看本文档的相关部分
2. 检查错误响应中的错误代码
3. 联系技术支持：<EMAIL>
4. 查看状态页面：https://status.storyweaver.com

---

*API文档版本: v1.0.0*
*最后更新: 2024-01-15*