# StoryWeaver 后端架构设计

## 📋 技术栈

- **运行环境**: Cloudflare Workers
- **数据存储**: 
  - KV Store (用户会话、缓存)
  - R2 Storage (图片、音频文件)
  - D1 Database (用户数据、故事元数据)
- **AI服务**: Gemini API (文本生成 + 图像生成 + TTS)
- **认证**: Google OAuth 2.0
- **支付**: Stripe API

## 🏗️ 项目结构

```
backend/
├── src/
│   ├── handlers/           # 请求处理器
│   │   ├── auth.ts        # 认证相关
│   │   ├── stories.ts     # 故事相关
│   │   ├── users.ts       # 用户相关
│   │   ├── payments.ts    # 支付相关
│   │   └── books.ts       # 实体书相关
│   ├── services/          # 业务服务
│   │   ├── gemini.ts      # Gemini AI服务
│   │   ├── storage.ts     # 存储服务
│   │   ├── auth.ts        # 认证服务
│   │   └── payment.ts     # 支付服务
│   ├── models/            # 数据模型
│   │   ├── user.ts        # 用户模型
│   │   ├── story.ts       # 故事模型
│   │   └── subscription.ts # 订阅模型
│   ├── middleware/        # 中间件
│   │   ├── auth.ts        # 认证中间件
│   │   ├── cors.ts        # CORS中间件
│   │   └── rateLimit.ts   # 限流中间件
│   ├── utils/             # 工具函数
│   │   ├── validation.ts  # 数据验证
│   │   ├── errors.ts      # 错误处理
│   │   └── constants.ts   # 常量定义
│   ├── types/             # TypeScript类型
│   │   ├── api.ts         # API类型
│   │   ├── gemini.ts      # Gemini API类型
│   │   └── database.ts    # 数据库类型
│   └── index.ts           # 入口文件
├── migrations/            # 数据库迁移
├── schemas/               # 数据库Schema
├── wrangler.toml         # Cloudflare配置
└── package.json
```

## 🔧 环境配置

```toml
# wrangler.toml
name = "storyweaver-api"
main = "src/index.ts"
compatibility_date = "2024-01-01"

[env.production]
vars = { ENVIRONMENT = "production" }

[[env.production.kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[env.production.d1_databases]]
binding = "DB"
database_name = "storyweaver"
database_id = "your-d1-database-id"

[vars]
GEMINI_API_KEY = "your-gemini-api-key"
GOOGLE_CLIENT_ID = "your-google-client-id"
GOOGLE_CLIENT_SECRET = "your-google-client-secret"
STRIPE_SECRET_KEY = "your-stripe-secret-key"
JWT_SECRET = "your-jwt-secret"
```

## 🚀 部署说明

1. 安装依赖: `npm install`
2. 配置环境变量: 更新 `wrangler.toml`
3. 运行数据库迁移: `npm run migrate`
4. 本地开发: `npm run dev`
5. 部署到生产: `npm run deploy`