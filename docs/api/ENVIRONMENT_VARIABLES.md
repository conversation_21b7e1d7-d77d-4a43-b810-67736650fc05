# StoryWeaver Backend 环境变量配置指南

## 📋 概述

本文档详细说明了StoryWeaver后端项目所需的所有环境变量配置，确保开发和生产环境的功能完整性。

## 🔧 环境变量列表

### 1. 认证相关
| 变量名 | 用途 | 示例值 | 必需 |
|--------|------|--------|------|
| `JWT_SECRET` | JWT令牌签名密钥 | `storyweaver-jwt-secret-key-2024` | ✅ |
| `GOOGLE_CLIENT_ID` | Google OAuth客户端ID | `463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com` | ✅ |
| `GOOGLE_CLIENT_SECRET` | Google OAuth客户端密钥 | `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` | ✅ |

### 2. 支付相关
| 变量名 | 用途 | 示例值 | 必需 |
|--------|------|--------|------|
| `STRIPE_SECRET_KEY` | Stripe支付密钥 | `sk_test_51RfGiA2azdJC9VD2y5T6LIwDx4dL...` | ✅ |
| `STRIPE_WEBHOOK_SECRET` | Stripe Webhook验证密钥 | `whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA` | ✅ |

### 3. AI服务相关
| 变量名 | 用途 | 示例值 | 必需 |
|--------|------|--------|------|
| `GEMINI_API_KEY` | Google Gemini AI API密钥 | `AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw` | ✅ |

### 4. 环境配置
| 变量名 | 用途 | 示例值 | 必需 |
|--------|------|--------|------|
| `ENVIRONMENT` | 运行环境标识 | `development` / `production` | ✅ |
| `CORS_ORIGIN` | CORS允许的源 | `https://storyweaver.pages.dev` | ✅ |

## 📁 配置文件

### 开发环境 - `.dev.vars`
```bash
# StoryWeaver Backend Development Environment Variables
# 这些变量用于本地开发环境，确保所有功能正常工作

# AI 服务配置
GEMINI_API_KEY=AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw

# 认证相关配置
JWT_SECRET=storyweaver-jwt-secret-key-2024

# Google OAuth 配置
GOOGLE_CLIENT_ID=463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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

# Stripe 支付配置（测试环境）
STRIPE_SECRET_KEY=sk_test_51RfGiA2azdJC9VD2y5T6LIwDx4dLgb6EHOXdAiCWmz7LwW48jYnyLz4xQbsqQmj7wCV7UK1y43TlOrh9CwJgoPUd00z5VOHjsG
STRIPE_WEBHOOK_SECRET=whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA
```

### 生产环境 - `wrangler.toml`
```toml
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.pages.dev"
# 认证相关配置
JWT_SECRET = "storyweaver-jwt-secret-key-2024"
# Google OAuth 配置
GOOGLE_CLIENT_ID = "463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET = "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"
# Stripe 支付配置（测试环境）
STRIPE_SECRET_KEY = "sk_test_51RfGiA2azdJC9VD2y5T6LIwDx4dLgb6EHOXdAiCWmz7LwW48jYnyLz4xQbsqQmj7wCV7UK1y43TlOrh9CwJgoPUd00z5VOHjsG"
STRIPE_WEBHOOK_SECRET = "whsec_7M25OLASFIA5SGrAJigVVgaxKKKq81cA"
# AI 服务配置
GEMINI_API_KEY = "AIzaSyBOlLgWpsquc9L_aQ9hRbml-9b-lAgo6fw"
```

## 🚀 使用方法

### 开发环境启动
```bash
# 启动本地开发服务器
wrangler dev --env development --port 8787

# 或者使用默认配置
npm run dev
```

### 生产环境部署
```bash
# 部署到生产环境
wrangler deploy --env production

# 或者使用部署脚本
npm run deploy
```

## ✅ 验证配置

### 检查环境变量加载
启动开发服务器时，应该看到以下输出：
```
Your worker has access to the following bindings:
- Vars:
  - ENVIRONMENT: "development"
  - CORS_ORIGIN: "http://localhost:3000,http://localhos..."
  - GEMINI_API_KEY: "(hidden)"
  - JWT_SECRET: "(hidden)"
  - GOOGLE_CLIENT_ID: "(hidden)"
  - GOOGLE_CLIENT_SECRET: "(hidden)"
  - STRIPE_SECRET_KEY: "(hidden)"
  - STRIPE_WEBHOOK_SECRET: "(hidden)"
```

### 功能测试
1. **健康检查**: `curl http://localhost:8787/health`
2. **认证功能**: 测试Google OAuth登录
3. **支付功能**: 测试Stripe Checkout创建
4. **AI功能**: 测试故事生成

## 🔒 安全注意事项

1. **敏感信息保护**: 
   - 生产环境建议使用 `wrangler secret` 命令管理敏感变量
   - 开发环境的 `.dev.vars` 文件不应提交到版本控制

2. **密钥轮换**:
   - 定期更新JWT_SECRET
   - 监控API密钥使用情况

3. **环境隔离**:
   - 开发和生产环境使用不同的API密钥
   - 确保测试数据不会影响生产环境

## 🛠️ 故障排除

### 常见问题

#### 1. 环境变量未加载
**症状**: 启动时显示变量缺失
**解决**: 检查 `.dev.vars` 文件是否存在且格式正确

#### 2. JWT认证失败
**症状**: 返回401 Unauthorized错误
**解决**: 确认JWT_SECRET配置正确且前后端一致

#### 3. Google OAuth失败
**症状**: 登录重定向失败
**解决**: 检查GOOGLE_CLIENT_ID和GOOGLE_CLIENT_SECRET配置

#### 4. Stripe支付失败
**症状**: 支付创建失败
**解决**: 验证STRIPE_SECRET_KEY是否有效

## 📝 更新日志

- **2025-07-03**: 完善环境变量配置，添加所有必需变量
- **2025-07-03**: 修复Stripe支付认证问题
- **2025-07-03**: 统一开发和生产环境配置
