# StoryWeaver 开发指南 - 第四部分

## 6. 教育版权益系统

### 6.1 业务需求分析

#### 6.1.1 目标用户群体
```
🎓 幼儿园 (3-6岁)
- 班级规模：20-30人
- 需求：集体故事时间、个性化教学
- 预算：中等，注重性价比

🏫 小学 (6-12岁)
- 班级规模：30-40人
- 需求：语言教学、阅读理解、创意写作
- 预算：较高，注重教学效果

🏛️ 教育机构
- 学员规模：50-200人
- 需求：课程体系、学习管理、进度跟踪
- 预算：高，注重系统化管理

👨‍🏫 个人教师
- 学生规模：10-50人
- 需求：教学辅助、作业布置、家长沟通
- 预算：低，注重实用性
```

#### 6.1.2 核心价值主张
```
📚 教学资源丰富化
- 无限故事生成，满足不同教学场景
- 多样化主题，覆盖各学科知识点
- 个性化内容，适应不同学生需求

📊 学习数据分析
- 学生阅读行为追踪
- 学习进度可视化
- 教学效果评估

👥 班级管理功能
- 批量账户创建和管理
- 分组教学支持
- 作业布置和收集

💰 成本效益优化
- 教育版专属定价
- 批量购买优惠
- 长期合作折扣
```

### 6.2 系统架构设计

#### 6.2.1 用户角色模型
```typescript
// 用户角色定义
enum UserRole {
  STUDENT = 'student',           // 学生
  TEACHER = 'teacher',           // 教师
  ADMIN = 'admin',              // 机构管理员
  SUPER_ADMIN = 'super_admin'   // 系统管理员
}

// 机构类型
enum InstitutionType {
  KINDERGARTEN = 'kindergarten',     // 幼儿园
  PRIMARY_SCHOOL = 'primary_school', // 小学
  TRAINING_CENTER = 'training_center', // 培训机构
  INDIVIDUAL = 'individual'          // 个人教师
}

// 教育版用户扩展
interface EducationUser extends User {
  role: UserRole;
  institutionId?: string;
  classIds: string[];
  permissions: Permission[];
  educationProfile: EducationProfile;
}

interface EducationProfile {
  grade?: string;           // 年级
  subject?: string[];       // 教授科目
  experience?: number;      // 教学经验（年）
  certification?: string[]; // 教师资格证
  bio?: string;            // 个人简介
}
```

#### 6.2.2 机构管理模型
```typescript
// 教育机构
interface Institution {
  id: string;
  name: string;
  type: InstitutionType;
  address: {
    country: string;
    province: string;
    city: string;
    district: string;
    street: string;
    postalCode: string;
  };
  contact: {
    phone: string;
    email: string;
    website?: string;
  };
  settings: InstitutionSettings;
  subscription: InstitutionSubscription;
  stats: InstitutionStats;
  createdAt: string;
  updatedAt: string;
}

interface InstitutionSettings {
  allowStudentRegistration: boolean;
  requireParentConsent: boolean;
  contentFiltering: 'strict' | 'moderate' | 'relaxed';
  defaultLanguage: string;
  timezone: string;
  academicYear: {
    startDate: string;
    endDate: string;
  };
}

// 班级管理
interface Class {
  id: string;
  institutionId: string;
  name: string;
  grade: string;
  subject?: string;
  teacherId: string;
  studentIds: string[];
  settings: ClassSettings;
  stats: ClassStats;
  createdAt: string;
  updatedAt: string;
}

interface ClassSettings {
  maxStudents: number;
  allowCollaboration: boolean;
  parentNotifications: boolean;
  storySharing: 'private' | 'class' | 'institution';
}
```

### 6.3 权益系统设计

#### 6.3.1 教育版订阅计划
```typescript
// 教育版订阅计划
interface EducationPlan {
  id: string;
  name: string;
  type: 'individual' | 'classroom' | 'institution';
  pricing: {
    monthly: number;
    yearly: number;
    currency: string;
  };
  limits: {
    maxTeachers: number;
    maxStudents: number;
    maxClasses: number;
    maxStoriesPerMonth: number;
    maxStorageGB: number;
  };
  features: EducationFeature[];
  discounts: {
    volumeDiscount: number; // 批量折扣
    loyaltyDiscount: number; // 忠诚度折扣
    seasonalDiscount: number; // 季节性折扣
  };
}

// 教育版特色功能
enum EducationFeature {
  // 基础功能
  UNLIMITED_STORIES = 'unlimited_stories',
  BATCH_USER_MANAGEMENT = 'batch_user_management',
  CLASS_MANAGEMENT = 'class_management',
  
  // 教学功能
  LESSON_PLANNING = 'lesson_planning',
  ASSIGNMENT_CREATION = 'assignment_creation',
  PROGRESS_TRACKING = 'progress_tracking',
  PARENT_REPORTS = 'parent_reports',
  
  // 内容功能
  CURRICULUM_ALIGNMENT = 'curriculum_alignment',
  CUSTOM_THEMES = 'custom_themes',
  EDUCATIONAL_TEMPLATES = 'educational_templates',
  
  // 管理功能
  ADMIN_DASHBOARD = 'admin_dashboard',
  USAGE_ANALYTICS = 'usage_analytics',
  CONTENT_MODERATION = 'content_moderation',
  
  // 集成功能
  LMS_INTEGRATION = 'lms_integration',
  GOOGLE_CLASSROOM = 'google_classroom',
  API_ACCESS = 'api_access'
}
```

#### 6.3.2 权限管理系统
```typescript
// 权限定义
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
}

interface PermissionCondition {
  field: string;
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'lt';
  value: any;
}

// 角色权限映射
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.STUDENT]: [
    { id: 'story.create', name: '创建故事', resource: 'story', action: 'create' },
    { id: 'story.read.own', name: '查看自己的故事', resource: 'story', action: 'read', 
      conditions: [{ field: 'userId', operator: 'eq', value: 'self' }] },
    { id: 'story.read.class', name: '查看班级故事', resource: 'story', action: 'read',
      conditions: [{ field: 'sharing', operator: 'in', value: ['class', 'institution'] }] }
  ],
  
  [UserRole.TEACHER]: [
    // 学生权限 + 教师权限
    { id: 'class.manage', name: '管理班级', resource: 'class', action: 'manage' },
    { id: 'student.view', name: '查看学生', resource: 'user', action: 'read',
      conditions: [{ field: 'role', operator: 'eq', value: 'student' }] },
    { id: 'assignment.create', name: '创建作业', resource: 'assignment', action: 'create' },
    { id: 'report.generate', name: '生成报告', resource: 'report', action: 'create' }
  ],
  
  [UserRole.ADMIN]: [
    // 教师权限 + 管理员权限
    { id: 'institution.manage', name: '管理机构', resource: 'institution', action: 'manage' },
    { id: 'user.manage', name: '管理用户', resource: 'user', action: 'manage' },
    { id: 'subscription.manage', name: '管理订阅', resource: 'subscription', action: 'manage' }
  ]
};
```

### 6.4 核心功能实现

#### 6.4.1 班级管理系统
```typescript
// backend/src/services/education/classService.ts
export class ClassService {
  private classRepo: ClassRepository;
  private userRepo: UserRepository;
  
  constructor(env: any) {
    this.classRepo = new ClassRepository(env.DB);
    this.userRepo = new UserRepository(env.DB);
  }

  /**
   * 创建班级
   */
  async createClass(teacherId: string, data: CreateClassRequest): Promise<Class> {
    // 验证教师权限
    const teacher = await this.userRepo.findById(teacherId);
    if (!teacher || teacher.role !== UserRole.TEACHER) {
      throw new Error('Only teachers can create classes');
    }

    // 检查机构限制
    const institution = await this.getTeacherInstitution(teacherId);
    await this.validateClassLimits(institution);

    const classId = uuidv4();
    const newClass = await this.classRepo.create({
      id: classId,
      institutionId: institution.id,
      name: data.name,
      grade: data.grade,
      subject: data.subject,
      teacherId,
      studentIds: [],
      settings: {
        maxStudents: data.maxStudents || 30,
        allowCollaboration: data.allowCollaboration || true,
        parentNotifications: data.parentNotifications || true,
        storySharing: data.storySharing || 'class'
      },
      stats: {
        totalStudents: 0,
        totalStories: 0,
        avgReadingTime: 0,
        lastActivity: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    return newClass;
  }

  /**
   * 批量添加学生
   */
  async addStudentsBatch(
    classId: string, 
    teacherId: string, 
    students: CreateStudentRequest[]
  ): Promise<{ success: User[], failed: { email: string, error: string }[] }> {
    // 验证权限
    await this.validateTeacherClassAccess(teacherId, classId);

    const results = {
      success: [] as User[],
      failed: [] as { email: string, error: string }[]
    };

    for (const studentData of students) {
      try {
        // 检查邮箱是否已存在
        const existingUser = await this.userRepo.findByEmail(studentData.email);
        if (existingUser) {
          results.failed.push({
            email: studentData.email,
            error: 'Email already exists'
          });
          continue;
        }

        // 创建学生账户
        const student = await this.createStudentAccount(studentData, classId);
        results.success.push(student);

        // 发送欢迎邮件给家长
        await this.sendParentWelcomeEmail(student, studentData.parentEmail);

      } catch (error) {
        results.failed.push({
          email: studentData.email,
          error: error.message
        });
      }
    }

    // 更新班级学生数量
    await this.updateClassStats(classId);

    return results;
  }

  /**
   * 生成班级报告
   */
  async generateClassReport(
    classId: string, 
    teacherId: string, 
    options: ReportOptions
  ): Promise<ClassReport> {
    await this.validateTeacherClassAccess(teacherId, classId);

    const classInfo = await this.classRepo.findById(classId);
    const students = await this.userRepo.findByIds(classInfo.studentIds);
    
    // 获取学生故事数据
    const storyStats = await this.getStudentStoryStats(classInfo.studentIds, options);
    
    // 生成报告
    const report: ClassReport = {
      id: uuidv4(),
      classId,
      teacherId,
      period: options.period,
      generatedAt: new Date().toISOString(),
      summary: {
        totalStudents: students.length,
        activeStudents: storyStats.activeStudents,
        totalStories: storyStats.totalStories,
        avgStoriesPerStudent: storyStats.totalStories / students.length,
        avgReadingTime: storyStats.avgReadingTime,
        popularThemes: storyStats.popularThemes
      },
      studentDetails: await this.generateStudentDetails(students, storyStats),
      recommendations: this.generateTeachingRecommendations(storyStats)
    };

    return report;
  }

  /**
   * 创建教学作业
   */
  async createAssignment(
    teacherId: string,
    data: CreateAssignmentRequest
  ): Promise<Assignment> {
    // 验证权限
    await this.validateTeacherClassAccess(teacherId, data.classId);

    const assignment = await this.assignmentRepo.create({
      id: uuidv4(),
      classId: data.classId,
      teacherId,
      title: data.title,
      description: data.description,
      requirements: data.requirements,
      dueDate: data.dueDate,
      status: 'active',
      submissions: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // 通知学生和家长
    await this.notifyAssignmentCreated(assignment);

    return assignment;
  }

  private async createStudentAccount(
    data: CreateStudentRequest, 
    classId: string
  ): Promise<User> {
    const studentId = uuidv4();
    
    // 生成临时密码
    const tempPassword = this.generateTempPassword();
    const passwordHash = await bcrypt.hash(tempPassword, 12);

    const student = await this.userRepo.create({
      id: studentId,
      email: data.email,
      name: data.name,
      passwordHash,
      authProvider: 'email',
      role: UserRole.STUDENT,
      classIds: [classId],
      emailVerified: false,
      educationProfile: {
        grade: data.grade,
        parentEmail: data.parentEmail,
        parentName: data.parentName
      },
      credits: 5, // 学生账户赠送5个积分
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    return student;
  }
}
```

#### 6.4.2 教育版仪表板
```typescript
// frontend/src/pages/education/EducationDashboard.tsx
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, 
  BookOpen, 
  TrendingUp, 
  Calendar,
  Download,
  Plus,
  Settings
} from 'lucide-react';

interface DashboardStats {
  totalStudents: number;
  activeStudents: number;
  totalStories: number;
  thisMonthStories: number;
  avgReadingTime: number;
  completionRate: number;
}

export const EducationDashboard: React.FC = () => {
  const { t } = useTranslation('education');
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth');

  useEffect(() => {
    loadDashboardData();
  }, [selectedPeriod]);

  const loadDashboardData = async () => {
    try {
      const response = await fetch(`/api/education/dashboard?period=${selectedPeriod}`);
      const data = await response.json();
      setStats(data.stats);
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    change?: number;
    icon: React.ReactNode;
    color: string;
  }> = ({ title, value, change, icon, color }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change !== undefined && (
            <p className={`text-sm ${change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {change >= 0 ? '+' : ''}{change}% {t('dashboard.fromLastPeriod')}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  if (!stats) {
    return <div className="flex justify-center items-center h-64">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>;
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            {t('dashboard.title')}
          </h1>
          <p className="text-gray-600">
            {t('dashboard.subtitle')}
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="btn btn-outline">
            <Download className="w-4 h-4 mr-2" />
            {t('dashboard.exportReport')}
          </button>
          <button className="btn btn-primary">
            <Plus className="w-4 h-4 mr-2" />
            {t('dashboard.createAssignment')}
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={t('dashboard.stats.totalStudents')}
          value={stats.totalStudents}
          icon={<Users className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title={t('dashboard.stats.activeStudents')}
          value={stats.activeStudents}
          change={12}
          icon={<TrendingUp className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title={t('dashboard.stats.totalStories')}
          value={stats.totalStories}
          change={8}
          icon={<BookOpen className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
        <StatCard
          title={t('dashboard.stats.avgReadingTime')}
          value={`${stats.avgReadingTime}${t('common.minutes')}`}
          change={-3}
          icon={<Calendar className="w-6 h-6 text-white" />}
          color="bg-orange-500"
        />
      </div>

      {/* 图表和详细信息 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 学习进度图表 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">
            {t('dashboard.learningProgress')}
          </h3>
          {/* 这里放置图表组件 */}
        </div>

        {/* 最近活动 */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">
            {t('dashboard.recentActivity')}
          </h3>
          {/* 这里放置活动列表 */}
        </div>
      </div>

      {/* 班级管理 */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">
              {t('dashboard.classManagement')}
            </h3>
            <button className="btn btn-outline btn-sm">
              <Settings className="w-4 h-4 mr-2" />
              {t('dashboard.manageClasses')}
            </button>
          </div>
        </div>
        <div className="p-6">
          {/* 班级列表 */}
        </div>
      </div>
    </div>
  );
};
```

### 6.5 定价策略

#### 6.5.1 教育版定价模型
```typescript
// 教育版定价计划
const EDUCATION_PLANS = {
  individual: {
    name: '个人教师版',
    monthlyPrice: 99,
    yearlyPrice: 999,
    features: [
      'unlimited_stories',
      'class_management',
      'progress_tracking',
      'parent_reports'
    ],
    limits: {
      maxStudents: 50,
      maxClasses: 3,
      maxStoriesPerMonth: 500
    }
  },
  
  classroom: {
    name: '班级版',
    monthlyPrice: 299,
    yearlyPrice: 2999,
    features: [
      'unlimited_stories',
      'batch_user_management',
      'assignment_creation',
      'usage_analytics',
      'curriculum_alignment'
    ],
    limits: {
      maxStudents: 150,
      maxClasses: 10,
      maxStoriesPerMonth: 2000
    }
  },
  
  institution: {
    name: '机构版',
    monthlyPrice: 999,
    yearlyPrice: 9999,
    features: [
      'unlimited_stories',
      'admin_dashboard',
      'lms_integration',
      'api_access',
      'custom_themes',
      'priority_support'
    ],
    limits: {
      maxStudents: 1000,
      maxClasses: 50,
      maxStoriesPerMonth: 10000
    }
  }
};

// 批量折扣策略
const VOLUME_DISCOUNTS = {
  students_50_100: 0.1,   // 50-100学生：9折
  students_100_300: 0.15, // 100-300学生：8.5折
  students_300_plus: 0.2  // 300+学生：8折
};
```

---

*本文档第四部分完成，下一部分将详细介绍前端组件开发和测试策略...*