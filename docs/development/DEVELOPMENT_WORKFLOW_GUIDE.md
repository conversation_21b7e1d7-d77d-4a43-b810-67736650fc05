# StoryWeaver 项目开发流程完整指南

**版本**: v1.0  
**更新时间**: 2025年7月12日  
**适用范围**: frontend-production统一架构  
**基于**: 项目结构重构后的新架构

---

## 🎯 1. 前端开发流程

### 1.1 开发环境准备

#### 环境要求
```bash
# 检查环境要求
node --version    # 需要 18+
npm --version     # 或使用 pnpm
git --version
```

#### 初始化开发环境
```bash
# 1. 进入前端目录
cd frontend-production

# 2. 安装依赖
npm install
# 或使用 pnpm install

# 3. 检查环境配置
cp .env.example .env.development  # 如果不存在
```

### 1.2 本地开发流程

#### 启动开发服务器
```bash
# 方式1：使用npm脚本
npm run dev

# 方式2：使用便捷脚本
./dev-full.sh

# 开发服务器信息
# - 前端端口: http://localhost:5173
# - API代理: /api/* -> backend
# - 热重载: 自动启用
```

#### 开发环境配置
```bash
# .env.development 配置示例
VITE_API_BASE_URL=http://localhost:8787/api
VITE_GOOGLE_CLIENT_ID=your_dev_client_id
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_dev_key
VITE_ENVIRONMENT=development
VITE_ENABLE_DEBUG=true
VITE_DEBUG_MODE=true
```

### 1.3 代码开发标准流程

#### 功能开发步骤
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature-name

# 2. 开发前创建变更文档
./scripts/create-change-report.sh NEW_FEATURE feature

# 3. 进行代码开发
# 在 frontend-production/src/ 中开发

# 4. 实时测试
npm run dev  # 开发服务器自动重载

# 5. 类型检查
npm run type-check

# 6. 代码格式化
npm run lint:fix
npm run format
```

#### 代码质量检查
```bash
# 完整的质量检查流程
npm run type-check    # TypeScript类型检查
npm run lint         # ESLint检查
npm run format:check # Prettier格式检查
npm run build        # 构建测试
```

### 1.4 构建和部署流程

#### 本地构建测试
```bash
# 1. 生产构建
npm run build

# 2. 本地预览构建结果
npm run preview
# 访问 http://localhost:4173

# 3. 检查构建文件
ls -la dist/
du -sh dist/*  # 查看文件大小
```

#### 部署到生产环境
```bash
# 方式1：完整部署（推荐）
./deploy-full.sh

# 方式2：生产环境部署
./deploy-production.sh

# 方式3：简单部署
./deploy-simple.sh

# 方式4：重新部署
./redeploy.sh
```

#### 部署脚本说明
```bash
# deploy-full.sh - 完整部署流程
# 1. 依赖检查和安装
# 2. TypeScript类型检查
# 3. 生产构建
# 4. 部署到Cloudflare Pages
# 5. 构建统计和验证

# deploy-production.sh - 生产环境专用
# 1. 使用生产环境配置
# 2. 优化构建设置
# 3. 安全检查
# 4. 生产部署

# redeploy.sh - 快速重新部署
# 1. 使用现有构建文件
# 2. 更新环境变量
# 3. 快速部署
```

---

## 📚 2. 文档管理流程

### 2.1 变更文档创建流程

#### 使用自动化脚本创建
```bash
# 1. 创建变更文档
./scripts/create-change-report.sh [功能名] [变更类型]

# 变更类型选项:
# - structure: 结构变更
# - feature: 功能更新  
# - docs: 文档整理
# - config: 配置修改
# - other: 其他

# 示例:
./scripts/create-change-report.sh USER_AUTHENTICATION feature
./scripts/create-change-report.sh API_OPTIMIZATION config
./scripts/create-change-report.sh DOCS_UPDATE docs
```

#### 脚本执行流程
```bash
# 脚本会自动:
# 1. 检查模板文件存在性
# 2. 创建目标目录（如不存在）
# 3. 复制模板并替换变量
# 4. 提供编辑器选项
# 5. 显示后续操作指导

# 生成的文件路径:
# docs/reports/progress/CHANGE_REPORT_2025-07-12_[功能名].md
```

### 2.2 变更文档编写流程

#### 文档结构填写
```markdown
# 1. 基本信息（脚本自动填写）
- 执行时间、变更类型、状态、执行者

# 2. 变更概述（手动填写）
- 变更目标、变更范围

# 3. 变更详情（手动填写）
- 变更前后状态对比
- 具体变更内容

# 4. 执行清单（手动填写）
- 准备、执行、验证、完成阶段的具体步骤

# 5. 影响分析（手动填写）
- 积极影响、潜在风险、缓解措施

# 6. 测试结果（执行后填写）
- 功能测试、性能测试、兼容性测试

# 7. 后续行动（手动填写）
- 短期、中期、长期任务
```

### 2.3 文档索引更新流程

#### 使用自动化脚本更新
```bash
# 变更完成后更新DOCS_INDEX.md
./scripts/update-docs-index.sh [变更类型] [变更标题] [变更报告路径]

# 示例:
./scripts/update-docs-index.sh "功能更新" "用户认证系统优化" \
  "docs/reports/implementation/CHANGE_REPORT_2025-07-12_USER_AUTHENTICATION.md"

./scripts/update-docs-index.sh "配置修改" "API性能优化" \
  "docs/reports/fixes/CHANGE_REPORT_2025-07-12_API_OPTIMIZATION.md"
```

#### 脚本自动处理
```bash
# 脚本会自动:
# 1. 备份原DOCS_INDEX.md文件
# 2. 在修改历史追踪部分插入新记录
# 3. 更新版本号（自动递增）
# 4. 更新最后修改时间
# 5. 提供查看选项
```

### 2.4 文档分类和归档

#### 文档分类规则
```bash
docs/reports/
├── progress/           # 项目进度和变更报告
│   ├── CHANGE_REPORT_*_*.md
│   └── PROJECT_*_REPORT.md
├── fixes/             # 问题修复报告
│   ├── *_FIX_REPORT.md
│   └── *_FIXES_*.md
├── implementation/    # 功能实现报告
│   ├── *_IMPLEMENTATION_*.md
│   └── FEATURE_*_REPORT.md
└── testing/           # 测试报告
    ├── *_TEST_REPORT.md
    └── TESTING_*.md
```

#### 手动归档流程
```bash
# 如果需要手动移动文档到其他分类
# 1. 移动文件
mv docs/reports/progress/CHANGE_REPORT_2025-07-12_BUG_FIX.md \
   docs/reports/fixes/

# 2. 更新目标目录的README.md
# 3. 更新DOCS_INDEX.md中的路径引用
```

---

## 🔧 3. 项目维护流程

### 3.1 日常开发文件组织

#### 目录结构规范
```bash
frontend-production/
├── src/
│   ├── components/     # 可复用组件
│   │   ├── ui/        # 基础UI组件
│   │   ├── features/  # 功能组件
│   │   └── layout/    # 布局组件
│   ├── pages/         # 页面组件
│   ├── stores/        # Zustand状态管理
│   ├── services/      # API服务层
│   ├── hooks/         # 自定义Hooks
│   ├── utils/         # 工具函数
│   ├── types/         # TypeScript类型
│   └── i18n/          # 国际化
├── public/            # 静态资源
├── dist/              # 构建输出（git忽略）
└── [配置文件]
```

#### 文件命名规范
```bash
# 组件文件
UserProfile.tsx         # PascalCase
UserProfileCard.tsx     # 复合组件名

# 页面文件
HomePage.tsx            # 页面组件
CreateStoryPage.tsx     # 功能页面

# 工具文件
dateUtils.ts            # camelCase
apiClient.ts            # 服务文件
userTypes.ts            # 类型文件

# 样式文件
globals.css             # 全局样式
components.css          # 组件样式
```

### 3.2 新功能开发流程

#### 完整开发流程
```bash
# 1. 需求分析和规划
./scripts/create-change-report.sh NEW_FEATURE feature

# 2. 创建功能分支
git checkout -b feature/new-feature

# 3. 开发环境准备
cd frontend-production
npm run dev

# 4. 代码开发
# - 创建组件/页面
# - 添加路由
# - 实现业务逻辑
# - 添加样式
# - 集成API

# 5. 本地测试
npm run type-check
npm run lint
npm run build
npm run preview

# 6. 更新变更文档
# 记录开发过程、测试结果、遇到的问题

# 7. 代码提交
git add .
git commit -m "feat: implement new feature"

# 8. 部署测试
./deploy-full.sh

# 9. 更新文档索引
./scripts/update-docs-index.sh "功能更新" "新功能实现" \
  "docs/reports/implementation/CHANGE_REPORT_2025-07-12_NEW_FEATURE.md"

# 10. 合并到主分支
git checkout main
git merge feature/new-feature
```

### 3.3 Bug修复流程

#### 快速修复流程
```bash
# 1. 创建修复文档
./scripts/create-change-report.sh BUG_FIX_NAME fixes

# 2. 创建修复分支
git checkout -b fix/bug-description

# 3. 问题诊断
# - 复现问题
# - 分析根本原因
# - 设计解决方案

# 4. 实施修复
# - 修改代码
# - 添加测试
# - 验证修复效果

# 5. 测试验证
npm run type-check
npm run build
# 手动测试修复效果

# 6. 部署验证
./deploy-full.sh
# 在生产环境验证修复

# 7. 更新文档
./scripts/update-docs-index.sh "问题修复" "Bug修复描述" \
  "docs/reports/fixes/CHANGE_REPORT_2025-07-12_BUG_FIX_NAME.md"
```

### 3.4 配置更改流程

#### 环境配置更新
```bash
# 1. 创建配置变更文档
./scripts/create-change-report.sh CONFIG_UPDATE config

# 2. 备份现有配置
cp .env.production .env.production.backup
cp wrangler.toml wrangler.toml.backup

# 3. 更新配置文件
# - 环境变量
# - 构建配置
# - 部署配置

# 4. 测试配置
npm run build
./deploy-full.sh

# 5. 验证配置效果
# - 功能测试
# - 性能测试
# - 安全检查

# 6. 更新文档
./scripts/update-docs-index.sh "配置修改" "配置更新描述" \
  "docs/reports/fixes/CHANGE_REPORT_2025-07-12_CONFIG_UPDATE.md"
```

---

## 👥 4. 团队协作流程

### 4.1 多人开发协作规范

#### Git工作流程
```bash
# 1. 同步主分支
git checkout main
git pull origin main

# 2. 创建功能分支
git checkout -b feature/team-member-feature

# 3. 开发过程中定期同步
git fetch origin
git rebase origin/main

# 4. 提交前检查
npm run type-check
npm run lint
npm run build

# 5. 提交代码
git add .
git commit -m "feat: implement team feature"
git push origin feature/team-member-feature

# 6. 创建Pull Request
# 在GitHub/GitLab中创建PR
# 添加描述和相关文档链接
```

#### 分支命名规范
```bash
# 功能开发
feature/user-authentication
feature/payment-integration
feature/story-generation

# Bug修复
fix/login-error
fix/payment-validation
fix/ui-responsive-issue

# 配置更改
config/api-endpoint-update
config/build-optimization
config/security-enhancement

# 文档更新
docs/api-documentation
docs/deployment-guide
docs/user-manual
```

### 4.2 文档更新和同步机制

#### 团队文档协作
```bash
# 1. 文档变更通知
# 每次使用自动化脚本时，通知团队成员

# 2. 文档同步检查
git pull origin main  # 获取最新文档
./scripts/create-change-report.sh TEAM_SYNC docs

# 3. 冲突解决
# 如果DOCS_INDEX.md有冲突:
git checkout main -- DOCS_INDEX.md
./scripts/update-docs-index.sh [类型] [标题] [路径]

# 4. 定期文档审查
# 每周检查docs/reports/目录
# 归档过时文档
# 更新过期信息
```

#### 文档版本控制
```bash
# 1. 文档版本追踪
# DOCS_INDEX.md自动维护版本号
# 每次更新自动递增

# 2. 重要文档备份
# 自动化脚本会创建备份
# 手动备份重要配置文件

# 3. 文档恢复
# 如果需要恢复文档:
git log --oneline DOCS_INDEX.md  # 查看历史
git checkout [commit-hash] -- DOCS_INDEX.md
```

### 4.3 代码审查和质量保证流程

#### Pull Request审查流程
```bash
# 1. 创建PR时包含:
# - 变更描述
# - 相关文档链接
# - 测试结果截图
# - 部署验证结果

# 2. 审查检查清单:
# - [ ] 代码质量（类型检查、Lint通过）
# - [ ] 功能测试（本地和部署环境）
# - [ ] 文档更新（变更文档完整）
# - [ ] 安全检查（无敏感信息泄露）
# - [ ] 性能影响（构建大小、加载速度）

# 3. 审查通过后:
# - 合并到main分支
# - 删除功能分支
# - 更新项目状态
```

#### 质量保证自动化
```bash
# 1. 预提交检查（建议设置Git hooks）
#!/bin/bash
# .git/hooks/pre-commit
npm run type-check
npm run lint
npm run build

# 2. CI/CD集成（如果使用）
# - 自动运行测试
# - 自动部署到测试环境
# - 自动生成构建报告

# 3. 定期质量检查
# 每周运行完整测试套件
# 每月进行代码质量审查
# 每季度进行架构评估
```

### 4.4 发布和版本管理

#### 版本发布流程
```bash
# 1. 准备发布
./scripts/create-change-report.sh RELEASE_V1_1 other

# 2. 版本标记
git tag -a v1.1.0 -m "Release version 1.1.0"

# 3. 生产部署
./deploy-production.sh

# 4. 发布验证
# - 功能测试
# - 性能测试
# - 用户验收测试

# 5. 发布文档
./scripts/update-docs-index.sh "版本发布" "v1.1.0发布" \
  "docs/reports/progress/CHANGE_REPORT_2025-07-12_RELEASE_V1_1.md"

# 6. 发布通知
# 通知团队和用户新版本发布
```

---

## 📋 最佳实践总结

### 开发最佳实践
1. **始终在frontend-production目录中开发**
2. **使用自动化脚本管理文档**
3. **遵循Git工作流程和分支命名规范**
4. **每次变更都创建相应的文档记录**
5. **定期进行代码质量检查和清理**

### 文档管理最佳实践
1. **变更前先创建文档，变更后及时更新**
2. **使用标准化的模板和命名规范**
3. **定期归档和清理过时文档**
4. **保持DOCS_INDEX.md的准确性和完整性**

### 团队协作最佳实践
1. **及时同步代码和文档变更**
2. **遵循代码审查流程**
3. **保持沟通和协作透明度**
4. **定期进行团队技术分享和培训**

---

*指南版本: v1.0*
*创建时间: 2025-07-12*
*适用项目: StoryWeaver (frontend-production架构)*
*维护者: 开发团队*
