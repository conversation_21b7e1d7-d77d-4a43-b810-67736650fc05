# Durable Objects 快速开始指南

## 🚀 立即开始实施

### 第1步: 创建开发分支
```bash
cd /Users/<USER>/Desktop/Keepsake-dev
git checkout -b feature/durable-objects-phase1
git push -u origin feature/durable-objects-phase1
```

### 第2步: 创建必要的目录结构
```bash
mkdir -p backend/src/durable-objects
mkdir -p frontend/src/services/durableObjects
```

### 第3步: 创建AITaskQueueDO类
创建文件: `backend/src/durable-objects/AITaskQueueDO.ts`

```typescript
import { DurableObject } from '@cloudflare/workers-types';

export class AITaskQueueDO extends DurableObject {
  private sessions: Set<WebSocket> = new Set();
  
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    if (url.pathname === '/websocket') {
      return this.handleWebSocket(request);
    }
    
    if (url.pathname === '/generate' && request.method === 'POST') {
      return this.handleGenerate(request);
    }
    
    return new Response('Not found', { status: 404 });
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    server.accept();
    this.sessions.add(server);

    server.addEventListener('close', () => {
      this.sessions.delete(server);
    });

    return new Response(null, {
      status: 101,
      webSocket: client,
    });
  }

  private async handleGenerate(request: Request): Promise<Response> {
    const { storyId, characterName, theme, setting, style, voice } = await request.json();
    
    // 创建任务队列
    const tasks = [
      { 
        id: `${storyId}-text`, 
        type: 'text', 
        status: 'pending', 
        progress: 0,
        params: { characterName, theme, setting, style }
      },
      { 
        id: `${storyId}-image`, 
        type: 'image', 
        status: 'pending', 
        progress: 0,
        params: { style, theme, setting }
      },
      { 
        id: `${storyId}-audio`, 
        type: 'audio', 
        status: 'pending', 
        progress: 0,
        params: { voice }
      }
    ];

    // 存储任务状态
    await this.ctx.storage.put(`tasks:${storyId}`, tasks);
    
    // 异步执行任务
    this.executeTasksAsync(storyId, tasks);
    
    return Response.json({ 
      success: true, 
      storyId, 
      message: 'Story generation started',
      tasks: tasks.map(t => ({ id: t.id, type: t.type, status: t.status, progress: t.progress }))
    });
  }

  private async executeTasksAsync(storyId: string, tasks: any[]) {
    for (const task of tasks) {
      try {
        // 更新任务状态为运行中
        task.status = 'running';
        await this.updateTaskStatus(storyId, task);

        // 模拟AI生成过程
        await this.simulateAIGeneration(task);

        // 标记任务完成
        task.status = 'completed';
        task.progress = 100;
        await this.updateTaskStatus(storyId, task);

      } catch (error) {
        task.status = 'failed';
        task.error = error.message;
        await this.updateTaskStatus(storyId, task);
      }
    }

    // 所有任务完成后的处理
    await this.handleAllTasksCompleted(storyId);
  }

  private async simulateAIGeneration(task: any) {
    // 模拟AI生成过程，实际实现时替换为真实的AI调用
    const steps = 10;
    for (let i = 1; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, 500)); // 模拟处理时间
      task.progress = (i / steps) * 100;
      
      // 广播进度更新
      this.broadcast({
        type: 'taskProgress',
        taskId: task.id,
        progress: task.progress,
        status: task.status
      });
    }
  }

  private async updateTaskStatus(storyId: string, task: any) {
    // 更新存储中的任务状态
    const tasks = await this.ctx.storage.get(`tasks:${storyId}`) as any[];
    const taskIndex = tasks.findIndex(t => t.id === task.id);
    if (taskIndex !== -1) {
      tasks[taskIndex] = task;
      await this.ctx.storage.put(`tasks:${storyId}`, tasks);
    }

    // 广播状态更新
    this.broadcast({
      type: 'taskUpdate',
      storyId,
      task: {
        id: task.id,
        type: task.type,
        status: task.status,
        progress: task.progress,
        error: task.error
      },
      timestamp: Date.now()
    });
  }

  private async handleAllTasksCompleted(storyId: string) {
    this.broadcast({
      type: 'storyCompleted',
      storyId,
      timestamp: Date.now()
    });
  }

  private broadcast(message: any) {
    const messageStr = JSON.stringify(message);
    this.sessions.forEach(session => {
      try {
        session.send(messageStr);
      } catch (error) {
        // 移除断开的连接
        this.sessions.delete(session);
      }
    });
  }
}
```

### 第4步: 更新wrangler.toml
在 `backend/wrangler.toml` 中添加：

```toml
# 在文件末尾添加
[durable_objects]
bindings = [
  { name = "AI_TASK_QUEUE", class_name = "AITaskQueueDO" }
]

[[migrations]]
tag = "v1"
new_classes = ["AITaskQueueDO"]
```

### 第5步: 更新入口文件
修改 `backend/src/index.ts`，添加DO路由：

```typescript
// 在现有导入后添加
import { AITaskQueueDO } from './durable-objects/AITaskQueueDO';

// 在路由配置前添加DO处理
app.get('/ai-queue/:storyId/*', async (c) => {
  const storyId = c.req.param('storyId');
  const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
  const stub = c.env.AI_TASK_QUEUE.get(id);
  return stub.fetch(c.req.raw);
});

app.post('/ai-queue/:storyId/*', async (c) => {
  const storyId = c.req.param('storyId');
  const id = c.env.AI_TASK_QUEUE.idFromName(storyId);
  const stub = c.env.AI_TASK_QUEUE.get(id);
  return stub.fetch(c.req.raw);
});

// 导出DO类
export { AITaskQueueDO };
```

### 第6步: 创建前端客户端
创建文件: `frontend/src/services/durableObjects/storyGenerationClient.ts`

```typescript
export interface Task {
  id: string;
  type: 'text' | 'image' | 'audio';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

export class StoryGenerationClient {
  private ws: WebSocket | null = null;
  private listeners: Map<string, Function[]> = new Map();
  private storyId: string;

  constructor(storyId: string) {
    this.storyId = storyId;
  }

  connect() {
    const wsUrl = `wss://storyweaver-api.stawky.workers.dev/ai-queue/${this.storyId}/websocket`;
    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log('WebSocket connected for story:', this.storyId);
    };

    this.ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.notifyListeners(message.type, message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    this.ws.onclose = () => {
      console.log('WebSocket disconnected, attempting to reconnect...');
      setTimeout(() => this.connect(), 5000);
    };

    this.ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }

  async startGeneration(params: {
    characterName: string;
    theme: string;
    setting: string;
    style: string;
    voice: string;
  }) {
    const response = await fetch(`https://storyweaver-api.stawky.workers.dev/ai-queue/${this.storyId}/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        storyId: this.storyId,
        ...params
      })
    });

    return response.json();
  }

  onTaskUpdate(callback: (data: { storyId: string; task: Task }) => void) {
    this.addListener('taskUpdate', callback);
  }

  onTaskProgress(callback: (data: { taskId: string; progress: number; status: string }) => void) {
    this.addListener('taskProgress', callback);
  }

  onStoryCompleted(callback: (data: { storyId: string }) => void) {
    this.addListener('storyCompleted', callback);
  }

  private addListener(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  }

  private notifyListeners(event: string, data: any) {
    const callbacks = this.listeners.get(event) || [];
    callbacks.forEach(callback => callback(data));
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}
```

### 第7步: 测试部署
```bash
cd backend
npm run dev
```

### 第8步: 验证功能
1. 访问 `http://localhost:8787/health` 确认服务运行
2. 测试WebSocket连接
3. 测试任务生成API

## 📋 下一步
完成基础实现后，参考 `StoryWeaver_DurableObjects_实施计划.md` 继续完善功能。

## 🔧 调试技巧
- 使用 `wrangler tail` 查看实时日志
- 在DO中添加 `console.log` 进行调试
- 使用浏览器开发者工具监控WebSocket连接

---
*快速开始指南 - 预计完成时间: 2-3小时*
