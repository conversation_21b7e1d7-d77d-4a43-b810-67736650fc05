# 前端开发指南 🎨

> StoryWeaver 前端开发的完整指南

## 📋 概述

StoryWeaver 前端是一个基于 React 18 + TypeScript 的现代化单页应用，专为儿童故事创作平台设计。本指南将帮助您快速上手前端开发。

## 🏗️ 技术栈

### 核心技术
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的JavaScript
- **Vite**: 快速的构建工具
- **React Router v6**: 客户端路由

### 样式和UI
- **Tailwind CSS**: 实用优先的CSS框架
- **CSS Modules**: 模块化CSS
- **Framer Motion**: 动画库
- **Lucide React**: 图标库

### 状态管理
- **Zustand**: 轻量级状态管理
- **React Query**: 服务器状态管理
- **React Hook Form**: 表单管理

### 开发工具
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子
- **Lint-staged**: 暂存文件检查

## 📁 项目结构

```
frontend/
├── public/                     # 静态资源
│   ├── favicon.ico
│   ├── logo192.png
│   └── manifest.json
├── src/
│   ├── components/             # 可复用组件
│   │   ├── ui/                # 基础UI组件
│   │   │   ├── Button/
│   │   │   ├── Input/
│   │   │   ├── Modal/
│   │   │   └── index.ts
│   │   ├── layout/            # 布局组件
│   │   │   ├── Header/
│   │   │   ├── Footer/
│   │   │   ├── Sidebar/
│   │   │   └── Layout/
│   │   ├── features/          # 功能组件
│   │   │   ├── StoryCreator/
│   │   │   ├── StoryViewer/
│   │   │   ├── AudioPlayer/
│   │   │   ├── PaymentModal/
│   │   │   └── UserProfile/
│   │   └── common/            # 通用组件
│   │       ├── LoadingSpinner/
│   │       ├── ErrorBoundary/
│   │       └── ProtectedRoute/
│   ├── pages/                 # 页面组件
│   │   ├── HomePage/
│   │   ├── CreateStoryPage/
│   │   ├── MyStoriesPage/
│   │   ├── PricingPage/
│   │   ├── ProfilePage/
│   │   └── AuthPage/
│   ├── hooks/                 # 自定义Hooks
│   │   ├── useAuth.ts
│   │   ├── useStories.ts
│   │   ├── usePayment.ts
│   │   └── useLocalStorage.ts
│   ├── services/              # API服务
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   ├── stories.ts
│   │   ├── payments.ts
│   │   └── users.ts
│   ├── stores/                # Zustand状态管理
│   │   ├── authStore.ts
│   │   ├── storyStore.ts
│   │   ├── uiStore.ts
│   │   └── index.ts
│   ├── utils/                 # 工具函数
│   │   ├── constants.ts
│   │   ├── helpers.ts
│   │   ├── validation.ts
│   │   └── formatters.ts
│   ├── types/                 # TypeScript类型
│   │   ├── api.ts
│   │   ├── user.ts
│   │   ├── story.ts
│   │   └── common.ts
│   ├── styles/                # 全局样式
│   │   ├── globals.css
│   │   ├── components.css
│   │   └── animations.css
│   ├── assets/                # 静态资源
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   ├── App.tsx                # 根组件
│   ├── main.tsx               # 入口文件
│   └── vite-env.d.ts          # Vite类型声明
├── .env.example               # 环境变量示例
├── .eslintrc.js               # ESLint配置
├── .prettierrc                # Prettier配置
├── tailwind.config.js         # Tailwind配置
├── tsconfig.json              # TypeScript配置
├── vite.config.ts             # Vite配置
└── package.json               # 依赖配置
```

## 🎨 设计系统

### 色彩方案

```css
/* 主色调 */
:root {
  --primary-blue: #4A90E2;
  --primary-blue-light: #7BB3F0;
  --primary-blue-dark: #357ABD;
  
  --accent-orange: #FF8C42;
  --accent-orange-light: #FFB366;
  --accent-orange-dark: #E6792B;
  
  --soft-pink: #FFB6C1;
  --nature-green: #7ED321;
  
  --text-primary: #2C3E50;
  --text-secondary: #7F8C8D;
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
}
```

### 字体系统

```css
/* 字体家族 */
.font-primary {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.font-display {
  font-family: 'Poppins', sans-serif;
}

.font-mono {
  font-family: 'JetBrains Mono', monospace;
}

/* 字体大小 */
.text-xs { font-size: 0.75rem; }    /* 12px */
.text-sm { font-size: 0.875rem; }   /* 14px */
.text-base { font-size: 1rem; }     /* 16px */
.text-lg { font-size: 1.125rem; }   /* 18px */
.text-xl { font-size: 1.25rem; }    /* 20px */
.text-2xl { font-size: 1.5rem; }    /* 24px */
.text-3xl { font-size: 1.875rem; }  /* 30px */
.text-4xl { font-size: 2.25rem; }   /* 36px */
```

### 间距系统

```css
/* Tailwind 间距 */
.space-1 { margin: 0.25rem; }   /* 4px */
.space-2 { margin: 0.5rem; }    /* 8px */
.space-3 { margin: 0.75rem; }   /* 12px */
.space-4 { margin: 1rem; }      /* 16px */
.space-6 { margin: 1.5rem; }    /* 24px */
.space-8 { margin: 2rem; }      /* 32px */
.space-12 { margin: 3rem; }     /* 48px */
.space-16 { margin: 4rem; }     /* 64px */
```

## 🧩 组件开发

### 基础UI组件

#### Button 组件

```tsx
// src/components/ui/Button/Button.tsx
import React from 'react';
import { cn } from '@/utils/helpers';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  className,
  children,
  disabled,
  ...props
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variants = {
    primary: 'bg-primary-blue text-white hover:bg-primary-blue-dark focus:ring-primary-blue',
    secondary: 'bg-accent-orange text-white hover:bg-accent-orange-dark focus:ring-accent-orange',
    outline: 'border border-primary-blue text-primary-blue hover:bg-primary-blue hover:text-white',
    ghost: 'text-primary-blue hover:bg-primary-blue/10'
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        (disabled || loading) && 'opacity-50 cursor-not-allowed',
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );
};
```

#### Input 组件

```tsx
// src/components/ui/Input/Input.tsx
import React from 'react';
import { cn } from '@/utils/helpers';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  className,
  ...props
}) => {
  return (
    <div className="w-full">
      {label && (
        <label className="block text-sm font-medium text-text-primary mb-1">
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            {leftIcon}
          </div>
        )}
        
        <input
          className={cn(
            'block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm',
            'focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent',
            'placeholder-gray-400',
            leftIcon && 'pl-10',
            rightIcon && 'pr-10',
            error && 'border-red-500 focus:ring-red-500',
            className
          )}
          {...props}
        />
        
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            {rightIcon}
          </div>
        )}
      </div>
      
      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
      
      {helperText && !error && (
        <p className="mt-1 text-sm text-text-secondary">{helperText}</p>
      )}
    </div>
  );
};
```

### 功能组件

#### StoryCreator 组件

```tsx
// src/components/features/StoryCreator/StoryCreator.tsx
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button, Input, Select, Textarea } from '@/components/ui';
import { useStoryStore } from '@/stores/storyStore';
import { CreateStoryRequest } from '@/types/api';

interface StoryCreatorProps {
  onStoryCreated?: (storyId: string) => void;
}

export const StoryCreator: React.FC<StoryCreatorProps> = ({ onStoryCreated }) => {
  const [step, setStep] = useState(1);
  const { createStory, isCreating } = useStoryStore();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch
  } = useForm<CreateStoryRequest>();
  
  const onSubmit = async (data: CreateStoryRequest) => {
    try {
      const storyId = await createStory(data);
      onStoryCreated?.(storyId);
    } catch (error) {
      console.error('Failed to create story:', error);
    }
  };
  
  const renderStep1 = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-display font-bold text-text-primary">
        告诉我们你的主角
      </h2>
      
      <Input
        label="主角姓名"
        placeholder="例如：小明、艾莎、超人..."
        {...register('characterName', { required: '请输入主角姓名' })}
        error={errors.characterName?.message}
      />
      
      <Input
        label="主角年龄"
        type="number"
        placeholder="例如：5"
        {...register('characterAge', { 
          required: '请输入主角年龄',
          min: { value: 1, message: '年龄必须大于0' },
          max: { value: 18, message: '年龄不能超过18' }
        })}
        error={errors.characterAge?.message}
      />
      
      <div>
        <label className="block text-sm font-medium text-text-primary mb-2">
          性格特点 (选择2-3个)
        </label>
        <div className="grid grid-cols-2 gap-2">
          {['勇敢', '善良', '聪明', '调皮', '害羞', '好奇'].map((trait) => (
            <label key={trait} className="flex items-center space-x-2">
              <input
                type="checkbox"
                value={trait}
                {...register('characterTraits')}
                className="rounded border-gray-300 text-primary-blue focus:ring-primary-blue"
              />
              <span className="text-sm">{trait}</span>
            </label>
          ))}
        </div>
      </div>
      
      <Button onClick={() => setStep(2)} className="w-full">
        下一步
      </Button>
    </div>
  );
  
  const renderStep2 = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-display font-bold text-text-primary">
        选择故事设定
      </h2>
      
      <Select
        label="故事主题"
        {...register('theme', { required: '请选择故事主题' })}
        error={errors.theme?.message}
      >
        <option value="">请选择主题</option>
        <option value="adventure">冒险探险</option>
        <option value="friendship">友谊成长</option>
        <option value="family">家庭温情</option>
        <option value="magic">魔法奇幻</option>
        <option value="animal">动物世界</option>
        <option value="space">太空探索</option>
      </Select>
      
      <Select
        label="故事背景"
        {...register('setting', { required: '请选择故事背景' })}
        error={errors.setting?.message}
      >
        <option value="">请选择背景</option>
        <option value="forest">神秘森林</option>
        <option value="castle">古老城堡</option>
        <option value="ocean">深海世界</option>
        <option value="city">现代城市</option>
        <option value="village">乡村小镇</option>
        <option value="space">太空站</option>
      </Select>
      
      <div className="flex space-x-4">
        <Button variant="outline" onClick={() => setStep(1)} className="flex-1">
          上一步
        </Button>
        <Button onClick={() => setStep(3)} className="flex-1">
          下一步
        </Button>
      </div>
    </div>
  );
  
  const renderStep3 = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-display font-bold text-text-primary">
        个性化设置
      </h2>
      
      <Select
        label="插画风格"
        {...register('style', { required: '请选择插画风格' })}
        error={errors.style?.message}
      >
        <option value="">请选择风格</option>
        <option value="cartoon">卡通风格</option>
        <option value="watercolor">水彩画风</option>
        <option value="sketch">简笔画风</option>
        <option value="fantasy">奇幻风格</option>
        <option value="realistic">写实风格</option>
      </Select>
      
      <Select
        label="旁白声音"
        {...register('voice', { required: '请选择旁白声音' })}
        error={errors.voice?.message}
      >
        <option value="">请选择声音</option>
        <option value="female_warm">温暖女声</option>
        <option value="male_gentle">温和男声</option>
        <option value="child_cute">可爱童声</option>
        <option value="elderly_wise">智慧老者</option>
      </Select>
      
      <Textarea
        label="特殊要求 (可选)"
        placeholder="例如：希望故事中包含小狗、要有教育意义等..."
        {...register('specialRequests')}
        rows={3}
      />
      
      <div className="flex space-x-4">
        <Button variant="outline" onClick={() => setStep(2)} className="flex-1">
          上一步
        </Button>
        <Button 
          onClick={handleSubmit(onSubmit)} 
          loading={isCreating}
          className="flex-1"
        >
          开始创作故事
        </Button>
      </div>
    </div>
  );
  
  return (
    <div className="max-w-2xl mx-auto p-6">
      {/* 进度指示器 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {[1, 2, 3].map((stepNumber) => (
            <div
              key={stepNumber}
              className={`flex items-center justify-center w-8 h-8 rounded-full ${
                step >= stepNumber
                  ? 'bg-primary-blue text-white'
                  : 'bg-gray-200 text-gray-500'
              }`}
            >
              {stepNumber}
            </div>
          ))}
        </div>
        <div className="mt-2 h-2 bg-gray-200 rounded-full">
          <div
            className="h-full bg-primary-blue rounded-full transition-all duration-300"
            style={{ width: `${(step / 3) * 100}%` }}
          />
        </div>
      </div>
      
      {/* 步骤内容 */}
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
    </div>
  );
};
```

## 🔄 状态管理

### Zustand Store 示例

```tsx
// src/stores/authStore.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginCredentials } from '@/types/user';
import { authService } from '@/services/auth';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: async (credentials) => {
        set({ isLoading: true });
        try {
          const { user, tokens } = await authService.login(credentials);
          localStorage.setItem('accessToken', tokens.accessToken);
          localStorage.setItem('refreshToken', tokens.refreshToken);
          set({ user, isAuthenticated: true, isLoading: false });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },
      
      logout: () => {
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        set({ user: null, isAuthenticated: false });
      },
      
      refreshToken: async () => {
        try {
          const refreshToken = localStorage.getItem('refreshToken');
          if (!refreshToken) throw new Error('No refresh token');
          
          const { tokens } = await authService.refreshToken(refreshToken);
          localStorage.setItem('accessToken', tokens.accessToken);
          localStorage.setItem('refreshToken', tokens.refreshToken);
        } catch (error) {
          get().logout();
          throw error;
        }
      },
      
      updateUser: (userData) => {
        set((state) => ({
          user: state.user ? { ...state.user, ...userData } : null
        }));
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      })
    }
  )
);
```

## 🎯 路由配置

```tsx
// src/App.tsx
import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Layout } from '@/components/layout/Layout';
import { ProtectedRoute } from '@/components/common/ProtectedRoute';
import {
  HomePage,
  CreateStoryPage,
  MyStoriesPage,
  PricingPage,
  ProfilePage,
  AuthPage
} from '@/pages';

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/auth" element={<AuthPage />} />
            <Route path="/pricing" element={<PricingPage />} />
            
            <Route path="/create" element={
              <ProtectedRoute>
                <CreateStoryPage />
              </ProtectedRoute>
            } />
            
            <Route path="/my-stories" element={
              <ProtectedRoute>
                <MyStoriesPage />
              </ProtectedRoute>
            } />
            
            <Route path="/profile" element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            } />
          </Routes>
        </Layout>
      </BrowserRouter>
    </QueryClientProvider>
  );
}

export default App;
```

## 🔧 开发工具配置

### Vite 配置

```ts
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8787',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
});
```

### Tailwind 配置

```js
// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'primary-blue': '#4A90E2',
        'primary-blue-light': '#7BB3F0',
        'primary-blue-dark': '#357ABD',
        'accent-orange': '#FF8C42',
        'accent-orange-light': '#FFB366',
        'accent-orange-dark': '#E6792B',
        'soft-pink': '#FFB6C1',
        'nature-green': '#7ED321',
        'text-primary': '#2C3E50',
        'text-secondary': '#7F8C8D',
        'bg-primary': '#FFFFFF',
        'bg-secondary': '#F8F9FA',
      },
      fontFamily: {
        'primary': ['Inter', 'sans-serif'],
        'display': ['Poppins', 'sans-serif'],
        'mono': ['JetBrains Mono', 'monospace'],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

## 🧪 测试策略

### 组件测试

```tsx
// src/components/ui/Button/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders correctly', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button')).toHaveTextContent('Click me');
  });
  
  it('handles click events', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  it('shows loading state', () => {
    render(<Button loading>Loading</Button>);
    expect(screen.getByRole('button')).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
```

## 🚀 部署配置

### 环境变量

```bash
# .env.production
VITE_API_BASE_URL=https://api-storyweaver.jamintextiles.com
VITE_GOOGLE_CLIENT_ID=your-google-client-id
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
VITE_ENVIRONMENT=production
```

### 构建脚本

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "deploy": "npm run build && wrangler pages publish dist",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint src --ext ts,tsx --fix"
  }
}
```

## 📚 最佳实践

### 1. 组件设计原则
- 单一职责原则
- 可复用性
- 可测试性
- 可访问性

### 2. 性能优化
- 使用 React.memo 避免不必要的重渲染
- 懒加载路由和组件
- 图片优化和懒加载
- 代码分割

### 3. 用户体验
- 加载状态指示
- 错误边界处理
- 响应式设计
- 无障碍访问

### 4. 代码质量
- TypeScript 严格模式
- ESLint 和 Prettier
- 单元测试覆盖
- 代码审查

---

<div align="center">
  <p>让我们一起构建美好的用户体验 ✨</p>
  <p>Happy Coding! 🎨</p>
</div>