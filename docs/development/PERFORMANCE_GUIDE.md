# 性能优化指南 ⚡

> StoryWeaver 项目的性能优化策略和最佳实践

## 📋 概述

性能是用户体验的关键因素。本指南提供了全面的性能优化策略，涵盖前端、后端、数据库和基础设施等各个层面。

## 🎯 性能目标

### 核心指标

| 指标 | 目标值 | 当前值 | 状态 |
|------|--------|--------|------|
| **首屏加载时间** | < 2秒 | 1.8秒 | ✅ |
| **API响应时间** | < 500ms | 350ms | ✅ |
| **故事生成时间** | < 60秒 | 45秒 | ✅ |
| **图片加载时间** | < 3秒 | 2.5秒 | ✅ |
| **音频生成时间** | < 30秒 | 25秒 | ✅ |

### Web Vitals 目标

- **LCP (Largest Contentful Paint)**: < 2.5秒
- **FID (First Input Delay)**: < 100ms
- **CLS (Cumulative Layout Shift)**: < 0.1
- **FCP (First Contentful Paint)**: < 1.8秒
- **TTI (Time to Interactive)**: < 3.5秒

## 🚀 前端性能优化

### 1. 代码分割和懒加载

#### 路由级别的代码分割
```tsx
// src/App.tsx
import { lazy, Suspense } from 'react';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

// 懒加载页面组件
const HomePage = lazy(() => import('@/pages/HomePage'));
const CreateStoryPage = lazy(() => import('@/pages/CreateStoryPage'));
const MyStoriesPage = lazy(() => import('@/pages/MyStoriesPage'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/create" element={<CreateStoryPage />} />
        <Route path="/my-stories" element={<MyStoriesPage />} />
      </Routes>
    </Suspense>
  );
}
```

#### 组件级别的懒加载
```tsx
// 大型组件的懒加载
const StoryEditor = lazy(() => import('@/components/features/StoryEditor'));
const AudioPlayer = lazy(() => import('@/components/features/AudioPlayer'));

// 条件渲染时使用懒加载
const ConditionalComponent = ({ showEditor }: { showEditor: boolean }) => (
  <Suspense fallback={<div>Loading editor...</div>}>
    {showEditor && <StoryEditor />}
  </Suspense>
);
```

### 2. 资源优化

#### 图片优化
```tsx
// 响应式图片组件
interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  priority?: boolean;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  width,
  height,
  priority = false
}) => {
  return (
    <picture>
      <source
        srcSet={`${src}?format=webp&w=${width}&h=${height}`}
        type="image/webp"
      />
      <source
        srcSet={`${src}?format=avif&w=${width}&h=${height}`}
        type="image/avif"
      />
      <img
        src={`${src}?w=${width}&h=${height}`}
        alt={alt}
        width={width}
        height={height}
        loading={priority ? 'eager' : 'lazy'}
        decoding="async"
      />
    </picture>
  );
};
```

#### 字体优化
```css
/* 字体预加载 */
@font-face {
  font-family: 'Inter';
  src: url('/fonts/inter-var.woff2') format('woff2');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

/* 关键字体预加载 */
<link
  rel="preload"
  href="/fonts/inter-var.woff2"
  as="font"
  type="font/woff2"
  crossOrigin="anonymous"
/>
```

### 3. 缓存策略

#### Service Worker 缓存
```typescript
// public/sw.js
const CACHE_NAME = 'storyweaver-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/fonts/inter-var.woff2'
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // 缓存命中，返回缓存的资源
        if (response) {
          return response;
        }
        return fetch(event.request);
      })
  );
});
```

#### HTTP 缓存头
```typescript
// Cloudflare Workers 缓存配置
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    
    // 静态资源长期缓存
    if (url.pathname.startsWith('/assets/')) {
      const response = await fetch(request);
      const newResponse = new Response(response.body, response);
      newResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      return newResponse;
    }
    
    // API 响应短期缓存
    if (url.pathname.startsWith('/api/')) {
      const response = await fetch(request);
      const newResponse = new Response(response.body, response);
      newResponse.headers.set('Cache-Control', 'public, max-age=300');
      return newResponse;
    }
    
    return fetch(request);
  }
};
```

### 4. React 性能优化

#### 组件优化
```tsx
// 使用 React.memo 避免不必要的重渲染
const StoryCard = React.memo<StoryCardProps>(({ story, onSelect }) => {
  return (
    <div className="story-card" onClick={() => onSelect(story.id)}>
      <h3>{story.title}</h3>
      <p>{story.description}</p>
    </div>
  );
});

// 使用 useMemo 缓存计算结果
const ExpensiveComponent = ({ items }: { items: Item[] }) => {
  const expensiveValue = useMemo(() => {
    return items.reduce((acc, item) => acc + item.value, 0);
  }, [items]);
  
  return <div>Total: {expensiveValue}</div>;
};

// 使用 useCallback 缓存函数
const ParentComponent = () => {
  const [count, setCount] = useState(0);
  
  const handleClick = useCallback(() => {
    setCount(prev => prev + 1);
  }, []);
  
  return <ChildComponent onClick={handleClick} />;
};
```

#### 虚拟滚动
```tsx
// 大列表虚拟滚动
import { FixedSizeList as List } from 'react-window';

const VirtualizedStoryList = ({ stories }: { stories: Story[] }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <StoryCard story={stories[index]} />
    </div>
  );
  
  return (
    <List
      height={600}
      itemCount={stories.length}
      itemSize={120}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

## ⚙️ 后端性能优化

### 1. API 优化

#### 响应时间优化
```typescript
// 并行处理
const generateStoryWithAssets = async (request: CreateStoryRequest) => {
  const startTime = Date.now();
  
  try {
    // 并行生成故事文本和图片
    const [storyText, images] = await Promise.all([
      geminiService.generateStory(request),
      geminiService.generateImages(request.imagePrompts, request.style)
    ]);
    
    // 生成音频（依赖于故事文本）
    const audio = await geminiService.generateAudio(storyText.fullText, request.voice);
    
    const endTime = Date.now();
    console.log(`Story generation completed in ${endTime - startTime}ms`);
    
    return { storyText, images, audio };
  } catch (error) {
    console.error('Story generation failed:', error);
    throw error;
  }
};
```

#### 请求批处理
```typescript
// 批量API请求
class BatchProcessor {
  private queue: Array<{ request: any; resolve: Function; reject: Function }> = [];
  private processing = false;
  
  async add<T>(request: any): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push({ request, resolve, reject });
      this.process();
    });
  }
  
  private async process() {
    if (this.processing || this.queue.length === 0) return;
    
    this.processing = true;
    const batch = this.queue.splice(0, 10); // 批量处理10个请求
    
    try {
      const results = await this.processBatch(batch.map(item => item.request));
      batch.forEach((item, index) => item.resolve(results[index]));
    } catch (error) {
      batch.forEach(item => item.reject(error));
    }
    
    this.processing = false;
    if (this.queue.length > 0) {
      this.process();
    }
  }
  
  private async processBatch(requests: any[]) {
    // 批量处理逻辑
    return Promise.all(requests.map(request => this.processRequest(request)));
  }
}
```

### 2. 数据库优化

#### 查询优化
```sql
-- 索引优化
CREATE INDEX CONCURRENTLY idx_stories_user_created 
ON stories(user_id, created_at DESC);

CREATE INDEX CONCURRENTLY idx_stories_status_created 
ON stories(status, created_at DESC) 
WHERE status = 'completed';

-- 查询优化
-- ✅ 好的查询：使用索引
SELECT s.id, s.title, s.created_at, u.name as author
FROM stories s
JOIN users u ON s.user_id = u.id
WHERE s.user_id = $1 
  AND s.status = 'completed'
ORDER BY s.created_at DESC
LIMIT 20;

-- ❌ 避免：全表扫描
SELECT * FROM stories 
WHERE title LIKE '%adventure%'
ORDER BY created_at DESC;
```

#### 连接池优化
```typescript
// 数据库连接池配置
const dbConfig = {
  // 连接池大小
  max: 20,
  min: 5,
  
  // 连接超时
  acquireTimeoutMillis: 30000,
  createTimeoutMillis: 30000,
  destroyTimeoutMillis: 5000,
  idleTimeoutMillis: 30000,
  reapIntervalMillis: 1000,
  createRetryIntervalMillis: 100,
  
  // 连接验证
  validate: (connection: any) => {
    return connection.isConnected();
  }
};
```

### 3. 缓存策略

#### Redis 缓存
```typescript
// 多层缓存策略
class CacheService {
  private redis: Redis;
  private memoryCache: Map<string, any> = new Map();
  
  async get<T>(key: string): Promise<T | null> {
    // L1: 内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // L2: Redis 缓存
    const redisValue = await this.redis.get(key);
    if (redisValue) {
      const parsed = JSON.parse(redisValue);
      this.memoryCache.set(key, parsed);
      return parsed;
    }
    
    return null;
  }
  
  async set<T>(key: string, value: T, ttl: number = 3600): Promise<void> {
    // 设置内存缓存
    this.memoryCache.set(key, value);
    
    // 设置 Redis 缓存
    await this.redis.setex(key, ttl, JSON.stringify(value));
  }
  
  async invalidate(pattern: string): Promise<void> {
    // 清除内存缓存
    for (const key of this.memoryCache.keys()) {
      if (key.includes(pattern)) {
        this.memoryCache.delete(key);
      }
    }
    
    // 清除 Redis 缓存
    const keys = await this.redis.keys(`*${pattern}*`);
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}
```

#### CDN 缓存
```typescript
// Cloudflare R2 + CDN 配置
const uploadToR2WithCDN = async (file: File, key: string) => {
  // 上传到 R2
  await env.ASSETS.put(key, file.stream(), {
    httpMetadata: {
      contentType: file.type,
      cacheControl: 'public, max-age=31536000, immutable'
    }
  });
  
  // 返回 CDN URL
  return `https://cdn.storyweaver.com/${key}`;
};
```

## 🗄️ 存储优化

### 1. 文件存储优化

#### 图片压缩和格式优化
```typescript
// 图片处理服务
class ImageOptimizationService {
  async optimizeImage(imageBuffer: Buffer, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg';
  }) {
    const { width, height, quality = 80, format = 'webp' } = options;
    
    let pipeline = sharp(imageBuffer);
    
    // 调整尺寸
    if (width || height) {
      pipeline = pipeline.resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      });
    }
    
    // 格式转换和压缩
    switch (format) {
      case 'webp':
        pipeline = pipeline.webp({ quality });
        break;
      case 'avif':
        pipeline = pipeline.avif({ quality });
        break;
      case 'jpeg':
        pipeline = pipeline.jpeg({ quality });
        break;
    }
    
    return pipeline.toBuffer();
  }
  
  async generateResponsiveImages(originalBuffer: Buffer) {
    const sizes = [400, 800, 1200, 1600];
    const formats = ['webp', 'avif', 'jpeg'];
    
    const variants = [];
    
    for (const size of sizes) {
      for (const format of formats) {
        const optimized = await this.optimizeImage(originalBuffer, {
          width: size,
          format: format as any,
          quality: 80
        });
        
        variants.push({
          size,
          format,
          buffer: optimized,
          url: `images/story-${size}.${format}`
        });
      }
    }
    
    return variants;
  }
}
```

### 2. 音频优化

#### 音频压缩
```typescript
// 音频处理服务
class AudioOptimizationService {
  async optimizeAudio(audioBuffer: Buffer, options: {
    bitrate?: number;
    format?: 'mp3' | 'aac' | 'opus';
  }) {
    const { bitrate = 128, format = 'mp3' } = options;
    
    // 使用 FFmpeg 进行音频压缩
    const outputBuffer = await this.processWithFFmpeg(audioBuffer, {
      codec: format,
      bitrate: `${bitrate}k`,
      channels: 2,
      sampleRate: 44100
    });
    
    return outputBuffer;
  }
  
  async generateAudioVariants(originalBuffer: Buffer) {
    return Promise.all([
      // 高质量版本
      this.optimizeAudio(originalBuffer, { bitrate: 192, format: 'aac' }),
      // 标准质量版本
      this.optimizeAudio(originalBuffer, { bitrate: 128, format: 'mp3' }),
      // 低质量版本（移动端）
      this.optimizeAudio(originalBuffer, { bitrate: 64, format: 'opus' })
    ]);
  }
}
```

## 📊 监控和分析

### 1. 性能监控

#### 前端性能监控
```typescript
// Web Vitals 监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

const sendToAnalytics = (metric: any) => {
  // 发送到分析服务
  fetch('/api/analytics/performance', {
    method: 'POST',
    body: JSON.stringify(metric),
    headers: { 'Content-Type': 'application/json' }
  });
};

// 监控核心指标
getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);

// 自定义性能指标
const measureCustomMetrics = () => {
  // 故事加载时间
  performance.mark('story-load-start');
  
  // 在故事加载完成后
  performance.mark('story-load-end');
  performance.measure('story-load-time', 'story-load-start', 'story-load-end');
  
  const measure = performance.getEntriesByName('story-load-time')[0];
  sendToAnalytics({
    name: 'story-load-time',
    value: measure.duration,
    timestamp: Date.now()
  });
};
```

#### 后端性能监控
```typescript
// API 性能中间件
const performanceMiddleware = (c: Context, next: Next) => {
  const start = Date.now();
  
  return next().finally(() => {
    const duration = Date.now() - start;
    
    // 记录性能指标
    c.env.ANALYTICS.writeDataPoint({
      measurement: 'api_performance',
      tags: {
        endpoint: c.req.path,
        method: c.req.method,
        status: c.res.status
      },
      fields: {
        duration,
        timestamp: Date.now()
      }
    });
    
    // 慢查询告警
    if (duration > 1000) {
      console.warn(`Slow API call: ${c.req.method} ${c.req.path} took ${duration}ms`);
    }
  });
};
```

### 2. 性能分析工具

#### Lighthouse CI 集成
```yaml
# .github/workflows/lighthouse.yml
name: Lighthouse CI
on: [push, pull_request]
jobs:
  lighthouse:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Use Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Build
        run: npm run build
      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.9.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

#### Bundle 分析
```json
{
  "scripts": {
    "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.json",
    "build:analyze": "vite build --mode analyze"
  }
}
```

## 🚀 部署优化

### 1. Cloudflare 优化

#### Workers 配置
```toml
# wrangler.toml
[env.production]
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# CPU 时间限制
[limits]
cpu_ms = 30000

# 缓存配置
[site]
bucket = "./dist"
entry-point = "workers-site"

# 路由优化
[[routes]]
pattern = "storyweaver.com/api/*"
zone_name = "storyweaver.com"
```

#### 边缘缓存策略
```typescript
// 智能缓存策略
const cacheStrategy = (request: Request): CacheStrategy => {
  const url = new URL(request.url);
  
  // 静态资源：长期缓存
  if (url.pathname.match(/\.(js|css|png|jpg|jpeg|gif|svg|woff2?)$/)) {
    return {
      browserTTL: 31536000, // 1年
      edgeTTL: 31536000,
      cacheEverything: true
    };
  }
  
  // API 响应：短期缓存
  if (url.pathname.startsWith('/api/')) {
    return {
      browserTTL: 300, // 5分钟
      edgeTTL: 600,    // 10分钟
      cacheByDeviceType: true
    };
  }
  
  // HTML 页面：动态缓存
  return {
    browserTTL: 0,
    edgeTTL: 3600, // 1小时
    cacheByDeviceType: true
  };
};
```

### 2. 预加载策略

#### 关键资源预加载
```html
<!-- 关键CSS -->
<link rel="preload" href="/css/critical.css" as="style" onload="this.onload=null;this.rel='stylesheet'">

<!-- 关键字体 -->
<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin>

<!-- 关键JavaScript -->
<link rel="preload" href="/js/app.js" as="script">

<!-- DNS 预解析 -->
<link rel="dns-prefetch" href="//api.storyweaver.com">
<link rel="dns-prefetch" href="//cdn.storyweaver.com">
```

#### 智能预取
```typescript
// 智能预取服务
class IntelligentPrefetch {
  private observer: IntersectionObserver;
  private prefetchedUrls = new Set<string>();
  
  constructor() {
    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            this.prefetchLink(entry.target as HTMLAnchorElement);
          }
        });
      },
      { rootMargin: '100px' }
    );
  }
  
  observe(links: NodeListOf<HTMLAnchorElement>) {
    links.forEach(link => this.observer.observe(link));
  }
  
  private prefetchLink(link: HTMLAnchorElement) {
    const href = link.href;
    
    if (this.prefetchedUrls.has(href)) return;
    if (!this.shouldPrefetch(href)) return;
    
    this.prefetchedUrls.add(href);
    
    // 预取页面
    const linkElement = document.createElement('link');
    linkElement.rel = 'prefetch';
    linkElement.href = href;
    document.head.appendChild(linkElement);
  }
  
  private shouldPrefetch(url: string): boolean {
    // 只预取同域名链接
    return url.startsWith(window.location.origin);
  }
}
```

## 📈 性能测试

### 1. 负载测试

#### K6 负载测试
```javascript
// load-test.js
import http from 'k6/http';
import { check, sleep } from 'k6';

export let options = {
  stages: [
    { duration: '2m', target: 100 }, // 2分钟内增加到100用户
    { duration: '5m', target: 100 }, // 保持100用户5分钟
    { duration: '2m', target: 200 }, // 2分钟内增加到200用户
    { duration: '5m', target: 200 }, // 保持200用户5分钟
    { duration: '2m', target: 0 },   // 2分钟内减少到0用户
  ],
};

export default function () {
  // 测试首页加载
  let response = http.get('https://storyweaver.com');
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2s': (r) => r.timings.duration < 2000,
  });
  
  // 测试API响应
  response = http.get('https://api.storyweaver.com/api/health');
  check(response, {
    'API status is 200': (r) => r.status === 200,
    'API response time < 500ms': (r) => r.timings.duration < 500,
  });
  
  sleep(1);
}
```

### 2. 性能基准测试

#### 自动化性能测试
```typescript
// performance-test.ts
import puppeteer from 'puppeteer';

const runPerformanceTest = async () => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  
  // 启用性能监控
  await page.tracing.start({ path: 'trace.json' });
  
  // 导航到页面
  const response = await page.goto('https://storyweaver.com', {
    waitUntil: 'networkidle2'
  });
  
  // 获取性能指标
  const metrics = await page.metrics();
  const performanceEntries = await page.evaluate(() => {
    return JSON.stringify(performance.getEntriesByType('navigation'));
  });
  
  // 停止追踪
  await page.tracing.stop();
  
  // 分析结果
  console.log('Performance Metrics:', {
    loadTime: metrics.TaskDuration,
    domContentLoaded: JSON.parse(performanceEntries)[0].domContentLoadedEventEnd,
    firstContentfulPaint: await page.evaluate(() => {
      return performance.getEntriesByName('first-contentful-paint')[0]?.startTime;
    })
  });
  
  await browser.close();
};
```

## 🔧 性能调优工具

### 1. 开发工具

#### Webpack Bundle Analyzer
```bash
# 安装分析工具
npm install --save-dev webpack-bundle-analyzer

# 分析构建结果
npm run build:analyze
```

#### Chrome DevTools 性能分析
```typescript
// 性能标记
performance.mark('component-render-start');
// 组件渲染
performance.mark('component-render-end');
performance.measure('component-render', 'component-render-start', 'component-render-end');
```

### 2. 监控工具

#### 实时性能监控
```typescript
// 性能监控服务
class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // 保持最近100个值
    if (values.length > 100) {
      values.shift();
    }
    
    // 检查性能阈值
    this.checkThresholds(name, value);
  }
  
  private checkThresholds(name: string, value: number) {
    const thresholds = {
      'api-response-time': 1000,
      'page-load-time': 3000,
      'story-generation-time': 60000
    };
    
    const threshold = thresholds[name as keyof typeof thresholds];
    if (threshold && value > threshold) {
      console.warn(`Performance threshold exceeded: ${name} = ${value}ms (threshold: ${threshold}ms)`);
    }
  }
  
  getStats(name: string) {
    const values = this.metrics.get(name) || [];
    if (values.length === 0) return null;
    
    const sorted = [...values].sort((a, b) => a - b);
    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: values.reduce((a, b) => a + b, 0) / values.length,
      p50: sorted[Math.floor(sorted.length * 0.5)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)]
    };
  }
}
```

---

<div align="center">
  <p>性能优化是一个持续的过程 ⚡</p>
  <p>让我们一起打造极致的用户体验！</p>
</div>