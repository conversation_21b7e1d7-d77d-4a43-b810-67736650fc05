# StoryWeaver 开发指南 - 第二部分

## 3. 新功能开发规划

### 3.1 开发优先级

```
优先级排序:
1. 🔥 邮箱注册功能 (基础功能，影响用户转化)
2. 🔥 国际化支持 (市场扩展，技术基础)
3. 🔥 教育版权益 (新收入来源，差异化竞争)
```

### 3.2 功能依赖关系

```mermaid
graph TD
    A[邮箱注册功能] --> B[用户系统增强]
    C[国际化支持] --> D[多语言界面]
    C --> E[多语言内容生成]
    B --> F[教育版权益系统]
    D --> F
    F --> G[批量用户管理]
    F --> H[教育版定价]
```

### 3.3 技术风险评估

#### 3.3.1 高风险项
```
🔴 多语言AI内容生成
风险: Gemini API多语言支持不稳定
缓解: 分阶段实现，先支持界面国际化

🔴 教育版用户权限复杂度
风险: 权限系统设计复杂，影响性能
缓解: 采用RBAC模型，分层设计

🔴 数据库迁移
风险: 现有数据结构变更影响线上服务
缓解: 向后兼容设计，灰度发布
```

#### 3.3.2 中风险项
```
🟡 邮箱验证服务稳定性
风险: 第三方邮件服务可能不稳定
缓解: 多服务商备份，本地队列机制

🟡 国际化资源管理
风险: 翻译质量和维护成本
缓解: 专业翻译服务，自动化工具
```

---

## 4. 国际化支持实现

### 4.1 技术架构设计

#### 4.1.1 前端国际化架构
```typescript
// 国际化架构设计
interface I18nConfig {
  defaultLanguage: 'zh-CN' | 'en-US';
  supportedLanguages: Language[];
  fallbackLanguage: 'zh-CN';
  loadPath: string;
  detection: {
    order: ['localStorage', 'navigator', 'htmlTag'];
    caches: ['localStorage'];
  };
}

interface Language {
  code: string;
  name: string;
  nativeName: string;
  flag: string;
  rtl: boolean;
}

// 支持的语言列表
const SUPPORTED_LANGUAGES: Language[] = [
  {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false
  },
  {
    code: 'en-US',
    name: 'English (US)',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false
  }
];
```

#### 4.1.2 后端国际化支持
```typescript
// API响应国际化
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  messageKey?: string; // 用于前端国际化
  error?: {
    code: string;
    message: string;
    messageKey?: string;
  };
}

// 多语言内容模型
interface MultiLanguageContent {
  zh: string;
  en: string;
  defaultLang: 'zh' | 'en';
}

// 故事生成请求增强
interface CreateStoryRequest {
  // 现有字段...
  language: 'zh-CN' | 'en-US';
  characterName: string;
  characterAge: number;
  characterTraits: string[];
  theme: string;
  setting: string;
  style: StoryStyle;
  voice: VoiceType;
}
```

### 4.2 实现步骤

#### 4.2.1 第一阶段：基础架构 (1周)
```
任务清单:
□ 安装和配置 react-i18next
□ 创建语言资源文件结构
□ 实现语言切换组件
□ 配置语言检测和存储
□ 更新构建配置

文件结构:
frontend/src/
├── i18n/
│   ├── index.ts              # i18n配置
│   ├── resources/
│   │   ├── zh-CN/
│   │   │   ├── common.json   # 通用翻译
│   │   │   ├── pages.json    # 页面翻译
│   │   │   ├── components.json # 组件翻译
│   │   │   └── errors.json   # 错误信息翻译
│   │   └── en-US/
│   │       ├── common.json
│   │       ├── pages.json
│   │       ├── components.json
│   │       └── errors.json
│   └── types.ts              # 类型定义
```

#### 4.2.2 第二阶段：界面国际化 (2周)
```
任务清单:
□ 翻译所有静态文本
□ 更新所有React组件
□ 实现动态文本格式化
□ 处理日期、数字本地化
□ 测试语言切换功能

重点组件:
- Header/Footer导航
- 故事创作流程
- 用户设置页面
- 支付和定价页面
- 帮助和文档页面
```

#### 4.2.3 第三阶段：内容国际化 (2周)
```
任务清单:
□ 扩展AI提示词支持多语言
□ 实现多语言故事生成
□ 添加语言特定的语音选项
□ 优化多语言SEO
□ 性能优化和测试

技术要点:
- Gemini API多语言提示词
- 语音合成语言适配
- 图像生成文本本地化
- 内容安全检查多语言支持
```

### 4.3 关键代码实现

#### 4.3.1 i18n配置文件
```typescript
// frontend/src/i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// 导入翻译资源
import zhCN from './resources/zh-CN';
import enUS from './resources/en-US';

const resources = {
  'zh-CN': zhCN,
  'en-US': enUS,
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'zh-CN',
    defaultNS: 'common',
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
      lookupLocalStorage: 'i18nextLng',
    },

    interpolation: {
      escapeValue: false,
    },

    react: {
      useSuspense: false,
    },
  });

export default i18n;
```

#### 4.3.2 语言切换组件
```typescript
// frontend/src/components/common/LanguageSwitcher.tsx
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Globe } from 'lucide-react';

const LANGUAGES = [
  { code: 'zh-CN', name: '中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', flag: '🇺🇸' },
];

export const LanguageSwitcher: React.FC = () => {
  const { i18n, t } = useTranslation();

  const handleLanguageChange = (langCode: string) => {
    i18n.changeLanguage(langCode);
  };

  return (
    <div className="relative group">
      <button className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100">
        <Globe className="w-4 h-4" />
        <span className="text-sm">
          {LANGUAGES.find(lang => lang.code === i18n.language)?.flag}
        </span>
      </button>
      
      <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all">
        {LANGUAGES.map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className={`w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${
              i18n.language === language.code ? 'bg-blue-50 text-blue-600' : ''
            }`}
          >
            <span className="text-lg">{language.flag}</span>
            <span className="text-sm font-medium">{language.name}</span>
          </button>
        ))}
      </div>
    </div>
  );
};
```

#### 4.3.3 翻译资源示例
```json
// frontend/src/i18n/resources/zh-CN/common.json
{
  "navigation": {
    "home": "首页",
    "create": "创作故事",
    "myStories": "我的故事",
    "pricing": "定价",
    "help": "帮助",
    "login": "登录",
    "logout": "退出"
  },
  "buttons": {
    "save": "保存",
    "cancel": "取消",
    "submit": "提交",
    "next": "下一步",
    "previous": "上一步",
    "finish": "完成"
  },
  "messages": {
    "loading": "加载中...",
    "success": "操作成功",
    "error": "操作失败",
    "confirm": "确认操作"
  }
}

// frontend/src/i18n/resources/en-US/common.json
{
  "navigation": {
    "home": "Home",
    "create": "Create Story",
    "myStories": "My Stories",
    "pricing": "Pricing",
    "help": "Help",
    "login": "Login",
    "logout": "Logout"
  },
  "buttons": {
    "save": "Save",
    "cancel": "Cancel",
    "submit": "Submit",
    "next": "Next",
    "previous": "Previous",
    "finish": "Finish"
  },
  "messages": {
    "loading": "Loading...",
    "success": "Operation successful",
    "error": "Operation failed",
    "confirm": "Confirm operation"
  }
}
```

### 4.4 多语言AI内容生成

#### 4.4.1 Gemini API多语言支持
```typescript
// backend/src/services/gemini.ts (增强版)
export class GeminiService {
  /**
   * 生成多语言故事
   */
  async generateStory(request: CreateStoryRequest): Promise<GeminiStoryResponse> {
    const prompt = this.buildMultiLanguageStoryPrompt(request);
    
    // 根据语言选择不同的模型配置
    const modelConfig = this.getLanguageModelConfig(request.language);
    
    try {
      const response = await fetch(`${this.baseUrl}/models/gemini-2.5-flash:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }],
          generationConfig: {
            ...modelConfig,
            temperature: 0.8,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 2048,
          },
          safetySettings: this.getSafetySettings()
        })
      });

      const data = await response.json();
      return this.parseStoryResponse(data.candidates[0].content.parts[0].text);
    } catch (error) {
      console.error('Story generation failed:', error);
      throw new Error(`故事生成失败: ${error.message}`);
    }
  }

  /**
   * 构建多语言故事提示词
   */
  private buildMultiLanguageStoryPrompt(request: CreateStoryRequest): string {
    const prompts = {
      'zh-CN': this.buildChinesePrompt(request),
      'en-US': this.buildEnglishPrompt(request)
    };

    return prompts[request.language] || prompts['zh-CN'];
  }

  private buildChinesePrompt(request: CreateStoryRequest): string {
    return `
你是一个专业的儿童故事创作者。请根据以下信息创作一个适合${request.characterAge}岁儿童的中文故事：

主角信息：
- 姓名：${request.characterName}
- 年龄：${request.characterAge}岁
- 性格特征：${request.characterTraits.join('、')}

故事设定：
- 主题：${request.theme}
- 场景：${request.setting}

创作要求：
1. 使用简体中文
2. 故事长度：8-10页，每页60-100字
3. 内容积极正面，富有教育意义
4. 语言简单易懂，适合${request.characterAge}岁儿童理解
5. 包含冒险、友谊、成长、学习等正面元素
6. 结局温馨美好，传递正能量

输出格式要求（严格按照JSON格式）：
{
  "title": "故事标题",
  "pages": [
    {
      "pageNumber": 1,
      "text": "第一页的故事文字内容",
      "imagePrompt": "详细的插图描述，用于AI绘图"
    }
  ],
  "fullText": "完整的故事文本，用于语音合成"
}
`;
  }

  private buildEnglishPrompt(request: CreateStoryRequest): string {
    return `
You are a professional children's story writer. Please create an English story suitable for ${request.characterAge}-year-old children based on the following information:

Character Information:
- Name: ${request.characterName}
- Age: ${request.characterAge} years old
- Personality traits: ${request.characterTraits.join(', ')}

Story Setting:
- Theme: ${request.theme}
- Setting: ${request.setting}

Writing Requirements:
1. Use English language
2. Story length: 8-10 pages, 60-100 words per page
3. Positive and educational content
4. Simple language appropriate for ${request.characterAge}-year-old children
5. Include elements of adventure, friendship, growth, and learning
6. Warm and positive ending with good values

Output Format (strict JSON format):
{
  "title": "Story Title",
  "pages": [
    {
      "pageNumber": 1,
      "text": "Story text content for the first page",
      "imagePrompt": "Detailed illustration description for AI image generation"
    }
  ],
  "fullText": "Complete story text for voice synthesis"
}
`;
  }

  /**
   * 获取语言特定的模型配置
   */
  private getLanguageModelConfig(language: string) {
    const configs = {
      'zh-CN': {
        // 中文优化配置
        candidateCount: 1,
        stopSequences: ["。", "！", "？"]
      },
      'en-US': {
        // 英文优化配置
        candidateCount: 1,
        stopSequences: [".", "!", "?"]
      }
    };

    return configs[language] || configs['zh-CN'];
  }
}
```

---

*本文档第二部分完成，下一部分将详细介绍邮箱注册功能的实现...*