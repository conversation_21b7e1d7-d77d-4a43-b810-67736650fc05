# StoryWeaver 开发指南 - 第三部分

## 5. 邮箱注册功能

### 5.1 功能需求分析

#### 5.1.1 核心功能
```
✅ 邮箱+密码注册
✅ 邮箱验证机制
✅ 密码重置功能
✅ 登录状态管理
✅ 与Google登录并存
✅ 安全性保障
```

#### 5.1.2 用户流程设计
```mermaid
graph TD
    A[用户访问注册页] --> B[选择注册方式]
    B --> C[Google登录]
    B --> D[邮箱注册]
    D --> E[填写注册信息]
    E --> F[提交注册]
    F --> G[发送验证邮件]
    G --> H[用户点击验证链接]
    H --> I[账户激活成功]
    I --> J[自动登录]
    
    K[忘记密码] --> L[输入邮箱]
    L --> M[发送重置邮件]
    M --> N[点击重置链接]
    N --> O[设置新密码]
    O --> P[密码重置成功]
```

### 5.2 数据库设计

#### 5.2.1 用户表扩展
```sql
-- 扩展现有用户表
ALTER TABLE users ADD COLUMN password_hash TEXT;
ALTER TABLE users ADD COLUMN email_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN verification_token TEXT;
ALTER TABLE users ADD COLUMN verification_expires_at TEXT;
ALTER TABLE users ADD COLUMN password_reset_token TEXT;
ALTER TABLE users ADD COLUMN password_reset_expires_at TEXT;
ALTER TABLE users ADD COLUMN auth_provider TEXT DEFAULT 'email'; -- 'email' | 'google'
ALTER TABLE users ADD COLUMN last_login_at TEXT;
ALTER TABLE users ADD COLUMN login_attempts INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN locked_until TEXT;

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_verification_token ON users(verification_token);
CREATE INDEX IF NOT EXISTS idx_users_password_reset_token ON users(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON users(auth_provider);
```

#### 5.2.2 邮件日志表
```sql
-- 邮件发送日志表
CREATE TABLE IF NOT EXISTS email_logs (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    email_type TEXT NOT NULL, -- 'verification' | 'password_reset' | 'welcome'
    recipient_email TEXT NOT NULL,
    subject TEXT NOT NULL,
    status TEXT NOT NULL, -- 'pending' | 'sent' | 'delivered' | 'failed'
    provider TEXT NOT NULL, -- 'sendgrid' | 'resend'
    provider_message_id TEXT,
    error_message TEXT,
    sent_at TEXT,
    delivered_at TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_email_logs_user_id ON email_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_email_logs_email_type ON email_logs(email_type);
CREATE INDEX IF NOT EXISTS idx_email_logs_status ON email_logs(status);
CREATE INDEX IF NOT EXISTS idx_email_logs_created_at ON email_logs(created_at);
```

### 5.3 后端API设计

#### 5.3.1 认证相关接口
```typescript
// backend/src/types/auth.ts
interface EmailRegisterRequest {
  email: string;
  password: string;
  name: string;
  agreeToTerms: boolean;
  language?: string;
}

interface EmailLoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface VerifyEmailRequest {
  token: string;
}

interface ForgotPasswordRequest {
  email: string;
  language?: string;
}

interface ResetPasswordRequest {
  token: string;
  newPassword: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// API响应类型
interface AuthResponse {
  success: boolean;
  data?: {
    user: User;
    tokens: AuthTokens;
  };
  message?: string;
  error?: string;
}
```

#### 5.3.2 路由设计
```typescript
// backend/src/handlers/auth.ts
import { Hono } from 'hono';
import { AuthService } from '../services/auth';
import { EmailService } from '../services/email';
import { validateEmailRegister, validateEmailLogin } from '../utils/validation';

const auth = new Hono();

// 邮箱注册
auth.post('/register/email', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = validateEmailRegister(body);
    
    const authService = new AuthService(c.env);
    const result = await authService.registerWithEmail(validatedData);
    
    return c.json({
      success: true,
      data: result,
      message: 'Registration successful. Please check your email for verification.'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 400);
  }
});

// 邮箱登录
auth.post('/login/email', async (c) => {
  try {
    const body = await c.req.json();
    const validatedData = validateEmailLogin(body);
    
    const authService = new AuthService(c.env);
    const result = await authService.loginWithEmail(validatedData);
    
    return c.json({
      success: true,
      data: result
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 401);
  }
});

// 邮箱验证
auth.post('/verify-email', async (c) => {
  try {
    const { token } = await c.req.json();
    
    const authService = new AuthService(c.env);
    const result = await authService.verifyEmail(token);
    
    return c.json({
      success: true,
      data: result,
      message: 'Email verified successfully'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 400);
  }
});

// 忘记密码
auth.post('/forgot-password', async (c) => {
  try {
    const { email, language } = await c.req.json();
    
    const authService = new AuthService(c.env);
    await authService.sendPasswordResetEmail(email, language);
    
    return c.json({
      success: true,
      message: 'Password reset email sent'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 400);
  }
});

// 重置密码
auth.post('/reset-password', async (c) => {
  try {
    const { token, newPassword } = await c.req.json();
    
    const authService = new AuthService(c.env);
    const result = await authService.resetPassword(token, newPassword);
    
    return c.json({
      success: true,
      data: result,
      message: 'Password reset successfully'
    });
  } catch (error) {
    return c.json({
      success: false,
      error: error.message
    }, 400);
  }
});

export default auth;
```

### 5.4 核心服务实现

#### 5.4.1 认证服务
```typescript
// backend/src/services/auth.ts
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { EmailService } from './email';
import { UserRepository } from '../repositories/user';

export class AuthService {
  private userRepo: UserRepository;
  private emailService: EmailService;
  
  constructor(env: any) {
    this.userRepo = new UserRepository(env.DB);
    this.emailService = new EmailService(env);
  }

  /**
   * 邮箱注册
   */
  async registerWithEmail(data: EmailRegisterRequest): Promise<AuthResponse['data']> {
    // 检查邮箱是否已存在
    const existingUser = await this.userRepo.findByEmail(data.email);
    if (existingUser) {
      throw new Error('Email already registered');
    }

    // 密码加密
    const passwordHash = await bcrypt.hash(data.password, 12);
    
    // 生成验证令牌
    const verificationToken = uuidv4();
    const verificationExpiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时

    // 创建用户
    const userId = uuidv4();
    const user = await this.userRepo.create({
      id: userId,
      email: data.email,
      name: data.name,
      passwordHash,
      authProvider: 'email',
      emailVerified: false,
      verificationToken,
      verificationExpiresAt: verificationExpiresAt.toISOString(),
      credits: 1, // 新用户赠送1个积分
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // 发送验证邮件
    await this.emailService.sendVerificationEmail(
      user.email,
      user.name,
      verificationToken,
      data.language || 'zh-CN'
    );

    // 生成JWT令牌（未验证状态）
    const tokens = this.generateTokens(user);

    return {
      user: this.sanitizeUser(user),
      tokens
    };
  }

  /**
   * 邮箱登录
   */
  async loginWithEmail(data: EmailLoginRequest): Promise<AuthResponse['data']> {
    // 查找用户
    const user = await this.userRepo.findByEmail(data.email);
    if (!user || user.authProvider !== 'email') {
      throw new Error('Invalid email or password');
    }

    // 检查账户锁定
    if (user.lockedUntil && new Date(user.lockedUntil) > new Date()) {
      throw new Error('Account temporarily locked due to too many failed attempts');
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(data.password, user.passwordHash);
    if (!isValidPassword) {
      // 增加失败次数
      await this.handleFailedLogin(user);
      throw new Error('Invalid email or password');
    }

    // 重置登录尝试次数
    await this.userRepo.update(user.id, {
      loginAttempts: 0,
      lockedUntil: null,
      lastLoginAt: new Date().toISOString()
    });

    // 生成JWT令牌
    const tokens = this.generateTokens(user);

    return {
      user: this.sanitizeUser(user),
      tokens
    };
  }

  /**
   * 验证邮箱
   */
  async verifyEmail(token: string): Promise<User> {
    const user = await this.userRepo.findByVerificationToken(token);
    if (!user) {
      throw new Error('Invalid verification token');
    }

    if (new Date(user.verificationExpiresAt) < new Date()) {
      throw new Error('Verification token expired');
    }

    // 更新用户状态
    const updatedUser = await this.userRepo.update(user.id, {
      emailVerified: true,
      verificationToken: null,
      verificationExpiresAt: null,
      updatedAt: new Date().toISOString()
    });

    // 发送欢迎邮件
    await this.emailService.sendWelcomeEmail(user.email, user.name);

    return this.sanitizeUser(updatedUser);
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(email: string, language: string = 'zh-CN'): Promise<void> {
    const user = await this.userRepo.findByEmail(email);
    if (!user || user.authProvider !== 'email') {
      // 为了安全，即使用户不存在也返回成功
      return;
    }

    // 生成重置令牌
    const resetToken = uuidv4();
    const resetExpiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1小时

    // 更新用户
    await this.userRepo.update(user.id, {
      passwordResetToken: resetToken,
      passwordResetExpiresAt: resetExpiresAt.toISOString(),
      updatedAt: new Date().toISOString()
    });

    // 发送重置邮件
    await this.emailService.sendPasswordResetEmail(
      user.email,
      user.name,
      resetToken,
      language
    );
  }

  /**
   * 重置密码
   */
  async resetPassword(token: string, newPassword: string): Promise<AuthResponse['data']> {
    const user = await this.userRepo.findByPasswordResetToken(token);
    if (!user) {
      throw new Error('Invalid reset token');
    }

    if (new Date(user.passwordResetExpiresAt) < new Date()) {
      throw new Error('Reset token expired');
    }

    // 加密新密码
    const passwordHash = await bcrypt.hash(newPassword, 12);

    // 更新用户
    const updatedUser = await this.userRepo.update(user.id, {
      passwordHash,
      passwordResetToken: null,
      passwordResetExpiresAt: null,
      loginAttempts: 0,
      lockedUntil: null,
      updatedAt: new Date().toISOString()
    });

    // 生成新的JWT令牌
    const tokens = this.generateTokens(updatedUser);

    return {
      user: this.sanitizeUser(updatedUser),
      tokens
    };
  }

  /**
   * 处理登录失败
   */
  private async handleFailedLogin(user: User): Promise<void> {
    const attempts = (user.loginAttempts || 0) + 1;
    const updates: any = { loginAttempts: attempts };

    // 5次失败后锁定账户30分钟
    if (attempts >= 5) {
      updates.lockedUntil = new Date(Date.now() + 30 * 60 * 1000).toISOString();
    }

    await this.userRepo.update(user.id, updates);
  }

  /**
   * 生成JWT令牌
   */
  private generateTokens(user: User): AuthTokens {
    const payload = {
      userId: user.id,
      email: user.email,
      verified: user.emailVerified
    };

    const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: '24h'
    });

    const refreshToken = jwt.sign(
      { userId: user.id },
      process.env.JWT_REFRESH_SECRET,
      { expiresIn: '7d' }
    );

    return {
      accessToken,
      refreshToken,
      expiresIn: 24 * 60 * 60 // 24小时（秒）
    };
  }

  /**
   * 清理用户敏感信息
   */
  private sanitizeUser(user: User): User {
    const { passwordHash, verificationToken, passwordResetToken, ...sanitized } = user;
    return sanitized;
  }
}
```

#### 5.4.2 邮件服务
```typescript
// backend/src/services/email.ts
export class EmailService {
  private apiKey: string;
  private fromEmail: string;
  private baseUrl: string;

  constructor(env: any) {
    this.apiKey = env.SENDGRID_API_KEY;
    this.fromEmail = env.FROM_EMAIL || '<EMAIL>';
    this.baseUrl = env.APP_URL || 'https://storyweaver.pages.dev';
  }

  /**
   * 发送验证邮件
   */
  async sendVerificationEmail(
    email: string,
    name: string,
    token: string,
    language: string = 'zh-CN'
  ): Promise<void> {
    const verificationUrl = `${this.baseUrl}/verify-email?token=${token}`;
    
    const templates = {
      'zh-CN': {
        subject: '验证您的StoryWeaver账户',
        html: this.getChineseVerificationTemplate(name, verificationUrl),
        text: `您好 ${name}，请点击以下链接验证您的邮箱：${verificationUrl}`
      },
      'en-US': {
        subject: 'Verify your StoryWeaver account',
        html: this.getEnglishVerificationTemplate(name, verificationUrl),
        text: `Hello ${name}, please click the following link to verify your email: ${verificationUrl}`
      }
    };

    const template = templates[language] || templates['zh-CN'];

    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(
    email: string,
    name: string,
    token: string,
    language: string = 'zh-CN'
  ): Promise<void> {
    const resetUrl = `${this.baseUrl}/reset-password?token=${token}`;
    
    const templates = {
      'zh-CN': {
        subject: '重置您的StoryWeaver密码',
        html: this.getChinesePasswordResetTemplate(name, resetUrl),
        text: `您好 ${name}，请点击以下链接重置您的密码：${resetUrl}`
      },
      'en-US': {
        subject: 'Reset your StoryWeaver password',
        html: this.getEnglishPasswordResetTemplate(name, resetUrl),
        text: `Hello ${name}, please click the following link to reset your password: ${resetUrl}`
      }
    };

    const template = templates[language] || templates['zh-CN'];

    await this.sendEmail({
      to: email,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    await this.sendEmail({
      to: email,
      subject: '欢迎加入StoryWeaver！',
      html: this.getWelcomeTemplate(name),
      text: `欢迎加入StoryWeaver，${name}！开始创作您的第一个故事吧。`
    });
  }

  /**
   * 发送邮件核心方法
   */
  private async sendEmail(options: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<void> {
    try {
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: options.to }]
          }],
          from: { email: this.fromEmail, name: 'StoryWeaver' },
          subject: options.subject,
          content: [
            { type: 'text/plain', value: options.text },
            { type: 'text/html', value: options.html }
          ]
        })
      });

      if (!response.ok) {
        throw new Error(`SendGrid API error: ${response.status}`);
      }
    } catch (error) {
      console.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  }

  // 邮件模板方法...
  private getChineseVerificationTemplate(name: string, url: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>验证您的邮箱</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #4F46E5;">欢迎加入StoryWeaver！</h1>
          <p>您好 ${name}，</p>
          <p>感谢您注册StoryWeaver账户。请点击下面的按钮验证您的邮箱地址：</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${url}" style="background-color: #4F46E5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">验证邮箱</a>
          </div>
          <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
          <p style="word-break: break-all; color: #666;">${url}</p>
          <p>此链接将在24小时后失效。</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            如果您没有注册StoryWeaver账户，请忽略此邮件。<br>
            StoryWeaver团队
          </p>
        </div>
      </body>
      </html>
    `;
  }

  private getEnglishVerificationTemplate(name: string, url: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Verify your email</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #4F46E5;">Welcome to StoryWeaver!</h1>
          <p>Hello ${name},</p>
          <p>Thank you for signing up for StoryWeaver. Please click the button below to verify your email address:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${url}" style="background-color: #4F46E5; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Verify Email</a>
          </div>
          <p>If the button doesn't work, please copy and paste the following link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${url}</p>
          <p>This link will expire in 24 hours.</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px;">
            If you didn't sign up for StoryWeaver, please ignore this email.<br>
            The StoryWeaver Team
          </p>
        </div>
      </body>
      </html>
    `;
  }
}
```

---

*本文档第三部分完成，下一部分将详细介绍教育版权益系统的设计...*