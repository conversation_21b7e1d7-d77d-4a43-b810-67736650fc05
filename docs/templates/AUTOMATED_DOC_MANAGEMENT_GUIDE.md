# StoryWeaver 自动化文档管理流程指南

**版本**: v1.0  
**创建时间**: 2025年7月12日  
**适用范围**: StoryWeaver项目所有变更  
**执行者**: Augment Agent / 开发团队

---

## 🎯 流程概述

### 目标
建立标准化、自动化的文档管理流程，确保每次项目变更都有完整的记录和追踪。

### 核心原则
1. **标准化**: 使用统一的模板和命名规范
2. **自动化**: 减少手动操作，提高效率
3. **可追踪**: 建立完整的变更历史链
4. **分类管理**: 按类型和时间进行文档分类

---

## 📋 标准化流程

### 第一步：变更前准备
1. **确定变更类型**
   - 结构变更 (Structure Change)
   - 功能更新 (Feature Update)
   - 文档整理 (Documentation Reorganization)
   - 配置修改 (Configuration Change)
   - 其他 (Other)

2. **生成变更文档**
   ```bash
   # 使用模板创建变更文档
   cp docs/templates/CHANGE_REPORT_TEMPLATE.md \
      docs/reports/progress/CHANGE_REPORT_$(date +%Y-%m-%d)_[功能名].md
   ```

3. **填写基本信息**
   - 执行时间
   - 变更类型
   - 执行者
   - 优先级

### 第二步：变更执行
1. **按模板执行**
   - 遵循执行清单
   - 记录每个步骤
   - 标记完成状态

2. **实时更新文档**
   - 更新执行进度
   - 记录遇到的问题
   - 记录解决方案

### 第三步：变更完成
1. **完成验证**
   - 功能测试
   - 文档检查
   - 路径验证

2. **更新DOCS_INDEX.md**
   - 添加到修改历史追踪
   - 更新相关文档路径
   - 更新版本信息

3. **归档处理**
   - 移动到相应分类目录
   - 更新目录README
   - 清理临时文件

---

## 📁 文档分类规则

### 报告分类 (`docs/reports/`)
- **progress/**: 项目进度和变更报告
- **fixes/**: 问题修复报告
- **implementation/**: 功能实现报告
- **testing/**: 测试报告

### 命名规范
```
CHANGE_REPORT_YYYY-MM-DD_[功能名].md
```

**示例**:
- `CHANGE_REPORT_2025-07-12_FRONTEND_RESTRUCTURE.md`
- `CHANGE_REPORT_2025-07-12_DOCS_REORGANIZATION.md`
- `CHANGE_REPORT_2025-07-12_API_UPDATE.md`

---

## 🔄 自动化脚本

### 创建变更文档脚本
```bash
#!/bin/bash
# create-change-report.sh

FEATURE_NAME=$1
DATE=$(date +%Y-%m-%d)
FILENAME="CHANGE_REPORT_${DATE}_${FEATURE_NAME}.md"
FILEPATH="docs/reports/progress/${FILENAME}"

if [ -z "$FEATURE_NAME" ]; then
    echo "用法: ./create-change-report.sh [功能名]"
    exit 1
fi

# 复制模板
cp docs/templates/CHANGE_REPORT_TEMPLATE.md "$FILEPATH"

# 替换模板变量
sed -i "s/\[变更标题\]/${FEATURE_NAME}/g" "$FILEPATH"
sed -i "s/YYYY年MM月DD日/$(date '+%Y年%m月%d日')/g" "$FILEPATH"
sed -i "s/YYYY-MM-DD/${DATE}/g" "$FILEPATH"

echo "✅ 变更文档已创建: $FILEPATH"
echo "📝 请编辑文档并填写详细信息"
```

### 更新DOCS_INDEX脚本
```bash
#!/bin/bash
# update-docs-index.sh

CHANGE_TYPE=$1
CHANGE_TITLE=$2
CHANGE_REPORT=$3
DATE=$(date +%Y-%m-%d)

# 添加到修改历史追踪
cat >> DOCS_INDEX.md << EOF

### ${DATE} - ${CHANGE_TITLE}
- **变更类型**: ${CHANGE_TYPE}
- **主要变更**: ${CHANGE_TITLE}
- **执行者**: Augment Agent
- **变更报告**: \`${CHANGE_REPORT}\`
EOF

echo "✅ DOCS_INDEX.md 已更新"
```

---

## 📊 质量检查清单

### 文档完整性检查
- [ ] 标题和基本信息完整
- [ ] 变更概述清晰
- [ ] 执行清单详细
- [ ] 影响分析全面
- [ ] 测试结果记录
- [ ] 后续行动明确

### 格式规范检查
- [ ] Markdown格式正确
- [ ] 表格结构完整
- [ ] 链接路径正确
- [ ] 图标使用一致
- [ ] 代码块格式正确

### 内容准确性检查
- [ ] 时间信息准确
- [ ] 路径引用正确
- [ ] 状态标识准确
- [ ] 影响范围完整
- [ ] 执行步骤可行

---

## 🔧 工具和资源

### 必需工具
- **文本编辑器**: VS Code / Vim / 其他
- **版本控制**: Git
- **文档生成**: Markdown处理器
- **自动化脚本**: Bash / Python

### 模板文件
- `docs/templates/CHANGE_REPORT_TEMPLATE.md` - 变更报告模板
- `docs/templates/AUTOMATED_DOC_MANAGEMENT_GUIDE.md` - 本指南

### 参考文档
- `DOCS_INDEX.md` - 文档索引
- `docs/reports/README.md` - 报告中心说明
- 各分类目录的README文件

---

## 📈 持续改进

### 定期评估
- **频率**: 每月评估一次
- **内容**: 流程效率、文档质量、用户反馈
- **改进**: 根据评估结果优化流程

### 版本管理
- **模板版本**: 跟踪模板文件的版本变化
- **流程版本**: 记录流程的重大更新
- **工具版本**: 维护自动化工具的版本

### 培训和推广
- **新人培训**: 为新团队成员提供流程培训
- **最佳实践**: 收集和分享最佳实践案例
- **工具改进**: 根据使用反馈改进工具

---

## ✅ 实施检查

- [ ] 模板文件创建完成
- [ ] 自动化脚本准备就绪
- [ ] 文档分类规则明确
- [ ] 质量检查清单建立
- [ ] 团队培训完成

**流程状态**: 🎉 **已建立**

---

*指南创建时间: 2025-07-12*  
*创建者: Augment Agent*  
*版本: v1.0*
