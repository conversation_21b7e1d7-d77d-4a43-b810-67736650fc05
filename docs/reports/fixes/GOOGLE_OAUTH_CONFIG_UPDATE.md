# Google OAuth 配置更新指南

## 🚨 紧急修复：更新重定向URI

### 问题描述
当前Google OAuth配置的重定向URI指向旧域名，需要更新为正确的生产环境域名。

### 当前配置状态
- **客户端ID**: `463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com`
- **当前重定向URI**: `https://storyweaver.jamintextiles.com/auth/callback`
- **需要更新为**: `https://storyweaver.pages.dev/auth/callback`

## 📋 修复步骤

### 1. 访问Google Cloud Console
1. 打开 [Google Cloud Console](https://console.cloud.google.com/)
2. 确保选择了正确的项目（StoryWeaver项目）

### 2. 导航到OAuth配置
1. 在左侧菜单中，点击 **"APIs & Services"**
2. 选择 **"Credentials"**
3. 找到客户端ID：`463479209198-u6am2scn7k0bqs1om1fnlmp8r8j43nus.apps.googleusercontent.com`
4. 点击编辑按钮（铅笔图标）

### 3. 更新重定向URI
在 **"Authorized redirect URIs"** 部分：

**添加新的重定向URI**：
```
https://storyweaver.pages.dev/auth/callback
```

**建议保留的URI列表**：
```
https://storyweaver.pages.dev/auth/callback          # 生产环境（新）
https://storyweaver.jamintextiles.com/auth/callback  # 旧域名（可选保留）
http://localhost:3000/auth/callback                  # 开发环境
http://localhost:5173/auth/callback                  # Vite开发环境
```

### 4. 保存配置
1. 点击 **"Save"** 按钮
2. 等待配置生效（通常需要几分钟）

## ✅ 验证配置

### 方法1：使用测试页面
访问项目根目录的 `test-oauth.html` 文件进行测试。

### 方法2：直接测试OAuth流程
1. 访问 `https://storyweaver.pages.dev/auth`
2. 点击Google登录按钮
3. 完成OAuth授权流程
4. 验证是否正确重定向到 `/auth/callback`

### 方法3：检查浏览器控制台
确认没有以下错误：
- `Cross-Origin-Opener-Policy` 错误
- `redirect_uri_mismatch` 错误
- 模块加载失败错误

## 🔧 故障排除

### 如果仍然出现 redirect_uri_mismatch 错误
1. 检查Google Cloud Console中的配置是否已保存
2. 清除浏览器缓存
3. 等待5-10分钟让配置生效
4. 使用无痕模式重新测试

### 如果无法访问Google Cloud Console
请联系项目管理员或具有Google Cloud项目访问权限的团队成员。

## 📞 联系信息
如果在配置过程中遇到问题，请：
1. 检查Google Cloud项目权限
2. 确认客户端ID是否正确
3. 验证重定向URI格式是否正确

## ⚠️ 重要提醒
- 配置更新后需要等待几分钟生效
- 建议保留旧的重定向URI以确保向后兼容
- 测试完成后可以移除不需要的重定向URI

---

**配置完成后，请继续执行后续的部署和验证步骤。**
