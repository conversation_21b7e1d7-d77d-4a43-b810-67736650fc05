# StoryWeaver 修复验证 - 手动测试指南

**测试日期**: 2025-07-10  
**测试环境**: 生产环境  
**前端URL**: https://storyweaver.pages.dev  
**后端URL**: https://storyweaver-api.stawky.workers.dev  

## 🚀 部署状态确认

### ✅ 部署成功确认
- **前端部署**: ✅ 成功部署到 https://9230fecb.storyweaver.pages.dev
- **后端部署**: ✅ 成功部署到 https://storyweaver-api.stawky.workers.dev
- **前端可访问性**: ✅ HTTP 200 响应
- **后端可访问性**: ✅ HTTP 200 响应

## 📋 手动测试清单

### 测试1：路由修复验证 🔍

**目标**: 验证故事创建后跳转到正确的路由 `/stories/{id}` 而非 `/story/{id}`

**测试步骤**:
1. 打开浏览器，访问 https://storyweaver.pages.dev
2. 点击"开始创作"或"创建故事"按钮
3. 填写故事创建表单（角色名称、年龄、主题等）
4. 点击"开始生成"按钮
5. **关键验证点**: 观察浏览器地址栏的URL变化

**预期结果**:
- ✅ URL应该跳转到 `/stories/{story-id}` 格式
- ❌ 不应该跳转到 `/story/{story-id}` 格式
- ✅ 页面应该正常加载，显示6阶段进度界面
- ❌ 不应该出现404错误页面

**验证方法**:
```javascript
// 在浏览器控制台中检查当前URL
console.log('Current URL:', window.location.pathname);
// 应该显示类似: /stories/abc123-def456-ghi789
```

---

### 测试2：状态显示修复验证 📊

**目标**: 验证故事状态显示为6阶段状态，不显示"未知状态"

**测试步骤**:
1. 访问 https://storyweaver.pages.dev/my-stories
2. 查看已有故事的状态显示
3. 如果没有故事，先创建一个故事
4. **关键验证点**: 检查状态文字显示

**预期结果**:
- ✅ 状态应该显示为以下之一：
  - "准备生成" (preparing)
  - "创作文本" (generating_text)
  - "绘制插图" (generating_images)
  - "合成语音" (generating_audio)
  - "最终合成" (composing)
  - "生成完毕" (completed)
  - "生成失败" (failed)
- ❌ 不应该显示"未知状态"
- ❌ 不应该显示旧的状态值如"generating"

**验证方法**:
```javascript
// 在浏览器控制台中检查状态元素
document.querySelectorAll('[class*="status"]').forEach(el => {
  console.log('Status text:', el.textContent);
});
```

---

### 测试3：智能轮询验证 ⏱️

**目标**: 验证轮询间隔为8秒左右，不再是2.5秒的高频轮询

**测试步骤**:
1. 创建一个新故事或进入正在生成的故事详情页
2. 打开浏览器开发者工具 (F12)
3. 切换到"Network"(网络)标签页
4. 清空网络日志
5. **关键验证点**: 观察API请求的时间间隔

**预期结果**:
- ✅ API请求间隔应该在7-9秒之间（目标8秒±1秒）
- ❌ 不应该出现2-3秒的高频请求
- ✅ 请求应该有轻微的时间抖动（避免雷群效应）
- ❌ 不应该触发Cloudflare的频率限制

**验证方法**:
1. 在Network标签中筛选XHR/Fetch请求
2. 查找对 `/api/stories/{id}` 的请求
3. 记录连续几次请求的时间戳
4. 计算时间间隔

**计算示例**:
```
请求1: 14:30:00
请求2: 14:30:08  (间隔: 8秒) ✅
请求3: 14:30:16  (间隔: 8秒) ✅
请求4: 14:30:25  (间隔: 9秒) ✅ (有抖动)
```

---

## 🔧 高级验证

### 开发者工具验证

**检查智能轮询日志**:
```javascript
// 在浏览器控制台中查看轮询日志
// 应该看到类似以下的日志：
// "🔄 SmartPoller: Starting with 8000ms interval"
// "📡 SmartPoller: Polling (attempt 1)"
// "✅ SmartPoller: Polling completed successfully"
```

**检查状态处理**:
```javascript
// 检查状态规范化函数是否工作
// 在控制台中测试：
if (window.normalizeStatus) {
  console.log('Legacy status mapping:');
  console.log('generating ->', window.normalizeStatus('generating'));
  console.log('text ->', window.normalizeStatus('text'));
}
```

### 网络性能验证

**检查CDN响应**:
- 查看Response Headers中的 `cf-ray` 字段
- 确认请求通过Cloudflare CDN
- 验证没有429 (Too Many Requests) 错误

**检查响应时间**:
- API响应时间应该 < 500ms
- 静态资源加载时间应该 < 200ms

---

## 📊 测试结果记录表

| 测试项目 | 预期结果 | 实际结果 | 状态 | 备注 |
|---------|---------|---------|------|------|
| 路由跳转 | `/stories/{id}` | _________ | ⬜ | ____________ |
| 状态显示 | 6阶段状态 | _________ | ⬜ | ____________ |
| 轮询间隔 | 8秒±1秒 | _________ | ⬜ | ____________ |
| 页面加载 | 无404错误 | _________ | ⬜ | ____________ |
| API响应 | < 500ms | _________ | ⬜ | ____________ |

**状态说明**: ✅ 通过 | ❌ 失败 | ⚠️ 部分通过 | ⬜ 待测试

---

## 🚨 问题报告模板

如果发现问题，请按以下格式记录：

**问题类型**: [路由/状态/轮询/其他]  
**问题描述**: ________________________________  
**重现步骤**: 
1. ________________________________
2. ________________________________
3. ________________________________

**预期行为**: ________________________________  
**实际行为**: ________________________________  
**浏览器信息**: ________________________________  
**错误截图**: [附加截图]  
**控制台错误**: ________________________________  

---

## ✅ 测试完成确认

**测试人员**: ________________  
**测试完成时间**: ________________  
**总体评估**: ⬜ 全部通过 | ⬜ 部分通过 | ⬜ 需要修复  
**部署建议**: ⬜ 可以发布 | ⬜ 需要修复后发布 | ⬜ 需要回滚  

**签名确认**: ________________
