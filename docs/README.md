# StoryWeaver 项目文档中心 📚

欢迎来到 StoryWeaver 项目的文档中心！这里包含了项目的所有技术文档、开发指南和相关资料。

## 📁 文档结构

### 🏠 [core/](./core/) - 核心项目文档
项目的基础文档，包括项目介绍、状态总结、贡献指南等核心信息。

**主要文档：**
- [README.md](./core/README.md) - 项目完整技术文档
- [项目介绍.md](./core/项目介绍.md) - 项目概念和愿景
- [项目状态总结_2025-01-02.md](./core/项目状态总结_2025-01-02.md) - 最新项目状态
- [CONTRIBUTING.md](./core/CONTRIBUTING.md) - 贡献指南
- [CODE_OF_CONDUCT.md](./core/CODE_OF_CONDUCT.md) - 行为准则
- [SECURITY.md](./core/SECURITY.md) - 安全指南

### 🏗️ [architecture/](./architecture/) - 架构设计文档
系统架构、技术选型、设计方案等技术架构相关文档。

**主要文档：**
- [StoryWeaver_完整项目计划.md](./architecture/StoryWeaver_完整项目计划.md) - 项目整体规划
- [StoryWeaver_DurableObjects_实施计划.md](./architecture/StoryWeaver_DurableObjects_实施计划.md) - 实时功能架构
- [technical_implementation.md](./architecture/technical_implementation.md) - 技术实现方案
- [website_framework.md](./architecture/website_framework.md) - 网站框架设计
- [ui_ux_design_guide.md](./architecture/ui_ux_design_guide.md) - UI/UX设计指南

### 👨‍💻 [development/](./development/) - 开发指南
开发环境搭建、开发流程、最佳实践等开发相关文档。

**主要文档：**
- [DEVELOPMENT_GUIDE.md](./development/DEVELOPMENT_GUIDE.md) - 基础开发指南
- [DEVELOPMENT_GUIDE_PART2.md](./development/DEVELOPMENT_GUIDE_PART2.md) - 高级开发技巧
- [DEVELOPMENT_GUIDE_PART3.md](./development/DEVELOPMENT_GUIDE_PART3.md) - 测试和调试
- [DEVELOPMENT_GUIDE_PART4.md](./development/DEVELOPMENT_GUIDE_PART4.md) - 部署和维护
- [FRONTEND_DEVELOPMENT_GUIDE.md](./development/FRONTEND_DEVELOPMENT_GUIDE.md) - 前端开发指南
- [DurableObjects_快速开始指南.md](./development/DurableObjects_快速开始指南.md) - 实时功能开发
- [PERFORMANCE_GUIDE.md](./development/PERFORMANCE_GUIDE.md) - 性能优化指南

### 🚀 [deployment/](./deployment/) - 部署文档
部署流程、环境配置、生产环境管理等部署相关文档。

**主要文档：**
- [DEPLOYMENT_GUIDE.md](./deployment/DEPLOYMENT_GUIDE.md) - 基础部署指南
- [PRODUCTION_DEPLOYMENT_GUIDE.md](./deployment/PRODUCTION_DEPLOYMENT_GUIDE.md) - 生产环境部署
- [PRODUCTION_DEPLOYMENT_REPORT.md](./deployment/PRODUCTION_DEPLOYMENT_REPORT.md) - 部署状态报告

### 🔌 [api/](./api/) - API文档
后端API接口文档、环境配置、测试指南等后端相关文档。

**主要文档：**
- [README.md](./api/README.md) - 后端API概览
- [API_DOCUMENTATION.md](./api/API_DOCUMENTATION.md) - 完整API文档
- [ENVIRONMENT_VARIABLES.md](./api/ENVIRONMENT_VARIABLES.md) - 环境变量配置
- [DEPLOYMENT.md](./api/DEPLOYMENT.md) - 后端部署指南
- [TESTING.md](./api/TESTING.md) - API测试指南

### 🎨 [frontend/](./frontend/) - 前端文档
前端开发、构建、部署、支付集成等前端相关文档。

**主要文档：**
- [README.md](./frontend/README.md) - 前端项目概览
- [README-StoryWeaver.md](./frontend/README-StoryWeaver.md) - StoryWeaver前端说明
- [BUILD-GUIDE.md](./frontend/BUILD-GUIDE.md) - 前端构建指南
- [DEPLOYMENT-FULL.md](./frontend/DEPLOYMENT-FULL.md) - 前端部署指南
- [STRIPE_INTEGRATION.md](./frontend/STRIPE_INTEGRATION.md) - Stripe支付集成
- [test-stripe-payment.md](./frontend/test-stripe-payment.md) - 支付功能测试

### 📁 [archive/](./archive/) - 历史归档
历史修复记录、过时文档等归档内容。

**子目录：**
- [fixes/](./archive/fixes/) - 历史问题修复记录

## 🔍 如何使用这些文档

### 新手开发者
1. 从 [core/README.md](./core/README.md) 开始了解项目整体
2. 阅读 [core/项目介绍.md](./core/项目介绍.md) 了解项目背景
3. 按照 [development/DEVELOPMENT_GUIDE.md](./development/DEVELOPMENT_GUIDE.md) 搭建开发环境
4. 查看 [architecture/](./architecture/) 了解系统架构

### 前端开发者
1. 查看 [frontend/README.md](./frontend/README.md) 了解前端项目
2. 按照 [frontend/BUILD-GUIDE.md](./frontend/BUILD-GUIDE.md) 进行构建
3. 参考 [development/FRONTEND_DEVELOPMENT_GUIDE.md](./development/FRONTEND_DEVELOPMENT_GUIDE.md)

### 后端开发者
1. 查看 [api/README.md](./api/README.md) 了解后端架构
2. 阅读 [api/API_DOCUMENTATION.md](./api/API_DOCUMENTATION.md) 了解API接口
3. 按照 [api/ENVIRONMENT_VARIABLES.md](./api/ENVIRONMENT_VARIABLES.md) 配置环境

### 运维人员
1. 查看 [deployment/](./deployment/) 目录下的部署文档
2. 参考 [deployment/PRODUCTION_DEPLOYMENT_GUIDE.md](./deployment/PRODUCTION_DEPLOYMENT_GUIDE.md)

## 📝 文档维护

- 所有文档都应该保持最新状态
- 新增功能时请同步更新相关文档
- 修复问题后请更新对应的文档
- 过时的文档应移动到 [archive/](./archive/) 目录

## 🤝 贡献文档

如果您发现文档有错误或需要改进，请：

1. 创建 Issue 报告问题
2. 提交 Pull Request 修复文档
3. 遵循项目的文档编写规范

---

*文档中心最后更新: 2025-01-04*