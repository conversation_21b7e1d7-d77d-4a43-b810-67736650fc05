# StoryWeaver 生产环境部署指南

## 🎯 部署概述

本指南将帮助您将StoryWeaver项目部署到Cloudflare平台的生产环境。

### 部署架构
- **前端**: Cloudflare Pages (https://storyweaver.pages.dev)
- **后端**: Cloudflare Workers (https://storyweaver-api.stawky.workers.dev)
- **存储**: Cloudflare R2 + D1 Database

## 📋 部署前准备

### 1. 环境要求
- Node.js >= 18
- Wrangler CLI (最新版本)
- Cloudflare账户

### 2. 安装Wrangler CLI
```bash
npm install -g wrangler
```

### 3. 登录Cloudflare
```bash
wrangler login
```

## 🚀 部署步骤

### 第一步：部署后端

1. **进入后端目录**
```bash
cd backend
```

2. **设置生产环境密钥**
```bash
./setup-production-secrets.sh
```
需要输入以下密钥：
- Gemini API Key
- Google Client Secret
- Stripe Secret Key
- Stripe Webhook Secret
- JWT Secret

3. **部署后端服务**
```bash
./deploy-production.sh
```

4. **验证后端部署**
```bash
curl https://storyweaver-api.stawky.workers.dev/health
```

### 第二步：部署前端

1. **进入前端目录**
```bash
cd frontend
```

2. **部署前端应用**
```bash
./deploy-production.sh
```

3. **验证前端部署**
访问: https://storyweaver.pages.dev

## 🔧 生产环境配置

### 后端配置 (backend/wrangler.toml)
```toml
[env.production]
[env.production.vars]
ENVIRONMENT = "production"
CORS_ORIGIN = "https://storyweaver.pages.dev"

[[env.production.r2_buckets]]
binding = "ASSETS"
bucket_name = "storyweaver-assets"

[[env.production.d1_databases]]
binding = "DB"
database_name = "storyweaver"
```

### 前端配置 (frontend/.env.production)
```env
VITE_API_BASE_URL=https://storyweaver-api.stawky.workers.dev/api
VITE_ENVIRONMENT=production
VITE_ENABLE_DEBUG=false
VITE_DEBUG_MODE=false
```

## 🛡️ 安全配置

### 1. 环境变量安全
- 所有敏感信息通过`wrangler secret`管理
- 生产环境禁用调试模式
- 移除所有调试路由和日志

### 2. CORS配置
- 仅允许生产域名访问
- 禁用开发环境域名

### 3. 内容安全
- 启用Gemini Safety API
- 用户输入验证和清理

## 📊 监控和日志

### 查看实时日志
```bash
# 后端日志
wrangler tail --env production

# 前端访问日志
wrangler pages deployment list --project-name=storyweaver
```

### 性能监控
- Cloudflare Analytics
- Workers Analytics
- Pages Analytics

## 🧪 功能验证

### 1. API健康检查
```bash
curl https://storyweaver-api.stawky.workers.dev/health
```

### 2. 用户认证测试
- Google OAuth登录
- JWT令牌验证

### 3. 核心功能测试
- 故事生成
- 图片上传
- 音频生成
- 支付流程

## 🔄 更新部署

### 后端更新
```bash
cd backend
npm run build
wrangler deploy --env production
```

### 前端更新
```bash
cd frontend
npm run build
wrangler pages deploy dist --project-name=storyweaver
```

## 🐛 故障排除

### 常见问题

#### 1. 部署失败
- 检查Wrangler CLI版本
- 验证Cloudflare账户权限
- 确认资源配置正确

#### 2. API连接错误
- 检查CORS配置
- 验证环境变量设置
- 确认域名解析

#### 3. 功能异常
- 查看实时日志
- 检查环境变量
- 验证数据库连接

### 调试命令
```bash
# 检查密钥设置
wrangler secret list --env production

# 查看资源状态
wrangler r2 bucket list
wrangler d1 list

# 测试数据库连接
wrangler d1 console storyweaver --env production
```

## 📞 支持信息

### 生产环境地址
- **前端**: https://storyweaver.pages.dev
- **后端**: https://storyweaver-api.stawky.workers.dev
- **健康检查**: https://storyweaver-api.stawky.workers.dev/health

### 重要提醒
1. 生产环境已禁用所有调试功能
2. 所有敏感信息通过Cloudflare安全管理
3. 定期检查和更新依赖包
4. 监控系统性能和错误日志

## ✅ 部署检查清单

- [ ] Wrangler CLI已安装并登录
- [ ] 生产环境密钥已设置
- [ ] 后端编译无错误
- [ ] 后端部署成功
- [ ] 前端构建无错误
- [ ] 前端部署成功
- [ ] API健康检查通过
- [ ] 前后端连接正常
- [ ] 核心功能测试通过
- [ ] 监控和日志配置完成

🎉 **恭喜！StoryWeaver已成功部署到生产环境！**
