# StoryWeaver 修复版本部署指南

**版本**: v1.2.0  
**修复内容**: 路由、状态显示、智能轮询  
**部署日期**: 2025-07-10  

## 🚀 快速部署

### 前置条件
- [x] Cloudflare账户和API Token
- [x] Wrangler CLI已安装
- [x] Node.js 18+ 环境
- [x] 项目代码已更新到最新版本

### 一键部署脚本

```bash
#!/bin/bash
# 快速部署脚本

echo "🚀 开始部署StoryWeaver修复版本..."

# 1. 部署前端
echo "📦 部署前端到Cloudflare Pages..."
cd frontend-production
npm run build
wrangler pages publish dist --project-name=storyweaver

# 2. 部署后端
echo "🔧 部署后端到Cloudflare Workers..."
cd ../backend
wrangler deploy --env production

echo "✅ 部署完成！"
```

## 📋 详细部署步骤

### 步骤1：前端部署

```bash
# 进入前端生产目录
cd /path/to/storyweaver/frontend-production

# 安装依赖（如果需要）
npm ci

# 构建生产版本
npm run build

# 部署到Cloudflare Pages
wrangler pages publish dist --project-name=storyweaver
```

**预期输出**：
```
✨ Success! Uploaded 35 files (2.1 sec)

✨ Deployment complete! Take a peek over at https://storyweaver.pages.dev
```

### 步骤2：后端部署

```bash
# 进入后端目录
cd /path/to/storyweaver/backend

# 部署到Cloudflare Workers
wrangler deploy --env production
```

**预期输出**：
```
 ⛅️ wrangler 3.x.x
------------------
Total Upload: 1.2 MB / gzip: 350 KB
Uploaded storyweaver-api (2.3 sec)
Published storyweaver-api (0.8 sec)
  https://storyweaver-api.stawky.workers.dev
```

### 步骤3：验证部署

```bash
# 验证前端
curl -I https://storyweaver.pages.dev

# 验证后端API
curl https://storyweaver-api.stawky.workers.dev/api/stories

# 验证路由修复
curl -I https://storyweaver.pages.dev/stories/test-id
```

## 🔍 部署验证清单

### 前端验证
- [ ] 主页加载正常 (https://storyweaver.pages.dev)
- [ ] 路由 `/stories/:id` 返回200状态码
- [ ] 静态资源加载正常
- [ ] 控制台无JavaScript错误

### 后端验证  
- [ ] API健康检查通过 (`/api/stories`)
- [ ] 状态接口返回新的6阶段状态
- [ ] 认证流程正常工作
- [ ] 数据库连接正常

### 功能验证
- [ ] 故事创建后正确跳转到 `/stories/{id}`
- [ ] 状态显示为6阶段状态，不显示"未知状态"
- [ ] 轮询频率为8秒左右，不触发CDN拦截
- [ ] 进度更新实时显示

## 🛠️ 故障排除

### 常见问题

#### 1. 前端部署失败
```bash
# 错误：Authentication error
# 解决：设置Cloudflare API Token
export CLOUDFLARE_API_TOKEN=your_token_here

# 错误：Project not found
# 解决：检查项目名称
wrangler pages publish dist --project-name=storyweaver
```

#### 2. 后端部署失败
```bash
# 错误：Wrangler not found
# 解决：安装Wrangler CLI
npm install -g wrangler

# 错误：Environment variables missing
# 解决：检查wrangler.toml配置
```

#### 3. 路由404错误
```bash
# 检查前端路由配置
grep -r "stories/:id" frontend-production/src/router/

# 检查跳转逻辑
grep -r "navigate.*stories" frontend-production/src/
```

#### 4. 状态显示异常
```bash
# 检查后端状态返回
curl https://storyweaver-api.stawky.workers.dev/api/stories | jq '.data.items[].status'

# 检查前端状态处理
grep -r "normalizeStatus" frontend-production/src/
```

### 回滚方案

如果部署出现问题，可以快速回滚：

```bash
# 前端回滚到上一个版本
wrangler pages deployment list --project-name=storyweaver
wrangler pages deployment rollback <deployment-id> --project-name=storyweaver

# 后端回滚
wrangler rollback --env production
```

## 📊 监控和观察

### 部署后监控

1. **实时监控**（前15分钟）：
```bash
# 监控API响应
watch -n 30 'curl -s https://storyweaver-api.stawky.workers.dev/api/stories | jq .success'

# 监控前端状态
watch -n 30 'curl -I https://storyweaver.pages.dev/stories/test'
```

2. **Cloudflare Analytics**：
   - 访问 Cloudflare Dashboard
   - 检查 Pages 和 Workers 的分析数据
   - 监控错误率和响应时间

3. **用户反馈收集**：
   - 监控客服渠道
   - 检查用户报告的问题
   - 收集性能反馈

### 关键指标

| 指标 | 目标值 | 监控方法 |
|------|--------|----------|
| 前端可用性 | >99.9% | Cloudflare Analytics |
| API响应时间 | <500ms | Workers Analytics |
| 路由成功率 | >99% | 访问日志分析 |
| 轮询频率 | 8-30秒 | 浏览器开发者工具 |
| 状态准确性 | 100% | 用户反馈 |

## 🔄 后续优化建议

### 短期优化（1周内）
1. **性能监控**：设置自动化监控告警
2. **用户反馈**：收集用户体验数据
3. **错误追踪**：完善错误日志和追踪

### 中期优化（1个月内）
1. **A/B测试**：测试不同轮询策略的效果
2. **缓存优化**：优化API响应缓存策略
3. **用户体验**：根据反馈优化界面交互

### 长期优化（3个月内）
1. **WebSocket升级**：考虑升级到WebSocket实时通信
2. **边缘计算**：利用Cloudflare Edge优化性能
3. **国际化**：支持多语言和多地区部署

## 📞 支持联系

### 技术支持
- **开发团队**：Augment Agent
- **部署文档**：本文档
- **问题报告**：GitHub Issues

### 紧急联系
- **生产环境问题**：立即回滚并联系技术团队
- **用户投诉**：记录问题并优先处理
- **安全问题**：立即暂停服务并评估影响

---

**部署检查完成时间**: ___________  
**部署负责人签名**: ___________  
**验证负责人签名**: ___________  

✅ **部署状态**: 准备就绪，可以开始部署
